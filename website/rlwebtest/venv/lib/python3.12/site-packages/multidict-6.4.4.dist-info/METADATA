Metadata-Version: 2.4
Name: multidict
Version: 6.4.4
Summary: multidict implementation
Home-page: https://github.com/aio-libs/multidict
Author: <PERSON>
Author-email: and<PERSON>.<EMAIL>
License: Apache 2
Project-URL: Chat: Matrix, https://matrix.to/#/#aio-libs:matrix.org
Project-URL: Chat: Matrix Space, https://matrix.to/#/#aio-libs-space:matrix.org
Project-URL: CI: GitHub, https://github.com/aio-libs/multidict/actions
Project-URL: Code of Conduct, https://github.com/aio-libs/.github/blob/master/CODE_OF_CONDUCT.md
Project-URL: Coverage: codecov, https://codecov.io/github/aio-libs/multidict
Project-URL: Docs: Changelog, https://multidict.aio-libs.org/en/latest/changes/
Project-URL: Docs: RTD, https://multidict.aio-libs.org
Project-URL: GitHub: issues, https://github.com/aio-libs/multidict/issues
Project-URL: GitHub: repo, https://github.com/aio-libs/multidict
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Python: >=3.9
Description-Content-Type: text/x-rst
License-File: LICENSE
Requires-Dist: typing-extensions>=4.1.0; python_version < "3.11"
Dynamic: license-file

=========
multidict
=========

.. image:: https://github.com/aio-libs/multidict/actions/workflows/ci-cd.yml/badge.svg
   :target: https://github.com/aio-libs/multidict/actions
   :alt: GitHub status for master branch

.. image:: https://codecov.io/gh/aio-libs/multidict/branch/master/graph/badge.svg?flag=pytest
   :target: https://codecov.io/gh/aio-libs/multidict?flags[]=pytest
   :alt: Coverage metrics

.. image:: https://img.shields.io/pypi/v/multidict.svg
   :target: https://pypi.org/project/multidict
   :alt: PyPI

.. image:: https://readthedocs.org/projects/multidict/badge/?version=latest
   :target: https://multidict.aio-libs.org
   :alt: Read The Docs build status badge

.. image:: https://img.shields.io/endpoint?url=https://codspeed.io/badge.json
   :target: https://codspeed.io/aio-libs/multidict
   :alt: CodSpeed

.. image:: https://img.shields.io/pypi/pyversions/multidict.svg
   :target: https://pypi.org/project/multidict
   :alt: Python versions

.. image:: https://img.shields.io/matrix/aio-libs:matrix.org?label=Discuss%20on%20Matrix%20at%20%23aio-libs%3Amatrix.org&logo=matrix&server_fqdn=matrix.org&style=flat
   :target: https://matrix.to/#/%23aio-libs:matrix.org
   :alt: Matrix Room — #aio-libs:matrix.org

.. image:: https://img.shields.io/matrix/aio-libs-space:matrix.org?label=Discuss%20on%20Matrix%20at%20%23aio-libs-space%3Amatrix.org&logo=matrix&server_fqdn=matrix.org&style=flat
   :target: https://matrix.to/#/%23aio-libs-space:matrix.org
   :alt: Matrix Space — #aio-libs-space:matrix.org

Multidict is dict-like collection of *key-value pairs* where key
might occur more than once in the container.

Introduction
------------

*HTTP Headers* and *URL query string* require specific data structure:
*multidict*. It behaves mostly like a regular ``dict`` but it may have
several *values* for the same *key* and *preserves insertion ordering*.

The *key* is ``str`` (or ``istr`` for case-insensitive dictionaries).

``multidict`` has four multidict classes:
``MultiDict``, ``MultiDictProxy``, ``CIMultiDict``
and ``CIMultiDictProxy``.

Immutable proxies (``MultiDictProxy`` and
``CIMultiDictProxy``) provide a dynamic view for the
proxied multidict, the view reflects underlying collection changes. They
implement the ``collections.abc.Mapping`` interface.

Regular mutable (``MultiDict`` and ``CIMultiDict``) classes
implement ``collections.abc.MutableMapping`` and allows them to change
their own content.


*Case insensitive* (``CIMultiDict`` and
``CIMultiDictProxy``) assume the *keys* are case
insensitive, e.g.::

   >>> dct = CIMultiDict(key='val')
   >>> 'Key' in dct
   True
   >>> dct['Key']
   'val'

*Keys* should be ``str`` or ``istr`` instances.

The library has optional C Extensions for speed.


License
-------

Apache 2

Library Installation
--------------------

.. code-block:: bash

   $ pip install multidict

The library is Python 3 only!

PyPI contains binary wheels for Linux, Windows and MacOS.  If you want to install
``multidict`` on another operating system (or *Alpine Linux* inside a Docker) the
tarball will be used to compile the library from source.  It requires a C compiler and
Python headers to be installed.

To skip the compilation, please use the `MULTIDICT_NO_EXTENSIONS` environment variable,
e.g.:

.. code-block:: bash

   $ MULTIDICT_NO_EXTENSIONS=1 pip install multidict

Please note, the pure Python (uncompiled) version is about 20-50 times slower depending on
the usage scenario!!!

For extension development, set the ``MULTIDICT_DEBUG_BUILD`` environment variable to compile
the extensions in debug mode:

.. code-block:: console

   $ MULTIDICT_DEBUG_BUILD=1 pip install multidict

Changelog
---------
See `RTD page <http://multidict.aio-libs.org/en/latest/changes>`_.
