2025-07-08T15:15:33.028+0800	INFO	logs/logger.go:38	CodeKG process 1
2025-07-08T15:15:33.029+0800	INFO	logs/logger.go:38	CKG local embedding: true, embedding storage type is sqlite_vec
2025-07-08T15:15:33.029+0800	INFO	logs/logger.go:38	ckg version is 0.0.90
2025-07-08T15:15:33.029+0800	INFO	logs/logger.go:38	server start at 51009
2025-07-08T15:15:33.029+0800	INFO	logs/logger.go:38	tea report using default url
2025-07-08T15:15:33.030+0800	INFO	logs/logger.go:61	0217519589330280000000000000000000000000000000034ffb4 afterPanic %!s(bool=false) memory usage: {18651296 20413032 32218128 0 54524 17032 18651296 24084480 4341760 19742720 4341760 37492 1081344 1081344 179424 309624 1200 62400 5255 4132088 2542937 26395432 1751958933015003552 684346 [89173 244225 222511 128437 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0] [1751958932978578057 1751958932985388235 1751958932996468358 1751958933015003552 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0] 4 0 0.02684236639955719 true false [{0 0 0} {8 1176 378} {16 13501 3662} {24 1555 79} {32 5940 1629} {48 4240 455} {64 2361 705} {80 983 372} {96 467 71} {112 14653 6767} {128 1420 688} {144 108 0} {160 1804 364} {176 107 0} {192 65 23} {208 432 15} {224 19 1} {240 26 0} {256 896 27} {288 56 7} {320 841 328} {352 20 1} {384 64 13} {416 200 4} {448 11 0} {480 12 0} {512 28 0} {576 31 4} {640 753 224} {704 16 0} {768 18 4} {896 127 7} {1024 47 1} {1152 19 4} {1280 472 102} {1408 5 0} {1536 8 2} {1792 70 1} {2048 9 0} {2304 10 2} {2688 170 41} {3072 9 1} {3200 2 0} {3456 1 0} {4096 126 0} {4864 3 0} {5376 70 12} {6144 4 0} {6528 0 0} {6784 1 0} {6912 0 0} {8192 166 0} {9472 44 1} {9728 0 0} {10240 1 0} {10880 13 2} {12288 2 0} {13568 0 0} {14336 150 0} {16384 1 0} {18432 2 0}]}, goroutine number:  77
2025-07-08T15:15:33.031+0800	INFO	logs/logger.go:38	[PeriodicInitManager] started periodic task, interval 10 minutes
2025-07-08T15:15:33.031+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] started periodic task, interval 10 minutes
2025-07-08T15:15:33.173+0800	INFO	logs/logger.go:61	02175195893302900000000000000000000000000000000a4c33a codekg feature gate config is fetch for , map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 2025070815153316E15DDCCFE0EB0EA7CB
2025-07-08T15:15:33.173+0800	INFO	logs/logger.go:61	02175195893302900000000000000000000000000000000a4c33a exit when ppid changed is enabled
2025-07-08T15:15:35.451+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T15:15:35.451+0800	INFO	logs/logger.go:61	021751958935451000000000000000000000000000000003f70ec user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T15:15:35.482+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T15:15:36.336+0800	INFO	logs/logger.go:61	021751958935482000000000000000000000000000000009fcb77 single flight key: /protocol.CodeKG/SetUp_99f6354eae5d0cc79a43e809c73765e751a4e91e0b03967fed6ff5e841522fe5 is shared
2025-07-08T15:15:36.336+0800	INFO	logs/logger.go:61	0217519589354910000000000000000000000000000000075dc47 single flight key: /protocol.CodeKG/SetUp_99f6354eae5d0cc79a43e809c73765e751a4e91e0b03967fed6ff5e841522fe5 is shared
2025-07-08T15:15:36.336+0800	INFO	logs/logger.go:61	021751958935489000000000000000000000000000000001631ee single flight key: /protocol.CodeKG/SetUp_99f6354eae5d0cc79a43e809c73765e751a4e91e0b03967fed6ff5e841522fe5 is shared
2025-07-08T15:15:36.338+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T15:15:36.339+0800	INFO	logs/logger.go:61	021751958936338000000000000000000000000000000006253c7 user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T15:15:36.341+0800	INFO	logs/logger.go:61	021751958936338000000000000000000000000000000006253c7 single flight key: /protocol.CodeKG/RefreshToken_315acb3a0f9c62c968bbdb42b155dd222798d170df03cb984db515271654b215 is shared
2025-07-08T15:15:36.342+0800	INFO	logs/logger.go:61	021751958936340000000000000000000000000000000005e36c9 single flight key: /protocol.CodeKG/RefreshToken_315acb3a0f9c62c968bbdb42b155dd222798d170df03cb984db515271654b215 is shared
2025-07-08T15:15:36.522+0800	INFO	logs/logger.go:61	02175195893634300000000000000000000000000000000ae14b9 codekg ab config is fetch for ****************, &{QueryRewrite:map[] Rerank:map[] Embedding:map[] Chunking:map[] RecallStrategy:map[ner_recall:false] Implementation:map[]}
2025-07-08T15:15:36.667+0800	INFO	logs/logger.go:61	02175195893634300000000000000000000000000000000ae14b9 codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 202507081515364F92E2A8B55FB60E97E7
2025-07-08T15:15:36.667+0800	INFO	logs/logger.go:61	02175195893634300000000000000000000000000000000ae14b9 single flight key: /protocol.CodeKG/IsCKGEnabledForNonWorkspaceScenario_e0fae3b9915c3c93f15557226f32514275b2080ff6b486fb13979bc9d14c7659 is shared
2025-07-08T15:15:36.667+0800	INFO	logs/logger.go:61	02175195893634400000000000000000000000000000000be6a6e single flight key: /protocol.CodeKG/IsCKGEnabledForNonWorkspaceScenario_e0fae3b9915c3c93f15557226f32514275b2080ff6b486fb13979bc9d14c7659 is shared
2025-07-08T15:15:36.831+0800	INFO	logs/logger.go:38	DocumentSelect request start, documents: [], userID: ****************, req filePaths size: 1, files: [rXhif7BhZh8w0inHQkrJu37hn4rawO8J9KafcRmoVmTPRKvgaWuAOVTa9Rb9o2OVknUAIDy29EObYcp3NdLPcB94huVkk4ah5bGxTSeT9cxKlDrjSVi4]
2025-07-08T15:15:36.836+0800	INFO	logs/logger.go:38	Init repo request start, req: projects:{project_id:"/home/<USER>/projects" storage_path:"/home/<USER>/.trae-cn-server/.ckg/storage/u_abde125458973caa655a189ade95d990b68d8006384e55770e4c66be1a688b9c" ignore_file:"/home/<USER>/projects/.trae/.ignore"} user_id:"****************"
2025-07-08T15:15:36.837+0800	ERROR	logs/logger.go:68	0217519589368360000000000000000000000000000000079feed [migrateIndexData] remove file err: remove /home/<USER>/.trae-cn-server/.ckg/storage/u_abde125458973caa655a189ade95d990b68d8006384e55770e4c66be1a688b9c/env_codekg.db: no such file or directory, file storage: /home/<USER>/.trae-cn-server/.ckg/storage/u_abde125458973caa655a189ade95d990b68d8006384e55770e4c66be1a688b9c/env_codekg.db
2025-07-08T15:15:36.837+0800	INFO	logs/logger.go:61	0217519589368360000000000000000000000000000000079feed InitProject start
2025-07-08T15:15:36.953+0800	INFO	logs/logger.go:61	0217519589368360000000000000000000000000000000079feed codekg config is fetch for ****************, &{FileCountLimit:3000 FileSizeThreshold:1048576 SingleFileLineThreshold:5000 DatastoreName:default-latest EnableRerank:false DefaultRecallNum:25 ForceIndexFileCountLimit:10000 IndexFileLineLimit:1500000 DefaultIgnoreRules:# Dir blacklist
node_modules/
__pycache__/
venv/
vendor/
tmp/
temp/
Pods/
bundle/
obj/
target/
output/
dist/
eggs/
gradle/
# File Ext blacklist
*.class
*.jar
*.dex
*.o
*.obj
*.a
*.out
*.pyc
*.pyo
*.pyd
*.egg
*.whl
*.log
*.lock
*.bak
**/.env
**/.env.*
**/credentials.json
**/credentials.*.json
**/secret.json
**/secrets.json
**/*.key
**/*.pem
**/*.pfx
**/*.p12
**/*.crt
**/*.cer
**/id_rsa
**/id_dsa
**/.ssh/id_*
**/id_ed25519
**/id_ecdsa
**/id_ecdsa_sk
**/id_ed25519_sk
 DefaultOmitProjectRoots: DebounceTime:3 BinaryContentCheckThreshold:0 RecentCursorRelationNum:5 TaskSleepTimeFactor:1 RAGRemoteIndexThreshold:10000 RAGRepoWhiteList: RAGRepoNotOnlyWhiteList:false PeriodicInitIntervalMinutes:10 ExcludeFolderRule: ExcludeFileRule: ExcludeExtFileRule: IncludeExtFileRule: OutlineRetrieveConfig:0xc001382190 VirtualProjectConfig:0xc0013821e0}
2025-07-08T15:15:36.954+0800	INFO	logs/logger.go:61	0217519589368360000000000000000000000000000000079feed codekg config &{FileCountLimit:3000 FileSizeThreshold:1048576 SingleFileLineThreshold:5000 DatastoreName:default-latest EnableRerank:false DefaultRecallNum:25 ForceIndexFileCountLimit:10000 IndexFileLineLimit:1500000 DefaultIgnoreRules:# Dir blacklist
node_modules/
__pycache__/
venv/
vendor/
tmp/
temp/
Pods/
bundle/
obj/
target/
output/
dist/
eggs/
gradle/
# File Ext blacklist
*.class
*.jar
*.dex
*.o
*.obj
*.a
*.out
*.pyc
*.pyo
*.pyd
*.egg
*.whl
*.log
*.lock
*.bak
**/.env
**/.env.*
**/credentials.json
**/credentials.*.json
**/secret.json
**/secrets.json
**/*.key
**/*.pem
**/*.pfx
**/*.p12
**/*.crt
**/*.cer
**/id_rsa
**/id_dsa
**/.ssh/id_*
**/id_ed25519
**/id_ecdsa
**/id_ecdsa_sk
**/id_ed25519_sk
 DefaultOmitProjectRoots: DebounceTime:3 BinaryContentCheckThreshold:0 RecentCursorRelationNum:5 TaskSleepTimeFactor:1 RAGRemoteIndexThreshold:10000 RAGRepoWhiteList: RAGRepoNotOnlyWhiteList:false PeriodicInitIntervalMinutes:10 ExcludeFolderRule: ExcludeFileRule: ExcludeExtFileRule: IncludeExtFileRule: OutlineRetrieveConfig:0xc001382190 VirtualProjectConfig:0xc0013821e0}
2025-07-08T15:15:36.954+0800	INFO	logs/logger.go:61	0217519589368360000000000000000000000000000000079feed codekg ab config &{QueryRewrite:map[] Rerank:map[] Embedding:map[] Chunking:map[] RecallStrategy:map[ner_recall:false] Implementation:map[]}
2025-07-08T15:15:36.954+0800	INFO	logs/logger.go:61	0217519589368360000000000000000000000000000000079feed ckg_init start
2025-07-08T15:15:36.955+0800	INFO	logs/logger.go:61	0217519589368360000000000000000000000000000000079feed [RemoveRedundantStorage] auto clean, delete db paths: []
2025-07-08T15:15:36.955+0800	INFO	logs/logger.go:61	0217519589368360000000000000000000000000000000079feed ignore service start
2025-07-08T15:15:36.955+0800	WARN	logs/logger.go:75	0217519589368360000000000000000000000000000000079feed failed to read customized ignore file, err: open /home/<USER>/projects/.trae/.ignore: no such file or directory
2025-07-08T15:15:36.958+0800	INFO	logs/logger.go:61	0217519589368360000000000000000000000000000000079feed collect all uri start
2025-07-08T15:15:36.965+0800	INFO	logs/logger.go:61	0217519589368360000000000000000000000000000000079feed /home/<USER>/projects has 38 files
2025-07-08T15:15:36.966+0800	INFO	logs/logger.go:61	0217519589368360000000000000000000000000000000079feed get storage smoothly
2025-07-08T15:15:36.969+0800	INFO	logs/logger.go:61	0217519589368360000000000000000000000000000000079feed [Init] InitRelationManager failed project is /home/<USER>/projects err is relation manager init failed, manager version: 
2025-07-08T15:15:37.682+0800	INFO	logs/logger.go:61	0217519589368360000000000000000000000000000000079feed [GetEmbeddingStorageFromUserStorage] embedding db type is 1
2025-07-08T15:15:37.683+0800	INFO	logs/logger.go:61	0217519589368360000000000000000000000000000000079feed [InitProject] from v1 -> v1, from codekg_bge_m3 -> codekg_bge_m3, from 2 -> 2
2025-07-08T15:15:37.683+0800	INFO	logs/logger.go:61	0217519589368360000000000000000000000000000000079feed [UpsertProjectID] update project id record. codekg_db_path: /home/<USER>/.trae-cn-server/.ckg/storage/u_abde125458973caa655a189ade95d990b68d8006384e55770e4c66be1a688b9c/projects_2a58fa01266619_63d06e_codekg.db, embedding_db_path: /home/<USER>/.trae-cn-server/.ckg/storage/u_abde125458973caa655a189ade95d990b68d8006384e55770e4c66be1a688b9c/projects_2a58fa01266619_dthlw0_embedding_vec.db, embedding_storage_type: 1, db_version: 2, ckg_version: 0.0.90
2025-07-08T15:15:37.686+0800	INFO	logs/logger.go:61	0217519589368360000000000000000000000000000000079feed create local data storage
2025-07-08T15:15:37.688+0800	INFO	logs/logger.go:61	0217519589368360000000000000000000000000000000079feed calculate all files that need indexing
2025-07-08T15:15:37.688+0800	INFO	logs/logger.go:61	0217519589368360000000000000000000000000000000079feed currently use local embedding storage? true
2025-07-08T15:15:37.688+0800	INFO	logs/logger.go:61	0217519589368360000000000000000000000000000000079feed [localIndex] before gc, mem stat, alloc: 18.99 MB, in use: 21.16 MB
2025-07-08T15:15:37.716+0800	INFO	logs/logger.go:61	0217519589368360000000000000000000000000000000079feed [localIndex] gc cost: 28 ms
2025-07-08T15:15:37.716+0800	INFO	logs/logger.go:61	0217519589368360000000000000000000000000000000079feed [localIndex] after gc, mem stat, alloc: 16.96 MB, in use: 20.16 MB
2025-07-08T15:15:37.727+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] updated user ****************
2025-07-08T15:15:37.728+0800	INFO	logs/logger.go:61	0217519589368360000000000000000000000000000000079feed [localIndex] project /home/<USER>/projects initialize finished, build time: 41 ms, file indexed: 0, folder indexed: 0
2025-07-08T15:15:37.729+0800	INFO	logs/logger.go:61	0217519589368360000000000000000000000000000000079feed [RemoveRedundantStorage] auto clean, delete db paths: []
2025-07-08T15:15:37.864+0800	INFO	logs/logger.go:38	GetBuildStatus request start: user_id:"****************"
2025-07-08T15:15:45.399+0800	INFO	logs/logger.go:38	DocumentSelect request start, documents: [], userID: ****************, req filePaths size: 2, files: [ySBrspkHv0nYxW7UOVHupNw1ESdwKqpveLrTvwiolfwXLF3MvGk/oU7on+f63POuYtrBFLvblWVm5agCzRE7 OmVrU1RXepMCq6wkO7K3IVWlClCGIfolSyKeykj6fYpal5U5fRq975aHNV7AhcGfPJrI3TijSlaq9ndnz/L82OaUErZH3g==]
2025-07-08T15:16:33.097+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T15:17:33.096+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T15:17:55.946+0800	INFO	logs/logger.go:38	DocumentChange request start, userID: ****************, documents: [], req files (1): [xWXBSEubt/pUHUOszlSQeFwquiXgLsDWQPbDQL0n60s4wVNS5APKdo5R7HBIsf8FzrDOKQ7KtNd9/yNw/qvVPp2so49czrj8EMQ55j2cseUr3wB/dl7Y], req filePaths (1): [SyWmvysi/U0803Zf3OgW4YdVp5tK0SmuGzGywDFVXjLs0DzlHgAS34CoWoSe+A0jOR9KbQdrrju4t7L4p8DHvGWktzfD0saNRR6mUSKr1RrKFP4quBet]
2025-07-08T15:17:59.448+0800	INFO	logs/logger.go:61	02175195907900000000000000000000000000000000000274b13 file SYV1DH/mkTlx0xxPx+RKP0LqxXWU/1VlnFxcgchNCvaTBPP17P4S0XvpN8kAZ7727Wu98X4AOH6LssSnSssGvbFctsUQf/buGPqvS0i9AIRJnDdMg+EM changed
2025-07-08T15:18:13.809+0800	INFO	logs/logger.go:38	DocumentSelect request start, documents: [], userID: ****************, req filePaths size: 1, files: [z2Y+Q8CvzIQst5Jg9aF/rAzwxyaXxNkC56vV9Vy/I+XBzY8tglwrX6VIHzYurxgNsvfnVIcen8a8pvPjbqGfGHvf5ZK4OnPbJVLwMvwiJJ03FAaXFgyY]
2025-07-08T15:18:13.809+0800	INFO	logs/logger.go:38	DocumentChange request start, userID: ****************, documents: [], req files (1): [IlHywqPIUnz1Rcth1HSV4G/tEYXV0CdQUn7kA9LMpJnHFX6rDfRgWY/jitBBt0uOQwmAVX1eIcWYzwLJrpbhc7QQHJm+6Rx7zIC7cxncGsk=], req filePaths (1): [R/xbV4F6R/JHOez0aJ9+T/b/pbiEShVB5fhiX8GDuj8l/yYMj2SBFuHzhsZZExQZ58VhGzbZ2Ikrg50oET5ZgHJHmD0AqdGiZWP23hYlZSI=]
2025-07-08T15:18:16.814+0800	INFO	logs/logger.go:38	DocumentSelect request start, documents: [], userID: ****************, req filePaths size: 1, files: [2qrcdKq+CRkDseGNhh/jZT05eQ1jAmvVzrqoClAxFvQkv69/pwCSqnRhx5kx8gI6ZG3nE9l8aeelRo5wUzlW47egxUYgiQEzzA==]
2025-07-08T15:18:17.523+0800	INFO	logs/logger.go:61	02175195909688600000000000000000000000000000000c7cb22 file jAgd2atSYqhK6QaBowU0ZcpDdqZdSIKZBbP0N1BIHms0Q/1FBCMN4MQKDMVawBFvwbG4eWbpnNEWzmIz75ROoIkte2s6z7DgDZvefZvrVXg= changed
2025-07-08T15:18:22.464+0800	INFO	logs/logger.go:38	DocumentSelect request start, documents: [], userID: ****************, req filePaths size: 1, files: [6KBca91FchyA68GbyC+ntmteTfz2hdNz5s3pVwqSuE4siaGP6VB7E0r9j9m3rPbgctn5gr8WI/luP+Kmt3fwXwfehN8tmzviTQ==]
2025-07-08T15:18:22.465+0800	INFO	logs/logger.go:38	DocumentChange request start, userID: ****************, documents: [], req files (1): [ygIirb8uKFbft5ZtwWPS7MhoPVtDAumVQAXnzYH2RyGyX5VHonnX3GoYiT69kLY6SfVVC9I0oXYVpsGMiBOHqIr+yYYYLNY=], req filePaths (1): [OyyxxFEkbqykRhOkVCQzUhOgTQOf3c/AJyLCuJizd3jBNafgmvCHKZoUHV7P/kdCV0bxtIZ/q+BlOIo4vRLog9Z97+vNBa8=]
2025-07-08T15:18:25.733+0800	INFO	logs/logger.go:38	DocumentSelect request start, documents: [], userID: ****************, req filePaths size: 1, files: [NebRepChuP/j3hzORswjWxtJfutfoHdG3hUv6OBRmM6ZhSr9T546MwcnB+TwsWHrcryqml1GBuLzwsTZ9muMThA9]
2025-07-08T15:18:26.079+0800	INFO	logs/logger.go:61	02175195910553400000000000000000000000000000000fb8ae2 file n5gOMt4dOu8wMCyg/0CNuiRdG3srBOgqppZYJ+tIkEyqBoh/DYBA/cje13uoru09PgXdavxg2YNTjFDYPAdKtEoZnFSwlvw= changed
2025-07-08T15:18:30.640+0800	INFO	logs/logger.go:38	DocumentChange request start, userID: ****************, documents: [], req files (1): [+Qndin4HKyUXBegFxjHxloF7I9fDbZpFgk11rtLinCiosAHxIMNwR7AYi+oqcReNm1jbjuuk7QvzDEJTB9MY05xQRP6xxA==], req filePaths (1): [R7WdbgzZQNcH8SYd2A3XZZJqvgkYbdULqN6xLTWZWHjc9Zgiw/ongB2KHP7tIElJRMxDJIYecSXic/izozA7Mxh3/tekgw==]
2025-07-08T15:18:30.640+0800	INFO	logs/logger.go:38	DocumentSelect request start, documents: [], userID: ****************, req filePaths size: 1, files: [bPQKZCfzD8+uKfy8NDp260pSghRdQ/7MrlAgbI6R6KaTKrwyK5pdB4fRARgWaFCMSNJQxJYvwC0Mh+0uY55A/97V]
2025-07-08T15:18:34.176+0800	INFO	logs/logger.go:61	0217519591136750000000000000000000000000000000035662b file dbfhv2/GKM1LFUh9iUvBBYHZfJJStc5uKLloX3O10ryXJUAnQMSRM76aADEmTsr5fO5ro8/rA+omNR1oWgWonkwYkSmv1g== changed
2025-07-08T15:18:36.095+0800	INFO	logs/logger.go:38	DocumentSelect request start, documents: [], userID: ****************, req filePaths size: 1, files: [RSA4qUW4Ln40llmDGZQ5PlNkmq9AmJtkAbHr/rjKK41OMnz5vett++hs3fBn0tjnBdS5c+53Xyb2fyXNHHzQDsz+]
2025-07-08T15:18:36.096+0800	INFO	logs/logger.go:38	DocumentChange request start, userID: ****************, documents: [], req files (1): [TOBQf4NFzZ+OoUIqXU1H6rd1IKLXCnltq5DGMb3mBQM/EwKGZOH9NPr4sf04uU2QFOVB04AWjwnpzRMGN8T+mNaxsBCg], req filePaths (1): [r6b2/ugLwKPoRf/JUJyDKKXsvoM4aOZz60zVZ4SBkeVkDuQiWzXiMtFGVZxoQ2uq3lOk1gpn00tGEzHSQ9syGoW5mR1U]
2025-07-08T15:18:39.713+0800	INFO	logs/logger.go:61	021751959119102000000000000000000000000000000005a2edd file RgpmYVZe9g1+mmTkK8jzZDxoscZGpinmK/wovABpqG28srHwJqbpgUNMvhq/267n01F3um9hOONRwHH+7g99sIFrNmdv changed
2025-07-08T15:18:39.926+0800	INFO	logs/logger.go:38	DocumentSelect request start, documents: [], userID: ****************, req filePaths size: 1, files: [yJp7N8f9qBkFyktFxNNCdUsbll6CLz4dNgn5RezHgx11zAc251N5rtMd/j1+s7QTP5yKdng/Mg==]
2025-07-08T15:18:47.575+0800	INFO	logs/logger.go:38	DocumentChange request start, userID: ****************, documents: [], req files (1): [aM5i23YYEioxcpW+T85qWmP95tB7qwBrug9Ddw0pxGdLPYcdHPq0I9UbAFDKNcXXXhxmbQhWrw16W5CazJZBtQ==], req filePaths (1): [litCZ93pmXFQYSjUp2PWNygKr7Umd3meOy6Q6uhBDn6wimSPGXVEE+9EyDH6Boe08YwLe7HyAR8N/zhru9FznA==]
2025-07-08T15:18:47.575+0800	INFO	logs/logger.go:38	DocumentSelect request start, documents: [], userID: ****************, req filePaths size: 1, files: [Q/YUjTUol/7TYuTlNmQ9ZxgyWu2zWN8Bve8/Pm+G2WBUriJXVo7JoTr3m+GjbX41iOEahRgx0Q==]
2025-07-08T15:18:51.168+0800	INFO	logs/logger.go:61	02175195913066000000000000000000000000000000000b95854 file bFpD5+wFlCa1tXdeW5oMnsZqxDRCcd36D/7SdHRjdA93mQI5TLpgM7PlgyNNDEE4Vi6RtjSZHB4kW7SBGhRp2g== changed
2025-07-08T15:18:52.337+0800	INFO	logs/logger.go:38	DocumentChange request start, userID: ****************, documents: [], req files (1): [ciJIm5Sds4bTciI7+4ZKoYG+CKtmNQTieRrycPvkro9BAQMQnQLfBZFHcagpAHNQLcIRr2147Godt4R05Q==], req filePaths (1): [ATJVNE0D5tDZqbvStwrxGjSHd8u19ei1arnb63F/1Cy/cf6r0MX4eqfvNCB4yHAJRv/ACsYv1hFl1IBSqw==]
2025-07-08T15:18:52.337+0800	INFO	logs/logger.go:38	DocumentSelect request start, documents: [], userID: ****************, req filePaths size: 1, files: [3XT6UIyOYgoNCgsvfzUBGeyz4rvm+4melDsXj8WbS0XxSQMc2ri8pL73ftZiCr4KAMSArR/3+rsYm83sbbUKsAHxVDFAbg==]
2025-07-08T15:18:53.941+0800	INFO	logs/logger.go:38	DocumentSelect request start, documents: [], userID: ****************, req filePaths size: 1, files: [fJAQ1HZwR9d1UMCOQ697nwfttj7ygUBeyWfz+m8eoPPNSaMX0Cujl1LskKVj95zjyI9E9J3UiEhHFVrN76U=]
2025-07-08T15:18:55.911+0800	INFO	logs/logger.go:61	02175195913538600000000000000000000000000000000160790 file Qt25xT/DFSzF7zW34JuLP+2h4JeIFtRw9z+/+tmDydACaT3Hu93KZRwsYbLGVi9ti28A7lOYYQJIJRMEOw== changed
2025-07-08T15:18:59.027+0800	INFO	logs/logger.go:38	DocumentChange request start, userID: ****************, documents: [], req files (1): [DsNDNPLTLTN5x6+pP5vZm7GXyyeLNk3/+7Gnl5+MQF6n6eZFlFO4NsfNKb7ZzuF4ADhmNuzj3Ki7pz5b55y+6s/HZH2jVrSWwy8eYVB+0ryZvLAQ/pxdsK/Klod8luA=], req filePaths (1): [92ghwdVwRRM7rTlEgA3Z8ijTpv+d0TOsnCDClIoYrvzQBoWNfSUCDG6PU17+vZOAn8WTzo6XqjiRMQqRp1/9nFVjV8Y0VsDwEyfYbMPHXBJSl+qKrzCqUzhH2Y+QlZk=]
2025-07-08T15:18:59.027+0800	INFO	logs/logger.go:38	DocumentSelect request start, documents: [], userID: ****************, req filePaths size: 2, files: [xl0ALnkSyYCJdAqZz+/g7bAplIbC3wQrLfTge0QPzMycojcbZgVERGQ1HvZfSQkZZRgqOYNe1T7BpbqocyE= Nk4Iwx7ZAi/uyLXZVUdWYL9YhQCofZCyH0Sq6YDLkNADOHYPO3+sQFSGO62wWxT8wk0TMGY39FpC8rlQmMY=]
2025-07-08T15:19:02.733+0800	INFO	logs/logger.go:61	0217519591421250000000000000000000000000000000033e37e file 6Ce93cicf25++KYz9+D1KKALbK6tjAA3U+7RNWkrwetdhuQD0hbOMH+MUlYVlHd3MLceHI12bqAetdXCGR+PxEQxdYKZilKApSmdBxkbrUiaKmOvzEH5OSBI3cp60zA= changed
2025-07-08T15:19:04.174+0800	INFO	logs/logger.go:38	DocumentChange request start, userID: ****************, documents: [], req files (1): [OGnRuLogAz79v/wO/S6LfkiOqfL4nUZqAAKYJOE6pukJRHNo0a7gBwv8ixMel7rWfn3tVG6pdDRTq3rXtv+D0FCNdA==], req filePaths (1): [YcSKSBUZfqpVXBkhDaTdBRRxwkBBgDipYigDty4vNISNGHC2FoZiIGzt0z9kgKc0uSCDhHc3UqYmW7efr3/NbFBkWA==]
2025-07-08T15:19:04.174+0800	INFO	logs/logger.go:38	DocumentSelect request start, documents: [], userID: ****************, req filePaths size: 2, files: [oMp+4V3h1IXE/JmDGILbgcNpZjECToNuaar6+vGin9ABiZjdfg8mwUVAENeP7hZYi74H3gR71e3p0TjBKPQ= KvFQQgAnH9JY6E/Rf6AszF5oi2EVXaAbXOV4AEjOeaf3mWGpv33O0bC2gn2F00oqiHR2N7aA9cqHfBtgLsDEjtEQ1sCLBQ==]
2025-07-08T15:19:06.572+0800	INFO	logs/logger.go:38	DocumentSelect request start, documents: [], userID: ****************, req filePaths size: 1, files: [WWjE+O3DvN7VBhh82OpfbJdmIAKtYhzHu02axUHNLdUN0AxFwQe9/qpMCslxtFsbcIGGMnR00bFKY5BCaeTE+CE=]
2025-07-08T15:19:07.851+0800	INFO	logs/logger.go:61	02175195914725800000000000000000000000000000000d7359a file VLhuvqItGH95qmkWU1ih/Ah3YmyBH5uLuGWvXs1Wrbzkh6FARdjeIqoxHXcEw9uenbqbqGwIlxMDMyD8DSfFgfit6g== changed
2025-07-08T15:19:10.661+0800	INFO	logs/logger.go:38	DocumentChange request start, userID: ****************, documents: [], req files (1): [MJ6qJq1NzS+3xA1zYpB0JDrUxwX4tsNPNDd9kb7XJBbn8I8PABEfXJdNwj3/ROcLFujFPOyzhGwl2Xhw9A==], req filePaths (1): [AsLTyJM13sdO/NqhmjU0+rvjOAD+w+rbzK9r8tmir1T+0/mWjzHd2UZfGZLPRpH6MO1WiEHXx4BqG14t1g==]
2025-07-08T15:19:10.661+0800	INFO	logs/logger.go:38	DocumentSelect request start, documents: [], userID: ****************, req filePaths size: 1, files: [IVggwnLdD0pVe+kr/KPeMKqP4mWjVAvSQr72rr+B4xNy93qxHXoevwW2wKca9FG+vRpkVmAf8XJCk8scsPpioEE=]
2025-07-08T15:19:14.197+0800	INFO	logs/logger.go:61	02175195915369900000000000000000000000000000000ae2cd2 file CWVq5hHqweTLwtn1e77UNzkKhiBBoJJkLmn0s/gM6F243N24ALDe1QDFqnodtaxFglqofQQOwZiKEJjb5w== changed
2025-07-08T15:19:18.212+0800	INFO	logs/logger.go:38	DocumentChange request start, userID: ****************, documents: [], req files (1): [8RQ+nkPmdWl9/TFwC5NZAi6JbGrzwjfx8fqTxwL6OCGQM3wuvYdYGPVQ5FfmtaSq8fHIKjKoGF29aWKwnWneQDN+oofBiqdsmKpjIE6EIqjkJBKg/fi25czg2A==], req filePaths (1): [/bdawEgVMYuEhiUmehsPGC/Bj5GfGkluR1maIgvkbs4zjgLODIuAHBSv/BX1yF2gXQ3nB40k7dqkFNTtm2jjAT0lJnyrPuvcf6Jew1dpDrepO9kdky3dTKz6ew==]
2025-07-08T15:19:21.673+0800	INFO	logs/logger.go:61	0217519591612370000000000000000000000000000000066bc58 file a2Ny64qVLTD/10jQrftRaXXqFvA4TZLEfSxKUaxCfPWsSOsuIt0LSoYWLb+Hf+Xmd7mUKfVHJGL84aqZto7zERKTgNZA/miYFsc8TZIVR530BcqNbMETTUEBOQ== changed
2025-07-08T15:19:35.641+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T15:19:35.642+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T15:19:35.642+0800	INFO	logs/logger.go:61	02175195917564200000000000000000000000000000000d038ef user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T15:19:36.845+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T15:19:36.845+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T15:19:36.845+0800	INFO	logs/logger.go:61	0217519591768450000000000000000000000000000000070ce2c user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T15:19:44.021+0800	INFO	logs/logger.go:38	CancelIndex request: project_id:"/home/<USER>/projects" user_id:"****************"
2025-07-08T15:19:44.021+0800	INFO	logs/logger.go:61	021751959184021000000000000000000000000000000002221bc ckg_cancel start
2025-07-08T15:19:44.022+0800	INFO	logs/logger.go:61	021751959184021000000000000000000000000000000002221bc set project /home/<USER>/projects cancelFlag success
2025-07-08T15:19:44.022+0800	INFO	logs/logger.go:61	021751959184021000000000000000000000000000000002221bc cancel project, removed folder tasks: 0, removed local index file tasks: 0, removed batch local index split tasks: 0, removed batch local index embedding tasks: 0, removed sync code file index tasks: 0
2025-07-08T15:27:58.265+0800	INFO	logs/logger.go:38	CodeKG process 1
2025-07-08T15:27:58.265+0800	INFO	logs/logger.go:38	CKG local embedding: true, embedding storage type is sqlite_vec
2025-07-08T15:27:58.266+0800	INFO	logs/logger.go:38	ckg version is 0.0.90
2025-07-08T15:27:58.266+0800	INFO	logs/logger.go:38	server start at 51008
2025-07-08T15:27:58.266+0800	INFO	logs/logger.go:38	tea report using default url
2025-07-08T15:27:58.267+0800	INFO	logs/logger.go:61	02175195967826500000000000000000000000000000000a7ac68 afterPanic %!s(bool=false) memory usage: {18650816 20413048 32218128 0 54459 17027 18650816 23986176 4308992 19677184 4308992 37432 1179648 1179648 175392 325920 1200 62400 5255 4140328 2518401 26678552 1751959678248098911 842344 [262759 226049 137180 216356 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0] [1751959678201574937 1751959678209823173 1751959678223219462 1751959678248098911 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0] 4 0 0.022850279048049187 true false [{0 0 0} {8 1175 378} {16 13482 3662} {24 1550 79} {32 5932 1629} {48 4238 455} {64 2355 705} {80 983 372} {96 465 71} {112 14653 6767} {128 1418 688} {144 100 0} {160 1802 364} {176 107 0} {192 65 23} {208 416 16} {224 19 1} {240 26 0} {256 899 27} {288 60 8} {320 841 328} {352 17 1} {384 64 13} {416 206 4} {448 11 0} {480 12 0} {512 28 0} {576 31 4} {640 753 224} {704 16 0} {768 18 4} {896 127 7} {1024 50 1} {1152 19 4} {1280 472 102} {1408 5 0} {1536 8 2} {1792 70 1} {2048 9 0} {2304 10 2} {2688 170 41} {3072 9 1} {3200 2 0} {3456 1 0} {4096 126 0} {4864 3 0} {5376 70 12} {6144 4 0} {6528 0 0} {6784 1 0} {6912 0 0} {8192 166 0} {9472 44 1} {9728 0 0} {10240 1 0} {10880 13 2} {12288 2 0} {13568 0 0} {14336 150 0} {16384 1 0} {18432 2 0}]}, goroutine number:  77
2025-07-08T15:27:58.268+0800	INFO	logs/logger.go:38	[PeriodicInitManager] started periodic task, interval 10 minutes
2025-07-08T15:27:58.269+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] started periodic task, interval 10 minutes
2025-07-08T15:27:58.391+0800	INFO	logs/logger.go:61	02175195967826600000000000000000000000000000000e301e9 codekg feature gate config is fetch for , map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 2025070815275817D16C82B8048F02E802
2025-07-08T15:27:58.391+0800	INFO	logs/logger.go:61	02175195967826600000000000000000000000000000000e301e9 exit when ppid changed is enabled
2025-07-08T15:28:00.305+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T15:28:00.305+0800	INFO	logs/logger.go:61	02175195968030500000000000000000000000000000000e541b9 user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T15:28:00.314+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T15:28:58.319+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T15:29:58.320+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T15:30:50.903+0800	INFO	logs/logger.go:61	021751959850721000000000000000000000000000000002d6b92 codekg ab config is fetch for ****************, &{QueryRewrite:map[] Rerank:map[] Embedding:map[] Chunking:map[] RecallStrategy:map[ner_recall:false] Implementation:map[]}
2025-07-08T15:30:51.010+0800	INFO	logs/logger.go:61	021751959850721000000000000000000000000000000002d6b92 codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 20250708153050E3F95E2977CB4F041515
2025-07-08T15:31:58.319+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T15:32:02.212+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T15:32:02.212+0800	INFO	logs/logger.go:61	02175195992221100000000000000000000000000000000dc306c user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T15:32:02.213+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T15:32:58.320+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T15:33:58.319+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T15:34:28.320+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T15:35:58.319+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T15:36:02.376+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T15:36:02.376+0800	INFO	logs/logger.go:61	021751960162376000000000000000000000000000000005b5293 user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T15:36:02.377+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T15:36:58.321+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T15:37:25.697+0800	INFO	logs/logger.go:61	021751960245548000000000000000000000000000000009def43 codekg ab config is fetch for ****************, &{QueryRewrite:map[] Rerank:map[] Embedding:map[] Chunking:map[] RecallStrategy:map[ner_recall:false] Implementation:map[]}
2025-07-08T15:37:25.782+0800	INFO	logs/logger.go:61	021751960245548000000000000000000000000000000009def43 codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 2025070815372518C22B0734C2FF49073F
2025-07-08T15:37:58.269+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] userID is empty, do not metric
2025-07-08T15:38:28.319+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T15:38:58.320+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T15:39:28.320+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T15:40:02.450+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T15:40:02.450+0800	INFO	logs/logger.go:61	021751960402450000000000000000000000000000000009233ad user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T15:40:02.452+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T15:40:44.622+0800	INFO	logs/logger.go:38	CodeKG process 1
2025-07-08T15:40:44.623+0800	INFO	logs/logger.go:38	CKG local embedding: true, embedding storage type is sqlite_vec
2025-07-08T15:40:44.623+0800	INFO	logs/logger.go:38	ckg version is 0.0.90
2025-07-08T15:40:44.623+0800	INFO	logs/logger.go:38	server start at 51009
2025-07-08T15:40:44.623+0800	INFO	logs/logger.go:38	tea report using default url
2025-07-08T15:40:44.624+0800	INFO	logs/logger.go:61	0217519604446220000000000000000000000000000000020fdd6 afterPanic %!s(bool=false) memory usage: {18650824 20413824 32218128 0 54507 17035 18650824 24018944 4308992 19709952 4308992 37472 1146880 1146880 171696 309624 1200 62400 5255 4138256 2536769 25945640 1751960444608869448 602273 [74022 224975 225059 78217 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0] [1751960444576148086 1751960444582488585 1751960444592218642 1751960444608869448 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0] 4 0 0.02676754836939628 true false [{0 0 0} {8 1175 378} {16 13497 3661} {24 1553 79} {32 5940 1629} {48 4240 455} {64 2361 705} {80 983 372} {96 466 71} {112 14653 6767} {128 1419 688} {144 108 0} {160 1801 364} {176 107 0} {192 65 23} {208 419 17} {224 19 1} {240 26 0} {256 898 27} {288 60 10} {320 841 328} {352 17 1} {384 64 13} {416 204 4} {448 11 0} {480 12 0} {512 28 0} {576 31 4} {640 753 224} {704 16 0} {768 18 4} {896 127 7} {1024 49 1} {1152 19 4} {1280 472 102} {1408 5 0} {1536 8 2} {1792 70 1} {2048 9 0} {2304 10 2} {2688 170 41} {3072 9 1} {3200 2 0} {3456 1 0} {4096 126 0} {4864 3 0} {5376 70 12} {6144 4 0} {6528 0 0} {6784 1 0} {6912 0 0} {8192 166 0} {9472 44 1} {9728 0 0} {10240 1 0} {10880 13 2} {12288 2 0} {13568 0 0} {14336 150 0} {16384 1 0} {18432 2 0}]}, goroutine number:  77
2025-07-08T15:40:44.625+0800	INFO	logs/logger.go:38	[PeriodicInitManager] started periodic task, interval 10 minutes
2025-07-08T15:40:44.626+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] started periodic task, interval 10 minutes
2025-07-08T15:40:44.803+0800	INFO	logs/logger.go:61	021751960444623000000000000000000000000000000001afaa0 codekg feature gate config is fetch for , map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 202507081540444C0590E7E55703C563F6
2025-07-08T15:40:44.803+0800	INFO	logs/logger.go:61	021751960444623000000000000000000000000000000001afaa0 exit when ppid changed is enabled
2025-07-08T15:40:46.003+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T15:40:46.004+0800	INFO	logs/logger.go:61	02175196044600300000000000000000000000000000000f2a82c user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T15:40:46.014+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T15:40:46.920+0800	INFO	logs/logger.go:38	Init repo request start, req: projects:{project_id:"/home/<USER>/荣联新闻管理平台" storage_path:"/home/<USER>/.trae-cn-server/.ckg/storage/u_6b5b65d8d70b666ff288dc00bdc9399bcc3642f927f20a2d435629785b7e6b8e" ignore_file:"/home/<USER>/荣联新闻管理平台/.trae/.ignore"} user_id:"****************"
2025-07-08T15:40:46.920+0800	ERROR	logs/logger.go:68	0217519604469190000000000000000000000000000000093edbf [migrateIndexData] remove file err: remove /home/<USER>/.trae-cn-server/.ckg/storage/u_6b5b65d8d70b666ff288dc00bdc9399bcc3642f927f20a2d435629785b7e6b8e/env_codekg.db: no such file or directory, file storage: /home/<USER>/.trae-cn-server/.ckg/storage/u_6b5b65d8d70b666ff288dc00bdc9399bcc3642f927f20a2d435629785b7e6b8e/env_codekg.db
2025-07-08T15:40:47.000+0800	INFO	logs/logger.go:61	0217519604469190000000000000000000000000000000093edbf codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 2025070815404617E700DD43413B015A25
2025-07-08T15:40:47.001+0800	INFO	logs/logger.go:61	0217519604469190000000000000000000000000000000093edbf InitProject start
2025-07-08T15:40:47.225+0800	INFO	logs/logger.go:61	0217519604469190000000000000000000000000000000093edbf codekg config is fetch for ****************, &{FileCountLimit:3000 FileSizeThreshold:1048576 SingleFileLineThreshold:5000 DatastoreName:default-latest EnableRerank:false DefaultRecallNum:25 ForceIndexFileCountLimit:10000 IndexFileLineLimit:1500000 DefaultIgnoreRules:# Dir blacklist
node_modules/
__pycache__/
venv/
vendor/
tmp/
temp/
Pods/
bundle/
obj/
target/
output/
dist/
eggs/
gradle/
# File Ext blacklist
*.class
*.jar
*.dex
*.o
*.obj
*.a
*.out
*.pyc
*.pyo
*.pyd
*.egg
*.whl
*.log
*.lock
*.bak
**/.env
**/.env.*
**/credentials.json
**/credentials.*.json
**/secret.json
**/secrets.json
**/*.key
**/*.pem
**/*.pfx
**/*.p12
**/*.crt
**/*.cer
**/id_rsa
**/id_dsa
**/.ssh/id_*
**/id_ed25519
**/id_ecdsa
**/id_ecdsa_sk
**/id_ed25519_sk
 DefaultOmitProjectRoots: DebounceTime:3 BinaryContentCheckThreshold:0 RecentCursorRelationNum:5 TaskSleepTimeFactor:1 RAGRemoteIndexThreshold:10000 RAGRepoWhiteList: RAGRepoNotOnlyWhiteList:false PeriodicInitIntervalMinutes:10 ExcludeFolderRule: ExcludeFileRule: ExcludeExtFileRule: IncludeExtFileRule: OutlineRetrieveConfig:0xc0001714a0 VirtualProjectConfig:0xc0001714f0}
2025-07-08T15:40:47.225+0800	INFO	logs/logger.go:61	0217519604469190000000000000000000000000000000093edbf codekg config &{FileCountLimit:3000 FileSizeThreshold:1048576 SingleFileLineThreshold:5000 DatastoreName:default-latest EnableRerank:false DefaultRecallNum:25 ForceIndexFileCountLimit:10000 IndexFileLineLimit:1500000 DefaultIgnoreRules:# Dir blacklist
node_modules/
__pycache__/
venv/
vendor/
tmp/
temp/
Pods/
bundle/
obj/
target/
output/
dist/
eggs/
gradle/
# File Ext blacklist
*.class
*.jar
*.dex
*.o
*.obj
*.a
*.out
*.pyc
*.pyo
*.pyd
*.egg
*.whl
*.log
*.lock
*.bak
**/.env
**/.env.*
**/credentials.json
**/credentials.*.json
**/secret.json
**/secrets.json
**/*.key
**/*.pem
**/*.pfx
**/*.p12
**/*.crt
**/*.cer
**/id_rsa
**/id_dsa
**/.ssh/id_*
**/id_ed25519
**/id_ecdsa
**/id_ecdsa_sk
**/id_ed25519_sk
 DefaultOmitProjectRoots: DebounceTime:3 BinaryContentCheckThreshold:0 RecentCursorRelationNum:5 TaskSleepTimeFactor:1 RAGRemoteIndexThreshold:10000 RAGRepoWhiteList: RAGRepoNotOnlyWhiteList:false PeriodicInitIntervalMinutes:10 ExcludeFolderRule: ExcludeFileRule: ExcludeExtFileRule: IncludeExtFileRule: OutlineRetrieveConfig:0xc0001714a0 VirtualProjectConfig:0xc0001714f0}
2025-07-08T15:40:47.379+0800	INFO	logs/logger.go:61	0217519604469190000000000000000000000000000000093edbf codekg ab config is fetch for ****************, &{QueryRewrite:map[] Rerank:map[] Embedding:map[] Chunking:map[] RecallStrategy:map[ner_recall:false] Implementation:map[]}
2025-07-08T15:40:47.379+0800	INFO	logs/logger.go:61	0217519604469190000000000000000000000000000000093edbf codekg ab config &{QueryRewrite:map[] Rerank:map[] Embedding:map[] Chunking:map[] RecallStrategy:map[ner_recall:false] Implementation:map[]}
2025-07-08T15:40:47.379+0800	INFO	logs/logger.go:61	0217519604469190000000000000000000000000000000093edbf ckg_init start
2025-07-08T15:40:47.380+0800	INFO	logs/logger.go:61	0217519604469190000000000000000000000000000000093edbf [RemoveRedundantStorage] auto clean, delete db paths: []
2025-07-08T15:40:47.380+0800	INFO	logs/logger.go:61	0217519604469190000000000000000000000000000000093edbf ignore service start
2025-07-08T15:40:47.380+0800	WARN	logs/logger.go:75	0217519604469190000000000000000000000000000000093edbf failed to read customized ignore file, err: open /home/<USER>/荣联新闻管理平台/.trae/.ignore: no such file or directory
2025-07-08T15:40:47.490+0800	INFO	logs/logger.go:61	0217519604469190000000000000000000000000000000093edbf codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 20250708154047920E06E97F568D121499
2025-07-08T15:40:47.490+0800	INFO	logs/logger.go:61	0217519604469190000000000000000000000000000000093edbf collect all uri start
2025-07-08T15:40:47.490+0800	INFO	logs/logger.go:61	0217519604469190000000000000000000000000000000093edbf /home/<USER>/荣联新闻管理平台 has 1 files
2025-07-08T15:40:47.491+0800	INFO	logs/logger.go:61	0217519604469190000000000000000000000000000000093edbf [Init] InitRelationManager failed project is /home/<USER>/荣联新闻管理平台 err is relation manager init failed, manager version: 
2025-07-08T15:40:47.491+0800	WARN	logs/logger.go:75	0217519604469190000000000000000000000000000000093edbf [GetLocalStrategyOrDefault] GetProjectIDByProjectPath error: record not found
2025-07-08T15:40:47.491+0800	WARN	logs/logger.go:75	0217519604469190000000000000000000000000000000093edbf [GetStorageLocalVersion] GetProjectIDByProjectPath error: record not found
2025-07-08T15:40:47.492+0800	INFO	logs/logger.go:61	0217519604469190000000000000000000000000000000093edbf [GetStorageFromUserStorage] GetProjectIDByProjectPath err is record not found
2025-07-08T15:40:47.492+0800	INFO	logs/logger.go:61	0217519604469190000000000000000000000000000000093edbf [Create] entity db not exist for project /home/<USER>/荣联新闻管理平台, new db path is /home/<USER>/.trae-cn-server/.ckg/storage/u_6b5b65d8d70b666ff288dc00bdc9399bcc3642f927f20a2d435629785b7e6b8e/荣联新闻管理平台_6d8ccc4054ffac_ix9to8_codekg.db
2025-07-08T15:40:48.196+0800	INFO	logs/logger.go:38	GetBuildStatus request start: user_id:"****************"
2025-07-08T15:40:48.297+0800	INFO	logs/logger.go:61	0217519604469190000000000000000000000000000000093edbf [GetEmbeddingStorageFromUserStorage] GetProjectIDByProjectPath err is record not found
2025-07-08T15:40:48.297+0800	INFO	logs/logger.go:61	0217519604469190000000000000000000000000000000093edbf [CreateSQLiteVecStorageV2] embedding db not exist for project /home/<USER>/荣联新闻管理平台, new db path is /home/<USER>/.trae-cn-server/.ckg/storage/u_6b5b65d8d70b666ff288dc00bdc9399bcc3642f927f20a2d435629785b7e6b8e/荣联新闻管理平台_6d8ccc4054ffac_5yg2ly_embedding_vec.db
2025-07-08T15:40:48.300+0800	INFO	logs/logger.go:61	0217519604469190000000000000000000000000000000093edbf [UpsertProjectID] insert new project id record: {ID:0 ProjectPath:/home/<USER>/荣联新闻管理平台 CodeKGDBPath:/home/<USER>/.trae-cn-server/.ckg/storage/u_6b5b65d8d70b666ff288dc00bdc9399bcc3642f927f20a2d435629785b7e6b8e/荣联新闻管理平台_6d8ccc4054ffac_ix9to8_codekg.db EmbeddingDBPath:/home/<USER>/.trae-cn-server/.ckg/storage/u_6b5b65d8d70b666ff288dc00bdc9399bcc3642f927f20a2d435629785b7e6b8e/荣联新闻管理平台_6d8ccc4054ffac_5yg2ly_embedding_vec.db EmbeddingDBType:1 DBVersion:2 CKGVersion:0.0.90 ChunkingMethod:v1 EmbeddingModel:codekg_bge_m3 CreateTime:2025-07-08 15:40:48.300636 +0800 CST m=+3.735199955 ShrinkTime:<nil> InitTime:<nil> ProjectType:0 ProjectURI:file:///home/<USER>/荣联新闻管理平台}
2025-07-08T15:40:48.304+0800	INFO	logs/logger.go:61	0217519604469190000000000000000000000000000000093edbf create local data storage
2025-07-08T15:40:48.304+0800	INFO	logs/logger.go:61	0217519604469190000000000000000000000000000000093edbf calculate all files that need indexing
2025-07-08T15:40:48.304+0800	INFO	logs/logger.go:61	0217519604469190000000000000000000000000000000093edbf currently use local embedding storage? true
2025-07-08T15:40:48.304+0800	INFO	logs/logger.go:61	0217519604469190000000000000000000000000000000093edbf [localIndex] before gc, mem stat, alloc: 18.64 MB, in use: 20.81 MB
2025-07-08T15:40:48.319+0800	INFO	logs/logger.go:61	0217519604469190000000000000000000000000000000093edbf [localIndex] gc cost: 15 ms
2025-07-08T15:40:48.319+0800	INFO	logs/logger.go:61	0217519604469190000000000000000000000000000000093edbf [localIndex] after gc, mem stat, alloc: 16.97 MB, in use: 20.08 MB
2025-07-08T15:40:48.320+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] updated user ****************
2025-07-08T15:40:48.354+0800	INFO	logs/logger.go:61	0217519604469190000000000000000000000000000000093edbf [localIndex] project /home/<USER>/荣联新闻管理平台 initialize finished, build time: 49 ms, file indexed: 0, folder indexed: 1
2025-07-08T15:40:48.355+0800	INFO	logs/logger.go:61	0217519604469190000000000000000000000000000000093edbf [RemoveRedundantStorage] auto clean, delete db paths: []
2025-07-08T15:40:49.253+0800	INFO	logs/logger.go:38	GetBuildStatus request start: user_id:"****************"
2025-07-08T15:40:58.321+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T15:40:58.321+0800	INFO	logs/logger.go:38	ppid changed or killed, exit ckg process
2025-07-08T15:41:10.299+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T15:41:10.300+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T15:41:10.300+0800	INFO	logs/logger.go:61	02175196047030000000000000000000000000000000000d539f8 user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T15:41:20.730+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T15:41:20.730+0800	INFO	logs/logger.go:61	02175196048073000000000000000000000000000000000c08dd9 user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T15:41:20.733+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T15:41:20.792+0800	INFO	logs/logger.go:38	Init repo request start, req: projects:{project_id:"/home/<USER>/rlxwglpt" storage_path:"/home/<USER>/.trae-cn-server/.ckg/storage/u_4fc47c21032436a4b4060d6240a0cb233c74c66ae9cafe4a39b8e5b488c51b11" ignore_file:"/home/<USER>/rlxwglpt/.trae/.ignore"} user_id:"****************"
2025-07-08T15:41:20.792+0800	ERROR	logs/logger.go:68	021751960480792000000000000000000000000000000008c4bf1 [migrateIndexData] remove file err: remove /home/<USER>/.trae-cn-server/.ckg/storage/u_4fc47c21032436a4b4060d6240a0cb233c74c66ae9cafe4a39b8e5b488c51b11/env_codekg.db: no such file or directory, file storage: /home/<USER>/.trae-cn-server/.ckg/storage/u_4fc47c21032436a4b4060d6240a0cb233c74c66ae9cafe4a39b8e5b488c51b11/env_codekg.db
2025-07-08T15:41:20.792+0800	INFO	logs/logger.go:61	021751960480792000000000000000000000000000000008c4bf1 InitProject start
2025-07-08T15:41:20.792+0800	INFO	logs/logger.go:61	021751960480792000000000000000000000000000000008c4bf1 codekg config &{FileCountLimit:3000 FileSizeThreshold:1048576 SingleFileLineThreshold:5000 DatastoreName:default-latest EnableRerank:false DefaultRecallNum:25 ForceIndexFileCountLimit:10000 IndexFileLineLimit:1500000 DefaultIgnoreRules:# Dir blacklist
node_modules/
__pycache__/
venv/
vendor/
tmp/
temp/
Pods/
bundle/
obj/
target/
output/
dist/
eggs/
gradle/
# File Ext blacklist
*.class
*.jar
*.dex
*.o
*.obj
*.a
*.out
*.pyc
*.pyo
*.pyd
*.egg
*.whl
*.log
*.lock
*.bak
**/.env
**/.env.*
**/credentials.json
**/credentials.*.json
**/secret.json
**/secrets.json
**/*.key
**/*.pem
**/*.pfx
**/*.p12
**/*.crt
**/*.cer
**/id_rsa
**/id_dsa
**/.ssh/id_*
**/id_ed25519
**/id_ecdsa
**/id_ecdsa_sk
**/id_ed25519_sk
 DefaultOmitProjectRoots: DebounceTime:3 BinaryContentCheckThreshold:0 RecentCursorRelationNum:5 TaskSleepTimeFactor:1 RAGRemoteIndexThreshold:10000 RAGRepoWhiteList: RAGRepoNotOnlyWhiteList:false PeriodicInitIntervalMinutes:10 ExcludeFolderRule: ExcludeFileRule: ExcludeExtFileRule: IncludeExtFileRule: OutlineRetrieveConfig:0xc0001714a0 VirtualProjectConfig:0xc0001714f0}
2025-07-08T15:41:20.792+0800	INFO	logs/logger.go:61	021751960480792000000000000000000000000000000008c4bf1 codekg ab config &{QueryRewrite:map[] Rerank:map[] Embedding:map[] Chunking:map[] RecallStrategy:map[ner_recall:false] Implementation:map[]}
2025-07-08T15:41:20.792+0800	INFO	logs/logger.go:61	021751960480792000000000000000000000000000000008c4bf1 ckg_init start
2025-07-08T15:41:20.793+0800	INFO	logs/logger.go:61	021751960480792000000000000000000000000000000008c4bf1 [RemoveRedundantStorage] auto clean, delete db paths: []
2025-07-08T15:41:20.793+0800	INFO	logs/logger.go:61	021751960480792000000000000000000000000000000008c4bf1 ignore service start
2025-07-08T15:41:20.793+0800	WARN	logs/logger.go:75	021751960480792000000000000000000000000000000008c4bf1 failed to read customized ignore file, err: open /home/<USER>/rlxwglpt/.trae/.ignore: no such file or directory
2025-07-08T15:41:20.793+0800	INFO	logs/logger.go:61	021751960480792000000000000000000000000000000008c4bf1 collect all uri start
2025-07-08T15:41:20.793+0800	INFO	logs/logger.go:61	021751960480792000000000000000000000000000000008c4bf1 /home/<USER>/rlxwglpt has 1 files
2025-07-08T15:41:20.794+0800	WARN	logs/logger.go:75	021751960480792000000000000000000000000000000008c4bf1 [GetLocalStrategyOrDefault] GetProjectIDByProjectPath error: record not found
2025-07-08T15:41:20.794+0800	WARN	logs/logger.go:75	021751960480792000000000000000000000000000000008c4bf1 [GetStorageLocalVersion] GetProjectIDByProjectPath error: record not found
2025-07-08T15:41:20.794+0800	INFO	logs/logger.go:61	021751960480792000000000000000000000000000000008c4bf1 [GetStorageFromUserStorage] GetProjectIDByProjectPath err is record not found
2025-07-08T15:41:20.794+0800	INFO	logs/logger.go:61	021751960480792000000000000000000000000000000008c4bf1 [Create] entity db not exist for project /home/<USER>/rlxwglpt, new db path is /home/<USER>/.trae-cn-server/.ckg/storage/u_4fc47c21032436a4b4060d6240a0cb233c74c66ae9cafe4a39b8e5b488c51b11/rlxwglpt_e369bbf320d213_0wteft_codekg.db
2025-07-08T15:41:20.794+0800	INFO	logs/logger.go:61	021751960480792000000000000000000000000000000008c4bf1 [Init] InitRelationManager failed project is /home/<USER>/rlxwglpt err is relation manager init failed, manager version: 
2025-07-08T15:41:21.527+0800	INFO	logs/logger.go:61	021751960480792000000000000000000000000000000008c4bf1 [GetEmbeddingStorageFromUserStorage] GetProjectIDByProjectPath err is record not found
2025-07-08T15:41:21.527+0800	INFO	logs/logger.go:61	021751960480792000000000000000000000000000000008c4bf1 [CreateSQLiteVecStorageV2] embedding db not exist for project /home/<USER>/rlxwglpt, new db path is /home/<USER>/.trae-cn-server/.ckg/storage/u_4fc47c21032436a4b4060d6240a0cb233c74c66ae9cafe4a39b8e5b488c51b11/rlxwglpt_e369bbf320d213_r4h6i5_embedding_vec.db
2025-07-08T15:41:21.531+0800	INFO	logs/logger.go:61	021751960480792000000000000000000000000000000008c4bf1 [UpsertProjectID] insert new project id record: {ID:0 ProjectPath:/home/<USER>/rlxwglpt CodeKGDBPath:/home/<USER>/.trae-cn-server/.ckg/storage/u_4fc47c21032436a4b4060d6240a0cb233c74c66ae9cafe4a39b8e5b488c51b11/rlxwglpt_e369bbf320d213_0wteft_codekg.db EmbeddingDBPath:/home/<USER>/.trae-cn-server/.ckg/storage/u_4fc47c21032436a4b4060d6240a0cb233c74c66ae9cafe4a39b8e5b488c51b11/rlxwglpt_e369bbf320d213_r4h6i5_embedding_vec.db EmbeddingDBType:1 DBVersion:2 CKGVersion:0.0.90 ChunkingMethod:v1 EmbeddingModel:codekg_bge_m3 CreateTime:2025-07-08 15:41:21.531150736 +0800 CST m=+36.965714690 ShrinkTime:<nil> InitTime:<nil> ProjectType:0 ProjectURI:file:///home/<USER>/rlxwglpt}
2025-07-08T15:41:21.536+0800	INFO	logs/logger.go:61	021751960480792000000000000000000000000000000008c4bf1 create local data storage
2025-07-08T15:41:21.536+0800	INFO	logs/logger.go:61	021751960480792000000000000000000000000000000008c4bf1 calculate all files that need indexing
2025-07-08T15:41:21.536+0800	INFO	logs/logger.go:61	021751960480792000000000000000000000000000000008c4bf1 currently use local embedding storage? true
2025-07-08T15:41:21.537+0800	INFO	logs/logger.go:61	021751960480792000000000000000000000000000000008c4bf1 [localIndex] before gc, mem stat, alloc: 18.02 MB, in use: 20.36 MB
2025-07-08T15:41:21.592+0800	INFO	logs/logger.go:61	021751960480792000000000000000000000000000000008c4bf1 [localIndex] gc cost: 55 ms
2025-07-08T15:41:21.592+0800	INFO	logs/logger.go:61	021751960480792000000000000000000000000000000008c4bf1 [localIndex] after gc, mem stat, alloc: 17.13 MB, in use: 20.12 MB
2025-07-08T15:41:21.640+0800	INFO	logs/logger.go:61	021751960480792000000000000000000000000000000008c4bf1 [localIndex] project /home/<USER>/rlxwglpt initialize finished, build time: 104 ms, file indexed: 0, folder indexed: 1
2025-07-08T15:41:21.641+0800	INFO	logs/logger.go:61	021751960480792000000000000000000000000000000008c4bf1 [RemoveRedundantStorage] auto clean, delete db paths: []
2025-07-08T15:41:22.017+0800	INFO	logs/logger.go:38	GetBuildStatus request start: user_id:"****************"
2025-07-08T15:41:37.255+0800	INFO	logs/logger.go:38	CancelIndex request: project_id:"/home/<USER>/荣联新闻管理平台" user_id:"****************"
2025-07-08T15:41:37.255+0800	INFO	logs/logger.go:61	0217519604972550000000000000000000000000000000012ad31 ckg_cancel start
2025-07-08T15:41:37.255+0800	INFO	logs/logger.go:61	0217519604972550000000000000000000000000000000012ad31 set project /home/<USER>/荣联新闻管理平台 cancelFlag success
2025-07-08T15:41:37.255+0800	INFO	logs/logger.go:61	0217519604972550000000000000000000000000000000012ad31 cancel project, removed folder tasks: 0, removed local index file tasks: 0, removed batch local index split tasks: 0, removed batch local index embedding tasks: 0, removed sync code file index tasks: 0
2025-07-08T15:42:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T15:43:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T15:44:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T15:44:57.507+0800	INFO	logs/logger.go:38	DocumentCreate request start, userID: ****************, documents: [], req files (1): [GTMeeY+2HrGkjuJ59arRFmBKrRzngSG/FMIQk17LvYl4RkNMDEKWdIiJTrODQNp/1/PfOoBnXdvQQUxVomJm8c80ASXS7S1jrA==], req filePaths (1): [R54VQEoTSSkgRAXS7Cz+Fk0SYdvXl1YcpislLza0FSpPj/VzLOIM6nVF+jgnglBW6RxpDDOcEkZU8MQzLVnoY1I1WdWKBYiqlw==]
2025-07-08T15:44:57.635+0800	INFO	logs/logger.go:61	02175196069762400000000000000000000000000000000864e8a folder RZRBslhHRp7HxfbDBBnAwHhITXz7pzgXu3rx1bgnbeaiw+pBy1xiof141053X2cAJ4g= sync succeed, task time: 10 ms, sleep time: 10 ms
2025-07-08T15:45:02.063+0800	INFO	logs/logger.go:38	DocumentSelect request start, documents: [], userID: ****************, req filePaths size: 1, files: [a6w1ylr9PyAl5Y7o5gYAnDbjwSDERiyCJHfN8pxLO0ONsUpWEe7p8HjNm+wyUEYCfToMLljaoYtEaiX3MZv+jrDbZdxPdLYO6w==]
2025-07-08T15:45:20.805+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T15:45:20.805+0800	INFO	logs/logger.go:61	021751960720805000000000000000000000000000000008e20ea user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T15:45:20.815+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T15:46:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T15:47:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T15:49:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T15:49:20.871+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T15:49:20.871+0800	INFO	logs/logger.go:61	0217519609608700000000000000000000000000000000090798e user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T15:49:20.874+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T15:50:14.693+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T15:50:44.779+0800	INFO	logs/logger.go:61	02175196104462600000000000000000000000000000000af727b codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 20250708155044759A3DE2B9C65E1FFEBD
2025-07-08T15:50:44.780+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] finished at 2025-07-08T15:50:44+08:00 cost time 0 ms
2025-07-08T15:51:02.192+0800	INFO	logs/logger.go:61	021751961061988000000000000000000000000000000004e4169 codekg ab config is fetch for ****************, &{QueryRewrite:map[] Rerank:map[] Embedding:map[] Chunking:map[] RecallStrategy:map[ner_recall:false] Implementation:map[]}
2025-07-08T15:51:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T15:51:46.505+0800	INFO	logs/logger.go:38	DocumentChange request start, userID: ****************, documents: [], req files (2): [t3PsUbkgibKj82oBK6DjwpoQu6h7cwlnpqOKB/XsFcieKBqC7DMHGb2HecB9+EebuVE= uFZn3cu2GR1+uGqu0rQrKE3P9dF1HVXAM2CFE2TTziPYPvb0WC46QWyrMImyq2fJToKzQZyNtOAR1/7vJEwFOHW5AVlYPRhdeg==], req filePaths (2): [Egdn/pwH918FVRsBaTqzvp4KHGmVrvegZyQNXUsSwETV3QsDnMsJv6YuwIe+mR0mjUI= ssEg8XC3n5LSMV339JbiRGSTjmRT3/OhE3dQH1OE66iTDNrJfPwOdHnt+i3492/N95m0SK1OjcToLJ0llzM7jJ0imCO2f7YnSQ==]
2025-07-08T15:51:46.635+0800	INFO	logs/logger.go:61	021751961106505000000000000000000000000000000001e06ad codekg config is fetch for ****************, &{FileCountLimit:3000 FileSizeThreshold:1048576 SingleFileLineThreshold:5000 DatastoreName:default-latest EnableRerank:false DefaultRecallNum:25 ForceIndexFileCountLimit:10000 IndexFileLineLimit:1500000 DefaultIgnoreRules:# Dir blacklist
node_modules/
__pycache__/
venv/
vendor/
tmp/
temp/
Pods/
bundle/
obj/
target/
output/
dist/
eggs/
gradle/
# File Ext blacklist
*.class
*.jar
*.dex
*.o
*.obj
*.a
*.out
*.pyc
*.pyo
*.pyd
*.egg
*.whl
*.log
*.lock
*.bak
**/.env
**/.env.*
**/credentials.json
**/credentials.*.json
**/secret.json
**/secrets.json
**/*.key
**/*.pem
**/*.pfx
**/*.p12
**/*.crt
**/*.cer
**/id_rsa
**/id_dsa
**/.ssh/id_*
**/id_ed25519
**/id_ecdsa
**/id_ecdsa_sk
**/id_ed25519_sk
 DefaultOmitProjectRoots: DebounceTime:3 BinaryContentCheckThreshold:0 RecentCursorRelationNum:5 TaskSleepTimeFactor:1 RAGRemoteIndexThreshold:10000 RAGRepoWhiteList: RAGRepoNotOnlyWhiteList:false PeriodicInitIntervalMinutes:10 ExcludeFolderRule: ExcludeFileRule: ExcludeExtFileRule: IncludeExtFileRule: OutlineRetrieveConfig:0xc001850910 VirtualProjectConfig:0xc001850960}
2025-07-08T15:51:49.847+0800	WARN	logs/logger.go:75	0217519611098470000000000000000000000000000000039b7c4 cannot get change file status for directory, uri: /home/<USER>/rlxwglpt
2025-07-08T15:51:49.847+0800	INFO	logs/logger.go:61	0217519611098470000000000000000000000000000000039b7c4 file k6SKifHsAMfMuSSiMAgqgasaZMXOYVchoeSqPbCqWbsNCbtgSrCNvUJRoWirNPJYqlo= changed
2025-07-08T15:51:50.132+0800	INFO	logs/logger.go:61	02175196110968000000000000000000000000000000000ba5189 file YnnlMwx83/ArGBOSyeyMr3CizKExLRGp/Uggz9caVKL5kVP0nKS4mC4gYp8syQ8FVv+hOp4FPrONbERxtx8xl+6kSUmzW0ZgLQ== changed
2025-07-08T15:52:08.212+0800	INFO	logs/logger.go:38	DocumentChange request start, userID: ****************, documents: [], req files (2): [c5f8lqyQ5H4EOd27Be56v3RlDJC26CakZ4q8ecT7KELt9OXqi++10JJPbxzoqoEVPqwAMpnR4SIAfyCnRQ== oqwnVP9dcv16pZPjZDPSpVe5JzLrOKs/NIsWGdFZPtxkltWLnHKoF9WMKvP8Xs0SsNsWv8yvVeR112CebE1epNWOAaHblN1G1A==], req filePaths (2): [EcBN3lyYl2kmMUZOekI049Oelt7NdQ44jAsZ+QXh9kFP8kAY+Z0s/d8yQZBPrHrkDokfPI+XQflvv0w0BQ== ZRP3254kOd1YsFXSjPqusfWxFpFHZv6JdnlrCH9APKiH63qd47IfKAJJTPQUVG5m6IEwreUD+cexKa5x3NHnMS+AcOglV/iZSw==]
2025-07-08T15:52:11.721+0800	INFO	logs/logger.go:61	02175196113125500000000000000000000000000000000e9e42c file EPLB4yAVxi++zfXEcWabeaYUSAV4/eK3F30oo32VJvfSytuycr03iJEaRzv0SZcd3jggFYcvmGCTEEwteP4gr2Ffs/5DA1rC9A== changed
2025-07-08T15:52:12.119+0800	INFO	logs/logger.go:61	021751961131422000000000000000000000000000000006b4ac4 file cUfgTOPXobO+ObtbG3LtRta0VTw+I98EX674r6h8D4BOpe9KzEA8V98t/vs2dCd2mAMgtVLopkWq37s8Bw== changed
2025-07-08T15:52:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T15:53:20.937+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T15:53:20.940+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T15:53:20.940+0800	INFO	logs/logger.go:61	02175196120094000000000000000000000000000000000208339 user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T15:54:22.814+0800	INFO	logs/logger.go:38	DocumentChange request start, userID: ****************, documents: [], req files (1): [/k2OsA3zkulgvvuZ1P6zBEub/Ml9I/YYalmcXSvSvUX48NA+9xBsVJ6TuVRa02+/I8erx7qhwDzwSKuLttR+G7aI5J4/ANpTAg==], req filePaths (1): [olk2x/Ap+mwBIvHh7SgTINXDGrgiKFCY7faYaYIDvo75+EifTdeehZrpHdcrvOKjnSACCLv8eZ2Cwq8wuPMZ1mUetGnhrVhvTw==]
2025-07-08T15:54:26.212+0800	INFO	logs/logger.go:61	021751961265845000000000000000000000000000000005f463c file uQOa9xDvi7IJrV0QJfI+97WTF42HIiYhjndrM2IfsrTT8565znNjCua6fbU1tQpSln6/AzxA/MzAO2PZtpy5vYMJjY/fide1hw== changed
2025-07-08T15:55:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T15:56:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T15:57:20.992+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T15:57:20.992+0800	INFO	logs/logger.go:61	0217519614409920000000000000000000000000000000079c29b user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T15:57:21.001+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T15:59:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:00:44.693+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:00:44.737+0800	INFO	logs/logger.go:61	02175196164462600000000000000000000000000000000d0d02f codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 202507081600447CE2C0D73FAF051503BF
2025-07-08T16:00:44.738+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] finished at 2025-07-08T16:00:44+08:00 cost time 0 ms
2025-07-08T16:01:21.091+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T16:01:21.091+0800	INFO	logs/logger.go:61	02175196168109100000000000000000000000000000000dfc773 user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T16:01:21.093+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T16:02:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:03:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:03:49.176+0800	INFO	logs/logger.go:38	DocumentChange request start, userID: ****************, documents: [], req files (1): [QltUDaTZgKpMHKoMyLurtfaFdVAOwIqpQ+KHlmTQBTTSJ9OSYvSuMGWjmNIfV/nnB5rO3MJBuwEzf2Ynb4IFvIbu3nXpTXIxwQ==], req filePaths (1): [7ed60kore0QGc4H9FweJDA84KTGq3mB8jTIFCbDIcWGkhMHlrZ1lkXCHEwgrYV8CXkYmFE5oASYSCoArZvQ54jwNHUSgK2Lmkw==]
2025-07-08T16:03:49.373+0800	INFO	logs/logger.go:61	0217519618291760000000000000000000000000000000035fa48 codekg config is fetch for ****************, &{FileCountLimit:3000 FileSizeThreshold:1048576 SingleFileLineThreshold:5000 DatastoreName:default-latest EnableRerank:false DefaultRecallNum:25 ForceIndexFileCountLimit:10000 IndexFileLineLimit:1500000 DefaultIgnoreRules:# Dir blacklist
node_modules/
__pycache__/
venv/
vendor/
tmp/
temp/
Pods/
bundle/
obj/
target/
output/
dist/
eggs/
gradle/
# File Ext blacklist
*.class
*.jar
*.dex
*.o
*.obj
*.a
*.out
*.pyc
*.pyo
*.pyd
*.egg
*.whl
*.log
*.lock
*.bak
**/.env
**/.env.*
**/credentials.json
**/credentials.*.json
**/secret.json
**/secrets.json
**/*.key
**/*.pem
**/*.pfx
**/*.p12
**/*.crt
**/*.cer
**/id_rsa
**/id_dsa
**/.ssh/id_*
**/id_ed25519
**/id_ecdsa
**/id_ecdsa_sk
**/id_ed25519_sk
 DefaultOmitProjectRoots: DebounceTime:3 BinaryContentCheckThreshold:0 RecentCursorRelationNum:5 TaskSleepTimeFactor:1 RAGRemoteIndexThreshold:10000 RAGRepoWhiteList: RAGRepoNotOnlyWhiteList:false PeriodicInitIntervalMinutes:10 ExcludeFolderRule: ExcludeFileRule: ExcludeExtFileRule: IncludeExtFileRule: OutlineRetrieveConfig:0xc0014e8f50 VirtualProjectConfig:0xc0014e8fa0}
2025-07-08T16:03:52.684+0800	INFO	logs/logger.go:61	021751961832406000000000000000000000000000000001a2662 file osO1+FR1e0SsRs0Gmna4iU6HQCYNF6405CA1iMb2L7+HLoYnnFgjwqUsQlB0i2zU4lCA0dGwE8uspt5e6QVLMVM3W41VfTlWXg== changed
2025-07-08T16:04:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:05:21.149+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T16:05:21.149+0800	INFO	logs/logger.go:61	02175196192114900000000000000000000000000000000e2f5c9 user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T16:05:21.152+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T16:06:08.048+0800	INFO	logs/logger.go:61	021751961967910000000000000000000000000000000008ba515 codekg ab config is fetch for ****************, &{QueryRewrite:map[] Rerank:map[] Embedding:map[] Chunking:map[] RecallStrategy:map[ner_recall:false] Implementation:map[]}
2025-07-08T16:06:08.156+0800	INFO	logs/logger.go:61	021751961967910000000000000000000000000000000008ba515 codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 2025070816060859C3A1F6BCE92A071837
2025-07-08T16:06:11.951+0800	INFO	logs/logger.go:38	DocumentSelect request start, documents: [], userID: ****************, req filePaths size: 1, files: [cTiPJRAnbUMx4ofTLJQhdnPLjSknDS6Wf7SyIDke8jd7c0nL8iDoZp3IMoMlkhAIVBA0EXHov2s8OU3YQ+QoeOErQQtQ3q60bQ==]
2025-07-08T16:06:50.412+0800	INFO	logs/logger.go:38	DocumentChange request start, userID: ****************, documents: [], req files (1): [AySmu48TAxJtrdpJimEBZPlEU+sDbXqAk+RrDGU1QuwuvH8U1OdkEypCkKh/xCbh7seWOC3GWS7BS6FL55eKvFFCazu2Xmydhg==], req filePaths (1): [TsL4a7iYzaq+K3D2WcRChvKULIUxV/MlFca0X4EkmMpJK0DDNX+n1HpHYqwV5bMNrawjVLZyj2EacKIkJwXccS3OHq4vyKJILg==]
2025-07-08T16:06:53.933+0800	INFO	logs/logger.go:61	02175196201349200000000000000000000000000000000886580 file 4cgQ141FTOumupxeP/8vB/OkoPnDYBqNY4XQTtr5c8ANzo9cOx2TnPk6bwO1v6GdfcefrtUIk5uI5Ehq8RANpy5YNPC/dRL09w== changed
2025-07-08T16:06:54.894+0800	INFO	logs/logger.go:38	DocumentSelect request start, documents: [], userID: ****************, req filePaths size: 1, files: [wFl8mKi1CL2T/7tWsdCQc3NofB7hXeSCsbAXp7SZQDbQj82MT2IBuW1S9sMRRaqqPQ2LizRP99054ey1Xw==]
2025-07-08T16:07:00.018+0800	INFO	logs/logger.go:38	DocumentChange request start, userID: ****************, documents: [], req files (1): [RWh5l/ExlxGgy+yCZlXdMQhtyEajlGFYjXJDqzitCKMYShxNZ56iQItZD0CLVaOpECoh3UNEs2vgs3h1Dw==], req filePaths (1): [roO17h+gegw/1XXJtSZcq0yOc4dPa7nNeFrVLQO+AoOjtcMsGGUH1dxuhz9L9IGbhrQSnlLVy6k1Zrb6uw==]
2025-07-08T16:07:03.478+0800	INFO	logs/logger.go:61	02175196202305100000000000000000000000000000000c5bd6e file 8rzBaU9RStmUaklKsVJhi+9vqmZ3MQevvztGMpj9k5XcU4Ex4yMFYTSQwScdMxJN1Rsi+w50N5+Dd4YYyQ== changed
2025-07-08T16:08:07.102+0800	INFO	logs/logger.go:38	DocumentSelect request start, documents: [], userID: ****************, req filePaths size: 1, files: [cAgKY2PPaLrWrNSI8x8zPj4ifMIuij+xNIO52i+en3+vmgP/kktWZkXgbX44SSeKFcMdUWCbsqjVl5vbGa278q3wuYt/Frqo5Q==]
2025-07-08T16:09:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:09:22.209+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T16:09:22.210+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T16:09:22.210+0800	INFO	logs/logger.go:61	021751962162210000000000000000000000000000000009a1841 user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T16:10:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:10:44.627+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] finished at 2025-07-08T16:10:44+08:00 cost time 0 ms
2025-07-08T16:11:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:12:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:13:23.216+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T16:13:23.216+0800	INFO	logs/logger.go:61	0217519624032160000000000000000000000000000000052bb14 user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T16:13:23.229+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T16:14:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:16:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:17:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:17:24.207+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T16:17:24.209+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T16:17:24.210+0800	INFO	logs/logger.go:61	021751962644209000000000000000000000000000000002136b0 user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T16:18:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:18:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:20:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:20:44.803+0800	INFO	logs/logger.go:61	021751962844626000000000000000000000000000000003b45e9 codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 202507081620449D8C9F8BA2BA0C28CC66
2025-07-08T16:20:44.804+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] finished at 2025-07-08T16:20:44+08:00 cost time 0 ms
2025-07-08T16:21:25.201+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T16:21:25.201+0800	INFO	logs/logger.go:61	02175196288520100000000000000000000000000000000f29ab1 user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T16:21:25.211+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T16:23:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:23:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:24:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:25:14.693+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:25:25.273+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T16:25:25.273+0800	INFO	logs/logger.go:61	02175196312527300000000000000000000000000000000a32241 user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T16:25:25.277+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T16:26:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:28:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:29:25.381+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T16:29:25.381+0800	INFO	logs/logger.go:61	02175196336538100000000000000000000000000000000f53b2e user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T16:29:25.392+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T16:30:44.748+0800	INFO	logs/logger.go:61	02175196344462600000000000000000000000000000000bd91bb codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 2025070816304419BDD99E0064F16658D4
2025-07-08T16:30:44.748+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] finished at 2025-07-08T16:30:44+08:00 cost time 0 ms
2025-07-08T16:31:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:32:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:32:44.693+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:33:25.446+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T16:33:25.447+0800	INFO	logs/logger.go:61	0217519636054460000000000000000000000000000000048487a user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T16:33:25.448+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T16:34:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:35:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:36:03.534+0800	INFO	logs/logger.go:61	021751963763350000000000000000000000000000000002cf880 codekg ab config is fetch for ****************, &{QueryRewrite:map[] Rerank:map[] Embedding:map[] Chunking:map[] RecallStrategy:map[ner_recall:false] Implementation:map[]}
2025-07-08T16:36:03.684+0800	INFO	logs/logger.go:61	021751963763350000000000000000000000000000000002cf880 codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 202507081636037D8AEB5B2C654C1910F9
2025-07-08T16:36:03.684+0800	INFO	logs/logger.go:61	021751963763350000000000000000000000000000000002cf880 single flight key: /protocol.CodeKG/IsCKGEnabledForNonWorkspaceScenario_e0fae3b9915c3c93f15557226f32514275b2080ff6b486fb13979bc9d14c7659 is shared
2025-07-08T16:36:03.684+0800	INFO	logs/logger.go:61	021751963763418000000000000000000000000000000005fe396 single flight key: /protocol.CodeKG/IsCKGEnabledForNonWorkspaceScenario_e0fae3b9915c3c93f15557226f32514275b2080ff6b486fb13979bc9d14c7659 is shared
2025-07-08T16:36:04.875+0800	INFO	logs/logger.go:38	DocumentSelect request start, documents: [], userID: ****************, req filePaths size: 1, files: [CHfB5/x5z/GCRQeQicr8UJ129Epsbx+KfXizcpgfJxY1u6JBn/dkfDrmCO1KWbIiIwgMmuMIsprNcPLACUhBUodIaDsQk3XXQQ==]
2025-07-08T16:36:08.792+0800	INFO	logs/logger.go:38	DocumentSelect request start, documents: [], userID: ****************, req filePaths size: 1, files: [MyJ1pTAY7yaDFWox9gaSzjTj/4imyFiMdg5vJh1x5uV4+v7HYjXSj2txicwOVEMTk6xwDAKW4HYpX0MAxh/E3+DdlviWgszo7w==]
2025-07-08T16:36:17.796+0800	INFO	logs/logger.go:38	DocumentSelect request start, documents: [], userID: ****************, req filePaths size: 1, files: [E/tQNrVVYXvIT8VP+Z4UJk42VhrkDMyoM/RN6z7wvzABks++9i+J6jZvd769P4012kH5VZ84AcBun5U0iX7cH4aOMAkCA6SUrg==]
2025-07-08T16:36:21.145+0800	INFO	logs/logger.go:38	DocumentSelect request start, documents: [], userID: ****************, req filePaths size: 1, files: [3zeey6vQN5PcsCldkco4Mr6vujViND5BqaIqnUTZqJnG01eED7kTet4SKH70U0bYfoXUMpyMMyIol+tJLyrkTPl2I9S/sP1nkw==]
2025-07-08T16:36:31.670+0800	INFO	logs/logger.go:38	DocumentSelect request start, documents: [], userID: ****************, req filePaths size: 1, files: [rCohXdNyPhmWc8WRtASu3KSJpnJN15Cx5dVNARk1gB9XkWLHUpqnER61+0eI9FyAc6UvrjG3qIrI3Tl6Ja8Hy2QtDj3FbUYdEg==]
2025-07-08T16:36:37.624+0800	INFO	logs/logger.go:38	DocumentSelect request start, documents: [], userID: ****************, req filePaths size: 1, files: [GZ6yvjekcfAJmnWJyIju1x8QyylYzcaBzkUqyKccCJVtFSD6Rn0iYVahq3GD0hydG1MmJzq30ZUChUpB1JkrnO2uW6xgur8tUg==]
2025-07-08T16:37:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:37:25.512+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T16:37:25.512+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T16:37:25.512+0800	INFO	logs/logger.go:61	02175196384551200000000000000000000000000000000d2c1b9 user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T16:38:14.693+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:38:41.258+0800	INFO	logs/logger.go:38	DocumentChange request start, userID: ****************, documents: [], req files (1): [DLa1/6Qy/ewURzFTD1kTRm0S7P9K52YgTfDDALjk5+BvfTYQ8PwP5w0veiCWl3ccEl68FCh0conYeUdA0lysdUr4RjHn//xbag==], req filePaths (1): [4YF8UIAgSsbbOniDCbEblVKHQ0EGsP4qzzVoPflVbCoqxmpSZoRP/pcu5Y1C7XqN9n2qXFUyQgjbTROx/poLDBHF2U4jWAqCqQ==]
2025-07-08T16:38:41.455+0800	INFO	logs/logger.go:61	02175196392125800000000000000000000000000000000650260 codekg config is fetch for ****************, &{FileCountLimit:3000 FileSizeThreshold:1048576 SingleFileLineThreshold:5000 DatastoreName:default-latest EnableRerank:false DefaultRecallNum:25 ForceIndexFileCountLimit:10000 IndexFileLineLimit:1500000 DefaultIgnoreRules:# Dir blacklist
node_modules/
__pycache__/
venv/
vendor/
tmp/
temp/
Pods/
bundle/
obj/
target/
output/
dist/
eggs/
gradle/
# File Ext blacklist
*.class
*.jar
*.dex
*.o
*.obj
*.a
*.out
*.pyc
*.pyo
*.pyd
*.egg
*.whl
*.log
*.lock
*.bak
**/.env
**/.env.*
**/credentials.json
**/credentials.*.json
**/secret.json
**/secrets.json
**/*.key
**/*.pem
**/*.pfx
**/*.p12
**/*.crt
**/*.cer
**/id_rsa
**/id_dsa
**/.ssh/id_*
**/id_ed25519
**/id_ecdsa
**/id_ecdsa_sk
**/id_ed25519_sk
 DefaultOmitProjectRoots: DebounceTime:3 BinaryContentCheckThreshold:0 RecentCursorRelationNum:5 TaskSleepTimeFactor:1 RAGRemoteIndexThreshold:10000 RAGRepoWhiteList: RAGRepoNotOnlyWhiteList:false PeriodicInitIntervalMinutes:10 ExcludeFolderRule: ExcludeFileRule: ExcludeExtFileRule: IncludeExtFileRule: OutlineRetrieveConfig:0xc0003af810 VirtualProjectConfig:0xc0003af860}
2025-07-08T16:38:44.866+0800	INFO	logs/logger.go:61	02175196392454400000000000000000000000000000000b27085 file Y2oAWvbLsL20zeHZz4AzAv5Pgro51utVMStCQGKCPBGGZQ8Lm4yucbZFyVCgCTZvRAP3pBdSX/gl+xEIzBTm01HNae/m3fbUKA== changed
2025-07-08T16:39:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:39:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:40:44.627+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] finished at 2025-07-08T16:40:44+08:00 cost time 0 ms
2025-07-08T16:40:44.693+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:41:25.575+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T16:41:25.575+0800	INFO	logs/logger.go:61	02175196408557500000000000000000000000000000000748f9a user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T16:41:25.583+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T16:43:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:44:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:45:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:45:25.634+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T16:45:25.635+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T16:45:25.635+0800	INFO	logs/logger.go:61	021751964325635000000000000000000000000000000008de9f6 user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T16:46:20.391+0800	INFO	logs/logger.go:38	DocumentChange request start, userID: ****************, documents: [], req files (1): [haNraVR1r/fOwO7ehrm/TGWfcCT8FVCmXRI/rM7DvWnGreiqCZArGiPW+TRTwxADkrlj6zWamZz1Ul2DKUr576y27MUqxFa2Sg==], req filePaths (1): [tmH5sp/vNd4e9Sc3B3a3LIwMV9BkSiGsTGWWq7Z7bsmG9os09u4ZdWT7cFzfHubNYxzafJY9GJBMPyslD0ZC+9Voe2t0uZCvtQ==]
2025-07-08T16:46:20.841+0800	INFO	logs/logger.go:61	021751964380390000000000000000000000000000000002094a5 codekg config is fetch for ****************, &{FileCountLimit:3000 FileSizeThreshold:1048576 SingleFileLineThreshold:5000 DatastoreName:default-latest EnableRerank:false DefaultRecallNum:25 ForceIndexFileCountLimit:10000 IndexFileLineLimit:1500000 DefaultIgnoreRules:# Dir blacklist
node_modules/
__pycache__/
venv/
vendor/
tmp/
temp/
Pods/
bundle/
obj/
target/
output/
dist/
eggs/
gradle/
# File Ext blacklist
*.class
*.jar
*.dex
*.o
*.obj
*.a
*.out
*.pyc
*.pyo
*.pyd
*.egg
*.whl
*.log
*.lock
*.bak
**/.env
**/.env.*
**/credentials.json
**/credentials.*.json
**/secret.json
**/secrets.json
**/*.key
**/*.pem
**/*.pfx
**/*.p12
**/*.crt
**/*.cer
**/id_rsa
**/id_dsa
**/.ssh/id_*
**/id_ed25519
**/id_ecdsa
**/id_ecdsa_sk
**/id_ed25519_sk
 DefaultOmitProjectRoots: DebounceTime:3 BinaryContentCheckThreshold:0 RecentCursorRelationNum:5 TaskSleepTimeFactor:1 RAGRemoteIndexThreshold:10000 RAGRepoWhiteList: RAGRepoNotOnlyWhiteList:false PeriodicInitIntervalMinutes:10 ExcludeFolderRule: ExcludeFileRule: ExcludeExtFileRule: IncludeExtFileRule: OutlineRetrieveConfig:0xc001400230 VirtualProjectConfig:0xc0014002d0}
2025-07-08T16:46:23.941+0800	INFO	logs/logger.go:61	021751964380390000000000000000000000000000000002094a5 codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 20250708164623D03DB8E3C26DF4EEAA81
2025-07-08T16:46:24.254+0800	INFO	logs/logger.go:61	02175196438396300000000000000000000000000000000b38484 file XwI2l+NYV/3nyYPDsMx4JGLSYPQAvhJuIDkWVHQ90tp7wFE/rruh67a06jTMQtkUUQYr7hpklDJynb8Yshv/YW3Ver6v196/fw== changed
2025-07-08T16:47:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:48:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:49:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:49:25.689+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T16:49:25.690+0800	INFO	logs/logger.go:61	02175196456568900000000000000000000000000000000fd1d03 user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T16:49:25.692+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T16:50:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:50:44.627+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] finished at 2025-07-08T16:50:44+08:00 cost time 0 ms
2025-07-08T16:51:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:52:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:53:25.754+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T16:53:25.755+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T16:53:25.755+0800	INFO	logs/logger.go:61	02175196480575500000000000000000000000000000000b22a89 user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T16:54:13.396+0800	INFO	logs/logger.go:38	DocumentChange request start, userID: ****************, documents: [], req files (1): [4P8bZu2g1S9/0PK530wS8GMRsgrc2Xqc8q+GUPimXRkvTdo6bc/tFGsm7iUGfSTGYLfBwIFdoQkWO8yWVgE6pfZzvhDCqSUrBA==], req filePaths (1): [X/Jp94hem8vu2pL5L+MncuyBS0JJyVyHBnFbl7W+QyWtW1zGuI9s2JIIFpcPxltqpM++HCW5dPUyzB76tvd3xZdDn+xIUGu8zQ==]
2025-07-08T16:54:13.571+0800	INFO	logs/logger.go:61	02175196485339600000000000000000000000000000000fa6791 codekg config is fetch for ****************, &{FileCountLimit:3000 FileSizeThreshold:1048576 SingleFileLineThreshold:5000 DatastoreName:default-latest EnableRerank:false DefaultRecallNum:25 ForceIndexFileCountLimit:10000 IndexFileLineLimit:1500000 DefaultIgnoreRules:# Dir blacklist
node_modules/
__pycache__/
venv/
vendor/
tmp/
temp/
Pods/
bundle/
obj/
target/
output/
dist/
eggs/
gradle/
# File Ext blacklist
*.class
*.jar
*.dex
*.o
*.obj
*.a
*.out
*.pyc
*.pyo
*.pyd
*.egg
*.whl
*.log
*.lock
*.bak
**/.env
**/.env.*
**/credentials.json
**/credentials.*.json
**/secret.json
**/secrets.json
**/*.key
**/*.pem
**/*.pfx
**/*.p12
**/*.crt
**/*.cer
**/id_rsa
**/id_dsa
**/.ssh/id_*
**/id_ed25519
**/id_ecdsa
**/id_ecdsa_sk
**/id_ed25519_sk
 DefaultOmitProjectRoots: DebounceTime:3 BinaryContentCheckThreshold:0 RecentCursorRelationNum:5 TaskSleepTimeFactor:1 RAGRemoteIndexThreshold:10000 RAGRepoWhiteList: RAGRepoNotOnlyWhiteList:false PeriodicInitIntervalMinutes:10 ExcludeFolderRule: ExcludeFileRule: ExcludeExtFileRule: IncludeExtFileRule: OutlineRetrieveConfig:0xc0012bde00 VirtualProjectConfig:0xc0012bde50}
2025-07-08T16:54:16.675+0800	INFO	logs/logger.go:61	02175196485339600000000000000000000000000000000fa6791 codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 2025070816541679D6D5ABB335784D6298
2025-07-08T16:54:17.040+0800	INFO	logs/logger.go:61	021751964856682000000000000000000000000000000006a4451 file Onp99dS2w7tu+oWtytgKUVAZSMGYM6VcbOg1EMjG9BdasBRwR6rdenBJY7tIsre91Jn5ctXdZm+fQNpyQV5Nrx+jdvetPC+THw== changed
2025-07-08T16:54:21.050+0800	INFO	logs/logger.go:38	DocumentChange request start, userID: ****************, documents: [], req files (1): [PlpSN78A/zVZOhOTChYsALLivCGe/pnU8giEi8o8KCsRATBYgjK717s2X2CPXdl9JPl9R30dIvTl9JmT//Yk8dcFQ56OlwMfyA==], req filePaths (1): [IWnbro61yBoUzBpmZtzrRbv5J0NwLZL7MIlfRDMAQNLVM3rxCZF8XP+pFCXz1QbEIeVJlhNGGQC3eaTwhKPlWzU0Cw0Elm+Fxw==]
2025-07-08T16:54:24.526+0800	INFO	logs/logger.go:61	021751964864141000000000000000000000000000000001cac82 file NxMoSR7EIkyV6tbhxVM9dfi7liDMUBC+EhzQHwoI8B4Y8QvSu6HihD90Kq7Culxg+7UIRMhJuleSr81sIYPC97EFR6gvbWrw/g== changed
2025-07-08T16:54:53.559+0800	INFO	logs/logger.go:38	DocumentChange request start, userID: ****************, documents: [], req files (1): [UZW5jnz9w9raGjUPXha2z2LG4I2FbGCIXeZJi3zT/Yzaz1K9lo4Wxt7TgapuiN+Kj69JxoSdOyXB0L65gXByX4DnpMvAXWenMA==], req filePaths (1): [OeIkDFffoixrKxSpcF+wBIYeYMBDzRlRSbCUQzLDGkoGFdjGOOQgTbheFUXnF3BHHZs3dCqqXI/uX/8tXH7sXC2kHD5d4/8Jww==]
2025-07-08T16:54:56.930+0800	INFO	logs/logger.go:61	02175196489664700000000000000000000000000000000207deb file Pcsb2UGhCwr+q6CTHH/f/o4IyR17UuGO6Pdq09xJ0/yA/fd7rSieW++l7mAhcrpGM2J0iiHUbtHfsAuOV0mvdPXSqw+S7IVt0A== changed
2025-07-08T16:56:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:57:25.812+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T16:57:25.812+0800	INFO	logs/logger.go:61	02175196504581200000000000000000000000000000000a02014 user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T16:57:25.819+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T16:58:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T16:59:14.693+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:00:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:00:44.730+0800	INFO	logs/logger.go:61	02175196524462600000000000000000000000000000000b72902 codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 20250708170044B9ED0753186322679DFD
2025-07-08T17:00:44.731+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] finished at 2025-07-08T17:00:44+08:00 cost time 0 ms
2025-07-08T17:01:25.883+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T17:01:25.883+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T17:01:25.883+0800	INFO	logs/logger.go:61	021751965285883000000000000000000000000000000009eb7d2 user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T17:02:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:03:44.693+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:05:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:05:25.957+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T17:05:25.957+0800	INFO	logs/logger.go:61	02175196552595700000000000000000000000000000000c07f8e user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T17:05:25.964+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T17:06:14.693+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:07:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:08:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:09:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:09:26.024+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T17:09:26.025+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T17:09:26.025+0800	INFO	logs/logger.go:61	02175196576602500000000000000000000000000000000d9162c user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T17:10:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:10:44.810+0800	INFO	logs/logger.go:61	02175196584462600000000000000000000000000000000bd3029 codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 20250708171044ECB33787F2526C0706CC
2025-07-08T17:10:44.810+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] finished at 2025-07-08T17:10:44+08:00 cost time 0 ms
2025-07-08T17:11:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:12:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:13:26.099+0800	INFO	logs/logger.go:38	RefreshToken request, req: token:"Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c" user_id:"****************"
2025-07-08T17:13:26.099+0800	INFO	logs/logger.go:61	0217519660060990000000000000000000000000000000031849c user token is updated, user is ****************, token: &{Cloud-IDE-JWT eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.mWBSnuUueuW8NAb4HCTJmAOl8Gj79xT4w89OGANj6ZRRzgGDfl7NxWCXnwAw1zSeH5EzJyCvV104I5LVF2YQjTzvbhk2JZRZLU4m9a2xWfO5lwxNYeuaBpCbVBw10oo4Hq6r_9DGZKpQuMAZ8EcRON1xe3kShS8b1MxzjCY5EZ_4_fULq9p492L-2Tlf8hb3sj_tLarQSFHg29XJLbZXIpckVvlHCwW4qEPziQcNRxXSNRK3D0DhSiVUoU1_cbxr_hCuGyVPUTsxFyQo9wpWcomUPGUriIQTxII_stSyaroRs0gAbqFLmc4KKP6k2YmziK0NW7woUxQ0k7VD1VyZhH3qdxiOaJmGPmNrzsY8A2Rjno2LtFH0c5vagMio6PHXSPRZrCmj3wDQOkPBePBVDCbaaSocbLr7T3gBOxMtIvD0t_mGOPmu0TqFiH__6uMr2wk1tJ5PfxaZZ2vbnsmVbb5_ty6KRdsymK9mqAddMxfVpLGt23KmiqqFZel2oPclLwK4trKxAioryuKDp8ntLjF8UZGDye5qvNh4i0EzEuKLQAkqFvFyuG7E97Dw2kU4raRkK0Lsk9oNoCaKYGscwTW1ZgK0CFFwBjNLX2_OtiiwMC2Nil0sO5S6EUD1CRhUE5oLVewQ6s7dunlkk0lhqLN2XDZ8QTqdRwEFbXkB03c ****************}
2025-07-08T17:13:26.107+0800	INFO	logs/logger.go:38	SetUp request start, req: host:"https://trae-api-cn.mchost.guru" region:"cn" device_cpu:"Intel" device_id:"****************" machine_id:"b50a411e646c4d653bac94a0f2e5a57deae3fbd497c0ecbef74133e5ca0ad560" device_brand:"21LE" device_type:"windows" os_version:"Microsoft Windows 11 家庭中文版"
2025-07-08T17:14:44.693+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:15:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:17:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:17:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:18:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:18:44.693+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:20:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:20:44.726+0800	INFO	logs/logger.go:61	0217519664446260000000000000000000000000000000015ee99 codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 202507081720442FC9057C2FE0DC6C772E
2025-07-08T17:20:44.726+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] finished at 2025-07-08T17:20:44+08:00 cost time 0 ms
2025-07-08T17:21:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:22:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:23:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:25:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:26:14.693+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:26:44.694+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:28:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:29:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:30:44.738+0800	INFO	logs/logger.go:61	02175196704462600000000000000000000000000000000a764e4 codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 20250708173044D751224EDAF2332C967D
2025-07-08T17:30:44.739+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] finished at 2025-07-08T17:30:44+08:00 cost time 0 ms
2025-07-08T17:31:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:32:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:33:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:34:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:35:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:36:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:37:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:39:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:39:44.693+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:40:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:40:44.774+0800	INFO	logs/logger.go:61	021751967644626000000000000000000000000000000006207b4 codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 20250708174044CCA40DEB2B1396545AF7
2025-07-08T17:40:44.774+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] finished at 2025-07-08T17:40:44+08:00 cost time 0 ms
2025-07-08T17:41:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:42:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:42:44.693+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:43:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:44:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:45:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:46:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:47:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:48:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:49:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:50:44.769+0800	INFO	logs/logger.go:61	02175196824462600000000000000000000000000000000f6e8dc codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 2025070817504419EF7D67266D3F6ED549
2025-07-08T17:50:44.769+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] finished at 2025-07-08T17:50:44+08:00 cost time 0 ms
2025-07-08T17:51:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:52:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:53:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:53:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:54:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:56:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:57:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:58:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T17:59:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:00:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:00:44.746+0800	INFO	logs/logger.go:61	021751968844626000000000000000000000000000000008326db codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 202507081800445733E1C19900CA542924
2025-07-08T18:00:44.746+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] finished at 2025-07-08T18:00:44+08:00 cost time 0 ms
2025-07-08T18:01:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:03:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:04:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:05:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:06:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:07:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:08:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:09:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:10:44.694+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:10:44.834+0800	INFO	logs/logger.go:61	02175196944462600000000000000000000000000000000539001 codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 202507081810449BB844A21744FE62B37F
2025-07-08T18:10:44.835+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] finished at 2025-07-08T18:10:44+08:00 cost time 0 ms
2025-07-08T18:12:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:13:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:16:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:19:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:20:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:20:44.778+0800	INFO	logs/logger.go:61	02175197004462600000000000000000000000000000000f5afd9 codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 20250708182044021ADF4A93EA35509960
2025-07-08T18:20:44.779+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] finished at 2025-07-08T18:20:44+08:00 cost time 0 ms
2025-07-08T18:21:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:22:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:23:14.693+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:24:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:26:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:28:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:29:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:30:44.752+0800	INFO	logs/logger.go:61	0217519706446260000000000000000000000000000000053b5af codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 2025070818304448B5A4960C0B50404500
2025-07-08T18:30:44.752+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] finished at 2025-07-08T18:30:44+08:00 cost time 0 ms
2025-07-08T18:31:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:31:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:32:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:33:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:35:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:37:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:38:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:39:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:40:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:40:44.775+0800	INFO	logs/logger.go:61	021751971244626000000000000000000000000000000009e6ac0 codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 202507081840442CDF83296DEA3125D322
2025-07-08T18:40:44.776+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] finished at 2025-07-08T18:40:44+08:00 cost time 0 ms
2025-07-08T18:41:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:42:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:44:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:45:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:46:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:47:14.693+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:48:14.693+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:49:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:50:44.806+0800	INFO	logs/logger.go:61	02175197184462600000000000000000000000000000000ddc2ba codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 202507081850440529411C119517251488
2025-07-08T18:50:44.806+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] finished at 2025-07-08T18:50:44+08:00 cost time 0 ms
2025-07-08T18:51:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:52:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:53:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:54:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:55:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:57:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T18:59:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:00:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:00:44.826+0800	INFO	logs/logger.go:61	02175197244462600000000000000000000000000000000f53073 codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 20250708190044C49D682437BD0429CD96
2025-07-08T19:00:44.827+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] finished at 2025-07-08T19:00:44+08:00 cost time 0 ms
2025-07-08T19:02:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:03:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:04:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:05:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:06:14.693+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:07:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:09:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:10:44.754+0800	INFO	logs/logger.go:61	02175197304462600000000000000000000000000000000a29d58 codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 20250708191044D61348E3CA85A74B0062
2025-07-08T19:10:44.755+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] finished at 2025-07-08T19:10:44+08:00 cost time 0 ms
2025-07-08T19:11:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:11:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:12:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:13:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:14:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:15:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:16:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:17:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:19:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:20:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:20:44.839+0800	INFO	logs/logger.go:61	02175197364462600000000000000000000000000000000944b71 codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 202507081920449514CE04B09D0A5421F0
2025-07-08T19:20:44.840+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] finished at 2025-07-08T19:20:44+08:00 cost time 0 ms
2025-07-08T19:21:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:22:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:22:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:24:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:25:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:26:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:27:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:28:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:30:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:30:44.821+0800	INFO	logs/logger.go:61	021751974244626000000000000000000000000000000006c9808 codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 2025070819304424225F7DC4F5715B4C9A
2025-07-08T19:30:44.822+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] finished at 2025-07-08T19:30:44+08:00 cost time 0 ms
2025-07-08T19:31:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:32:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:33:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:34:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:35:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:36:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:38:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:40:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:40:44.809+0800	INFO	logs/logger.go:61	0217519748446260000000000000000000000000000000050994e codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 20250708194044B13C55384D81C92905CB
2025-07-08T19:40:44.809+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] finished at 2025-07-08T19:40:44+08:00 cost time 0 ms
2025-07-08T19:41:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:41:44.693+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:44:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:45:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:46:14.693+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:47:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:48:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:50:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:50:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:50:44.939+0800	INFO	logs/logger.go:61	02175197544462700000000000000000000000000000000249db0 codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 20250708195044CD06E677F933A5ECC345
2025-07-08T19:50:44.940+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] finished at 2025-07-08T19:50:44+08:00 cost time 0 ms
2025-07-08T19:51:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:52:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:54:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:55:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:56:14.693+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:57:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:57:44.693+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T19:58:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T20:00:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T20:00:44.879+0800	INFO	logs/logger.go:61	02175197604462600000000000000000000000000000000c851ce codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 202507082000440529411C1195172EFDCB
2025-07-08T20:00:44.880+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] finished at 2025-07-08T20:00:44+08:00 cost time 0 ms
2025-07-08T20:01:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T20:01:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T20:03:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T20:04:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T20:05:14.693+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T20:06:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T20:07:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T20:08:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T20:10:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T20:10:44.829+0800	INFO	logs/logger.go:61	02175197664462600000000000000000000000000000000c5b496 codekg feature gate config is fetch for ****************, map[enable_batch_index:true enable_disk_io_optimize:true enable_interaction_graph:false enable_periodic_init:false enable_periodic_rate_limit_control:false enable_v2_chunking_method:false enable_v3_chunking_method:false exit_when_ppid_changed:true force_local_embedding:true periodic_disk_occupation_metrics:true use_embedding_db_v2:true use_rag_context_bank:false use_v2_ignore:true use_v2_split_embedding_api:true], log id: 20250708201044D44F6052A6FFDB24360C
2025-07-08T20:10:44.830+0800	INFO	logs/logger.go:38	[PeriodicMetricsManager] finished at 2025-07-08T20:10:44+08:00 cost time 0 ms
2025-07-08T20:11:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T20:11:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T20:13:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T20:14:44.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T20:16:14.692+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
2025-07-08T20:17:44.693+0800	INFO	logs/logger.go:38	start to check if ckg needs to exit
