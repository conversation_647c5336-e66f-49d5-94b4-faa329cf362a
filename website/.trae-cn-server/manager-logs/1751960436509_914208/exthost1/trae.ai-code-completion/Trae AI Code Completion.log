2025-07-08T15:40:52.314+08:00 [info] 2025-07-08 15:40:52.304 [info] [client] [<PERSON><PERSON><PERSON>-Analy<PERSON>] TreeSitter.init: start to init tree-sitter instance with resourcesRootPath '/home/<USER>/.trae-cn-server/bin/stable-24760da0f538fb0e77e8d5a54d9102c41a2e4dc4/extensions/ai-completion/resource/aiserver/resources'
2025-07-08T15:40:52.330+08:00 [info] 2025-07-08 15:40:52.330 [info] [client] [CKG-Analyzer] TreeSitter.loadLanguage: load language typescript done
2025-07-08T15:40:52.334+08:00 [info] 2025-07-08 15:40:52.334 [info] [client] [CKG-Analyzer] TreeSitter.loadLanguage: load language javascript done
2025-07-08T15:40:52.339+08:00 [info] 2025-07-08 15:40:52.339 [info] [client] [<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>] TreeSitter.loadLanguage: load language go done
2025-07-08T15:40:52.343+08:00 [info] 2025-07-08 15:40:52.343 [info] [client] [CKG-Analyzer] TreeSitter.loadLanguage: load language python done
2025-07-08T15:40:52.350+08:00 [info] 2025-07-08 15:40:52.350 [info] [client] [CKG-Analyzer] TreeSitter.loadLanguage: load language ruby done
2025-07-08T15:40:52.353+08:00 [info] 2025-07-08 15:40:52.353 [info] [client] [CKG-Analyzer] TreeSitter.loadLanguage: load language java done
2025-07-08T15:40:52.357+08:00 [info] 2025-07-08 15:40:52.357 [info] [client] [CKG-Analyzer] TreeSitter.loadLanguage: load language typescriptreact done
2025-07-08T15:40:52.360+08:00 [info] 2025-07-08 15:40:52.360 [info] [client] [CKG-Analyzer] TreeSitter.loadLanguage: load language c done
2025-07-08T15:40:52.415+08:00 [info] 2025-07-08 15:40:52.415 [info] [client] [CKG-Analyzer] TreeSitter.loadLanguage: load language cpp done
2025-07-08T15:40:52.440+08:00 [info] 2025-07-08 15:40:52.440 [info] [client] [CKG-Analyzer] TreeSitter.loadLanguage: load language c-sharp done
2025-07-08T15:40:52.455+08:00 [info] 2025-07-08 15:40:52.455 [info] [client] [CKG-Analyzer] TreeSitter.loadLanguage: load language kotlin done
2025-07-08T15:40:52.531+08:00 [info] 2025-07-08 15:40:52.531 [info] [client] [CKG-Analyzer] TreeSitter.loadLanguage: load language dart done
2025-07-08T15:40:52.552+08:00 [info] 2025-07-08 15:40:52.552 [info] [client] [CKG-Analyzer] TreeSitter.loadLanguage: load language php done
2025-07-08T15:40:52.556+08:00 [info] 2025-07-08 15:40:52.556 [info] [client] [CKG-Analyzer] TreeSitter.loadLanguage: load language rust done
2025-07-08T15:40:52.556+08:00 [info] 2025-07-08 15:40:52.556 [info] [client] [CKG-Analyzer] TreeSitter.init: init done with resourcesRootPath '/home/<USER>/.trae-cn-server/bin/stable-24760da0f538fb0e77e8d5a54d9102c41a2e4dc4/extensions/ai-completion/resource/aiserver/resources'
2025-07-08T15:40:52.576+08:00 [info] 2025-07-08 15:40:52.574 [info] [client] aiServer-pid:120117
2025-07-08T15:40:52.579+08:00 [info] 2025-07-08 15:40:52.578 [info] [client] this._commandPrefix: trae
2025-07-08T15:40:53.559+08:00 [info] 2025-07-08 15:40:53.559 [error] [client] [baseClient] (node:120117) MaxListenersExceededWarning: Possible EventEmitter memory leak detected. 11 fetchFeaturesSuccess listeners added to [EventEmitter]. MaxListeners is 10. Use emitter.setMaxListeners() to increase limit
(Use `node --trace-warnings ...` to show where the warning was created)
  undefined
2025-07-08T15:40:53.751+08:00 [info] 2025-07-08 15:40:53.748 [info] [client] [trace] Server initialized successfully, and now is running...  undefined
2025-07-08T15:40:53.751+08:00 [info] 2025-07-08 15:40:53.749 [info] [client] base isTob:true
2025-07-08T15:40:53.752+08:00 [info] 2025-07-08 15:40:53.752 [info] [client] [trace] BaseAIClient.registerFeatures features.length:8  undefined
2025-07-08T15:40:54.002+08:00 [info] 2025-07-08 15:40:54.001 [info] [client] features response error:Error: getaddrinfo ENOTFOUND iac.byted.org
2025-07-08T15:40:54.161+08:00 [info] 2025-07-08 15:40:54.161 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":true,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
2025-07-08T15:41:10.996+08:00 [info] 2025-07-08 15:41:10.995 [info] [client] features response error:Error: getaddrinfo ENOTFOUND iac.byted.org
2025-07-08T15:41:11.123+08:00 [info] 2025-07-08 15:41:11.123 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":true,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
2025-07-08T15:41:22.371+08:00 [info] 2025-07-08 15:41:22.356 [info] [client] [CKG-Analyzer] TreeSitter.init: start to init tree-sitter instance with resourcesRootPath '/home/<USER>/.trae-cn-server/bin/stable-24760da0f538fb0e77e8d5a54d9102c41a2e4dc4/extensions/ai-completion/resource/aiserver/resources'
2025-07-08T15:41:22.462+08:00 [info] 2025-07-08 15:41:22.462 [info] [client] [CKG-Analyzer] TreeSitter.loadLanguage: load language typescript done
2025-07-08T15:41:22.466+08:00 [info] 2025-07-08 15:41:22.466 [info] [client] [CKG-Analyzer] TreeSitter.loadLanguage: load language javascript done
2025-07-08T15:41:22.469+08:00 [info] 2025-07-08 15:41:22.468 [info] [client] [CKG-Analyzer] TreeSitter.loadLanguage: load language go done
2025-07-08T15:41:22.474+08:00 [info] 2025-07-08 15:41:22.473 [info] [client] [CKG-Analyzer] TreeSitter.loadLanguage: load language python done
2025-07-08T15:41:22.481+08:00 [info] 2025-07-08 15:41:22.481 [info] [client] [CKG-Analyzer] TreeSitter.loadLanguage: load language ruby done
2025-07-08T15:41:22.484+08:00 [info] 2025-07-08 15:41:22.484 [info] [client] [CKG-Analyzer] TreeSitter.loadLanguage: load language java done
2025-07-08T15:41:22.488+08:00 [info] 2025-07-08 15:41:22.488 [info] [client] [CKG-Analyzer] TreeSitter.loadLanguage: load language typescriptreact done
2025-07-08T15:41:22.491+08:00 [info] 2025-07-08 15:41:22.491 [info] [client] [CKG-Analyzer] TreeSitter.loadLanguage: load language c done
2025-07-08T15:41:22.505+08:00 [info] 2025-07-08 15:41:22.505 [info] [client] [CKG-Analyzer] TreeSitter.loadLanguage: load language cpp done
2025-07-08T15:41:22.516+08:00 [info] 2025-07-08 15:41:22.516 [info] [client] [CKG-Analyzer] TreeSitter.loadLanguage: load language c-sharp done
2025-07-08T15:41:22.529+08:00 [info] 2025-07-08 15:41:22.529 [info] [client] [CKG-Analyzer] TreeSitter.loadLanguage: load language kotlin done
2025-07-08T15:41:22.533+08:00 [info] 2025-07-08 15:41:22.533 [info] [client] [CKG-Analyzer] TreeSitter.loadLanguage: load language dart done
2025-07-08T15:41:22.538+08:00 [info] 2025-07-08 15:41:22.538 [info] [client] [CKG-Analyzer] TreeSitter.loadLanguage: load language php done
2025-07-08T15:41:22.545+08:00 [info] 2025-07-08 15:41:22.545 [info] [client] [CKG-Analyzer] TreeSitter.loadLanguage: load language rust done
2025-07-08T15:41:22.545+08:00 [info] 2025-07-08 15:41:22.545 [info] [client] [CKG-Analyzer] TreeSitter.init: init done with resourcesRootPath '/home/<USER>/.trae-cn-server/bin/stable-24760da0f538fb0e77e8d5a54d9102c41a2e4dc4/extensions/ai-completion/resource/aiserver/resources'
2025-07-08T15:41:22.568+08:00 [info] 2025-07-08 15:41:22.567 [info] [client] aiServer-pid:120516
2025-07-08T15:41:22.571+08:00 [info] 2025-07-08 15:41:22.570 [info] [client] this._commandPrefix: trae
2025-07-08T15:41:23.623+08:00 [info] 2025-07-08 15:41:23.622 [error] [client] [baseClient] (node:120516) MaxListenersExceededWarning: Possible EventEmitter memory leak detected. 11 fetchFeaturesSuccess listeners added to [EventEmitter]. MaxListeners is 10. Use emitter.setMaxListeners() to increase limit
(Use `node --trace-warnings ...` to show where the warning was created)
  undefined
2025-07-08T15:41:23.798+08:00 [info] 2025-07-08 15:41:23.795 [info] [client] [trace] Server initialized successfully, and now is running...  undefined
2025-07-08T15:41:23.798+08:00 [info] 2025-07-08 15:41:23.796 [info] [client] base isTob:true
2025-07-08T15:41:23.799+08:00 [info] 2025-07-08 15:41:23.799 [info] [client] [trace] BaseAIClient.registerFeatures features.length:8  undefined
2025-07-08T15:41:24.065+08:00 [info] 2025-07-08 15:41:24.065 [info] [client] features response error:Error: getaddrinfo ENOTFOUND iac.byted.org
2025-07-08T15:41:24.266+08:00 [info] 2025-07-08 15:41:24.266 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":true,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
2025-07-08T15:45:00.583+08:00 [info] 2025-07-08 15:45:00.583 [info] [client] DidOpenTextDocumentFeature: textDocument.uri: file:///home/<USER>/rlxwglpt/project_development.md
2025-07-08T15:51:01.993+08:00 [info] 2025-07-08 15:51:01.992 [info] [client] DidOpenTextDocumentFeature: textDocument.uri: untitled:Untitled-1
2025-07-08T15:51:12.906+08:00 [info] 2025-07-08 15:51:12.904 [info] [client] [EditorIndicator] hide: operationType:adding
2025-07-08T15:51:15.563+08:00 [info] 2025-07-08 15:51:15.562 [info] [client] [EditorIndicator] hide: operationType:undefined
2025-07-08T15:51:20.464+08:00 [info] 2025-07-08 15:51:20.462 [info] [client] [EditorIndicator] hide: operationType:adding
2025-07-08T15:51:24.182+08:00 [info] 2025-07-08 15:51:24.181 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":true,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
2025-07-08T15:52:02.924+08:00 [info] 2025-07-08 15:52:02.924 [info] [client] DidOpenTextDocumentFeature: textDocument.uri: file:///home/<USER>/rlxwglpt/Untitled-1
2025-07-08T15:52:02.925+08:00 [info] 2025-07-08 15:52:02.925 [info] [client] DidCreateTextDocumentFeature: textDocument.uri: file:///home/<USER>/rlxwglpt/Untitled-1
2025-07-08T15:52:03.019+08:00 [info] 2025-07-08 15:52:03.019 [info] [client] DidCloseTextDocumentFeature: textDocument.uri: untitled:Untitled-1
2025-07-08T15:52:08.072+08:00 [info] 2025-07-08 15:52:08.071 [warn] [client] Document inmemory://model/codeDiffPreviewWidget.codeEditorModel.txt does not match selector [object Object],[object Object],[object Object],[object Object],[object Object]
2025-07-08T15:52:12.030+08:00 [info] 2025-07-08 15:52:12.030 [info] [client] [EditorIndicator] hide: operationType:undefined
2025-07-08T16:01:24.156+08:00 [info] 2025-07-08 16:01:24.155 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":true,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
2025-07-08T16:06:07.905+08:00 [info] 2025-07-08 16:06:07.904 [info] [client] DidOpenTextDocumentFeature: textDocument.uri: untitled:Untitled-1
2025-07-08T16:06:48.884+08:00 [info] 2025-07-08 16:06:48.884 [info] [client] DidCloseTextDocumentFeature: textDocument.uri: untitled:Untitled-1
2025-07-08T16:11:24.210+08:00 [info] 2025-07-08 16:11:24.209 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":false,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
2025-07-08T16:21:24.183+08:00 [info] 2025-07-08 16:21:24.183 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":false,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
2025-07-08T16:31:24.140+08:00 [info] 2025-07-08 16:31:24.140 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":false,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
2025-07-08T16:31:36.189+08:00 [info] 2025-07-08 16:31:36.188 [warn] [client] Document inmemory://model/codeDiffPreviewWidget.codeEditorModel.txt does not match selector [object Object],[object Object],[object Object],[object Object],[object Object]
2025-07-08T16:31:37.688+08:00 [info] 2025-07-08 16:31:37.687 [info] [client] [EditorIndicator] hide: operationType:undefined
2025-07-08T16:36:22.489+08:00 [info] 2025-07-08 16:36:22.489 [info] [client] DidOpenTextDocumentFeature: textDocument.uri: vscode-userdata:/c%3A/Users/<USER>/AppData/Roaming/Trae%20CN/User/settings.json
2025-07-08T16:36:22.703+08:00 [info] 2025-07-08 16:36:22.703 [info] [client] DidCloseTextDocumentFeature: textDocument.uri: vscode-userdata:/c%3A/Users/<USER>/AppData/Roaming/Trae%20CN/User/settings.json
2025-07-08T16:36:24.506+08:00 [info] 2025-07-08 16:36:24.506 [info] [client] DidOpenTextDocumentFeature: textDocument.uri: vscode-userdata:/c%3A/Users/<USER>/AppData/Roaming/Trae%20CN/User/settings.json
2025-07-08T16:36:24.536+08:00 [info] 2025-07-08 16:36:24.536 [info] [client] DidCloseTextDocumentFeature: textDocument.uri: vscode-userdata:/c%3A/Users/<USER>/AppData/Roaming/Trae%20CN/User/settings.json
2025-07-08T16:36:36.172+08:00 [info] 2025-07-08 16:36:36.172 [info] [client] DidCloseTextDocumentFeature: textDocument.uri: file:///home/<USER>/rlxwglpt/Untitled-1
2025-07-08T16:38:59.697+08:00 [info] 2025-07-08 16:38:59.696 [info] [client] [EditorIndicator] hide: operationType:adding
2025-07-08T16:40:25.496+08:00 [info] 2025-07-08 16:40:25.495 [info] [client] [EditorIndicator] hide: operationType:adding
2025-07-08T16:41:24.112+08:00 [info] 2025-07-08 16:41:24.112 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":false,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
2025-07-08T16:50:08.508+08:00 [info] 2025-07-08 16:50:08.506 [warn] [client] Document inmemory://model/codeDiffPreviewWidget.codeEditorModel.txt does not match selector [object Object],[object Object],[object Object],[object Object],[object Object]
2025-07-08T16:50:08.509+08:00 [info] 2025-07-08 16:50:08.508 [info] [client] [EditorIndicator] hide: operationType:undefined
2025-07-08T16:50:09.863+08:00 [info] 2025-07-08 16:50:09.863 [warn] [client] Document inmemory://model/codeDiffPreviewWidget.codeEditorModel.txt does not match selector [object Object],[object Object],[object Object],[object Object],[object Object]
2025-07-08T16:50:11.623+08:00 [info] 2025-07-08 16:50:11.622 [info] [client] [EditorIndicator] hide: operationType:undefined
2025-07-08T16:50:14.026+08:00 [info] 2025-07-08 16:50:14.025 [warn] [client] Document inmemory://model/codeDiffPreviewWidget.codeEditorModel.txt does not match selector [object Object],[object Object],[object Object],[object Object],[object Object]
2025-07-08T16:50:16.513+08:00 [info] 2025-07-08 16:50:16.513 [info] [client] [EditorIndicator] hide: operationType:undefined
2025-07-08T16:51:24.163+08:00 [info] 2025-07-08 16:51:24.163 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":false,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
2025-07-08T16:52:20.285+08:00 [info] 2025-07-08 16:52:20.284 [warn] [client] Document inmemory://model/codeDiffPreviewWidget.codeEditorModel.txt does not match selector [object Object],[object Object],[object Object],[object Object],[object Object]
2025-07-08T16:52:21.846+08:00 [info] 2025-07-08 16:52:21.846 [info] [client] [EditorIndicator] hide: operationType:undefined
2025-07-08T16:52:29.459+08:00 [info] 2025-07-08 16:52:29.459 [warn] [client] Document inmemory://model/codeDiffPreviewWidget.codeEditorModel.txt does not match selector [object Object],[object Object],[object Object],[object Object],[object Object]
2025-07-08T16:52:37.231+08:00 [info] 2025-07-08 16:52:37.229 [info] [client] [EditorIndicator] hide: operationType:undefined
2025-07-08T16:52:54.957+08:00 [info] 2025-07-08 16:52:54.956 [warn] [client] Document inmemory://model/codeDiffPreviewWidget.codeEditorModel.txt does not match selector [object Object],[object Object],[object Object],[object Object],[object Object]
2025-07-08T16:52:59.809+08:00 [info] 2025-07-08 16:52:59.808 [info] [client] [EditorIndicator] hide: operationType:undefined
2025-07-08T16:53:19.904+08:00 [info] 2025-07-08 16:53:19.904 [warn] [client] Document inmemory://model/codeDiffPreviewWidget.codeEditorModel.txt does not match selector [object Object],[object Object],[object Object],[object Object],[object Object]
2025-07-08T16:53:21.777+08:00 [info] 2025-07-08 16:53:21.776 [info] [client] [EditorIndicator] hide: operationType:undefined
2025-07-08T16:53:27.074+08:00 [info] 2025-07-08 16:53:27.073 [warn] [client] Document inmemory://model/codeDiffPreviewWidget.codeEditorModel.txt does not match selector [object Object],[object Object],[object Object],[object Object],[object Object]
2025-07-08T16:53:29.970+08:00 [info] 2025-07-08 16:53:29.970 [info] [client] [EditorIndicator] hide: operationType:undefined
2025-07-08T16:53:40.364+08:00 [info] 2025-07-08 16:53:40.363 [info] [client] [EditorIndicator] hide: operationType:undefined
2025-07-08T16:53:43.296+08:00 [info] 2025-07-08 16:53:43.295 [warn] [client] Document inmemory://model/codeDiffPreviewWidget.codeEditorModel.txt does not match selector [object Object],[object Object],[object Object],[object Object],[object Object]
2025-07-08T16:53:50.804+08:00 [info] 2025-07-08 16:53:50.804 [info] [client] [EditorIndicator] hide: operationType:undefined
2025-07-08T16:54:00.152+08:00 [info] 2025-07-08 16:54:00.151 [warn] [client] Document inmemory://model/codeDiffPreviewWidget.codeEditorModel.txt does not match selector [object Object],[object Object],[object Object],[object Object],[object Object]
2025-07-08T16:54:00.184+08:00 [info] 2025-07-08 16:54:00.183 [info] [client] [EditorIndicator] hide: operationType:undefined
2025-07-08T16:54:51.983+08:00 [info] 2025-07-08 16:54:51.982 [info] [client] [EditorIndicator] hide: operationType:adding
2025-07-08T17:01:24.184+08:00 [info] 2025-07-08 17:01:24.183 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":false,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
2025-07-08T17:11:24.166+08:00 [info] 2025-07-08 17:11:24.166 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":false,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
2025-07-08T17:21:24.135+08:00 [info] 2025-07-08 17:21:24.135 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":false,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
2025-07-08T17:31:24.147+08:00 [info] 2025-07-08 17:31:24.146 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":false,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
2025-07-08T17:41:24.145+08:00 [info] 2025-07-08 17:41:24.145 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":false,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
2025-07-08T17:51:24.183+08:00 [info] 2025-07-08 17:51:24.183 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":false,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
2025-07-08T18:01:24.139+08:00 [info] 2025-07-08 18:01:24.139 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":false,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
2025-07-08T18:11:24.183+08:00 [info] 2025-07-08 18:11:24.183 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":false,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
2025-07-08T18:21:24.226+08:00 [info] 2025-07-08 18:21:24.225 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":false,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
2025-07-08T18:31:24.147+08:00 [info] 2025-07-08 18:31:24.147 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":false,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
2025-07-08T18:41:24.139+08:00 [info] 2025-07-08 18:41:24.138 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":false,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
2025-07-08T18:51:24.141+08:00 [info] 2025-07-08 18:51:24.141 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":false,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
2025-07-08T19:01:24.163+08:00 [info] 2025-07-08 19:01:24.162 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":false,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
2025-07-08T19:11:24.416+08:00 [info] 2025-07-08 19:11:24.415 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":false,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
2025-07-08T19:21:24.236+08:00 [info] 2025-07-08 19:21:24.235 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":false,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
2025-07-08T19:31:24.186+08:00 [info] 2025-07-08 19:31:24.186 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":false,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
2025-07-08T19:41:24.250+08:00 [info] 2025-07-08 19:41:24.250 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":false,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
2025-07-08T19:51:24.316+08:00 [info] 2025-07-08 19:51:24.316 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":false,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
2025-07-08T20:01:24.286+08:00 [info] 2025-07-08 20:01:24.286 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":false,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
2025-07-08T20:11:24.211+08:00 [info] 2025-07-08 20:11:24.211 [info] [client] [FeatureTAG]：workspaceName:MarsCodePlugin, features:{"ai_service_new_rpc_enable":true,"apm_enable_trace_global_exception":false,"apm_enable_trace_global_exception_v2":false,"chat_completion_request_with_no_abort":true,"disable_analyzer_event":false,"disable_analyzer_log":false,"disable_jetbrains_ckg_loading_view":false,"disable_vscode_ckg_loading_view":false,"embedding_storage_type":true,"enabel_multi_edit_indicator":true,"enable_agentic_chat":false,"enable_analyzer_ast_cache_analysis":true,"enable_analyzer_definition_analysis":true,"enable_analyzer_go_definition_analysis":true,"enable_analyzer_java_definition_analysis":true,"enable_analyzer_psi_lsp_request":true,"enable_analyzer_psi_lsp_timeout_interval":true,"enable_analyzer_python_definition_analysis":true,"enable_analyzer_ts_definition_analysis":true,"enable_apm_aiserver_ckg_trace":false,"enable_apm_aiserver_cpu_trace":true,"enable_apm_aiserver_event_delay_trace":true,"enable_apm_aiserver_mem_trace":true,"enable_apm_aiserver_rpc_duration_trace":true,"enable_apm_monitor_net_request":true,"enable_apm_vscode_cpu_trace":true,"enable_apm_vscode_event_delay_trace":true,"enable_apm_vscode_lsp_duration_trace":false,"enable_apm_vscode_mem_trace":true,"enable_apm_vscode_rpc_duration_trace":true,"enable_behavior_report":false,"enable_behavior_trace":true,"enable_chat_completion_backup_host":true,"enable_ckg_index_success_start_ignore_file_num":true,"enable_default_marscode_for_cn":true,"enable_go_unittest_context":true,"enable_hash_doc":false,"enable_hot_streak_post_process_1":true,"enable_hot_streak_post_process_2":true,"enable_inline_visible_judge":false,"enable_local_embedding":true,"enable_multi_edit":false,"enable_multi_edit_image_cache":true,"enable_multi_edit_indicator":true,"enable_multi_edit_render_both":false,"enable_multi_edit_syntax_highlight_cache":true,"enable_multi_edit_ui_opt":true,"enable_multi_embedding_context":true,"enable_multi_stream_diff":true,"enable_multi_sync_code_contribution":false,"enable_network_check":false,"enable_new_network_api":false,"enable_new_network_api_v2":false,"enable_new_network_api_v3":false,"enable_new_version_get_jwt_in_ai_server":true,"enable_optimize_accept_rate":true,"enable_post_process_keep_display_when_short_edit_distance":true,"enable_pred_workspace":true,"enable_real_time_code_contribution":true,"enable_register_local_cert":true,"enable_remote_jump":true,"enable_slardar_offline_log":false,"enable_trigger_inline_completion_when_multi_edit_hide":true,"enable_vmok_chatapp":false,"enable_workspace_keyword_rerank":false,"enable_workspace_keyword_search":false,"enable_workspace_keyword_search_v2":true,"feature_disable_valid_document_change_check":false,"feature_enable_code_contribution":true,"feature_enable_document_changed_not_in_bulk_mode":false,"feature_enable_document_changed_not_in_bulk_mode_v2":true,"feature_enable_document_selection_change":true,"feature_enable_full_line_conflict":false,"feature_enable_jetbrains_auth_v2":false,"feature_enable_jetbrains_webview":true,"feature_enable_load_avatar_async":true,"feature_enable_multi_await_login":false,"feature_enable_trace_global_exception":false,"feature_enable_vscode_auth_v2":false,"feature_jetbrains_reload_page_in_login":false,"feature_jtb_enable_hash_code":false,"feature_jtb_enable_hash_file":false,"ide_ad_switch":true,"is_metrics_revise_enable":false,"tabtab_feature_enable":false,"use_non_pipeline_chat":true,"use_pipeline_chat":false,"vscode_auth_v2_global":true,"vscode_enable_click_out_user_when_fresh_token_null":true}
