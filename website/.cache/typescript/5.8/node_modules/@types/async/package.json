{"name": "@types/async", "version": "3.2.24", "description": "TypeScript definitions for async", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/async", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "kern0", "url": "https://github.com/kern0"}, {"name": "<PERSON>", "githubUsername": "fenying", "url": "https://github.com/fenying"}, {"name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/pascalmartin"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "er<PERSON><PERSON><PERSON>", "url": "https://github.com/erossignon"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Juliiii"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/brendtumi"}, {"name": "<PERSON>", "githubUsername": "apnsngr", "url": "https://github.com/apnsngr"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/async"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "d22df6dcc4644f4e587d58121a6afb56868467fd940a0c9bb41bc28d5ef07504", "typeScriptVersion": "4.5"}