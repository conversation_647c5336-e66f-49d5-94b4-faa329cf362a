{"name": "@types/less", "version": "3.0.8", "description": "TypeScript definitions for less", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/less", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "thasner", "url": "https://github.com/thasner"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "pranaygp", "url": "https://github.com/pranaygp"}, {"name": "<PERSON>", "githubUsername": "chigix", "url": "https://github.com/chigix"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/less"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typesPublisherContentHash": "41973a7c73f991a83072c681d7a834b594297d3f106b0ed9d7b77cc116f78eb2", "typeScriptVersion": "5.0"}