# Installation
> `npm install --save @types/brace-expansion`

# Summary
This package contains type definitions for brace-expansion (https://github.com/juliangruber/brace-expansion).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/brace-expansion.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/brace-expansion/index.d.ts)
````ts
export = expand;

declare function expand(input: string): string[];

````

### Additional Details
 * Last updated: Mon, 06 Nov 2023 22:41:05 GMT
 * Dependencies: none

# Credits
These definitions were written by [<PERSON><PERSON><PERSON><PERSON>](https://github.com/BendingBender).
