{"name": "@types/express-session", "version": "1.18.2", "description": "TypeScript definitions for express-session", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/jacobbogers"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/builtinnya"}, {"name": "<PERSON>", "githubUsername": "ry7n", "url": "https://github.com/ry7n"}, {"name": "<PERSON>", "githubUsername": "fiznool", "url": "https://github.com/fiznool"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "githubUsername": "HoldYourWaffle", "url": "https://github.com/HoldYourWaffle"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON><PERSON>bas"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express-session"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "684a3bf08e3ce762fbe9e75a77866983245e65cc0e08830fa2880f92e9d8ae58", "typeScriptVersion": "5.1"}