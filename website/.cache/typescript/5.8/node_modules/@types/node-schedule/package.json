{"name": "@types/node-schedule", "version": "2.1.8", "description": "TypeScript definitions for node-schedule", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node-schedule", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/cyrilschumacher"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "flowpl", "url": "https://github.com/flowpl"}, {"name": "<PERSON><PERSON>", "githubUsername": "spike008t", "url": "https://github.com/spike008t"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "seohyun0120", "url": "https://github.com/seohyun0120"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/node-schedule"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "82335221db46f6c0ec7f9050ca871836b1ed55be6abedee9e283591ccb0f2365", "typeScriptVersion": "5.1"}