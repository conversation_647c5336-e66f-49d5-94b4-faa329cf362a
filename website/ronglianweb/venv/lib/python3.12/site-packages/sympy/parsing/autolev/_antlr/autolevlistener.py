# *** GENERATED BY `setup.py antlr`, DO NOT EDIT BY HAND ***
#
# Generated with antlr4
#    antlr4 is licensed under the BSD-3-Clause License
#    https://github.com/antlr/antlr4/blob/master/LICENSE.txt
from antlr4 import *
if __name__ is not None and "." in __name__:
    from .autolevparser import AutolevParser
else:
    from autolevparser import AutolevParser

# This class defines a complete listener for a parse tree produced by AutolevParser.
class AutolevListener(ParseTreeListener):

    # Enter a parse tree produced by AutolevParser#prog.
    def enterProg(self, ctx:AutolevParser.ProgContext):
        pass

    # Exit a parse tree produced by AutolevParser#prog.
    def exitProg(self, ctx:AutolevParser.ProgContext):
        pass


    # Enter a parse tree produced by AutolevParser#stat.
    def enterStat(self, ctx:AutolevParser.StatContext):
        pass

    # Exit a parse tree produced by AutolevParser#stat.
    def exitStat(self, ctx:AutolevParser.StatContext):
        pass


    # Enter a parse tree produced by AutolevParser#vecAssign.
    def enterVecAssign(self, ctx:AutolevParser.VecAssignContext):
        pass

    # Exit a parse tree produced by AutolevParser#vecAssign.
    def exitVecAssign(self, ctx:AutolevParser.VecAssignContext):
        pass


    # Enter a parse tree produced by AutolevParser#indexAssign.
    def enterIndexAssign(self, ctx:AutolevParser.IndexAssignContext):
        pass

    # Exit a parse tree produced by AutolevParser#indexAssign.
    def exitIndexAssign(self, ctx:AutolevParser.IndexAssignContext):
        pass


    # Enter a parse tree produced by AutolevParser#regularAssign.
    def enterRegularAssign(self, ctx:AutolevParser.RegularAssignContext):
        pass

    # Exit a parse tree produced by AutolevParser#regularAssign.
    def exitRegularAssign(self, ctx:AutolevParser.RegularAssignContext):
        pass


    # Enter a parse tree produced by AutolevParser#equals.
    def enterEquals(self, ctx:AutolevParser.EqualsContext):
        pass

    # Exit a parse tree produced by AutolevParser#equals.
    def exitEquals(self, ctx:AutolevParser.EqualsContext):
        pass


    # Enter a parse tree produced by AutolevParser#index.
    def enterIndex(self, ctx:AutolevParser.IndexContext):
        pass

    # Exit a parse tree produced by AutolevParser#index.
    def exitIndex(self, ctx:AutolevParser.IndexContext):
        pass


    # Enter a parse tree produced by AutolevParser#diff.
    def enterDiff(self, ctx:AutolevParser.DiffContext):
        pass

    # Exit a parse tree produced by AutolevParser#diff.
    def exitDiff(self, ctx:AutolevParser.DiffContext):
        pass


    # Enter a parse tree produced by AutolevParser#functionCall.
    def enterFunctionCall(self, ctx:AutolevParser.FunctionCallContext):
        pass

    # Exit a parse tree produced by AutolevParser#functionCall.
    def exitFunctionCall(self, ctx:AutolevParser.FunctionCallContext):
        pass


    # Enter a parse tree produced by AutolevParser#varDecl.
    def enterVarDecl(self, ctx:AutolevParser.VarDeclContext):
        pass

    # Exit a parse tree produced by AutolevParser#varDecl.
    def exitVarDecl(self, ctx:AutolevParser.VarDeclContext):
        pass


    # Enter a parse tree produced by AutolevParser#varType.
    def enterVarType(self, ctx:AutolevParser.VarTypeContext):
        pass

    # Exit a parse tree produced by AutolevParser#varType.
    def exitVarType(self, ctx:AutolevParser.VarTypeContext):
        pass


    # Enter a parse tree produced by AutolevParser#varDecl2.
    def enterVarDecl2(self, ctx:AutolevParser.VarDecl2Context):
        pass

    # Exit a parse tree produced by AutolevParser#varDecl2.
    def exitVarDecl2(self, ctx:AutolevParser.VarDecl2Context):
        pass


    # Enter a parse tree produced by AutolevParser#ranges.
    def enterRanges(self, ctx:AutolevParser.RangesContext):
        pass

    # Exit a parse tree produced by AutolevParser#ranges.
    def exitRanges(self, ctx:AutolevParser.RangesContext):
        pass


    # Enter a parse tree produced by AutolevParser#massDecl.
    def enterMassDecl(self, ctx:AutolevParser.MassDeclContext):
        pass

    # Exit a parse tree produced by AutolevParser#massDecl.
    def exitMassDecl(self, ctx:AutolevParser.MassDeclContext):
        pass


    # Enter a parse tree produced by AutolevParser#massDecl2.
    def enterMassDecl2(self, ctx:AutolevParser.MassDecl2Context):
        pass

    # Exit a parse tree produced by AutolevParser#massDecl2.
    def exitMassDecl2(self, ctx:AutolevParser.MassDecl2Context):
        pass


    # Enter a parse tree produced by AutolevParser#inertiaDecl.
    def enterInertiaDecl(self, ctx:AutolevParser.InertiaDeclContext):
        pass

    # Exit a parse tree produced by AutolevParser#inertiaDecl.
    def exitInertiaDecl(self, ctx:AutolevParser.InertiaDeclContext):
        pass


    # Enter a parse tree produced by AutolevParser#matrix.
    def enterMatrix(self, ctx:AutolevParser.MatrixContext):
        pass

    # Exit a parse tree produced by AutolevParser#matrix.
    def exitMatrix(self, ctx:AutolevParser.MatrixContext):
        pass


    # Enter a parse tree produced by AutolevParser#matrixInOutput.
    def enterMatrixInOutput(self, ctx:AutolevParser.MatrixInOutputContext):
        pass

    # Exit a parse tree produced by AutolevParser#matrixInOutput.
    def exitMatrixInOutput(self, ctx:AutolevParser.MatrixInOutputContext):
        pass


    # Enter a parse tree produced by AutolevParser#codeCommands.
    def enterCodeCommands(self, ctx:AutolevParser.CodeCommandsContext):
        pass

    # Exit a parse tree produced by AutolevParser#codeCommands.
    def exitCodeCommands(self, ctx:AutolevParser.CodeCommandsContext):
        pass


    # Enter a parse tree produced by AutolevParser#settings.
    def enterSettings(self, ctx:AutolevParser.SettingsContext):
        pass

    # Exit a parse tree produced by AutolevParser#settings.
    def exitSettings(self, ctx:AutolevParser.SettingsContext):
        pass


    # Enter a parse tree produced by AutolevParser#units.
    def enterUnits(self, ctx:AutolevParser.UnitsContext):
        pass

    # Exit a parse tree produced by AutolevParser#units.
    def exitUnits(self, ctx:AutolevParser.UnitsContext):
        pass


    # Enter a parse tree produced by AutolevParser#inputs.
    def enterInputs(self, ctx:AutolevParser.InputsContext):
        pass

    # Exit a parse tree produced by AutolevParser#inputs.
    def exitInputs(self, ctx:AutolevParser.InputsContext):
        pass


    # Enter a parse tree produced by AutolevParser#id_diff.
    def enterId_diff(self, ctx:AutolevParser.Id_diffContext):
        pass

    # Exit a parse tree produced by AutolevParser#id_diff.
    def exitId_diff(self, ctx:AutolevParser.Id_diffContext):
        pass


    # Enter a parse tree produced by AutolevParser#inputs2.
    def enterInputs2(self, ctx:AutolevParser.Inputs2Context):
        pass

    # Exit a parse tree produced by AutolevParser#inputs2.
    def exitInputs2(self, ctx:AutolevParser.Inputs2Context):
        pass


    # Enter a parse tree produced by AutolevParser#outputs.
    def enterOutputs(self, ctx:AutolevParser.OutputsContext):
        pass

    # Exit a parse tree produced by AutolevParser#outputs.
    def exitOutputs(self, ctx:AutolevParser.OutputsContext):
        pass


    # Enter a parse tree produced by AutolevParser#outputs2.
    def enterOutputs2(self, ctx:AutolevParser.Outputs2Context):
        pass

    # Exit a parse tree produced by AutolevParser#outputs2.
    def exitOutputs2(self, ctx:AutolevParser.Outputs2Context):
        pass


    # Enter a parse tree produced by AutolevParser#codegen.
    def enterCodegen(self, ctx:AutolevParser.CodegenContext):
        pass

    # Exit a parse tree produced by AutolevParser#codegen.
    def exitCodegen(self, ctx:AutolevParser.CodegenContext):
        pass


    # Enter a parse tree produced by AutolevParser#commands.
    def enterCommands(self, ctx:AutolevParser.CommandsContext):
        pass

    # Exit a parse tree produced by AutolevParser#commands.
    def exitCommands(self, ctx:AutolevParser.CommandsContext):
        pass


    # Enter a parse tree produced by AutolevParser#vec.
    def enterVec(self, ctx:AutolevParser.VecContext):
        pass

    # Exit a parse tree produced by AutolevParser#vec.
    def exitVec(self, ctx:AutolevParser.VecContext):
        pass


    # Enter a parse tree produced by AutolevParser#parens.
    def enterParens(self, ctx:AutolevParser.ParensContext):
        pass

    # Exit a parse tree produced by AutolevParser#parens.
    def exitParens(self, ctx:AutolevParser.ParensContext):
        pass


    # Enter a parse tree produced by AutolevParser#VectorOrDyadic.
    def enterVectorOrDyadic(self, ctx:AutolevParser.VectorOrDyadicContext):
        pass

    # Exit a parse tree produced by AutolevParser#VectorOrDyadic.
    def exitVectorOrDyadic(self, ctx:AutolevParser.VectorOrDyadicContext):
        pass


    # Enter a parse tree produced by AutolevParser#Exponent.
    def enterExponent(self, ctx:AutolevParser.ExponentContext):
        pass

    # Exit a parse tree produced by AutolevParser#Exponent.
    def exitExponent(self, ctx:AutolevParser.ExponentContext):
        pass


    # Enter a parse tree produced by AutolevParser#MulDiv.
    def enterMulDiv(self, ctx:AutolevParser.MulDivContext):
        pass

    # Exit a parse tree produced by AutolevParser#MulDiv.
    def exitMulDiv(self, ctx:AutolevParser.MulDivContext):
        pass


    # Enter a parse tree produced by AutolevParser#AddSub.
    def enterAddSub(self, ctx:AutolevParser.AddSubContext):
        pass

    # Exit a parse tree produced by AutolevParser#AddSub.
    def exitAddSub(self, ctx:AutolevParser.AddSubContext):
        pass


    # Enter a parse tree produced by AutolevParser#float.
    def enterFloat(self, ctx:AutolevParser.FloatContext):
        pass

    # Exit a parse tree produced by AutolevParser#float.
    def exitFloat(self, ctx:AutolevParser.FloatContext):
        pass


    # Enter a parse tree produced by AutolevParser#int.
    def enterInt(self, ctx:AutolevParser.IntContext):
        pass

    # Exit a parse tree produced by AutolevParser#int.
    def exitInt(self, ctx:AutolevParser.IntContext):
        pass


    # Enter a parse tree produced by AutolevParser#idEqualsExpr.
    def enterIdEqualsExpr(self, ctx:AutolevParser.IdEqualsExprContext):
        pass

    # Exit a parse tree produced by AutolevParser#idEqualsExpr.
    def exitIdEqualsExpr(self, ctx:AutolevParser.IdEqualsExprContext):
        pass


    # Enter a parse tree produced by AutolevParser#negativeOne.
    def enterNegativeOne(self, ctx:AutolevParser.NegativeOneContext):
        pass

    # Exit a parse tree produced by AutolevParser#negativeOne.
    def exitNegativeOne(self, ctx:AutolevParser.NegativeOneContext):
        pass


    # Enter a parse tree produced by AutolevParser#function.
    def enterFunction(self, ctx:AutolevParser.FunctionContext):
        pass

    # Exit a parse tree produced by AutolevParser#function.
    def exitFunction(self, ctx:AutolevParser.FunctionContext):
        pass


    # Enter a parse tree produced by AutolevParser#rangess.
    def enterRangess(self, ctx:AutolevParser.RangessContext):
        pass

    # Exit a parse tree produced by AutolevParser#rangess.
    def exitRangess(self, ctx:AutolevParser.RangessContext):
        pass


    # Enter a parse tree produced by AutolevParser#colon.
    def enterColon(self, ctx:AutolevParser.ColonContext):
        pass

    # Exit a parse tree produced by AutolevParser#colon.
    def exitColon(self, ctx:AutolevParser.ColonContext):
        pass


    # Enter a parse tree produced by AutolevParser#id.
    def enterId(self, ctx:AutolevParser.IdContext):
        pass

    # Exit a parse tree produced by AutolevParser#id.
    def exitId(self, ctx:AutolevParser.IdContext):
        pass


    # Enter a parse tree produced by AutolevParser#exp.
    def enterExp(self, ctx:AutolevParser.ExpContext):
        pass

    # Exit a parse tree produced by AutolevParser#exp.
    def exitExp(self, ctx:AutolevParser.ExpContext):
        pass


    # Enter a parse tree produced by AutolevParser#matrices.
    def enterMatrices(self, ctx:AutolevParser.MatricesContext):
        pass

    # Exit a parse tree produced by AutolevParser#matrices.
    def exitMatrices(self, ctx:AutolevParser.MatricesContext):
        pass


    # Enter a parse tree produced by AutolevParser#Indexing.
    def enterIndexing(self, ctx:AutolevParser.IndexingContext):
        pass

    # Exit a parse tree produced by AutolevParser#Indexing.
    def exitIndexing(self, ctx:AutolevParser.IndexingContext):
        pass



del AutolevParser
