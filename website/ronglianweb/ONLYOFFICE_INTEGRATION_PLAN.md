# OnlyOffice 集成方案 - MS Office兼容在线编辑器

## 🎯 方案概述

OnlyOffice Document Server 是目前最佳的MS Office兼容在线编辑解决方案，提供100%兼容的Word、Excel、PowerPoint在线编辑功能。

## 🏗️ 技术架构

### 核心组件
```
前端应用 (Vue 3)
    ↓
OnlyOffice Document Server
    ↓
文件存储系统
    ↓
后端API (Node.js)
```

### 技术栈
- **前端**: Vue 3.4+ + TypeScript + Element Plus
- **编辑器**: OnlyOffice Document Server 7.5+
- **后端**: Node.js + Express
- **数据库**: MongoDB (文档元数据)
- **文件存储**: 本地存储 + 阿里云OSS
- **认证**: JWT Token

## 📦 安装部署

### 1. OnlyOffice Document Server 安装

#### Docker 部署 (推荐)
```bash
# 拉取OnlyOffice镜像
docker pull onlyoffice/documentserver:latest

# 启动Document Server
docker run -i -t -d -p 8080:80 \
  -e JWT_ENABLED=true \
  -e JWT_SECRET=your_jwt_secret \
  -v /app/onlyoffice/DocumentServer/logs:/var/log/onlyoffice \
  -v /app/onlyoffice/DocumentServer/data:/var/www/onlyoffice/Data \
  -v /app/onlyoffice/DocumentServer/lib:/var/lib/onlyoffice \
  -v /app/onlyoffice/DocumentServer/db:/var/lib/postgresql \
  --name documentserver \
  onlyoffice/documentserver
```

#### Ubuntu 直接安装
```bash
# 添加OnlyOffice仓库
echo "deb https://download.onlyoffice.com/repo/debian squeeze main" | sudo tee /etc/apt/sources.list.d/onlyoffice.list
wget -qO - https://download.onlyoffice.com/GPG-KEY-ONLYOFFICE | sudo apt-key add -

# 安装Document Server
sudo apt-get update
sudo apt-get install onlyoffice-documentserver
```

### 2. 前端集成

#### Vue 3 组件
```vue
<template>
  <div class="document-editor">
    <div id="placeholder" style="height: 600px;"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

interface DocumentConfig {
  document: {
    fileType: string
    key: string
    title: string
    url: string
    permissions: {
      edit: boolean
      download: boolean
      review: boolean
    }
  }
  documentType: string
  editorConfig: {
    user: {
      id: string
      name: string
    }
    callbackUrl: string
    lang: string
  }
  width: string
  height: string
}

const props = defineProps<{
  documentUrl: string
  documentKey: string
  documentTitle: string
  canEdit: boolean
}>()

let docEditor: any = null

onMounted(() => {
  initEditor()
})

const initEditor = () => {
  const config: DocumentConfig = {
    document: {
      fileType: getFileExtension(props.documentTitle),
      key: props.documentKey,
      title: props.documentTitle,
      url: props.documentUrl,
      permissions: {
        edit: props.canEdit,
        download: true,
        review: true
      }
    },
    documentType: getDocumentType(props.documentTitle),
    editorConfig: {
      user: {
        id: getCurrentUserId(),
        name: getCurrentUserName()
      },
      callbackUrl: '/api/documents/callback',
      lang: 'zh-CN'
    },
    width: '100%',
    height: '600px'
  }

  // 初始化OnlyOffice编辑器
  docEditor = new DocsAPI.DocEditor('placeholder', config)
}

const getFileExtension = (filename: string): string => {
  return filename.split('.').pop()?.toLowerCase() || 'docx'
}

const getDocumentType = (filename: string): string => {
  const ext = getFileExtension(filename)
  if (['doc', 'docx', 'odt', 'rtf', 'txt'].includes(ext)) return 'text'
  if (['xls', 'xlsx', 'ods', 'csv'].includes(ext)) return 'spreadsheet'
  if (['ppt', 'pptx', 'odp'].includes(ext)) return 'presentation'
  return 'text'
}

onUnmounted(() => {
  if (docEditor) {
    docEditor.destroyEditor()
  }
})
</script>
```

### 3. 后端API实现

#### 文档管理API
```javascript
// routes/documents.js
const express = require('express')
const multer = require('multer')
const path = require('path')
const fs = require('fs')
const jwt = require('jsonwebtoken')

const router = express.Router()

// 文件上传配置
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/documents/')
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9)
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname))
  }
})

const upload = multer({ storage })

// 上传文档
router.post('/upload', upload.single('document'), async (req, res) => {
  try {
    const { originalname, filename, path: filePath } = req.file
    
    // 生成文档密钥
    const documentKey = generateDocumentKey(filename)
    
    // 保存文档信息到数据库
    const document = await saveDocumentInfo({
      title: originalname,
      filename,
      path: filePath,
      key: documentKey,
      uploadedBy: req.user.id,
      uploadedAt: new Date()
    })
    
    res.json({
      success: true,
      data: {
        id: document.id,
        title: document.title,
        key: document.key,
        url: `/api/documents/file/${document.filename}`
      }
    })
  } catch (error) {
    res.status(500).json({ success: false, message: error.message })
  }
})

// 获取文档文件
router.get('/file/:filename', (req, res) => {
  const filePath = path.join(__dirname, '../uploads/documents', req.params.filename)
  
  if (fs.existsSync(filePath)) {
    res.sendFile(path.resolve(filePath))
  } else {
    res.status(404).json({ success: false, message: '文件不存在' })
  }
})

// OnlyOffice回调处理
router.post('/callback', express.json(), async (req, res) => {
  try {
    const { key, status, url, users } = req.body
    
    // 验证JWT签名
    if (!verifyJWTSignature(req.body)) {
      return res.status(403).json({ error: 'Invalid signature' })
    }
    
    switch (status) {
      case 1: // 文档正在编辑
        console.log(`文档 ${key} 正在被编辑`)
        break
        
      case 2: // 文档准备保存
        if (url) {
          await downloadAndSaveDocument(key, url)
          console.log(`文档 ${key} 已保存`)
        }
        break
        
      case 3: // 文档保存出错
        console.error(`文档 ${key} 保存出错`)
        break
        
      case 4: // 文档关闭，无修改
        console.log(`文档 ${key} 已关闭`)
        break
        
      case 6: // 文档正在编辑，但当前用户已断开连接
        console.log(`用户已断开文档 ${key} 的连接`)
        break
        
      case 7: // 强制保存文档
        if (url) {
          await downloadAndSaveDocument(key, url)
          console.log(`文档 ${key} 已强制保存`)
        }
        break
    }
    
    res.json({ error: 0 })
  } catch (error) {
    console.error('回调处理错误:', error)
    res.status(500).json({ error: 1, message: error.message })
  }
})

// 生成文档密钥
function generateDocumentKey(filename) {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2)
  return `${filename}_${timestamp}_${random}`
}

// 下载并保存文档
async function downloadAndSaveDocument(key, url) {
  const axios = require('axios')
  const response = await axios.get(url, { responseType: 'stream' })
  
  // 根据key找到原始文件路径
  const document = await findDocumentByKey(key)
  if (document) {
    const filePath = path.join(__dirname, '../uploads/documents', document.filename)
    const writer = fs.createWriteStream(filePath)
    response.data.pipe(writer)
    
    return new Promise((resolve, reject) => {
      writer.on('finish', resolve)
      writer.on('error', reject)
    })
  }
}

module.exports = router
```

### 4. 数据库模型

#### 文档模型 (MongoDB)
```javascript
// models/Document.js
const mongoose = require('mongoose')

const documentSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true
  },
  filename: {
    type: String,
    required: true,
    unique: true
  },
  path: {
    type: String,
    required: true
  },
  key: {
    type: String,
    required: true,
    unique: true
  },
  fileType: {
    type: String,
    required: true
  },
  size: {
    type: Number,
    default: 0
  },
  uploadedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  uploadedAt: {
    type: Date,
    default: Date.now
  },
  lastModified: {
    type: Date,
    default: Date.now
  },
  version: {
    type: Number,
    default: 1
  },
  permissions: {
    canEdit: { type: Boolean, default: true },
    canDownload: { type: Boolean, default: true },
    canReview: { type: Boolean, default: true }
  },
  collaborators: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    permission: {
      type: String,
      enum: ['read', 'edit', 'admin'],
      default: 'read'
    }
  }]
}, {
  timestamps: true
})

module.exports = mongoose.model('Document', documentSchema)
```

## 🔧 配置说明

### OnlyOffice 配置文件
```javascript
// config/onlyoffice.js
module.exports = {
  documentServer: {
    url: process.env.ONLYOFFICE_URL || 'http://localhost:8080',
    jwtSecret: process.env.JWT_SECRET || 'your_jwt_secret',
    jwtHeader: 'Authorization',
    timeout: 120000
  },
  fileStorage: {
    path: process.env.UPLOAD_PATH || './uploads/documents',
    maxSize: 50 * 1024 * 1024, // 50MB
    allowedTypes: ['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.pdf', '.txt', '.rtf', '.odt', '.ods', '.odp']
  },
  features: {
    collaboration: true,
    comments: true,
    review: true,
    chat: true,
    plugins: true
  }
}
```

## 🚀 部署步骤

### 1. 环境准备
```bash
# 创建项目目录
mkdir onlyoffice-integration
cd onlyoffice-integration

# 初始化项目
npm init -y

# 安装依赖
npm install express multer mongoose jsonwebtoken axios cors helmet
npm install -D nodemon typescript @types/node
```

### 2. Docker Compose 部署
```yaml
# docker-compose.yml
version: '3.8'

services:
  documentserver:
    image: onlyoffice/documentserver:latest
    container_name: onlyoffice-documentserver
    ports:
      - "8080:80"
    environment:
      - JWT_ENABLED=true
      - JWT_SECRET=your_jwt_secret
    volumes:
      - ./data/onlyoffice/logs:/var/log/onlyoffice
      - ./data/onlyoffice/data:/var/www/onlyoffice/Data
      - ./data/onlyoffice/lib:/var/lib/onlyoffice
      - ./data/onlyoffice/db:/var/lib/postgresql
    restart: unless-stopped

  mongodb:
    image: mongo:latest
    container_name: mongodb
    ports:
      - "27017:27017"
    volumes:
      - ./data/mongodb:/data/db
    restart: unless-stopped

  app:
    build: .
    container_name: news-app
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - MONGODB_URL=mongodb://mongodb:27017/newsdb
      - ONLYOFFICE_URL=http://documentserver
      - JWT_SECRET=your_jwt_secret
    depends_on:
      - documentserver
      - mongodb
    volumes:
      - ./uploads:/app/uploads
    restart: unless-stopped
```

### 3. 启动服务
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 📊 功能特性

### 支持的文件格式
- **文档**: .doc, .docx, .odt, .rtf, .txt
- **表格**: .xls, .xlsx, .ods, .csv
- **演示**: .ppt, .pptx, .odp
- **PDF**: 查看模式

### 编辑功能
- ✅ **完整格式支持** - 字体、样式、表格、图片
- ✅ **实时协作** - 多人同时编辑
- ✅ **评论和审阅** - 文档审批流程
- ✅ **版本控制** - 自动保存历史版本
- ✅ **插件支持** - 扩展功能

### 集成优势
- ✅ **100% MS Office兼容** - 无格式丢失
- ✅ **云端存储** - 支持各种存储后端
- ✅ **权限控制** - 细粒度权限管理
- ✅ **API丰富** - 完整的开发接口

## 💰 成本分析

### 开源版本 (免费)
- ✅ 最多20个并发连接
- ✅ 基础编辑功能
- ✅ 协作编辑
- ❌ 无技术支持

### 企业版本 (付费)
- ✅ 无连接数限制
- ✅ 高级功能
- ✅ 技术支持
- ✅ 品牌定制

## 🎯 实施建议

1. **先部署测试环境** - 验证功能和性能
2. **逐步迁移** - 从新文档开始使用
3. **用户培训** - 熟悉在线编辑操作
4. **备份策略** - 确保数据安全
5. **性能监控** - 监控服务器资源使用

这个方案提供了**完整的MS Office兼容在线编辑解决方案**，是目前市场上最成熟的选择！
