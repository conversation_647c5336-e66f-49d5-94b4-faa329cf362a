<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>基础按钮测试</title>
    <link rel="stylesheet" href="/plugins/bootstrap/css/bootstrap.min.css">
    <style>
        .container { margin-top: 20px; }
        .test-section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 4px; }
        .result { margin-top: 10px; padding: 10px; border-radius: 4px; background-color: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <h1>基础按钮测试</h1>
        <p>这个页面测试最基本的按钮点击功能，不依赖任何复杂的库或功能。</p>
        
        <div class="test-section">
            <h3>1. 原生HTML按钮测试</h3>
            <button id="nativeBtn" class="btn btn-primary">原生按钮</button>
            <div id="nativeResult" class="result">结果将显示在这里</div>
        </div>
        
        <div class="test-section">
            <h3>2. jQuery按钮测试</h3>
            <button id="jqueryBtn" class="btn btn-success">jQuery按钮</button>
            <div id="jqueryResult" class="result">结果将显示在这里</div>
        </div>
        
        <div class="test-section">
            <h3>3. 链接按钮测试</h3>
            <a id="linkBtn" href="javascript:void(0);" class="btn btn-info">链接按钮</a>
            <div id="linkResult" class="result">结果将显示在这里</div>
        </div>
        
        <div class="test-section">
            <h3>4. 小型按钮测试</h3>
            <a id="smallBtn" href="javascript:void(0);" class="btn btn-xs btn-warning">小型按钮</a>
            <div id="smallResult" class="result">结果将显示在这里</div>
        </div>
        
        <div class="test-section">
            <h3>5. 动态添加按钮测试</h3>
            <div id="dynamicBtnContainer"></div>
            <button id="addDynamicBtn" class="btn btn-default">添加动态按钮</button>
            <div id="dynamicResult" class="result">结果将显示在这里</div>
        </div>
        
        <div class="test-section">
            <h3>6. 环境信息</h3>
            <div id="envInfo" class="result">加载中...</div>
        </div>
    </div>

    <!-- 先加载原生JavaScript -->
    <script>
        // 原生JavaScript按钮事件
        document.addEventListener('DOMContentLoaded', function() {
            // 原生按钮测试
            var nativeBtn = document.getElementById('nativeBtn');
            var nativeResult = document.getElementById('nativeResult');
            
            if (nativeBtn && nativeResult) {
                nativeBtn.addEventListener('click', function() {
                    nativeResult.textContent = '原生按钮点击成功！时间: ' + new Date().toLocaleTimeString();
                });
            }
        });
    </script>
    
    <!-- 然后加载jQuery -->
    <script src="/plugins/jquery/jquery-1.11.3.js"></script>
    <script src="/plugins/bootstrap/js/bootstrap.min.js"></script>
    
    <script>
        // jQuery就绪函数
        $(document).ready(function() {
            // 显示环境信息
            var envInfo = '';
            envInfo += 'jQuery版本: ' + $.fn.jquery + '<br>';
            envInfo += 'Bootstrap版本: ' + ($.fn.modal ? '已加载' : '未加载') + '<br>';
            envInfo += '浏览器: ' + navigator.userAgent + '<br>';
            envInfo += '页面加载时间: ' + new Date().toLocaleTimeString() + '<br>';
            $('#envInfo').html(envInfo);
            
            // jQuery按钮测试
            $('#jqueryBtn').on('click', function() {
                $('#jqueryResult').text('jQuery按钮点击成功！时间: ' + new Date().toLocaleTimeString());
            });
            
            // 链接按钮测试
            $('#linkBtn').on('click', function(e) {
                e.preventDefault();
                $('#linkResult').text('链接按钮点击成功！时间: ' + new Date().toLocaleTimeString());
            });
            
            // 小型按钮测试
            $('#smallBtn').on('click', function(e) {
                e.preventDefault();
                $('#smallResult').text('小型按钮点击成功！时间: ' + new Date().toLocaleTimeString());
            });
            
            // 动态添加按钮测试
            $('#addDynamicBtn').on('click', function() {
                var dynamicBtn = $('<a id="dynamicBtn" href="javascript:void(0);" class="btn btn-xs btn-danger">动态按钮</a>');
                $('#dynamicBtnContainer').empty().append(dynamicBtn);
                
                // 为动态添加的按钮绑定事件
                dynamicBtn.on('click', function() {
                    $('#dynamicResult').text('动态按钮点击成功！时间: ' + new Date().toLocaleTimeString());
                });
                
                $('#dynamicResult').text('动态按钮已添加，请点击测试');
            });
            
            // 添加一个全局点击处理器，用于调试
            $(document).on('click', function(e) {
                console.log('点击了元素:', e.target);
            });
        });
    </script>
</body>
</html>
