# Nginx反向代理配置指南

## 概述

通过Nginx反向代理，您可以：
- 使用80端口访问应用（无需管理员权限运行Node.js）
- 获得更好的性能和安全性
- 支持负载均衡和缓存
- 添加SSL/HTTPS支持

## 架构图

```
用户请求 → Nginx (80端口) → Node.js应用 (8080端口)
```

## 已完成的配置

### 1. Nginx安装
- ✅ Nginx 1.24.0 已安装
- ✅ 服务已启动并设置为开机自启

### 2. 反向代理配置
- ✅ 配置文件: `/etc/nginx/sites-available/ronglianweb`
- ✅ 已启用站点: `/etc/nginx/sites-enabled/ronglianweb`
- ✅ 已禁用默认站点

### 3. Node.js应用配置
- ✅ 应用运行在8080端口
- ✅ 通过Nginx代理到80端口

## 配置文件详解

### Nginx配置 (`nginx-ronglianweb.conf`)

```nginx
server {
    listen 80;
    server_name localhost;

    # 反向代理到Node.js应用
    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态文件缓存优化
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        proxy_pass http://localhost:8080;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Gzip压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript;
}
```

## 启动和管理

### 快速启动
```bash
# 使用启动脚本（推荐）
./start-with-nginx.sh
```

### 手动启动
```bash
# 1. 启动Node.js应用
PORT=8080 npm start

# 2. 确保Nginx运行
sudo systemctl start nginx
```

### 停止服务
```bash
# 使用停止脚本
./stop-services.sh

# 或手动停止
pkill -f "node.*bin/www"
sudo systemctl stop nginx
```

## 访问地址

### 通过Nginx代理 (推荐)
- 主页: `http://localhost`
- Vue 3 演示: `http://localhost/vue3-demo`
- Vue 3 测试: `http://localhost/vue3-test`
- Vue 3 主页: `http://localhost/vue3-home`

### 直接访问Node.js
- 主页: `http://localhost:8080`
- Vue 3 演示: `http://localhost:8080/vue3-demo`

## 日志和监控

### Nginx日志
```bash
# 访问日志
sudo tail -f /var/log/nginx/ronglianweb_access.log

# 错误日志
sudo tail -f /var/log/nginx/ronglianweb_error.log
```

### 服务状态检查
```bash
# 检查Nginx状态
sudo systemctl status nginx

# 检查端口占用
sudo netstat -tlnp | grep :80
sudo netstat -tlnp | grep :8080

# 检查进程
ps aux | grep nginx
ps aux | grep node
```

## 优势

### 1. 性能优势
- 静态文件缓存
- Gzip压缩
- 连接池管理
- 更好的并发处理

### 2. 安全优势
- 隐藏后端服务器信息
- 安全头设置
- 请求过滤
- DDoS防护

### 3. 运维优势
- 无需root权限运行Node.js
- 零停机部署
- 负载均衡支持
- SSL终端

## 故障排除

### 常见问题

1. **80端口被占用**
```bash
sudo lsof -i :80
sudo systemctl stop apache2  # 如果安装了Apache
```

2. **Nginx配置错误**
```bash
sudo nginx -t  # 测试配置
sudo systemctl reload nginx  # 重新加载配置
```

3. **Node.js应用无法访问**
```bash
curl http://localhost:8080  # 直接测试Node.js
```

4. **权限问题**
```bash
sudo chown -R www-data:www-data /var/log/nginx/
```

## 扩展配置

### 添加SSL支持
```nginx
server {
    listen 443 ssl;
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    # 其他配置...
}
```

### 负载均衡
```nginx
upstream backend {
    server localhost:8080;
    server localhost:8081;
    server localhost:8082;
}

server {
    location / {
        proxy_pass http://backend;
    }
}
```

## 总结

✅ **Nginx反向代理已成功配置！**

- **访问地址**: `http://localhost`
- **后端服务**: Node.js运行在8080端口
- **前端代理**: Nginx监听80端口
- **启动命令**: `./start-with-nginx.sh`
- **停止命令**: `./stop-services.sh`

这种配置方式是生产环境的最佳实践，提供了更好的性能、安全性和可维护性。
