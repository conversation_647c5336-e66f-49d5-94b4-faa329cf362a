doctype html
html
    head
        meta(charset='utf-8')
        meta(http-equiv='X-UA-Compatible', content='IE=edge')
        meta(name='viewport', content='width=device-width, initial-scale=1')
        title 荣联科技 - Word兼容编辑器测试
        link(rel='stylesheet', href='/plugins/bootstrap/css/bootstrap.min.css')
        link(rel='stylesheet', href='/plugins/admin/css/admin.css')
        style.
            .container { margin-top: 20px; }
            .tox-tinymce {
                border: 1px solid #ddd !important;
                border-radius: 4px !important;
            }
            .tox .tox-toolbar {
                background: #f8f9fa !important;
            }
            .word-actions {
                margin-bottom: 15px;
                padding: 15px;
                background: #f0f8ff;
                border: 1px solid #d1ecf1;
                border-radius: 4px;
            }
            .word-actions .btn {
                margin-right: 10px;
            }
            .test-info {
                background: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 4px;
                padding: 15px;
                margin-bottom: 20px;
            }
    body
        .container
            .row
                .col-md-12
                    .test-info
                        h4
                            i.glyphicon.glyphicon-info-sign
                            |  荣联科技 - Word兼容编辑器测试页面
                        p 这个页面用于测试新的Word兼容富文本编辑器功能：
                        ul
                            li ✅ 导入Word文档 (.docx格式)
                            li ✅ 保持Word文档格式和样式
                            li ✅ 在线编辑功能
                            li ✅ 导出为Word文档
                            li ✅ 图片上传和处理
                    
                    .panel.panel-default
                        .panel-heading
                            h3.panel-title
                                i.glyphicon.glyphicon-edit
                                |  荣联科技 - Word兼容编辑器演示
                        .panel-body
                            form#testForm
                                .form-group
                                    label.control-label(for="title") 文档标题
                                    input.form-control#title(type="text", placeholder="请输入文档标题", value="荣联科技Word兼容测试文档")
                                
                                .form-group
                                    label.control-label(for="content") 文档内容
                                    
                                    // Word导入/导出功能区
                                    .word-actions
                                        button.btn.btn-info.btn-sm#importWordBtn(type="button")
                                            i.glyphicon.glyphicon-import
                                            |  导入Word文档
                                        button.btn.btn-success.btn-sm#exportWordBtn(type="button")
                                            i.glyphicon.glyphicon-export
                                            |  导出为Word
                                        button.btn.btn-warning.btn-sm#clearBtn(type="button")
                                            i.glyphicon.glyphicon-trash
                                            |  清空内容
                                        input#wordFileInput(type="file", accept=".doc,.docx", style="display: none;")
                                        br
                                        small.text-muted 
                                            strong 使用说明：
                                            | 支持导入.docx格式文件，保持原有格式和样式。编辑完成后可导出为Word文档。
                                    
                                    // 编辑器容器
                                    #editor(style="height: 400px;")
                                    textarea#content(name="content", style="display: none;")
                                
                                .form-group
                                    .btn-group
                                        button.btn.btn-primary#saveBtn(type="button")
                                            i.glyphicon.glyphicon-floppy-disk
                                            |  保存内容
                                        button.btn.btn-default#previewBtn(type="button")
                                            i.glyphicon.glyphicon-eye-open
                                            |  预览
                                        button.btn.btn-info#getContentBtn(type="button")
                                            i.glyphicon.glyphicon-list-alt
                                            |  获取HTML
                    
                    // 预览区域
                    .panel.panel-info#previewPanel(style="display: none;")
                        .panel-heading
                            h4.panel-title
                                i.glyphicon.glyphicon-eye-open
                                |  内容预览
                        .panel-body#previewContent
                    
                    // HTML内容显示区域
                    .panel.panel-warning#htmlPanel(style="display: none;")
                        .panel-heading
                            h4.panel-title
                                i.glyphicon.glyphicon-code
                                |  HTML源码
                        .panel-body
                            pre#htmlContent(style="max-height: 300px; overflow-y: auto;")

        // 基础库
        script(src='/plugins/jquery/jquery-1.11.3.js')
        script(src='/plugins/bootstrap/js/bootstrap.min.js')
        
        // TinyMCE编辑器
        script(src='https://cdn.jsdelivr.net/npm/tinymce@6/tinymce.min.js')
        
        // Word处理库
        script(src='https://cdn.jsdelivr.net/npm/mammoth@1.6.0/mammoth.browser.min.js')
        script(src='https://cdn.jsdelivr.net/npm/html-docx-js@0.3.1/dist/html-docx.js')
        
        // 测试脚本
        script.
            $(document).ready(function() {
                let editor;
                
                // 初始化TinyMCE编辑器
                tinymce.init({
                    selector: '#editor',
                    height: 400,
                    language: 'zh_CN',
                    plugins: [
                        'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                        'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                        'insertdatetime', 'media', 'table', 'help', 'wordcount', 'paste'
                    ],
                    toolbar: [
                        'undo redo | blocks | bold italic underline strikethrough | alignleft aligncenter alignright alignjustify',
                        'bullist numlist outdent indent | removeformat | table link image media | code preview fullscreen'
                    ].join(' | '),
                    paste_data_images: true,
                    paste_word_valid_elements: "b,strong,i,em,h1,h2,h3,h4,h5,h6,p,ol,ul,li,a[href],span,color,font-size,font-family,mark,table,tr,td,th",
                    paste_retain_style_properties: "all",
                    paste_merge_formats: true,
                    automatic_uploads: false,
                    content_style: 'body { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 14px; line-height: 1.6; }',
                    setup: function(ed) {
                        editor = ed;
                        ed.on('change', function() {
                            $('#content').val(ed.getContent());
                        });
                        
                        // 编辑器加载完成后设置默认内容
                        ed.on('init', function() {
                            ed.setContent(`
                                <h1>荣联科技Word兼容编辑器测试</h1>
                                <p>这是一个支持MS Office Word格式的在线编辑器。</p>
                                <h2>主要功能</h2>
                                <ul>
                                    <li><strong>Word文档导入</strong> - 支持.docx格式文件导入</li>
                                    <li><strong>格式保持</strong> - 保留Word文档的样式和布局</li>
                                    <li><strong>在线编辑</strong> - 浏览器内直接编辑</li>
                                    <li><strong>Word导出</strong> - 导出为.docx格式文件</li>
                                </ul>
                                <h2>测试表格</h2>
                                <table border="1" style="border-collapse: collapse; width: 100%;">
                                    <tr>
                                        <th style="background-color: #f2f2f2; padding: 8px;">功能</th>
                                        <th style="background-color: #f2f2f2; padding: 8px;">状态</th>
                                        <th style="background-color: #f2f2f2; padding: 8px;">说明</th>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px;">Word导入</td>
                                        <td style="padding: 8px; color: green;">✅ 支持</td>
                                        <td style="padding: 8px;">支持.docx格式</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px;">Word导出</td>
                                        <td style="padding: 8px; color: green;">✅ 支持</td>
                                        <td style="padding: 8px;">导出为.docx格式</td>
                                    </tr>
                                </table>
                                <p><em>请尝试导入Word文档或编辑内容，然后导出测试！</em></p>
                            `);
                        });
                    }
                });
                
                // Word导入功能
                $('#importWordBtn').on('click', function() {
                    $('#wordFileInput').click();
                });
                
                $('#wordFileInput').on('change', function() {
                    const file = this.files[0];
                    if (file) {
                        importWordDocument(file);
                    }
                });
                
                // Word导出功能
                $('#exportWordBtn').on('click', function() {
                    exportToWord();
                });
                
                // 清空内容
                $('#clearBtn').on('click', function() {
                    if (confirm('确定要清空所有内容吗？')) {
                        editor.setContent('');
                    }
                });
                
                // 保存内容
                $('#saveBtn').on('click', function() {
                    const content = editor.getContent();
                    localStorage.setItem('wordEditorContent', content);
                    showMessage('内容已保存到本地存储', 'success');
                });
                
                // 预览
                $('#previewBtn').on('click', function() {
                    const content = editor.getContent();
                    const title = $('#title').val();
                    $('#previewContent').html('<h2>' + title + '</h2>' + content);
                    $('#previewPanel').show();
                });
                
                // 获取HTML
                $('#getContentBtn').on('click', function() {
                    const content = editor.getContent();
                    $('#htmlContent').text(content);
                    $('#htmlPanel').show();
                });
                
                // Word文档导入
                function importWordDocument(file) {
                    showMessage('正在导入Word文档，请稍候...', 'info');
                    
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const arrayBuffer = e.target.result;
                        
                        mammoth.convertToHtml({arrayBuffer: arrayBuffer})
                            .then(function(result) {
                                const html = result.value;
                                editor.setContent(html);
                                
                                if (result.messages.length > 0) {
                                    console.log('Word导入消息:', result.messages);
                                }
                                
                                showMessage('Word文档导入成功！', 'success');
                            })
                            .catch(function(error) {
                                console.error('Word导入错误:', error);
                                showMessage('Word文档导入失败：' + error.message, 'error');
                            });
                    };
                    
                    reader.readAsArrayBuffer(file);
                }
                
                // Word文档导出
                function exportToWord() {
                    const content = editor.getContent();
                    const title = $('#title').val() || 'Word兼容测试文档';
                    
                    const htmlContent = `
                        <!DOCTYPE html>
                        <html>
                        <head>
                            <meta charset="utf-8">
                            <title>${title}</title>
                            <style>
                                body { font-family: "Microsoft YaHei", Arial, sans-serif; line-height: 1.6; margin: 40px; }
                                h1, h2, h3, h4, h5, h6 { color: #333; margin: 20px 0 10px 0; }
                                p { margin: 10px 0; }
                                table { border-collapse: collapse; width: 100%; margin: 10px 0; }
                                table, th, td { border: 1px solid #ddd; }
                                th, td { padding: 8px; text-align: left; }
                                th { background-color: #f2f2f2; }
                                img { max-width: 100%; height: auto; }
                            </style>
                        </head>
                        <body>
                            ${content}
                        </body>
                        </html>
                    `;
                    
                    try {
                        const converted = htmlDocx.asBlob(htmlContent);
                        const link = document.createElement('a');
                        link.href = URL.createObjectURL(converted);
                        link.download = `${title}.docx`;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        
                        showMessage('Word文档导出成功！', 'success');
                    } catch (error) {
                        console.error('Word导出错误:', error);
                        showMessage('Word文档导出失败：' + error.message, 'error');
                    }
                }
                
                // 显示消息
                function showMessage(message, type) {
                    let alertClass = 'alert-info';
                    if (type === 'success') alertClass = 'alert-success';
                    if (type === 'error') alertClass = 'alert-danger';
                    
                    const alertHtml = `
                        <div class="alert ${alertClass} alert-dismissible" role="alert">
                            <button type="button" class="close" data-dismiss="alert">
                                <span>&times;</span>
                            </button>
                            ${message}
                        </div>
                    `;
                    
                    $('.alert').remove();
                    $('.container').prepend(alertHtml);
                    
                    setTimeout(function() {
                        $('.alert').fadeOut();
                    }, 3000);
                }
                
                // 加载保存的内容
                const savedContent = localStorage.getItem('wordEditorContent');
                if (savedContent) {
                    setTimeout(function() {
                        if (confirm('发现本地保存的内容，是否加载？')) {
                            editor.setContent(savedContent);
                        }
                    }, 1000);
                }
            });
