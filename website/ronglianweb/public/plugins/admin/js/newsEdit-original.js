// 新闻编辑JavaScript
$(document).ready(function() {
    let quill;
    let isEditing = !!window.newsId;

    // 初始化
    init();

    function init() {
        initEditor();
        bindEvents();
        
        if (isEditing) {
            loadNewsData();
        }
    }

    // 初始化富文本编辑器
    function initEditor() {
        quill = new Quill('#editor', {
            theme: 'snow',
            placeholder: '请输入新闻内容...',
            modules: {
                toolbar: [
                    [{ 'header': [1, 2, 3, false] }],
                    ['bold', 'italic', 'underline', 'strike'],
                    [{ 'color': [] }, { 'background': [] }],
                    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                    [{ 'align': [] }],
                    ['link', 'image'],
                    ['clean']
                ]
            }
        });

        // 监听内容变化
        quill.on('text-change', function() {
            $('#content').val(quill.root.innerHTML);
        });
    }

    // 绑定事件
    function bindEvents() {
        // 图片上传
        $('#uploadArea').on('click', function() {
            $('#imageInput').click();
        });

        $('#imageInput').on('change', function() {
            const file = this.files[0];
            if (file) {
                uploadImage(file);
            }
        });

        // 拖拽上传
        $('#uploadArea').on('dragover', function(e) {
            e.preventDefault();
            $(this).addClass('dragover');
        });

        $('#uploadArea').on('dragleave', function(e) {
            e.preventDefault();
            $(this).removeClass('dragover');
        });

        $('#uploadArea').on('drop', function(e) {
            e.preventDefault();
            $(this).removeClass('dragover');
            
            const files = e.originalEvent.dataTransfer.files;
            if (files.length > 0) {
                uploadImage(files[0]);
            }
        });

        // 保存按钮
        $('#saveDraftBtn').on('click', function() {
            saveNews('draft');
        });

        $('#savePublishBtn').on('click', function() {
            saveNews('published');
        });

        // 删除按钮
        $('#deleteBtn').on('click', function() {
            $('#deleteModal').modal('show');
        });

        $('#confirmDelete').on('click', function() {
            deleteNews();
        });
    }

    // 加载新闻数据（编辑模式）
    function loadNewsData() {
        $.get(`/api/admin/news/${window.newsId}`)
            .done(function(response) {
                if (response.success) {
                    const news = response.data;
                    $('#title').val(news.title);
                    $('#status').val(news.status);
                    $('#author').val(news.author);
                    $('#picMgid').val(news.picMgid);
                    
                    // 设置编辑器内容
                    quill.root.innerHTML = news.content;
                    $('#content').val(news.content);
                    
                    // 显示图片预览
                    if (news.picMgid) {
                        showImagePreview(news.picMgid);
                    }
                } else {
                    showMessage('加载新闻数据失败: ' + response.message, 'error');
                }
            })
            .fail(function() {
                showMessage('加载新闻数据失败', 'error');
            });
    }

    // 上传图片
    function uploadImage(file) {
        // 验证文件类型
        if (!file.type.match(/^image\/(jpeg|jpg|png|gif|webp)$/)) {
            showMessage('请选择有效的图片文件', 'error');
            return;
        }

        // 验证文件大小
        if (file.size > 5 * 1024 * 1024) {
            showMessage('图片大小不能超过5MB', 'error');
            return;
        }

        const formData = new FormData();
        formData.append('image', file);

        // 显示上传进度
        showMessage('正在上传图片...', 'info');

        $.ajax({
            url: '/api/admin/news/upload',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false
        })
        .done(function(response) {
            if (response.success) {
                $('#picMgid').val(response.data.url);
                showImagePreview(response.data.url);
                showMessage('图片上传成功', 'success');
            } else {
                showMessage('图片上传失败: ' + response.message, 'error');
            }
        })
        .fail(function() {
            showMessage('图片上传失败', 'error');
        });
    }

    // 显示图片预览
    function showImagePreview(imageUrl) {
        const preview = `
            <img src="${imageUrl}" class="image-preview" alt="封面图片">
            <br>
            <button type="button" class="btn btn-sm btn-danger" onclick="removeImage()">
                <i class="glyphicon glyphicon-trash"></i> 删除图片
            </button>
        `;
        $('#imagePreview').html(preview);
    }

    // 删除图片
    window.removeImage = function() {
        $('#picMgid').val('');
        $('#imagePreview').empty();
        showMessage('图片已删除', 'info');
    };

    // 保存新闻
    function saveNews(status) {
        // 验证表单
        const title = $('#title').val().trim();
        if (!title) {
            showMessage('请输入新闻标题', 'error');
            $('#title').focus();
            return;
        }

        const content = quill.root.innerHTML.trim();
        if (!content || content === '<p><br></p>') {
            showMessage('请输入新闻内容', 'error');
            quill.focus();
            return;
        }

        // 准备数据
        const newsData = {
            title: title,
            content: content,
            status: status,
            author: $('#author').val() || 'admin',
            picMgid: $('#picMgid').val() || '/images/news/default.jpg'
        };

        // 显示保存进度
        const saveBtn = status === 'draft' ? $('#saveDraftBtn') : $('#savePublishBtn');
        const originalText = saveBtn.text();
        saveBtn.prop('disabled', true).text('保存中...');

        // 发送请求
        const url = isEditing ? `/api/admin/news/${window.newsId}` : '/api/admin/news/create';
        const method = isEditing ? 'PUT' : 'POST';

        $.ajax({
            url: url,
            type: method,
            data: JSON.stringify(newsData),
            contentType: 'application/json'
        })
        .done(function(response) {
            if (response.success) {
                showMessage('新闻保存成功', 'success');
                
                // 如果是新建，跳转到编辑页面
                if (!isEditing) {
                    setTimeout(function() {
                        window.location.href = `/admin/news/edit/${response.data.news_id}`;
                    }, 1000);
                }
            } else {
                showMessage('保存失败: ' + response.message, 'error');
            }
        })
        .fail(function() {
            showMessage('保存失败', 'error');
        })
        .always(function() {
            saveBtn.prop('disabled', false).text(originalText);
        });
    }

    // 删除新闻
    function deleteNews() {
        if (!isEditing) return;

        $.ajax({
            url: `/api/admin/news/${window.newsId}`,
            type: 'DELETE'
        })
        .done(function(response) {
            if (response.success) {
                showMessage('新闻删除成功', 'success');
                setTimeout(function() {
                    window.location.href = '/admin/news';
                }, 1000);
            } else {
                showMessage('删除失败: ' + response.message, 'error');
            }
        })
        .fail(function() {
            showMessage('删除失败', 'error');
        });
    }

    // 显示消息
    function showMessage(message, type = 'info') {
        const alertClass = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'info': 'alert-info'
        }[type] || 'alert-info';

        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible" role="alert">
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
                ${message}
            </div>
        `;
        
        // 移除现有的alert
        $('.alert').remove();
        // 在页面顶部添加新的alert
        $('.container-fluid').prepend(alertHtml);
        
        // 3秒后自动消失
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 3000);
    }
});
