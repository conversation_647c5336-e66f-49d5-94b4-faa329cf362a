Vue.component('news-footer', {
    template: `
        <div class="news-web-footer">
            <div class="news-web-footer-conent">
                 <!-- 公司logo -->
                <div class="col-xs-12 col-md-12" style="padding:0;margin-bottom: 10px">
                    <img :src="imgBaseUrl+'/logo_bai.svg'" />
                </div>
                <!-- 底部链接 -->
                <div class="col-xs-12 col-md-9" style="padding:0;">
                    <div v-for="(item,key) in links"  class="col-xs-3 col-md-2" :class="{'col-md-3':key==1}"  style="margin-top:15px;padding:0;">
                            <div class="col-xs-12 col-md-12 news-web-footer-title" style="padding:0;">
                                {{item.title}}
                            </div>
                            <div v-for="(link,index) in item.children" style="padding:0;" class="col-xs-12 col-md-12 news-web-footer-link" >
                                <a class="news-web-footer-link-a" :target="link.target?link.target:'_self'" :href="link.href">{{link.title}}</a>
                            </div>
                    </div>
                    <div class="col-xs-12 col-md-3" style="margin-top:15px;padding:0;">
                            <div style="padding:0;" class="col-xs-12 col-md-12 news-web-footer-title" >
                                {{concat.title}}
                            </div>
                            <div v-for="(link,index) in concat.children" style="padding:0;" class="col-xs-12 col-md-12 news-web-footer-link" >
                                <a v-if="link.href" class="news-web-footer-link-a" :href="link.href">{{link.title}}</a>
                                <span v-if="!link.href" style="color:#ccc">
                                {{link.title}}
                                </span>
                            </div>
                    </div>
                </div>
                <!-- 关注我们 -->
                <div class="col-xs-12 col-md-3" style="text-align:center;">
                    <div class="col-xs-12 col-md-12 news-web-footer-title" style="padding:0;">
                        关注我们
                        <div>
                            <img style="margin-top:20px;" :src="imgBaseUrl+'/erwaim.jpg'" />
                        </div>
                        <div>
                            <a target="_blank" href="http://e.weibo.com/ronglian" style="display: inline-block;cursor: pointer;"><img style="margin-top:20px;" :src="imgBaseUrl+'/xinlang.svg'" /></a>
                            <a target="_blank" href="http://www.linkedin.com/company/united-electronics-co.-ltd." style="display: inline-block;cursor: pointer;"><img style="margin-top:20px;margin-left:30px" :src="imgBaseUrl+'/linkedin.svg'" /></a>                        
                        </div>
                    </div>
                </div>
                <!-- 底部信息 -->
                <div class="col-xs-12 col-md-12" style="margin-top:10px;">
                    <div class="news-web-footer-bottom-message" style="text-align:center;">
                        <a class="news-web-footer-link-a-bottom" href="../index/informmail">举报信箱</a> <span>|</span><a class="news-web-footer-link-a-bottom" href="../index/legalprotection">法律保护</a>
                    </div>
                    <div class="news-web-footer-bottom-message" style="text-align:center;margin-top:15px;">
                        <span>北京荣联科技股份有限公司 版权所有</span>
                        <span>京ICP备14049550号</span>
                        <span>京公网安备11010802017957</span>
                    </div>
                </div>
            </div>
        </div>
    `,
    data: function () {
        return {
            links: [
                {
                    title: '关于我们',
                    children: [{
                        title: "公司介绍",
                        href: this.viewUrl + "../index/about#div1",
                    }, {
                        title: "历史沿革",
                        href: this.viewUrl + "../index/about#div6",
                    }, {
                        title: "企业文化",
                        href: this.viewUrl + "../index/about#div7",
                    }, {
                        title: "成员企业",
                        href: this.viewUrl + "../index/about#div8",
                    }, {
                        title: "企业资质",
                        href: this.viewUrl + "../index/about2#div2",
                    }]
                },
                {
                    title: '公司动态',
                    children: [{
                        title: "新闻中心",
                        href: this.viewUrl + "../news/index",
                    }, {
                        title: "社会责任",
                        href: this.viewUrl + "../index/about3#div3",
                    }, {
                        title: "企业招聘",
                        href: this.viewUrl + "../index/about4#div4",
                    }]
                }, {
                    title: '投资者关系',
                    children: [{
                        title: "股票与公告",
                        href: this.viewUrl + "investor/#div1",
                    }, {
                        title: "定期报告",
                        href: this.viewUrl + "investor/#div2",
                    }, {
                        title: "投资者服务",
                        href: this.viewUrl + "investor/#div4",
                    }]
                },
                {
                    title: '友情链接',
                    children: [{
                        title: "荣联生物云",
                        href:  "http://bio.ronglian.com",
                        target:'_blank'
                    }, {
                        title: "昊天旭辉",
                        href:  "http://www.risingsun-tech.cn",
                        target:'_blank'
                    }]
                }
            ],
            concat: {
                title: '联系我们',
                children: [{
                    title: "电话：010-62602000",
                    href: ''
                }, {
                    title: "邮箱：<EMAIL>",
                    href: ''
                }, {
                    title: "地址：北京市朝阳区酒仙桥北路甲10号院106号楼荣联科技大厦",
                    href: ''
                }, {
                    title: "更多联系方式",
                    href: this.viewUrl + "../contactUs/stage#div5",
                }]
            }
        }
    }
})