#!/bin/bash

# Word兼容编辑器健康检查脚本

echo "=== Word兼容编辑器健康检查 ==="
echo ""

# 检查Node.js应用状态
echo "🔍 检查Node.js应用状态..."
if pgrep -f "node.*bin/www" > /dev/null; then
    echo "✅ Node.js应用正在运行"
    NODE_STATUS="✅ 运行中"
    NODE_PID=$(pgrep -f "node.*bin/www")
    echo "   进程ID: $NODE_PID"
else
    echo "❌ Node.js应用未运行"
    NODE_STATUS="❌ 未运行"
fi

# 检查Nginx状态
echo "🔍 检查Nginx状态..."
if sudo systemctl is-active --quiet nginx; then
    echo "✅ Nginx正在运行"
    NGINX_STATUS="✅ 运行中"
else
    echo "❌ Nginx未运行"
    NGINX_STATUS="❌ 未运行"
fi

# 检查HTTPS主站访问
echo "🔍 检查HTTPS主站访问..."
if curl -s -I -k https://********* > /dev/null; then
    echo "✅ HTTPS主站可访问 (IP: *********)"
    HTTPS_STATUS="✅ 可访问"
else
    echo "❌ HTTPS主站不可访问 (IP: *********)"
    HTTPS_STATUS="❌ 不可访问"
fi

# 检查Word编辑器测试页面
echo "🔍 检查Word编辑器测试页面..."
if curl -s -I -k https://*********/word-editor-test > /dev/null; then
    echo "✅ Word编辑器测试页面正常 (IP: *********)"
    WORD_EDITOR_STATUS="✅ 正常"
else
    echo "❌ Word编辑器测试页面异常 (IP: *********)"
    WORD_EDITOR_STATUS="❌ 异常"
fi

# 检查新闻管理页面
echo "🔍 检查新闻管理页面..."
if curl -s -I -k https://*********/admin/login > /dev/null; then
    echo "✅ 新闻管理登录页面正常"
    ADMIN_STATUS="✅ 正常"
else
    echo "❌ 新闻管理登录页面异常"
    ADMIN_STATUS="❌ 异常"
fi

# 检查Vue 3页面
echo "🔍 检查Vue 3页面..."
if curl -s -I -k https://*********/vue3-demo > /dev/null; then
    echo "✅ Vue 3演示页面正常"
    VUE3_STATUS="✅ 正常"
else
    echo "❌ Vue 3演示页面异常"
    VUE3_STATUS="❌ 异常"
fi

# 显示总结
echo ""
echo "📊 健康检查总结:"
echo "   Node.js应用:   $NODE_STATUS"
echo "   Nginx服务:     $NGINX_STATUS"
echo "   HTTPS主站:     $HTTPS_STATUS"
echo "   Word编辑器:    $WORD_EDITOR_STATUS"
echo "   新闻管理:      $ADMIN_STATUS"
echo "   Vue 3页面:     $VUE3_STATUS"
echo ""

# 显示访问地址
echo "🌐 访问地址:"
echo "   🔒 主站 (HTTPS):    https://*********"
echo "   📰 新闻页面:        https://*********/news/index"
echo "   🔐 管理登录:        https://*********/admin/login"
echo "   📝 Word编辑器:      https://*********/word-editor-test"
echo "   🎯 Vue 3演示:       https://*********/vue3-demo"
echo "   🧪 Vue 3测试:       https://*********/vue3-test"
echo ""

# 显示Word编辑器功能
echo "📝 Word编辑器功能:"
echo "   ✅ Word文档导入 (.docx格式)"
echo "   ✅ Word文档导出 (.docx格式)"
echo "   ✅ 格式保持 (样式、表格、图片)"
echo "   ✅ 在线编辑 (TinyMCE 6.x)"
echo "   ✅ 图片上传和处理"
echo "   ✅ 表格编辑功能"
echo ""

# 显示技术栈
echo "🔧 技术栈信息:"
echo "   前端框架:     Vue 3.3.4 + TinyMCE 6.x"
echo "   后端框架:     Node.js + Express"
echo "   模板引擎:     Jade/Pug"
echo "   Web服务器:    Nginx (反向代理)"
echo "   Word处理:     Mammoth.js + html-docx-js"
echo ""

# 显示日志命令
echo "📝 查看日志:"
echo "   Nginx访问日志: sudo tail -f /var/log/nginx/access.log"
echo "   Nginx错误日志: sudo tail -f /var/log/nginx/error.log"
echo "   应用日志:      tail -f logs/app.log"
echo ""

# 显示管理命令
echo "🛠️  管理命令:"
echo "   启动应用:      ./run.sh start"
echo "   停止应用:      ./run.sh stop"
echo "   重启应用:      ./run.sh restart"
echo "   查看状态:      ./run.sh status"
echo ""

# 检查是否所有服务都正常
if [[ "$NODE_STATUS" == *"✅"* ]] && [[ "$NGINX_STATUS" == *"✅"* ]] && [[ "$HTTPS_STATUS" == *"✅"* ]] && [[ "$WORD_EDITOR_STATUS" == *"✅"* ]]; then
    echo "🎉 所有服务运行正常！Word兼容编辑器已就绪！"
    exit 0
else
    echo "⚠️  部分服务存在问题，请检查上述状态"
    echo ""
    echo "🔧 故障排除建议:"
    
    if [[ "$NODE_STATUS" == *"❌"* ]]; then
        echo "   - Node.js应用未运行: 执行 ./run.sh start"
    fi
    
    if [[ "$NGINX_STATUS" == *"❌"* ]]; then
        echo "   - Nginx未运行: 执行 sudo systemctl start nginx"
    fi
    
    if [[ "$HTTPS_STATUS" == *"❌"* ]]; then
        echo "   - HTTPS访问异常: 检查SSL证书和Nginx配置"
    fi
    
    if [[ "$WORD_EDITOR_STATUS" == *"❌"* ]]; then
        echo "   - Word编辑器异常: 检查路由配置和模板文件"
    fi
    
    exit 1
fi
