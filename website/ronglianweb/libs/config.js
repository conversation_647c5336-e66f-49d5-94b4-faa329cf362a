//所有的配置，都需要写在这个文件中，如果需要用zookeeper进行集中化配置，将此文件的逻辑重新实现
/**
 * [config description]
 * @type {Object}
 */
var config = {
    RetailerService: {
        host: '**********',// //*********
        port: 8101,
        context: '',
        encrypt: true,//数据是否加密
        subsystemName: 'shopping',//系统名字
        subsystemKey: '2cfca5a66e8e409ab85d2f400b2d4e2a',//系统密钥
        retailersId: "255fa8683f6d4693a2b8d616b240a93e"//商户编号
    },
    ImageService: {
        host: '**********',
        port: 8103,
        context: '',
        encrypt: true,//数据是否加密
        subsystemName: 'shopping',//系统名字
        subsystemKey: '2cfca5a66e8e409ab85d2f400b2d4e2a',//系统密钥
        retailersId: "255fa8683f6d4693a2b8d616b240a93e",//商户编号
        domain:"http://im1.ronglian.com:8103"
    },
    LogService: {
        host: '**********',
        port: 8092,
        context: '',
        encrypt: true,//数据是否加密
        subsystemName: 'shopping',//系统名字
        subsystemKey: '2cfca5a66e8e409ab85d2f400b2d4e2a',//系统密钥
        retailersId: "255fa8683f6d4693a2b8d616b240a93e",//商户编号
        system: 'retailer'
    },
    DistrictService: {
        host: '**********',
        port: 8091,
        context: '',
        encrypt: true,//数据是否加密
        subsystemName: 'shopping',//系统名字
        subsystemKey: '2cfca5a66e8e409ab85d2f400b2d4e2a',//系统密钥
        retailersId: "255fa8683f6d4693a2b8d616b240a93e",//商户编号
        superId: '4cd36dbcc8b011e584680050569b1c58'//国家id
    },
    EmailService: {
        host: '**********',
        port: 8093,
        context: '',
        encrypt: true,//数据是否加密
        subsystemName: 'retailersnode',
        subsystemKey: 'erewfewfwe',
        retailersId: "11"//商户编号
    },
    AlipayService: {
        url: 'https://mapi.alipay.com/gateway.do?',//支付宝接口url
        payment_type: '1',
        _input_charset: 'UTF-8',
        service: 'create_direct_pay_by_user',
        //服务器异步通知页面路径,需http://格式的完整路径，不能加?id=123这类自定义参数
        notify_url: 'http://shopping.ronglian.com:8080/buy/ar',//支付宝异步返回接口url
        //页面跳转同步通知页面路径,需http://格式的完整路径，不能加?id=123这类自定义参数，不能写成http://localhost/
        return_url: 'http://shopping.ronglian.com:8080/buy/r',//支付宝同步返回接口url
        show_url: 'https://mail.ronglian.com',//商品展示的url,非必须
        verify_url: 'https://mapi.alipay.com/gateway.do?service=notify_verify&'//支付宝消息验证地址
    },
    WeChatService:{
        prePayUrl:'https://api.mch.weixin.qq.com/pay/unifiedorder',//预支付接口url
        notify_url:'http://weixin.bio.ronglian.com/buy/wr'//回调url
    },
    smsService:{//阿里短信平台
        appkey:'23440508',
        appsecret:'e16b1ffe621c5cc9ef585f3d4f766547',
        sms_free_sign_name: '荣联科技生物云',//头部签名
        verify_template_code: 'SMS_13890355',//验证码，模板ID
        retailersId:'255fa8683f6d4693a2b8d616b240a93e'//商户ID
    },
    redis: {
        port: '6379',
        ip: '**********',
        password:'Uec62602000!@#$',
        timeOut: '1296000' //单位秒
    },
    mongodb: {
        db: 'retailers',
        host: '**********',
        port:27017,
        dbRoot:'file',
        url:'mongodb://**********:27017/retailers',
        encoding:'utf-8'
    }

};
config.SHOPPINGURL=(process.env.SHOPPING_URL || "http://test1.ronglian.com");
//商户
config.RetailerService.host = (process.env.RetailerService_SERVICE_HOST || config.RetailerService.host);
config.RetailerService.port = (process.env.RetailerService_SERVICE_PORT || config.RetailerService.port);
config.RetailerService.encrypt = (process.env.RetailerService_SERVICE_ENCRYPT || config.RetailerService.port);
config.RetailerService.subsystem_name = (process.env.RetailerService_SERVICE_SUBSYSTEM_NAME || config.RetailerService.subsystem_name);
config.RetailerService.subsystem_key = (process.env.RetailerService_SERVICE_SUBSYSTEM_KEY || config.RetailerService.subsystem_key);
config.RetailerService.retailersId = (process.env.RetailerService_SERVICE_RETAILERSID || config.RetailerService.retailersId);

//图片
config.ImageService.host = (process.env.ImageService_SERVICE_HOST || config.ImageService.host);
config.ImageService.port = (process.env.ImageService_SERVICE_PORT || config.ImageService.port);
config.ImageService.encrypt = (process.env.ImageService_SERVICE_ENCRYPT || config.ImageService.port);
config.ImageService.subsystem_name = (process.env.ImageService_SERVICE_SUBSYSTEM_NAME || config.ImageService.subsystem_name);
config.ImageService.subsystem_key = (process.env.ImageService_SERVICE_SUBSYSTEM_KEY || config.ImageService.subsystem_key);
config.ImageService.retailersId = (process.env.ImageService_SERVICE_RETAILERSID || config.ImageService.retailersId);

//log
config.LogService.host = (process.env.LogService_SERVICE_HOST || config.LogService.host);
config.LogService.port = (process.env.LogService_SERVICE_PORT || config.LogService.port);
config.LogService.encrypt = (process.env.LogService_SERVICE_ENCRYPT || config.LogService.port);
config.LogService.subsystem_name = (process.env.LogService_SERVICE_SUBSYSTEM_NAME || config.LogService.subsystem_name);
config.LogService.subsystem_key = (process.env.LogService_SERVICE_SUBSYSTEM_KEY || config.LogService.subsystem_key);
config.LogService.retailersId = (process.env.LogService_SERVICE_RETAILERSID || config.LogService.retailersId);


//地区
config.DistrictService.host = (process.env.DistrictService_SERVICE_HOST || config.DistrictService.host);
config.DistrictService.port = (process.env.DistrictService_SERVICE_PORT || config.DistrictService.port);
config.DistrictService.encrypt = (process.env.DistrictService_SERVICE_ENCRYPT || config.DistrictService.port);
config.DistrictService.subsystem_name = (process.env.DistrictService_SERVICE_SUBSYSTEM_NAME || config.DistrictService.subsystem_name);
config.DistrictService.subsystem_key = (process.env.DistrictService_SERVICE_SUBSYSTEM_KEY || config.DistrictService.subsystem_key);
config.DistrictService.retailersId = (process.env.DistrictService_SERVICE_RETAILERSID || config.DistrictService.retailersId);
config.DistrictService.superId = (process.env.DISTRICTSERVICE_SERVICE_SUPERID || config.DistrictService.superId);

//email
config.EmailService.host = (process.env.EmailService_SERVICE_HOST || config.EmailService.host);
config.EmailService.port = (process.env.EmailService_SERVICE_PORT || config.EmailService.port);
config.EmailService.encrypt = (process.env.EmailService_SERVICE_ENCRYPT || config.EmailService.port);
config.EmailService.subsystem_name = (process.env.EmailService_SERVICE_SUBSYSTEM_NAME || config.EmailService.subsystem_name);
config.EmailService.subsystem_key = (process.env.EmailService_SERVICE_SUBSYSTEM_KEY || config.EmailService.subsystem_key);
config.EmailService.retailersId = (process.env.EmailService_SERVICE_RETAILERSID || config.EmailService.retailersId);


//redis
config.redis.port = (process.env.REDIS_PORT || config.redis.port);
config.redis.ip = (process.env.REDIS_IP || config.redis.ip);
config.redis.timeOut = (process.env.REDIS_TIMEOUT || config.redis.timeOut);
config.redis.password = (process.env.REDIS_PASSWORD || config.redis.password);
//mongodb
config.mongodb.db = (process.env.MONGODB_DB || config.mongodb.db);
config.mongodb.host = (process.env.MONGODB_HOST || config.mongodb.host);
config.mongodb.port = (process.env.MONGODB_PORT || config.mongodb.port);
config.mongodb.dbRoot = (process.env.MONGODB_DBROOT || config.mongodb.dbRoot);
config.mongodb.url = (process.env.MONGODB_URL || config.mongodb.url);
config.mongodb.encoding = (process.env.MONGODB_ENCODING || config.mongodb.encoding);
//支付宝
config.AlipayService.url = (process.env.ALIPAYSERVICE_URL || config.AlipayService.url);
config.AlipayService.payment_type = (process.env.ALIPAYSERVICE_PAYMENT_TYPE || config.AlipayService.payment_type);
config.AlipayService._input_charset = (process.env.ALIPAYSERVICE_INPUT_CHARSET || config.AlipayService._input_charset);
config.AlipayService.notify_url = (process.env.ALIPAYSERVICE_NOTIFY_URL || config.AlipayService.notify_url);
config.AlipayService.return_url = (process.env.ALIPAYSERVICE_RETURN_URL || config.AlipayService.return_url);
config.AlipayService.show_url = (process.env.ALIPAYSERVICE_SHOW_URL || config.AlipayService.show_url);
config.AlipayService.verify_url = (process.env.ALIPAYSERVICE_VERIFY_URL || config.AlipayService.verify_url);


//微信WeChatService
config.WeChatService.notify_url = (process.env.WeChatService_NOTIFY_URL || config.WeChatService.notify_url);

module.exports = config;
