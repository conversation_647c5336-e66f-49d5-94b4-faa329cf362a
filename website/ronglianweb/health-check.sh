#!/bin/bash

# 健康检查脚本

echo "=== 荣联Web应用健康检查 ==="
echo ""

# 检查Nginx状态
echo "🔍 检查Nginx状态..."
if sudo systemctl is-active --quiet nginx; then
    echo "✅ Nginx正在运行"
    NGINX_STATUS="✅ 运行中"
else
    echo "❌ Nginx未运行"
    NGINX_STATUS="❌ 未运行"
fi

# 检查Node.js应用状态
echo "🔍 检查Node.js应用状态..."
if pgrep -f "node.*bin/www" > /dev/null; then
    echo "✅ Node.js应用正在运行"
    NODE_STATUS="✅ 运行中"
    NODE_PID=$(pgrep -f "node.*bin/www")
    echo "   进程ID: $NODE_PID"
else
    echo "❌ Node.js应用未运行"
    NODE_STATUS="❌ 未运行"
fi

# 检查端口状态
echo "🔍 检查端口状态..."

# 检查80端口
if curl -s -I http://localhost > /dev/null; then
    echo "✅ 80端口可访问"
    PORT_80_STATUS="✅ 可访问"
else
    echo "❌ 80端口不可访问"
    PORT_80_STATUS="❌ 不可访问"
fi

# 检查8080端口
if curl -s -I http://localhost:8080 > /dev/null; then
    echo "✅ 8080端口可访问"
    PORT_8080_STATUS="✅ 可访问"
else
    echo "❌ 8080端口不可访问"
    PORT_8080_STATUS="❌ 不可访问"
fi

# 检查Vue 3页面
echo "🔍 检查Vue 3页面..."
if curl -s http://localhost/vue3-demo | grep -q "Vue 3"; then
    echo "✅ Vue 3演示页面正常"
    VUE3_STATUS="✅ 正常"
else
    echo "❌ Vue 3演示页面异常"
    VUE3_STATUS="❌ 异常"
fi

# 显示总结
echo ""
echo "📊 健康检查总结:"
echo "   Nginx:        $NGINX_STATUS"
echo "   Node.js:      $NODE_STATUS"
echo "   80端口:       $PORT_80_STATUS"
echo "   8080端口:     $PORT_8080_STATUS"
echo "   Vue 3页面:    $VUE3_STATUS"
echo ""

# 显示访问地址
echo "🌐 访问地址:"
echo "   主页:         http://localhost"
echo "   Vue 3演示:    http://localhost/vue3-demo"
echo "   Vue 3测试:    http://localhost/vue3-test"
echo "   直接访问:     http://localhost:8080"
echo ""

# 显示日志命令
echo "📝 查看日志:"
echo "   Nginx访问日志: sudo tail -f /var/log/nginx/ronglianweb_access.log"
echo "   Nginx错误日志: sudo tail -f /var/log/nginx/ronglianweb_error.log"
echo "   Node.js日志:   查看终端输出"
echo ""

# 检查是否所有服务都正常
if [[ "$NGINX_STATUS" == *"✅"* ]] && [[ "$NODE_STATUS" == *"✅"* ]] && [[ "$PORT_80_STATUS" == *"✅"* ]] && [[ "$PORT_8080_STATUS" == *"✅"* ]]; then
    echo "🎉 所有服务运行正常！"
    exit 0
else
    echo "⚠️  部分服务存在问题，请检查上述状态"
    exit 1
fi
