<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>新闻编辑功能测试</title>
    <link rel="stylesheet" href="/plugins/bootstrap/css/bootstrap.min.css">
    <style>
        .container { margin-top: 20px; }
        .test-section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 4px; }
        .test-result { margin-top: 10px; padding: 10px; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>新闻编辑功能测试</h1>
        
        <div class="test-section">
            <h3>1. 测试新闻API连接</h3>
            <button class="btn btn-primary" onclick="testNewsAPI()">测试API连接</button>
            <div id="apiResult" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 测试新闻列表获取</h3>
            <button class="btn btn-primary" onclick="testNewsList()">获取新闻列表</button>
            <div id="listResult" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 测试新闻详情获取</h3>
            <input type="text" id="newsIdInput" placeholder="输入新闻ID" class="form-control" style="width: 200px; display: inline-block;">
            <button class="btn btn-primary" onclick="testNewsDetail()">获取新闻详情</button>
            <div id="detailResult" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>4. 测试编辑器初始化</h3>
            <button class="btn btn-primary" onclick="testEditorInit()">初始化编辑器</button>
            <div id="editorResult" class="test-result"></div>
            <div id="testEditor" style="height: 200px; margin-top: 10px; border: 1px solid #ddd;"></div>
        </div>
        
        <div class="test-section">
            <h3>5. 快速访问链接</h3>
            <div class="btn-group">
                <a href="https://*********/admin/login" class="btn btn-info" target="_blank">管理员登录</a>
                <a href="https://*********/admin/news" class="btn btn-info" target="_blank">新闻管理</a>
                <a href="https://*********/admin/news/edit" class="btn btn-info" target="_blank">创建新闻</a>
                <a href="https://*********/word-editor-test" class="btn btn-info" target="_blank">Word编辑器测试</a>
            </div>
        </div>
    </div>

    <script src="/plugins/jquery/jquery-1.11.3.js"></script>
    <script src="/plugins/bootstrap/js/bootstrap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/tinymce@6/tinymce.min.js"></script>
    
    <script>
        let testEditor;
        
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.className = 'test-result ' + type;
            element.innerHTML = message;
        }
        
        function testNewsAPI() {
            showResult('apiResult', '正在测试API连接...', 'info');
            
            $.ajax({
                url: '/api/admin/news/list',
                type: 'GET',
                success: function(response) {
                    if (response.success) {
                        showResult('apiResult', '✅ API连接成功！返回数据: ' + JSON.stringify(response, null, 2), 'success');
                    } else {
                        showResult('apiResult', '❌ API返回错误: ' + response.message, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    showResult('apiResult', '❌ API连接失败: ' + error + ' (状态码: ' + xhr.status + ')', 'error');
                }
            });
        }
        
        function testNewsList() {
            showResult('listResult', '正在获取新闻列表...', 'info');
            
            $.ajax({
                url: '/api/admin/news/list',
                type: 'GET',
                data: { pageNo: 1, pageSize: 5 },
                success: function(response) {
                    if (response.success && response.data && response.data.records) {
                        const news = response.data.records;
                        let html = '✅ 获取到 ' + news.length + ' 条新闻:<br>';
                        news.forEach(function(item, index) {
                            html += `${index + 1}. ID: ${item.id}, 标题: ${item.title}, 状态: ${item.status}<br>`;
                        });
                        showResult('listResult', html, 'success');
                        
                        // 自动填充第一个新闻ID到测试框
                        if (news.length > 0) {
                            document.getElementById('newsIdInput').value = news[0].id;
                        }
                    } else {
                        showResult('listResult', '❌ 获取新闻列表失败: ' + (response.message || '未知错误'), 'error');
                    }
                },
                error: function(xhr, status, error) {
                    showResult('listResult', '❌ 请求失败: ' + error, 'error');
                }
            });
        }
        
        function testNewsDetail() {
            const newsId = document.getElementById('newsIdInput').value;
            if (!newsId) {
                showResult('detailResult', '❌ 请输入新闻ID', 'error');
                return;
            }
            
            showResult('detailResult', '正在获取新闻详情...', 'info');
            
            $.ajax({
                url: `/api/admin/news/${newsId}`,
                type: 'GET',
                success: function(response) {
                    if (response.success) {
                        const news = response.data;
                        let html = '✅ 获取新闻详情成功:<br>';
                        html += `标题: ${news.title}<br>`;
                        html += `作者: ${news.author}<br>`;
                        html += `状态: ${news.status}<br>`;
                        html += `内容长度: ${(news.content || '').length} 字符<br>`;
                        html += `创建时间: ${news.createTime}<br>`;
                        showResult('detailResult', html, 'success');
                    } else {
                        showResult('detailResult', '❌ 获取新闻详情失败: ' + response.message, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    showResult('detailResult', '❌ 请求失败: ' + error + ' (状态码: ' + xhr.status + ')', 'error');
                }
            });
        }
        
        function testEditorInit() {
            showResult('editorResult', '正在初始化TinyMCE编辑器...', 'info');
            
            if (testEditor) {
                testEditor.remove();
            }
            
            tinymce.init({
                selector: '#testEditor',
                height: 200,
                language: 'zh_CN',
                plugins: [
                    'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                    'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                    'insertdatetime', 'media', 'table', 'help', 'wordcount', 'paste'
                ],
                toolbar: [
                    'undo redo | blocks | bold italic underline strikethrough',
                    'alignleft aligncenter alignright alignjustify | bullist numlist outdent indent'
                ].join(' | '),
                setup: function(ed) {
                    testEditor = ed;
                    ed.on('init', function() {
                        showResult('editorResult', '✅ TinyMCE编辑器初始化成功！', 'success');
                        ed.setContent('<p>这是一个测试内容，编辑器工作正常！</p>');
                    });
                    ed.on('change', function() {
                        console.log('编辑器内容已更改');
                    });
                }
            });
        }
        
        // 页面加载完成后自动测试API
        $(document).ready(function() {
            setTimeout(function() {
                testNewsAPI();
            }, 1000);
        });
    </script>
</body>
</html>
