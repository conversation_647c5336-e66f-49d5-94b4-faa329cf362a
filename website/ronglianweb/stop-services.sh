#!/bin/bash

# 停止服务脚本

echo "=== 停止荣联Web应用服务 ==="
echo ""

# 停止Node.js进程
echo "🛑 停止Node.js应用..."
pkill -f "node.*bin/www" && echo "✅ Node.js应用已停止" || echo "ℹ️  Node.js应用未运行"

# 可选：停止Nginx (通常保持运行)
read -p "是否停止Nginx服务? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🛑 停止Nginx..."
    sudo systemctl stop nginx && echo "✅ Nginx已停止" || echo "❌ 停止Nginx失败"
else
    echo "ℹ️  Nginx保持运行状态"
fi

echo ""
echo "✅ 服务停止完成"
