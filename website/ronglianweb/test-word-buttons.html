<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Word按钮功能测试</title>
    <link rel="stylesheet" href="/plugins/bootstrap/css/bootstrap.min.css">
    <style>
        .container { margin-top: 20px; }
        .test-section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 4px; }
        .help-block .btn-xs {
            padding: 1px 5px;
            font-size: 11px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Word按钮功能测试</h1>
        
        <div class="test-section">
            <h3>1. 按钮HTML结构测试</h3>
            <div class="form-group">
                <label for="content">新闻内容 *</label>
                <div id="editor" style="height: 300px;"></div>
                <textarea id="content" name="content" style="display: none;"></textarea>
                
                <!-- 复制新闻编辑页面的按钮结构 -->
                <div class="help-block small" style="margin-top: 5px;">
                    <span class="text-muted">支持Word文档: </span>
                    <a class="btn btn-xs btn-info" id="importWordBtn" href="javascript:void(0);" style="margin-right: 5px;">
                        <i class="glyphicon glyphicon-import"></i> 导入
                    </a>
                    <a class="btn btn-xs btn-success" id="exportWordBtn" href="javascript:void(0);" style="margin-right: 5px;">
                        <i class="glyphicon glyphicon-export"></i> 导出
                    </a>
                    <a class="btn btn-xs btn-default" id="previewWebBtn" href="javascript:void(0);">
                        <i class="glyphicon glyphicon-eye-open"></i> 预览
                    </a>
                    <input id="wordFileInput" type="file" accept=".doc,.docx" style="display: none;">
                </div>
            </div>
            
            <div id="buttonTest" class="alert alert-info">
                <strong>按钮测试结果:</strong>
                <div id="testResults"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>2. 库加载测试</h3>
            <button class="btn btn-primary" onclick="testLibraries()">测试库加载</button>
            <div id="libraryResults" class="alert alert-info" style="margin-top: 10px;"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 手动功能测试</h3>
            <button class="btn btn-warning" onclick="manualImportTest()">手动测试导入</button>
            <button class="btn btn-success" onclick="manualExportTest()">手动测试导出</button>
            <button class="btn btn-info" onclick="manualPreviewTest()">手动测试预览</button>
            <div id="manualResults" class="alert alert-info" style="margin-top: 10px;"></div>
        </div>
    </div>

    <script src="/plugins/jquery/jquery-1.11.3.js"></script>
    <script src="/plugins/bootstrap/js/bootstrap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/tinymce@6/tinymce.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mammoth@1.6.0/mammoth.browser.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/html-docx-js@0.3.1/dist/html-docx.js"></script>
    
    <script>
        let editor;
        
        // 初始化TinyMCE
        tinymce.init({
            selector: '#editor',
            height: 200,
            language: 'zh_CN',
            plugins: ['advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview'],
            toolbar: 'undo redo | bold italic | alignleft aligncenter alignright | bullist numlist',
            setup: function(ed) {
                editor = ed;
                ed.on('init', function() {
                    console.log('TinyMCE初始化完成');
                    ed.setContent('<p>这是测试内容，可以编辑。</p>');
                    bindEvents();
                });
            }
        });
        
        function bindEvents() {
            console.log('绑定事件...');
            
            // 检查按钮是否存在
            const importBtn = $('#importWordBtn');
            const exportBtn = $('#exportWordBtn');
            const previewBtn = $('#previewWebBtn');
            
            let results = '';
            results += '导入按钮存在: ' + (importBtn.length > 0) + '<br>';
            results += '导出按钮存在: ' + (exportBtn.length > 0) + '<br>';
            results += '预览按钮存在: ' + (previewBtn.length > 0) + '<br>';
            
            $('#testResults').html(results);
            
            // 绑定事件
            importBtn.on('click', function(e) {
                e.preventDefault();
                console.log('点击了导入按钮');
                alert('导入按钮点击成功！');
                $('#wordFileInput').click();
            });
            
            exportBtn.on('click', function(e) {
                e.preventDefault();
                console.log('点击了导出按钮');
                alert('导出按钮点击成功！');
                exportToWord();
            });
            
            previewBtn.on('click', function(e) {
                e.preventDefault();
                console.log('点击了预览按钮');
                alert('预览按钮点击成功！');
                previewWebFormat();
            });
            
            $('#wordFileInput').on('change', function() {
                const file = this.files[0];
                if (file) {
                    console.log('选择了文件:', file.name);
                    alert('选择了文件: ' + file.name);
                    importWordDocument(file);
                }
            });
        }
        
        function testLibraries() {
            let results = '';
            results += 'jQuery: ' + (typeof $ !== 'undefined') + '<br>';
            results += 'TinyMCE: ' + (typeof tinymce !== 'undefined') + '<br>';
            results += 'Mammoth: ' + (typeof mammoth !== 'undefined') + '<br>';
            results += 'htmlDocx: ' + (typeof htmlDocx !== 'undefined') + '<br>';
            results += 'Editor对象: ' + (editor !== undefined) + '<br>';
            
            $('#libraryResults').html(results);
        }
        
        function manualImportTest() {
            if (typeof mammoth === 'undefined') {
                alert('Mammoth库未加载');
                return;
            }
            alert('导入功能可用，请选择Word文件');
            $('#wordFileInput').click();
        }
        
        function manualExportTest() {
            if (typeof htmlDocx === 'undefined') {
                alert('htmlDocx库未加载');
                return;
            }
            if (!editor) {
                alert('编辑器未初始化');
                return;
            }
            exportToWord();
        }
        
        function manualPreviewTest() {
            if (!editor) {
                alert('编辑器未初始化');
                return;
            }
            previewWebFormat();
        }
        
        // Word导入功能
        function importWordDocument(file) {
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const arrayBuffer = e.target.result;
                
                mammoth.convertToHtml({arrayBuffer: arrayBuffer})
                    .then(function(result) {
                        const html = result.value;
                        if (editor) {
                            editor.setContent(html);
                        }
                        alert('Word文档导入成功！');
                    })
                    .catch(function(error) {
                        console.error('Word导入错误:', error);
                        alert('Word文档导入失败：' + error.message);
                    });
            };
            reader.readAsArrayBuffer(file);
        }
        
        // Word导出功能
        function exportToWord() {
            if (!editor) return;
            
            const content = editor.getContent();
            const title = '测试文档';
            
            const htmlContent = `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="utf-8">
                    <title>${title}</title>
                </head>
                <body>
                    <h1>${title}</h1>
                    ${content}
                </body>
                </html>
            `;
            
            try {
                const converted = htmlDocx.asBlob(htmlContent);
                const link = document.createElement('a');
                link.href = URL.createObjectURL(converted);
                link.download = `${title}.docx`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                alert('Word文档导出成功！');
            } catch (error) {
                console.error('Word导出错误:', error);
                alert('Word文档导出失败：' + error.message);
            }
        }
        
        // 网页预览功能
        function previewWebFormat() {
            const content = editor ? editor.getContent() : '';
            const title = '预览测试';
            
            const previewWindow = window.open('', '_blank', 'width=800,height=600');
            previewWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="utf-8">
                    <title>网页格式预览 - ${title}</title>
                    <style>
                        body { font-family: "Microsoft YaHei", Arial, sans-serif; margin: 40px; }
                        h1 { color: #333; }
                    </style>
                </head>
                <body>
                    <h1>${title}</h1>
                    ${content}
                </body>
                </html>
            `);
        }
        
        // 页面加载完成后自动测试库
        $(document).ready(function() {
            setTimeout(testLibraries, 1000);
        });
    </script>
</body>
</html>
