# 问题排查和解决指南

## 问题描述

在配置Nginx反向代理后，发现页面无法正常显示，返回502 Bad Gateway或404错误。

## 问题排查过程

### 1. 初始问题诊断

**症状**: 访问 `http://localhost` 返回502 Bad Gateway错误

**排查步骤**:
```bash
# 检查Nginx状态
curl -v http://localhost

# 检查后端Node.js应用
curl -v http://localhost:8080

# 检查进程状态
ps aux | grep node
```

**发现**: Node.js应用没有在8080端口运行

### 2. Node.js应用问题

**症状**: Node.js应用进程被意外终止

**解决方案**:
```bash
# 重新启动Node.js应用
npm start
```

### 3. Vue 3页面404错误

**症状**: 访问 `/vue3-demo` 返回404错误，但 `/vue3-simple` 正常

**排查步骤**:
```bash
# 检查路由配置
cat routes/index.js

# 检查模板文件
ls views/website/vue3-*

# 验证Jade模板语法
node -e "const jade = require('jade'); jade.compileFile('./views/website/vue3-demo.jade');"
```

**发现**: Jade模板语法错误

### 4. Jade模板语法问题

**问题**: Vue.js的简写属性语法与Jade模板引擎冲突

**错误示例**:
```jade
// ❌ 错误 - Jade不支持冒号开头的属性
option(v-for="city in cities" :value="city") {{ city }}
.todo-item(:key="todo.id" :class="{completed: todo.completed}")
```

**修复方案**:
```jade
// ✅ 正确 - 使用完整的v-bind语法
option(v-for="city in cities" v-bind:value="city") {{ city }}
.todo-item(v-bind:key="todo.id" v-bind:class="{completed: todo.completed}")
```

## 解决方案总结

### 1. 修复的文件

- `views/website/vue3-demo.jade` - 修复Jade模板语法错误
- `routes/index.js` - 添加调试日志

### 2. 修复的语法错误

| 错误语法 | 正确语法 |
|---------|---------|
| `:value="city"` | `v-bind:value="city"` |
| `:key="todo.id"` | `v-bind:key="todo.id"` |
| `:class="{completed: todo.completed}"` | `v-bind:class="{completed: todo.completed}"` |

### 3. 验证步骤

```bash
# 1. 验证Jade语法
node -e "const jade = require('jade'); jade.compileFile('./views/website/vue3-demo.jade'); console.log('语法正确');"

# 2. 重启应用
npm start

# 3. 测试页面
curl -I http://localhost/vue3-demo

# 4. 运行健康检查
./health-check.sh
```

## 常见问题和解决方案

### 1. 502 Bad Gateway

**原因**: Nginx无法连接到后端Node.js应用

**解决**:
```bash
# 检查Node.js应用是否运行
ps aux | grep node
curl http://localhost:8080

# 重启Node.js应用
npm start
```

### 2. 404 Not Found

**原因**: 路由配置错误或模板文件问题

**解决**:
```bash
# 检查路由配置
grep -n "vue3-demo" routes/index.js

# 检查模板文件
ls -la views/website/vue3-demo.jade

# 验证模板语法
node -e "const jade = require('jade'); jade.compileFile('./views/website/vue3-demo.jade');"
```

### 3. Jade模板语法错误

**原因**: Vue.js简写语法与Jade不兼容

**解决**: 使用完整的Vue指令语法
- `:value` → `v-bind:value`
- `:key` → `v-bind:key`
- `:class` → `v-bind:class`
- `@click` → `v-on:click`

### 4. 端口占用

**原因**: 端口被其他进程占用

**解决**:
```bash
# 查看端口占用
sudo lsof -i :80
sudo lsof -i :8080

# 停止占用进程
pkill -f "node.*bin/www"
```

## 预防措施

### 1. 模板语法检查

在修改Jade模板后，始终验证语法：
```bash
node -e "const jade = require('jade'); jade.compileFile('./views/website/your-template.jade');"
```

### 2. 使用健康检查脚本

定期运行健康检查：
```bash
./health-check.sh
```

### 3. 日志监控

监控应用日志：
```bash
# Node.js应用日志
tail -f 终端输出

# Nginx日志
sudo tail -f /var/log/nginx/ronglianweb_access.log
sudo tail -f /var/log/nginx/ronglianweb_error.log
```

## 工具和脚本

### 1. 启动脚本
```bash
./start-with-nginx.sh  # 启动完整服务
```

### 2. 停止脚本
```bash
./stop-services.sh     # 停止所有服务
```

### 3. 健康检查
```bash
./health-check.sh      # 检查服务状态
```

### 4. 语法验证
```bash
# 验证特定模板
node -e "const jade = require('jade'); jade.compileFile('./views/website/template-name.jade');"
```

## 总结

通过系统性的问题排查，我们成功解决了：

1. ✅ Nginx反向代理配置
2. ✅ Node.js应用启动问题
3. ✅ Vue 3页面路由问题
4. ✅ Jade模板语法兼容性问题

现在所有服务都正常运行，可以通过 `http://localhost` 访问完整的Vue 3.0应用。
