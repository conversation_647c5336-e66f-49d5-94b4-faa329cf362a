doctype html
html
    head
        meta(charset='utf-8')
        meta(http-equiv='X-UA-Compatible', content='IE=edge')
        meta(name='viewport', content='width=device-width, initial-scale=1')
        title Vue 3 Demo Page
        link(rel='stylesheet', href='../plugins/bootstrap/css/bootstrap.min.css')
        link(rel='stylesheet', href='../plugins/common/css/common.css')
        style.
            .demo-container {
                padding: 40px 20px;
                max-width: 1200px;
                margin: 0 auto;
            }
            .demo-section {
                margin-bottom: 40px;
                padding: 20px;
                border: 1px solid #ddd;
                border-radius: 8px;
                background: #f9f9f9;
            }
            .demo-title {
                color: #333;
                margin-bottom: 20px;
                border-bottom: 2px solid #007bff;
                padding-bottom: 10px;
            }
            .counter-demo {
                text-align: center;
                padding: 20px;
            }
            .counter-display {
                font-size: 2em;
                color: #007bff;
                margin: 20px 0;
            }
            .todo-item {
                padding: 10px;
                margin: 5px 0;
                background: white;
                border: 1px solid #ddd;
                border-radius: 4px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .todo-item.completed {
                text-decoration: line-through;
                opacity: 0.6;
            }
    body
        .demo-container
            h1.text-center Vue 3.0 框架演示页面
            p.text-center.text-muted 这个页面展示了 Vue 3.0 的基本功能和组件系统
            
            #vue_app_demo
                // 计数器演示
                .demo-section
                    h2.demo-title 1. 响应式数据演示 - 计数器
                    .counter-demo
                        .counter-display {{ counter }}
                        button.btn.btn-primary.btn-lg(@click="increment") 点击增加
                        button.btn.btn-secondary.btn-lg.ml-2(@click="decrement") 点击减少
                        button.btn.btn-warning.btn-lg.ml-2(@click="reset") 重置
                
                // 表单演示
                .demo-section
                    h2.demo-title 2. 双向数据绑定演示
                    .row
                        .col-md-6
                            .form-group
                                label 输入您的姓名:
                                input.form-control(v-model="userName" placeholder="请输入姓名")
                            .form-group
                                label 选择您的城市:
                                select.form-control(v-model="selectedCity")
                                    option(value="") 请选择城市
                                    option(v-for="city in cities" v-bind:value="city") {{ city }}
                        .col-md-6
                            h4 实时预览:
                            .alert.alert-info
                                p(v-if="userName") 您好, {{ userName }}!
                                p(v-else) 请输入您的姓名
                                p(v-if="selectedCity") 您来自: {{ selectedCity }}
                                p(v-else) 请选择您的城市
                
                // 列表演示
                .demo-section
                    h2.demo-title 3. 列表渲染和条件渲染演示 - 待办事项
                    .row
                        .col-md-8
                            .form-group
                                .input-group
                                    input.form-control(v-model="newTodo" @keyup.enter="addTodo" placeholder="输入新的待办事项")
                                    .input-group-btn
                                        button.btn.btn-success(@click="addTodo") 添加
                            
                            div(v-if="todos.length === 0")
                                .alert.alert-warning 暂无待办事项，请添加一些任务！
                            
                            div(v-else)
                                .todo-item(v-for="(todo, index) in todos" :key="todo.id" :class="{completed: todo.completed}")
                                    span {{ todo.text }}
                                    div
                                        button.btn.btn-sm.btn-info(@click="toggleTodo(index)") 
                                            span(v-if="todo.completed") 取消完成
                                            span(v-else) 标记完成
                                        button.btn.btn-sm.btn-danger.ml-1(@click="removeTodo(index)") 删除
                        
                        .col-md-4
                            h4 统计信息:
                            .alert.alert-success
                                p 总任务数: {{ todos.length }}
                                p 已完成: {{ completedCount }}
                                p 未完成: {{ todos.length - completedCount }}
                
                // 组件演示
                .demo-section
                    h2.demo-title 4. 组件系统演示
                    p 这里展示了自定义的 Vue 3 组件:
                    demo-card(title="Vue 3 特性" content="Vue 3 提供了更好的性能、更小的包体积和更好的 TypeScript 支持")
                    demo-card(title="Composition API" content="新的 Composition API 让代码组织更加灵活")
                    demo-card(title="响应式系统" content="全新的响应式系统基于 Proxy，提供更好的性能")

        // 基础库
        script(src='../plugins/jquery/jquery-1.11.3.js')
        script(src='../plugins/bootstrap/js/bootstrap.min.js')
        
        // Vue 3核心库
        script(src='../plugins/vue3/vue.global.min.js')
        
        // Vue 3应用
        script.
            const { createApp } = Vue;
            
            // 创建Vue 3应用
            const app = createApp({
                data() {
                    return {
                        counter: 0,
                        userName: '',
                        selectedCity: '',
                        cities: ['北京', '上海', '广州', '深圳', '杭州', '成都'],
                        newTodo: '',
                        todos: [
                            { id: 1, text: '学习 Vue 3 基础', completed: true },
                            { id: 2, text: '创建第一个 Vue 3 应用', completed: false },
                            { id: 3, text: '掌握 Composition API', completed: false }
                        ],
                        nextTodoId: 4
                    }
                },
                computed: {
                    completedCount() {
                        return this.todos.filter(todo => todo.completed).length;
                    }
                },
                methods: {
                    increment() {
                        this.counter++;
                    },
                    decrement() {
                        this.counter--;
                    },
                    reset() {
                        this.counter = 0;
                    },
                    addTodo() {
                        if (this.newTodo.trim()) {
                            this.todos.push({
                                id: this.nextTodoId++,
                                text: this.newTodo.trim(),
                                completed: false
                            });
                            this.newTodo = '';
                        }
                    },
                    removeTodo(index) {
                        this.todos.splice(index, 1);
                    },
                    toggleTodo(index) {
                        this.todos[index].completed = !this.todos[index].completed;
                    }
                },
                mounted() {
                    console.log('Vue 3 Demo 应用已挂载');
                }
            });
            
            // 定义演示卡片组件
            app.component('demo-card', {
                props: ['title', 'content'],
                template: `
                    <div class="demo-card" style="margin: 10px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: white;">
                        <h5 style="color: #007bff; margin-bottom: 10px;">{{ title }}</h5>
                        <p style="margin: 0; color: #666;">{{ content }}</p>
                    </div>
                `
            });
            
            // 挂载应用
            app.mount('#vue_app_demo');
