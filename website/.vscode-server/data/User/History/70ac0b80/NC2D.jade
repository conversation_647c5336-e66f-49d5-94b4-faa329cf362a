extends layout

block css
  link(rel="stylesheet", href="/plugins/admin/css/admin.css")
  // TinyMCE样式 (替换Quill样式)
  style.
    .tox-tinymce {
      border: 1px solid #ddd !important;
      border-radius: 4px !important;
    }
    .tox .tox-toolbar {
      background: #f8f9fa !important;
    }
    // Word导入/导出按钮样式
    .word-actions {
      margin-bottom: 10px;
      padding: 10px;
      background: #f0f8ff;
      border: 1px solid #d1ecf1;
      border-radius: 4px;
    }
    .word-actions .btn {
      margin-right: 10px;
    }

block content
  .row
    .col-md-12
      .panel.panel-default
        .panel-heading
          h3.panel-title
            i.glyphicon.glyphicon-edit
            if newsId
              |  编辑新闻
            else
              |  创建新闻
          .pull-right
            a.btn.btn-default.btn-sm(href="/admin/news")
              i.glyphicon.glyphicon-arrow-left
              |  返回列表
        .panel-body
          form#newsForm
            // 基本信息区域
            .row
              .col-md-12
                .panel.panel-info
                  .panel-heading
                    h4.panel-title
                      i.glyphicon.glyphicon-pencil
                      |  📝 基本信息
                  .panel-body
                    .form-group
                      label.control-label(for="title") 新闻标题 *
                      input.form-control#title(type="text", name="title", required, placeholder="请输入新闻标题")
                    
                    .form-group
                      label.control-label(for="content") 新闻内容 *
                      #editor(style="height: 300px;")
                      textarea#content(name="content", style="display: none;")
            
            // 发布设置和封面图片
            .row
              .col-md-6
                .panel.panel-success
                  .panel-heading
                    h4.panel-title
                      i.glyphicon.glyphicon-cog
                      |  🚀 发布设置
                  .panel-body
                    .form-group
                      label.control-label(for="status") 状态
                      select.form-control#status(name="status")
                        option(value="draft") 草稿
                        option(value="published") 发布
                        option(value="unpublished") 下架
                        option(value="archived") 归档
                    
                    .form-group
                      label.control-label(for="author") 作者
                      input.form-control#author(type="text", name="author", readonly)
              
              .col-md-6
                .panel.panel-warning
                  .panel-heading
                    h4.panel-title
                      i.glyphicon.glyphicon-picture
                      |  🖼️ 封面图片
                  .panel-body
                    .form-group
                      label.control-label 上传封面图片
                      .upload-area#uploadArea
                        .upload-placeholder
                          i.glyphicon.glyphicon-cloud-upload
                          p 点击或拖拽图片到此处上传
                          small 支持 JPG、PNG、GIF 格式，大小不超过 5MB
                      input#imageFile(type="file", accept="image/*", style="display: none;")
                      input#coverImage(type="hidden", name="coverImage")
                    
                    .image-preview#imagePreview(style="display: none;")
                      img#previewImg(style="max-width: 100%; height: auto; border-radius: 4px;")
                      .image-actions
                        button.btn.btn-danger.btn-sm#removeImage(type="button") 删除图片
            
            // 操作按钮
            .row
              .col-md-12
                .form-actions
                  button.btn.btn-primary#saveBtn(type="submit")
                    i.glyphicon.glyphicon-floppy-disk
                    |  保存
                  button.btn.btn-success#publishBtn(type="button", style="margin-left: 10px;")
                    i.glyphicon.glyphicon-ok
                    |  保存并发布
                  button.btn.btn-default#previewBtn(type="button", style="margin-left: 10px;")
                    i.glyphicon.glyphicon-eye-open
                    |  预览
                  a.btn.btn-secondary(href="/admin/news", style="margin-left: 10px;")
                    i.glyphicon.glyphicon-remove
                    |  取消

block scripts
  // 富文本编辑器
  script(src="https://cdn.jsdelivr.net/npm/quill@1.3.7/dist/quill.min.js")
  script(src="/plugins/admin/js/newsEdit.js")
  script.
    // 传递新闻ID到前端
    window.newsId = '#{newsId}';
    window.currentUser = !{JSON.stringify(user || {})};
    
    // 初始化页面
    $(document).ready(function() {
      // 设置作者为当前用户
      if (window.currentUser && window.currentUser.name) {
        $('#author').val(window.currentUser.name);
      }
      
      // 如果是编辑模式，加载新闻数据
      if (window.newsId) {
        loadNewsData(window.newsId);
      }
    });
