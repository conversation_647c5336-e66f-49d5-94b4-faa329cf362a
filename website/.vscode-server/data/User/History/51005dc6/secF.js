var express = require('express');
var authRestApiProxy = require("../../libs/authRestApiProxy");
var config = require('../../libs/config');
var log4js = require('../../libs/log4jsUtil.js');
var router = express.Router();
var logger = log4js.getLogger('contactUs');
var merchantUtil = require("../../libs/merchantUtil.js");
var imgDomain = config.ImageService.domain;
var async = require("async");
var title = "荣联科技-新闻中心";
var newsTopTabsId = "news";
var WechatUserUtils = require('../../libs/WechatUserUtils.js');
// 引入新闻管理系统
const NewsManager = require('../../libs/newsManager');
const newsManager = new NewsManager();
// 新闻首页 /news/index
router.get('/', function (req, res, next) {
    //var param = req.query.code;
    //logger.info(next);
    //logger.info("获取code" + req.query.response_type);
    merchantUtil.getMerchantAllInfo(req, function (err, menuData) {
        //logger.info("加载企业微信用户信息开始");
        if (err == null && menuData != null) {
            menuData.topTabsId = newsTopTabsId;
            if (menuData.pageTabs) {
                for (var i = 0; i < menuData.pageTabs.length; i++) {
                    if (menuData.pageTabs[i].tabsId === menuData.topTabsId) {
                        menuData.title = menuData.pageTabs[i].title;
                    }
                }
            } else {
                menuData.title = title;
            }
            //WechatUserUtils.getWechatUser(req.query.type, param, res, "news/newsn", menuData, req);
			res.render('news/newsn', menuData);
        } else {
            res.render('error');
        }
    });
});
router.get('/uplus', function (req, res, next) {
    var param = req.query.code;
    merchantUtil.getMerchantAllInfo(req, function (err, menuData) {
        if (err == null && menuData != null) {
            menuData.topTabsId = newsTopTabsId;
            if (menuData.pageTabs) {
                for (var i = 0; i < menuData.pageTabs.length; i++) {
                    if (menuData.pageTabs[i].tabsId === menuData.topTabsId) {
                        menuData.title = menuData.pageTabs[i].title;
                    }
                }
            } else {
                menuData.title = title;
            }
            menuData.infoType = req.query.type;
            if(req.cookies.userid){
                menuData.userid =req.cookies.userid;
                res.render('news/unewsn', menuData);
            }else {
                WechatUserUtils.getWechatUser(req.query.type, param, res, "news/unewsn", menuData, req);
            }

        } else {
            res.render('error');
        }
    });
});
router.get('/uDetail', function (req, res, next) {
    logger.info(next);
    logger.info("获取code" + req.query.response_type);
    merchantUtil.getMerchantAllInfo(req, function (err, menuData) {
        if (err == null && menuData != null) {
            menuData.topTabsId = newsTopTabsId;
            if (menuData.pageTabs) {
                for (var i = 0; i < menuData.pageTabs.length; i++) {
                    if (menuData.pageTabs[i].tabsId === menuData.topTabsId) {
                        menuData.title = menuData.pageTabs[i].title;
                    }
                }
            } else {
                menuData.title = title;
            }
            menuData.info_ID = req.query.info_ID;
            menuData.infoType = req.query.infoType;
            menuData.userid = req.cookies.userid;
            res.render('news/udetail', menuData);
        } else {
            res.render('error');
        }
    });
});
// 新闻列表 /news/index/q
router.post('/q', function (req, res, next) {
    console.info(next);
    req.body.sortIndex = "n.create_on desc";//修改时间降序
    req.body.pageSize = 8;
    req.body.frontFlag = "1";

    try {
        // 使用新闻管理系统获取已发布的新闻
        const pageNo = req.body.pageNo || 1;
        const pageSize = req.body.pageSize || 8;

        const newsResult = newsManager.getPublishedNews(pageNo, pageSize);

        console.log(`📄 前台分页查询: 第${pageNo}页，每页${pageSize}条，总计${newsResult.totalCount}条已发布新闻，共${newsResult.totalPages}页`);

        // 返回符合前台期望格式的数据
        const responseData = {
            records: newsResult.records,
            totalCount: newsResult.totalCount,
            pageNo: newsResult.pageNo,
            pageSize: newsResult.pageSize,
            totalPages: newsResult.totalPages
        };

        res.send(responseData);
        return;

    } catch (error) {
        console.error('新闻管理系统查询失败，使用备用数据:', error);
    }

    // 备用：荣联科技模拟新闻数据（仅在新闻管理系统失败时使用）
    var mockNewsData = {
        records: [
            {
                news_id: '1',
                shortId: 'news001',
                title: '荣联科技集团荣获"海淀企业"荣誉称号',
                content: '近日，由海淀区人民政府主办的2025年海淀区经济社会高质量发展大会在京顺利召开，荣联科技集团受邀参会，并凭借在推动区域经济高质量发展中的卓越表现，荣获"海淀企业"荣誉称号。',
                picMgid: '/images/news/haidian-enterprise.jpg',
                year: '2025',
                month: '02',
                day: '17'
            },
            {
                news_id: '2',
                shortId: 'news002',
                title: '荣联科技集团荣获"2024大数据产业年度创新技术突破奖"',
                content: '荣联科技集团凭借在大数据技术创新方面的突出贡献，荣获"2024大数据产业年度创新技术突破奖"，这一荣誉充分体现了公司在大数据领域的技术实力和创新能力。',
                picMgid: '/images/news/bigdata-award.jpg',
                year: '2025',
                month: '01',
                day: '06'
            },
            {
                news_id: '3',
                shortId: 'news003',
                title: '荣联科技集团出席2024数字经济领航者大会',
                content: '荣联科技集团出席2024数字经济领航者大会，共话人工智能数字化创新应用。公司在会上分享了在AI赋能数字化转型方面的最新实践和成果，获得与会专家的高度认可。',
                picMgid: '/images/news/digital-economy-summit.jpg',
                year: '2025',
                month: '01',
                day: '02'
            },
            {
                news_id: '4',
                shortId: 'news004',
                title: '荣联科技：2024年度净利润约2811万元，成功扭亏为盈',
                content: '荣联科技发布2024年度业绩报告，实现营业收入20.22亿元，归属于上市公司股东的净利润2810.69万元，同比成功扭亏为盈，展现了公司强劲的经营韧性和发展潜力。',
                picMgid: '/images/news/financial-report.jpg',
                year: '2024',
                month: '12',
                day: '31'
            },
            {
                news_id: '5',
                shortId: 'news005',
                title: '荣联科技集团荣登"2024金融信创优秀服务商TOP50"',
                content: '由中国科学院主管权威媒体《互联网周刊》联合eNet研究院、德本咨询发布的"2024金融信创优秀服务商TOP50"榜单中，荣联科技集团凭借在金融信创领域的卓越表现成功入选。',
                picMgid: '/images/news/fintech-top50.jpg',
                year: '2024',
                month: '08',
                day: '15'
            },
            {
                news_id: '6',
                shortId: 'news006',
                title: '天府人工智能大会医工交叉分论坛顺利举行',
                content: '2024年9月29日，由电子科技大学医学院、四川省普通高校新医科建设教学指导委员会承办，荣联科技协办的天府人工智能大会医工交叉分论坛顺利举行，共同探讨AI在医疗健康领域的创新应用。',
                picMgid: '/images/news/ai-medical-forum.jpg',
                year: '2024',
                month: '09',
                day: '29'
            },
            {
                news_id: '7',
                shortId: 'news007',
                title: '荣联科技前三季度净利润约1048万元，业绩稳步提升',
                content: '荣联科技发布2024年前三季度业绩公告，实现净利润约1048万元，业绩稳步提升。公司在数字化服务领域持续深耕，为客户提供更加优质的解决方案和服务。',
                picMgid: '/images/news/q3-report.jpg',
                year: '2024',
                month: '10',
                day: '29'
            },
            {
                news_id: '8',
                shortId: 'news008',
                title: '荣联科技深化数字化转型服务，助力千行百业智能升级',
                content: '荣联科技作为专业的数字化服务提供商，围绕云计算、大数据等新一代信息技术，深入金融、政府公用、运营商、能源制造和生物医疗等优势行业核心应用，以先进的数字化服务赋能客户全面数字化转型升级。',
                picMgid: '/images/news/digital-transformation.jpg',
                year: '2024',
                month: '11',
                day: '15'
            }
        ],
        totalCount: 6,
        pageNo: req.body.pageNo || 1,
        pageSize: req.body.pageSize || 8
    };

    // 尝试调用真实API，如果失败则使用模拟数据
    authRestApiProxy.post('RetailerService', "/news/q/l", req.body, function (err, data) {
        if (data != null && err == null) {
            var tableData = data;
            res.send(tableData);
        } else {
            // 使用模拟数据
            console.log('使用模拟新闻数据');
            res.send(mockNewsData);
        }
    });
});

//新闻详情  /news/index/qd/newsShortId
router.get('/qd/:shortId', function (req, res, next) {
    console.info(next);
    async.waterfall([
        function (callback) {
            //加载头尾
            merchantUtil.getMerchantAllInfo(req, function (menuErr, menuData) {
                callback(menuErr, menuData);
            });
        },
        function (menuData, callback) {
            //查询详情
            var newsRequestVo = {};
            newsRequestVo.shortId = req.params.shortId;
            authRestApiProxy.post('RetailerService', "/news/n/s/x", newsRequestVo, function (newsDetailErr, newsDetail) {
                callback(newsDetailErr, menuData, newsDetail);
            });
        },
        //查询最新5条新闻
        function (menuData, newsDetail, callback) {
            authRestApiProxy.post('RetailerService', "/news/n/l/5", {}, function (latestNewsErr, latestNews) {
                callback(latestNewsErr, menuData, newsDetail, latestNews);
            });
        },
    ], function (err, menuData, newsDetail, latestNews) {
        if (err) {
            res.render('error');
        } else {
            //kData.keywords,kData.description
            var kData = {};//关键字和描述
            kData.keywords = newsDetail.keywords;
            kData.description = newsDetail.description;
            menuData.kData = kData;
            newsDetail.imgDomain = imgDomain;
            menuData.newsDetail = newsDetail;
            menuData.latestNews = latestNews;
            menuData.title = menuData.newsDetail.title;
            menuData.topTabsId = newsTopTabsId;
            res.render('news/news-detail', menuData);
        }
    });
});

//新闻详情(老地址，做301跳转)  /news/index/qd?newsId=
router.get('/qd', function (req, res, next) {
    console.info(next);
    var newAddr = '/news/index/qd/';
    var reqVo = {
        newsId: req.query.newsId
    };
    authRestApiProxy.post('RetailerService', '/news/n/x', reqVo, function resultData(err, data) {
        if (data && data.shortId !== 'undefined') {
            newAddr += data.shortId;
            res.redirect(301, newAddr);
        } else {
            res.render('error');
        }
    });
});
module.exports = router;
