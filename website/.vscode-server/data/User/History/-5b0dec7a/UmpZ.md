# 端口配置说明

## 当前配置

项目已配置为使用 **80端口**，但由于权限限制，提供了多种启动方式。

## 启动方式

### 方式1：使用8080端口（推荐 - 无需管理员权限）

```bash
# 临时使用8080端口
PORT=8080 npm start

# 或者修改 bin/www 文件中的默认端口为8080
```

访问地址：
- 主页: `http://localhost:8080`
- Vue 3 演示: `http://localhost:8080/vue3-demo`
- Vue 3 测试: `http://localhost:8080/vue3-test`

### 方式2：使用80端口（需要管理员权限）

#### 选项A：使用启动脚本
```bash
# 使用提供的启动脚本
./start-port80.sh
```

#### 选项B：手动使用sudo
```bash
# 设置环境变量并使用sudo启动
sudo PORT=80 npm start

# 或者直接使用sudo（使用默认80端口）
sudo npm start
```

访问地址：
- 主页: `http://localhost`
- Vue 3 演示: `http://localhost/vue3-demo`
- Vue 3 测试: `http://localhost/vue3-test`

### 方式3：使用其他端口

您可以使用任何大于1024的端口，无需管理员权限：

```bash
# 使用3000端口（原始端口）
PORT=3000 npm start

# 使用8000端口
PORT=8000 npm start

# 使用9000端口
PORT=9000 npm start
```

## 端口配置文件

端口配置位于：`bin/www` 文件的第15行

```javascript
var port = normalizePort(process.env.PORT || '80');
```

## 权限说明

### 为什么80端口需要管理员权限？

- 端口1-1023被称为"特权端口"或"系统端口"
- 这些端口只能由root用户或具有特殊权限的用户绑定
- 80端口是HTTP的标准端口
- 443端口是HTTPS的标准端口

### 替代方案

1. **开发环境**：使用8080、3000等端口
2. **生产环境**：
   - 使用反向代理（如Nginx）将80端口转发到应用端口
   - 使用Docker容器运行
   - 配置系统服务以特权用户运行

## 反向代理配置示例（Nginx）

如果您想在生产环境中使用80端口，推荐使用Nginx反向代理：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 当前状态

- ✅ 项目配置为80端口
- ✅ 提供了8080端口的替代方案
- ✅ 创建了管理员权限启动脚本
- ✅ **已配置Nginx反向代理**
- ✅ 所有Vue 3功能正常工作

## Nginx反向代理配置 (推荐方式)

### 已完成配置
- ✅ Nginx已安装并运行
- ✅ 反向代理配置已生效
- ✅ Node.js应用运行在8080端口
- ✅ 通过80端口访问应用

### 启动方式
```bash
# 使用Nginx反向代理启动脚本
./start-with-nginx.sh

# 或者手动启动
npm start  # Node.js应用在8080端口
# Nginx自动代理80端口到8080端口
```

### 访问地址
- 主页: `http://localhost`
- Vue 3 演示: `http://localhost/vue3-demo`
- Vue 3 测试: `http://localhost/vue3-test`

### 停止服务
```bash
./stop-services.sh
```

## 推荐使用

**开发和生产环境**：使用Nginx反向代理
```bash
./start-with-nginx.sh
```

**临时测试**：直接使用8080端口
```bash
PORT=8080 npm start
```
