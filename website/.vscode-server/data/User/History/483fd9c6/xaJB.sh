#!/bin/bash

# 一键修复502错误脚本 - 在dl380上执行
# 解决express-session模块缺失和依赖问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

echo -e "${CYAN}🔧 一键修复502错误脚本${NC}"
echo -e "${CYAN}========================${NC}"
echo "执行主机: $(hostname)"
echo "当前用户: $(whoami)"
echo "当前时间: $(date)"
echo "Node.js版本: $(node --version)"
echo "npm版本: $(npm --version)"
echo ""

# 步骤1: 进入应用目录
log_step "进入应用目录"
APP_DIR="/home/<USER>/ronglianweb"

if [ ! -d "$APP_DIR" ]; then
    log_error "应用目录不存在: $APP_DIR"
    exit 1
fi

cd "$APP_DIR"
log_info "当前目录: $(pwd)"

# 检查关键文件
if [ ! -f "package.json" ]; then
    log_error "package.json文件不存在"
    exit 1
fi

if [ ! -f "app.js" ]; then
    log_error "app.js文件不存在"
    exit 1
fi

if [ ! -f "bin/www" ]; then
    log_error "bin/www文件不存在"
    exit 1
fi

log_success "关键文件检查完成"

# 步骤2: 停止现有进程
log_step "停止现有Node.js进程"
pkill -f 'node.*bin/www' 2>/dev/null || true
pkill -f 'npm.*start' 2>/dev/null || true
sleep 3
log_success "现有进程已停止"

# 步骤3: 清理和重新安装依赖
log_step "清理npm缓存和依赖"

log_info "清理npm缓存..."
npm cache clean --force

log_info "删除node_modules和package-lock.json..."
rm -rf node_modules package-lock.json

log_success "清理完成"

# 步骤4: 重新安装依赖
log_step "重新安装应用依赖"

log_info "安装package.json中的依赖..."
npm install

if [ $? -ne 0 ]; then
    log_warning "npm install失败，尝试使用--legacy-peer-deps..."
    npm install --legacy-peer-deps
fi

log_success "基础依赖安装完成"

# 步骤5: 手动安装关键模块
log_step "手动安装关键模块"

# 关键模块列表
CRITICAL_MODULES=(
    "express"
    "express-session"
    "jade"
    "body-parser"
    "cookie-parser"
    "morgan"
    "serve-favicon"
    "multer"
    "debug"
    "http-errors"
)

log_info "安装关键模块..."
for module in "${CRITICAL_MODULES[@]}"; do
    if [ ! -d "node_modules/$module" ]; then
        log_info "安装缺失模块: $module"
        npm install "$module" --save
    else
        log_info "模块已存在: $module"
    fi
done

log_success "关键模块安装完成"

# 步骤6: 验证关键模块
log_step "验证关键模块安装"

MISSING_MODULES=()
for module in "${CRITICAL_MODULES[@]}"; do
    if [ ! -d "node_modules/$module" ]; then
        MISSING_MODULES+=("$module")
        log_error "模块缺失: $module"
    else
        log_success "模块已安装: $module"
    fi
done

if [ ${#MISSING_MODULES[@]} -gt 0 ]; then
    log_warning "发现缺失模块，尝试重新安装..."
    for module in "${MISSING_MODULES[@]}"; do
        npm install "$module" --save --force
    done
fi

# 步骤7: 设置权限
log_step "设置文件权限"

chmod +x bin/www
chmod +x run.sh deploy.sh deploy-https.sh 2>/dev/null || true
mkdir -p logs uploads
chmod 755 logs uploads

# 设置SSL证书权限（如果存在）
if [ -f "ssl/private.key" ] && [ -f "ssl/certificate.crt" ]; then
    chmod 600 ssl/private.key
    chmod 644 ssl/certificate.crt
    log_info "SSL证书权限已设置"
fi

log_success "文件权限设置完成"

# 步骤8: 测试应用启动
log_step "测试应用启动"

log_info "尝试直接启动应用进行测试..."
timeout 10 node bin/www 2>&1 | head -10 || log_warning "直接启动测试完成（可能有错误但继续）"

# 步骤9: 启动应用
log_step "启动Node.js应用"

log_info "使用npm start启动应用..."
nohup npm start > logs/app.log 2>&1 &

# 获取启动的进程ID
sleep 3
APP_PID=$(pgrep -f 'node.*bin/www' || echo "")

if [ -n "$APP_PID" ]; then
    log_info "应用已启动，PID: $APP_PID"
else
    log_warning "应用可能启动失败，等待更长时间..."
fi

# 等待应用完全启动
log_info "等待应用完全启动..."
for i in {1..20}; do
    echo -n "."
    sleep 1
done
echo ""

log_success "应用启动命令已执行"

# 步骤10: 验证应用状态
log_step "验证应用状态"

# 检查进程
NODE_PROCESSES=$(ps aux | grep 'node.*bin/www' | grep -v grep || echo "")
if [ -n "$NODE_PROCESSES" ]; then
    log_success "Node.js应用正在运行"
    echo "$NODE_PROCESSES"
else
    log_error "Node.js应用未运行"
    echo ""
    log_info "查看启动日志:"
    tail -30 logs/app.log
    echo ""
    log_info "查看package.json scripts:"
    cat package.json | grep -A 5 '"scripts"' || echo "无法读取scripts"
    exit 1
fi

# 检查端口3000
PORT_CHECK=$(netstat -tlnp | grep :3000 || echo "")
if [ -n "$PORT_CHECK" ]; then
    log_success "端口3000正在监听"
    echo "$PORT_CHECK"
else
    log_warning "端口3000未监听，应用可能还在初始化"
fi

# 步骤11: 测试访问
log_step "测试应用访问"

# 等待端口监听
log_info "等待端口3000开始监听..."
for i in {1..30}; do
    if netstat -tlnp | grep :3000 > /dev/null; then
        log_success "端口3000已开始监听"
        break
    fi
    echo -n "."
    sleep 1
done
echo ""

# 测试本地3000端口
log_info "测试本地3000端口..."
HTTP_CODE=$(curl -s -o /dev/null -w '%{http_code}' http://localhost:3000 2>/dev/null || echo "Failed")
echo "HTTP 3000端口测试: $HTTP_CODE"

# 测试HTTPS访问
log_info "测试HTTPS访问..."
HTTPS_CODE=$(curl -s -o /dev/null -w '%{http_code}' -k https://localhost 2>/dev/null || echo "Failed")
echo "HTTPS访问测试: $HTTPS_CODE"

# 测试健康检查
log_info "测试健康检查..."
HEALTH_CHECK=$(curl -s -k https://localhost/health 2>/dev/null || echo "Failed")
echo "健康检查结果: $HEALTH_CHECK"

# 步骤12: 最终结果
echo ""
echo -e "${CYAN}========================================${NC}"
echo -e "${CYAN}🎯 修复结果总结${NC}"
echo -e "${CYAN}========================================${NC}"

# 最终状态检查
FINAL_NODE_PROCESS=$(ps aux | grep 'node.*bin/www' | grep -v grep || echo "")
FINAL_PORT_CHECK=$(netstat -tlnp | grep :3000 || echo "")

# 判断修复是否成功
if [ -n "$FINAL_NODE_PROCESS" ] && [ -n "$FINAL_PORT_CHECK" ] && ([ "$HTTPS_CODE" = "200" ] || [ "$HTTPS_CODE" = "301" ] || [ "$HTTPS_CODE" = "302" ]); then
    echo -e "${GREEN}🎉 502错误修复完全成功！${NC}"
    echo ""
    echo -e "${GREEN}✅ 修复状态:${NC}"
    echo "   Node.js版本: $(node --version)"
    echo "   npm版本: $(npm --version)"
    echo "   应用状态: 正在运行"
    echo "   端口3000: 正在监听"
    echo "   HTTPS访问: 正常 ($HTTPS_CODE)"
    echo ""
    echo -e "${CYAN}🌐 访问地址:${NC}"
    echo "   🔒 主站 (HTTPS): https://10.1.0.63"
    echo "   📰 新闻页面: https://10.1.0.63/news/index"
    echo "   🔐 管理登录: https://10.1.0.63/admin/login"
    echo "   🔧 直接访问: http://10.1.0.63:3000"
    echo ""
    echo -e "${CYAN}🔐 登录信息:${NC}"
    echo "   管理员: admin / admin123"
    echo "   编辑员: user01 / user123"
    echo ""
    echo -e "${GREEN}🎊 现在可以正常访问网站了！${NC}"
    
elif [ -n "$FINAL_NODE_PROCESS" ] && [ -n "$FINAL_PORT_CHECK" ]; then
    echo -e "${YELLOW}⚠️ 应用已启动，端口正在监听，但HTTPS访问可能需要时间${NC}"
    echo ""
    echo "请等待1-2分钟后再次测试访问"
    echo "或检查Nginx配置是否正确"
    
elif [ -n "$FINAL_NODE_PROCESS" ]; then
    echo -e "${YELLOW}⚠️ 应用已启动，但端口可能还在初始化${NC}"
    echo ""
    echo "请等待片刻后再次检查端口状态"
    
else
    echo -e "${RED}❌ 修复失败，应用未能正常启动${NC}"
    echo ""
    echo "问题诊断:"
    echo "应用进程: $([ -n "$FINAL_NODE_PROCESS" ] && echo '运行中' || echo '未运行')"
    echo "端口3000: $([ -n "$FINAL_PORT_CHECK" ] && echo '监听中' || echo '未监听')"
    echo "HTTPS访问: $HTTPS_CODE"
    echo ""
    echo "请查看错误日志: tail -50 logs/app.log"
fi

echo ""
echo -e "${BLUE}📝 重要提示:${NC}"
echo "1. 应用日志位置: $APP_DIR/logs/app.log"
echo "2. 如需重启应用: cd $APP_DIR && ./run.sh restart"
echo "3. 查看实时日志: tail -f $APP_DIR/logs/app.log"
echo "4. 检查应用状态: ps aux | grep node"

echo ""
echo -e "${CYAN}一键修复脚本执行完成！${NC}"
