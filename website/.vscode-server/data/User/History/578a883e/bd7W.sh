#!/bin/bash

# 启动脚本 - 使用Nginx反向代理到80端口
# 这个脚本会启动Node.js应用在8080端口，并通过Nginx代理到80端口

echo "=== 荣联Web应用启动脚本 (Nginx反向代理) ==="
echo ""

# 检查Nginx是否安装
if ! command -v nginx &> /dev/null; then
    echo "❌ Nginx未安装，请先安装Nginx"
    echo "   sudo apt update && sudo apt install nginx -y"
    exit 1
fi

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装，请先安装Node.js"
    exit 1
fi

# 检查npm依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装npm依赖..."
    npm install
fi

echo "🔧 配置检查..."

# 检查Nginx配置是否存在
if [ ! -f "/etc/nginx/sites-available/ronglianweb" ]; then
    echo "⚙️  复制Nginx配置文件..."
    sudo cp nginx-ronglianweb.conf /etc/nginx/sites-available/ronglianweb
    
    echo "🔗 启用Nginx站点..."
    sudo rm -f /etc/nginx/sites-enabled/default
    sudo ln -s /etc/nginx/sites-available/ronglianweb /etc/nginx/sites-enabled/
fi

# 测试Nginx配置
echo "✅ 测试Nginx配置..."
if ! sudo nginx -t; then
    echo "❌ Nginx配置有误，请检查配置文件"
    exit 1
fi

# 重新加载Nginx
echo "🔄 重新加载Nginx..."
sudo systemctl reload nginx

# 检查Nginx状态
if ! sudo systemctl is-active --quiet nginx; then
    echo "🚀 启动Nginx..."
    sudo systemctl start nginx
fi

# 检查8080端口是否被占用
if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  端口8080已被占用，正在停止现有进程..."
    pkill -f "node.*bin/www" || true
    sleep 3
fi

echo "🚀 启动Node.js应用 (端口8080)..."

# 设置环境变量
export PORT=8080

# 启动Node.js应用
npm start &
NODE_PID=$!

# 等待应用启动
echo "⏳ 等待应用启动..."
sleep 3

# 检查应用是否成功启动
if curl -s http://localhost:8080 > /dev/null; then
    echo "✅ Node.js应用启动成功"
else
    echo "❌ Node.js应用启动失败"
    exit 1
fi

echo "✅ Node.js应用已启动 (PID: $NODE_PID)"
echo "✅ Nginx反向代理已配置"
echo ""
echo "🌐 访问地址:"
echo "   主页:        http://localhost"
echo "   Vue 3 演示:  http://localhost/vue3-demo"
echo "   Vue 3 测试:  http://localhost/vue3-test"
echo "   Vue 3 主页:  http://localhost/vue3-home"
echo ""
echo "📊 服务状态:"
echo "   Node.js:     http://localhost:8080 (后端)"
echo "   Nginx:       http://localhost (前端代理)"
echo ""
echo "🛑 停止服务:"
echo "   停止Node.js: kill $NODE_PID"
echo "   停止Nginx:   sudo systemctl stop nginx"
echo ""
echo "📝 日志查看:"
echo "   Nginx访问日志: sudo tail -f /var/log/nginx/ronglianweb_access.log"
echo "   Nginx错误日志: sudo tail -f /var/log/nginx/ronglianweb_error.log"
echo ""

# 等待用户中断
echo "按 Ctrl+C 停止应用..."
wait $NODE_PID
