# Preferences
- User prefers to use Vue 3.0 framework for development.
- User prefers to use port 80 instead of port 3000 for the web server.
- User prefers using nginx reverse proxy to access applications through port 80.
- User prefers to use IP address ********* with HTTPS protocol instead of localhost for web applications.
- User prefers to keep existing UI layouts and structures unchanged when making modifications or upgrades. This includes the news management system UI layout, specifically approving the Word-compatible editor approach that supports both Word import/export for editing and HTML format for web publishing.

# Server Configuration
- User's server IP address is ********* (not localhost).

# Software Requirements
- User requires rich text editor to be compatible with MS Office Word format and support online editing functionality.
- User requires the rich text editor to publish content in web-compatible format, not just Word format.