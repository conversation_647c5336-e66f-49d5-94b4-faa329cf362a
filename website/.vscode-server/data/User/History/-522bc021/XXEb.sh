#!/bin/bash
# 荣联科技网站运行脚本 (兼容原版本)
# 新功能请使用 deploy.sh 脚本

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

start_app() {
    # Install dependencies if node_modules directory doesn't exist
    if [ ! -d "node_modules" ]; then
        print_message $YELLOW "📦 Installing dependencies..."
        npm install
    fi

    # 检查是否已有Node.js进程在运行
    if pgrep -f "node.*bin/www" > /dev/null; then
        print_message $YELLOW "⚠️  Node.js应用已在运行"
        return 0
    fi

    # Start the application
    print_message $GREEN "🚀 Starting the application..."
    npm start &

    # 等待应用启动
    sleep 3

    # 检查应用是否启动成功
    if pgrep -f "node.*bin/www" > /dev/null; then
        print_message $GREEN "✅ Node.js应用启动成功 (端口3000)"
        print_message $BLUE "📱 直接访问: http://localhost:3000"
        print_message $BLUE "📱 Nginx代理: https://********* (推荐)"
    else
        print_message $RED "❌ Node.js应用启动失败"
        exit 1
    fi
}

case "$1" in
    start)
        print_message $GREEN "🌐 启动荣联科技网站 (兼容模式)..."
        print_message $BLUE "🔧 启动Nginx (HTTPS代理)..."

        # 测试nginx配置
        if ! sudo nginx -t > /dev/null 2>&1; then
            print_message $RED "❌ Nginx配置测试失败"
            exit 1
        fi

        # 启动nginx
        sudo systemctl start nginx
        if systemctl is-active --quiet nginx; then
            print_message $GREEN "✅ Nginx启动成功 (HTTPS代理已启用)"
        else
            print_message $RED "❌ Nginx启动失败"
            exit 1
        fi

        start_app

        print_message $GREEN ""
        print_message $GREEN "🎉 网站启动完成！"
        print_message $BLUE "📱 主要访问地址:"
        echo "   🔒 主站 (HTTPS): https://*********"
        echo "   📰 新闻页面: https://*********/news/index"
        echo "   🔐 管理登录: https://*********/admin/login"
        echo "   🔧 直接访问: http://localhost:3000"
        print_message $YELLOW ""
        print_message $YELLOW "💡 提示: 使用 './deploy.sh' 获得更多高级功能"
        ;;
    stop)
        print_message $YELLOW "🛑 停止荣联科技网站..."

        # 停止Node.js应用
        print_message $BLUE "🛑 停止Node.js应用..."
        pkill -f "node.*bin/www" 2>/dev/null
        pkill -f "npm.*start" 2>/dev/null
        print_message $GREEN "✅ Node.js应用已停止"

        # 停止nginx
        print_message $BLUE "🛑 停止Nginx..."
        sudo systemctl stop nginx
        print_message $GREEN "✅ Nginx已停止"

        print_message $GREEN "🎉 网站已完全停止"
        ;;
    restart)
        print_message $YELLOW "🔄 重启荣联科技网站..."

        # 停止服务
        print_message $BLUE "🛑 停止现有服务..."
        pkill -f "node.*bin/www" 2>/dev/null
        pkill -f "npm.*start" 2>/dev/null
        sudo systemctl stop nginx

        sleep 2

        # 重新启动
        print_message $BLUE "🚀 重新启动服务..."

        # 测试nginx配置
        if ! sudo nginx -t > /dev/null 2>&1; then
            print_message $RED "❌ Nginx配置测试失败"
            exit 1
        fi

        # 启动nginx
        sudo systemctl start nginx
        if systemctl is-active --quiet nginx; then
            print_message $GREEN "✅ Nginx重启成功"
        else
            print_message $RED "❌ Nginx重启失败"
            exit 1
        fi

        start_app

        print_message $GREEN "🎉 网站重启完成！"
        print_message $BLUE "📱 访问地址: https://localhost"
        ;;
    status)
        print_message $BLUE "📊 系统状态检查..."
        print_message $BLUE "=================="

        # 检查Node.js应用
        if pgrep -f "node.*bin/www" > /dev/null; then
            print_message $GREEN "✅ Node.js应用 - 运行中 (端口3000)"
        else
            print_message $RED "❌ Node.js应用 - 未运行"
        fi

        # 检查Nginx
        if systemctl is-active --quiet nginx; then
            print_message $GREEN "✅ Nginx服务 - 运行中"
        else
            print_message $RED "❌ Nginx服务 - 未运行"
        fi

        # 检查SSL证书
        if [ -f "ssl/private.key" ] && [ -f "ssl/certificate.crt" ]; then
            print_message $GREEN "✅ SSL证书 - 已配置"
        else
            print_message $YELLOW "⚠️  SSL证书 - 未配置"
        fi

        # 显示访问地址
        print_message $BLUE ""
        print_message $BLUE "🌐 访问地址:"
        if systemctl is-active --quiet nginx; then
            echo "   🔒 主站 (HTTPS): https://localhost"
            echo "   📰 新闻页面: https://localhost/news/index"
            echo "   🔐 管理登录: https://localhost/admin/login"
        fi
        if pgrep -f "node.*bin/www" > /dev/null; then
            echo "   🔧 直接访问: http://localhost:3000"
        fi
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status}"
        echo ""
        print_message $GREEN "💡 荣联科技网站运行脚本 (兼容模式)"
        echo ""
        echo "基础命令:"
        echo "  start    启动网站 (Nginx + Node.js)"
        echo "  stop     停止网站"
        echo "  restart  重启网站"
        echo "  status   查看系统状态"
        echo ""
        print_message $YELLOW "🚀 高级功能:"
        echo "   使用 './deploy.sh' 获得更多功能:"
        echo "   - HTTPS证书管理"
        echo "   - 详细的系统监控"
        echo "   - 新闻管理系统部署"
        echo "   - 运行 './deploy.sh help' 查看详细帮助"
        echo ""
        print_message $BLUE "📱 访问地址:"
        echo "   🔒 主站: https://localhost"
        echo "   🔐 管理: https://localhost/admin/login"
        echo ""
        exit 1
        ;;
esac

exit 0
