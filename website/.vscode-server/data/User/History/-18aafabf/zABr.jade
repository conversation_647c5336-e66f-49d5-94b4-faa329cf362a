extends layout

block css
  link(rel='stylesheet', href='/plugins/admin/css/admin.css')
  // TinyMCE样式 (替换Quill样式)
  style.
    .tox-tinymce {
      border: 1px solid #ddd !important;
      border-radius: 4px !important;
    }
    .tox .tox-toolbar {
      background: #f8f9fa !important;
    }
    // Word导入/导出按钮样式
    .word-actions {
      margin-bottom: 10px;
      padding: 10px;
      background: #f0f8ff;
      border: 1px solid #d1ecf1;
      border-radius: 4px;
    }
    .word-actions .btn {
      margin-right: 10px;
    }
  style.
    .image-preview {
      max-width: 200px;
      max-height: 200px;
      border: 1px solid #ddd;
      border-radius: 4px;
      margin-top: 10px;
    }
    .upload-area {
      border: 2px dashed #ccc;
      border-radius: 4px;
      padding: 20px;
      text-align: center;
      cursor: pointer;
      transition: border-color 0.3s;
    }
    .upload-area:hover {
      border-color: #667eea;
    }
    .upload-area.dragover {
        border-color: #667eea;
        background-color: #f8f9fa;
      }
      #editor {
        height: 300px;
      }
      .form-actions {
        background-color: #f8f9fa;
        padding: 20px;
        margin: 20px -15px -15px -15px;
        border-top: 1px solid #ddd;
      }
      /* 新增样式：优化上下布局 */
      .panel {
        margin-bottom: 20px;
      }
      .panel-title {
        font-size: 16px;
        font-weight: 600;
      }
      .row {
        margin-bottom: 15px;
      }
      /* 确保发布设置和封面图片面板高度一致 */
      .col-md-6 .panel {
        height: 100%;
      }
      .col-md-6 .panel-body {
        min-height: 200px;
      }

block content
  .container-fluid
      .row
        .col-md-12
          h1.page-header 
            | ✏️ #{newsId ? '编辑新闻' : '创建新闻'}
            .pull-right
              a.btn.btn-default(href="/admin/news") 
                i.glyphicon.glyphicon-arrow-left
                |  返回列表

      form#newsForm
        // 基本信息 - 上半部分
        .row
          .col-md-12
            .panel.panel-default
              .panel-heading
                h3.panel-title 📝 基本信息
              .panel-body
                .form-group
                  label(for="title") 新闻标题 *
                  input.form-control#title(type="text", name="title", required, placeholder="请输入新闻标题")

                .form-group
                  label(for="content") 新闻内容 *

                  // Word导入/导出功能区 (新增)
                  .word-actions
                    button.btn.btn-info.btn-sm#importWordBtn(type="button")
                      i.glyphicon.glyphicon-import
                      |  导入Word文档
                    button.btn.btn-success.btn-sm#exportWordBtn(type="button")
                      i.glyphicon.glyphicon-export
                      |  导出为Word
                    button.btn.btn-warning.btn-sm#previewWebBtn(type="button")
                      i.glyphicon.glyphicon-globe
                      |  预览网页效果
                    input#wordFileInput(type="file", accept=".doc,.docx", style="display: none;")
                    br
                    small.text-muted
                      strong 功能说明：
                      | 导入Word文档进行编辑，
                      strong 发布后自动转为网页格式
                      | 显示。Word导出仅用于备份编辑内容。

                  // 编辑器容器 (保持原有尺寸)
                  #editor(style="height: 300px;")
                  textarea#content(name="content", style="display: none;")

        // 发布设置和封面图片 - 下半部分
        .row
          .col-md-6
            // 发布设置
            .panel.panel-default
              .panel-heading
                h3.panel-title 🚀 发布设置
              .panel-body
                .form-group
                  label(for="status") 状态
                  select.form-control#status(name="status")
                    option(value="draft") 草稿
                    option(value="published") 发布
                    option(value="unpublished") 下架
                    option(value="archived") 归档

                .form-group
                  label(for="author") 作者
                  input.form-control#author(type="text", name="author", value="admin")

          .col-md-6
            // 封面图片
            .panel.panel-default
              .panel-heading
                h3.panel-title 🖼️ 封面图片
              .panel-body
                .form-group
                  label 上传图片
                  .upload-area#uploadArea
                    i.glyphicon.glyphicon-cloud-upload(style="font-size: 2em; color: #ccc;")
                    p 点击或拖拽图片到此处上传
                    p.text-muted 支持 JPG、PNG、GIF 格式，最大 5MB
                  input#imageInput(type="file", accept="image/*", style="display: none;")
                  input#picMgid(type="hidden", name="picMgid")
                  #imagePreview

        .form-actions
          .row
            .col-md-12.text-right
              button.btn.btn-default#saveDraftBtn(type="button") 保存草稿
              button.btn.btn-success#savePublishBtn(type="button") 保存并发布
              if newsId
                button.btn.btn-danger#deleteBtn(type="button") 删除新闻

    // 确认删除模态框
    .modal.fade#deleteModal(tabindex="-1", role="dialog")
      .modal-dialog(role="document")
        .modal-content
          .modal-header
            button.close(type="button", data-dismiss="modal")
              span &times;
            h4.modal-title 确认删除
          .modal-body
            p 确定要删除这条新闻吗？此操作不可恢复。
          .modal-footer
            button.btn.btn-default(type="button", data-dismiss="modal") 取消
            button.btn.btn-danger#confirmDelete 确认删除

block scripts
  // TinyMCE编辑器 (替换Quill)
  script(src="https://cdn.jsdelivr.net/npm/tinymce@6/tinymce.min.js")
  // Word处理库
  script(src="https://cdn.jsdelivr.net/npm/mammoth@1.6.0/mammoth.browser.min.js")
  script(src="https://cdn.jsdelivr.net/npm/html-docx-js@0.3.1/dist/html-docx.js")
  // 升级后的编辑器脚本
  script(src="/plugins/admin/js/newsEdit.js")
  script.
    // 传递新闻ID到前端
    window.newsId = '#{newsId}';
    window.currentUser = !{JSON.stringify(user || {})};

    // 初始化页面
    $(document).ready(function() {
      // 设置作者为当前用户
      if (window.currentUser && window.currentUser.name) {
        $('#author').val(window.currentUser.name);
      }

      // 如果是编辑模式，加载新闻数据
      if (window.newsId) {
        loadNewsData(window.newsId);
      }
    });
