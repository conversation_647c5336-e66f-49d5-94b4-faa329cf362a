extends layout

block css
  link(rel='stylesheet', href='/plugins/admin/css/admin.css')
  // TinyMCE样式 (替换Quill样式，保持原有编辑器外观)
  style.
    .tox-tinymce {
      border: 1px solid #ddd !important;
      border-radius: 4px !important;
    }
    .tox .tox-toolbar {
      background: #f8f9fa !important;
    }
    // 保持原有布局，Word功能按钮样式最小化
    .help-block .btn-xs {
      padding: 1px 5px;
      font-size: 11px;
      line-height: 1.5;
    }
  style.
    .image-preview {
      max-width: 200px;
      max-height: 200px;
      border: 1px solid #ddd;
      border-radius: 4px;
      margin-top: 10px;
    }
    .upload-area {
      border: 2px dashed #ccc;
      border-radius: 4px;
      padding: 20px;
      text-align: center;
      cursor: pointer;
      transition: border-color 0.3s;
    }
    .upload-area:hover {
      border-color: #667eea;
    }
    .upload-area.dragover {
        border-color: #667eea;
        background-color: #f8f9fa;
      }
      #editor {
        height: 300px;
      }
      .form-actions {
        background-color: #f8f9fa;
        padding: 20px;
        margin: 20px -15px -15px -15px;
        border-top: 1px solid #ddd;
      }
      /* 新增样式：优化上下布局 */
      .panel {
        margin-bottom: 20px;
      }
      .panel-title {
        font-size: 16px;
        font-weight: 600;
      }
      .row {
        margin-bottom: 15px;
      }
      /* 确保发布设置和封面图片面板高度一致 */
      .col-md-6 .panel {
        height: 100%;
      }
      .col-md-6 .panel-body {
        min-height: 200px;
      }

block content
  .container-fluid
      .row
        .col-md-12
          h1.page-header 
            | ✏️ #{newsId ? '编辑新闻' : '创建新闻'}
            .pull-right
              a.btn.btn-default(href="/admin/news") 
                i.glyphicon.glyphicon-arrow-left
                |  返回列表

      form#newsForm
        // 基本信息 - 上半部分
        .row
          .col-md-12
            .panel.panel-default
              .panel-heading
                h3.panel-title 📝 基本信息
              .panel-body
                .form-group
                  label(for="title") 新闻标题 *
                  input.form-control#title(type="text", name="title", required, placeholder="请输入新闻标题")

                .form-group
                  label(for="content") 新闻内容 *

                  // 编辑器容器 (保持原有尺寸)
                  #editor(style="height: 300px;")
                  textarea#content(name="content", style="display: none;")

                  // Word功能区 (以小字体提示形式添加，不改变原有布局)
                  .help-block.small(style="margin-top: 5px;")
                    span.text-muted 支持Word文档:
                    a.btn.btn-xs.btn-info#importWordBtn(href="javascript:void(0);", style="margin-right: 5px;", onclick="document.getElementById('wordFileInput').click(); return false;")
                      i.glyphicon.glyphicon-import
                      |  导入
                    a.btn.btn-xs.btn-success#exportWordBtn(href="javascript:void(0);", style="margin-right: 5px;", onclick="if(typeof exportToWord === 'function') { exportToWord(); } else { alert('导出功能未加载'); } return false;")
                      i.glyphicon.glyphicon-export
                      |  导出
                    a.btn.btn-xs.btn-default#previewWebBtn(href="javascript:void(0);", onclick="if(typeof previewWebFormat === 'function') { previewWebFormat(); } else { alert('预览功能未加载'); } return false;")
                      i.glyphicon.glyphicon-eye-open
                      |  预览
                    button.btn.btn-xs.btn-warning#debugBtn(type="button", style="margin-left: 10px;", onclick="if(typeof showDebugInfo === 'function') { showDebugInfo(); } else { alert('调试功能未加载'); }")
                      i.glyphicon.glyphicon-wrench
                      |  调试
                    input#wordFileInput(type="file", accept=".doc,.docx", style="display: none;", onchange="if(this.files[0] && typeof importWordDocument === 'function') { importWordDocument(this.files[0]); } else if(this.files[0]) { alert('Word导入功能未加载'); }")

        // 发布设置和封面图片 - 下半部分
        .row
          .col-md-6
            // 发布设置
            .panel.panel-default
              .panel-heading
                h3.panel-title 🚀 发布设置
              .panel-body
                .form-group
                  label(for="status") 状态
                  select.form-control#status(name="status")
                    option(value="draft") 草稿
                    option(value="published") 发布
                    option(value="unpublished") 下架
                    option(value="archived") 归档

                .form-group
                  label(for="author") 作者
                  input.form-control#author(type="text", name="author", value="admin")

          .col-md-6
            // 封面图片
            .panel.panel-default
              .panel-heading
                h3.panel-title 🖼️ 封面图片
              .panel-body
                .form-group
                  label 上传图片
                  .upload-area#uploadArea
                    i.glyphicon.glyphicon-cloud-upload(style="font-size: 2em; color: #ccc;")
                    p 点击或拖拽图片到此处上传
                    p.text-muted 支持 JPG、PNG、GIF 格式，最大 5MB
                  input#imageInput(type="file", accept="image/*", style="display: none;")
                  input#picMgid(type="hidden", name="picMgid")
                  #imagePreview

        .form-actions
          .row
            .col-md-12.text-right
              button.btn.btn-default#saveDraftBtn(type="button") 保存草稿
              button.btn.btn-success#savePublishBtn(type="button") 保存并发布
              if newsId
                button.btn.btn-danger#deleteBtn(type="button") 删除新闻

    // 确认删除模态框
    .modal.fade#deleteModal(tabindex="-1", role="dialog")
      .modal-dialog(role="document")
        .modal-content
          .modal-header
            button.close(type="button", data-dismiss="modal")
              span &times;
            h4.modal-title 确认删除
          .modal-body
            p 确定要删除这条新闻吗？此操作不可恢复。
          .modal-footer
            button.btn.btn-default(type="button", data-dismiss="modal") 取消
            button.btn.btn-danger#confirmDelete 确认删除

block scripts
  // TinyMCE编辑器 (替换Quill)
  script(src="https://cdn.jsdelivr.net/npm/tinymce@6/tinymce.min.js")
  // Word处理库
  script(src="https://cdn.jsdelivr.net/npm/mammoth@1.6.0/mammoth.browser.min.js")
  script(src="https://cdn.jsdelivr.net/npm/html-docx-js@0.3.1/dist/html-docx.js")
  // 升级后的编辑器脚本
  script(src="/plugins/admin/js/newsEdit.js")

  // 内联备份函数 (以防主脚本中的函数不可用)
  script.
    // 备份Word导入函数
    function backupImportWordDocument(file) {
      if (!file) return;

      console.log('使用备份导入函数');
      alert('正在导入Word文档，请稍候...');

      const reader = new FileReader();
      reader.onload = function(e) {
        const arrayBuffer = e.target.result;

        mammoth.convertToHtml({arrayBuffer: arrayBuffer})
          .then(function(result) {
            const html = result.value;

            // 将Word内容插入到编辑器
            if (window.tinymce && tinymce.activeEditor) {
              tinymce.activeEditor.setContent(html);
              $('#content').val(html);
              alert('Word文档导入成功！');
            } else {
              alert('编辑器未初始化，无法导入内容');
            }
          })
          .catch(function(error) {
            console.error('Word导入错误:', error);
            alert('Word文档导入失败：' + error.message);
          });
      };

      reader.readAsArrayBuffer(file);
    }

    // 备份Word导出函数
    function backupExportToWord() {
      console.log('使用备份导出函数');

      let content = '';
      let title = $('#title').val() || '新闻文档';

      // 尝试从编辑器获取内容
      if (window.tinymce && tinymce.activeEditor) {
        content = tinymce.activeEditor.getContent();
      } else {
        content = $('#content').val() || '<p>无法获取编辑器内容</p>';
      }

      // 创建完整的HTML文档
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>${title}</title>
          <style>
            body { font-family: "Microsoft YaHei", Arial, sans-serif; line-height: 1.6; margin: 40px; }
            h1, h2, h3, h4, h5, h6 { color: #333; margin: 20px 0 10px 0; }
            p { margin: 10px 0; }
            table { border-collapse: collapse; width: 100%; margin: 10px 0; }
            table, th, td { border: 1px solid #ddd; }
            th, td { padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
            img { max-width: 100%; height: auto; }
          </style>
        </head>
        <body>
          <h1>${title}</h1>
          ${content}
        </body>
        </html>
      `;

      try {
        // 转换为Word文档
        const converted = htmlDocx.asBlob(htmlContent);

        // 下载文件
        const link = document.createElement('a');
        link.href = URL.createObjectURL(converted);
        link.download = `${title}.docx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        alert('Word文档导出成功！');
      } catch (error) {
        console.error('Word导出错误:', error);
        alert('Word文档导出失败：' + error.message);
      }
    }

    // 备份网页预览函数
    function backupPreviewWebFormat() {
      console.log('使用备份预览函数');

      let content = '';
      let title = $('#title').val() || '预览';

      // 尝试从编辑器获取内容
      if (window.tinymce && tinymce.activeEditor) {
        content = tinymce.activeEditor.getContent();
      } else {
        content = $('#content').val() || '<p>无法获取编辑器内容</p>';
      }

      // 打开预览窗口
      const previewWindow = window.open('', '_blank', 'width=1000,height=700');
      previewWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>网页格式预览 - ${title}</title>
          <link rel="stylesheet" href="/plugins/bootstrap/css/bootstrap.min.css">
          <style>
            body {
              font-family: "Microsoft YaHei", Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 1000px;
              margin: 0 auto;
              padding: 20px;
            }
            .preview-header {
              background-color: #f8f9fa;
              padding: 20px;
              margin-bottom: 20px;
              border-bottom: 1px solid #ddd;
            }
            .preview-content {
              padding: 20px;
            }
            img {
              max-width: 100%;
              height: auto;
            }
          </style>
        </head>
        <body>
          <div class="preview-header">
            <h1>${title}</h1>
            <p class="text-muted">预览模式 - 显示网页发布后的效果</p>
          </div>
          <div class="preview-content">
            ${content}
          </div>
        </body>
        </html>
      `);
    }

    // 备份调试信息函数
    function backupShowDebugInfo() {
      console.log('使用备份调试函数');

      const debugInfo = {
        '编辑器状态': (window.tinymce && tinymce.activeEditor) ? '已初始化' : '未初始化',
        'jQuery版本': (typeof $ !== 'undefined') ? $.fn.jquery : '未加载',
        'TinyMCE可用': (typeof tinymce !== 'undefined') ? '是' : '否',
        'Mammoth可用': (typeof mammoth !== 'undefined') ? '是' : '否',
        'htmlDocx可用': (typeof htmlDocx !== 'undefined') ? '是' : '否',
        '导入按钮存在': $('#importWordBtn').length > 0 ? '是' : '否',
        '导出按钮存在': $('#exportWordBtn').length > 0 ? '是' : '否',
        '预览按钮存在': $('#previewWebBtn').length > 0 ? '是' : '否',
        '文件输入存在': $('#wordFileInput').length > 0 ? '是' : '否',
        '当前新闻ID': window.newsId || '无'
      };

      let debugHtml = '<h4>调试信息</h4><table class="table table-bordered table-condensed"><tbody>';
      for (const key in debugInfo) {
        debugHtml += `<tr><td><strong>${key}</strong></td><td>${debugInfo[key]}</td></tr>`;
      }
      debugHtml += '</tbody></table>';

      alert('调试信息已输出到控制台');
      console.table(debugInfo);
    }
  script.
    // 传递新闻ID到前端
    window.newsId = '#{newsId}';
    window.currentUser = !{JSON.stringify(user || {})};

    // 初始化页面
    $(document).ready(function() {
      console.log('页面DOM加载完成');
      console.log('newsId:', window.newsId);
      console.log('currentUser:', window.currentUser);

      // 设置作者为当前用户
      if (window.currentUser && window.currentUser.name) {
        $('#author').val(window.currentUser.name);
      }

      // 数据加载由newsEdit.js中的编辑器初始化完成后处理
      // 这里不再重复调用loadNewsData

      // 确保Word功能在全局作用域可用
      setTimeout(function() {
        console.log('检查Word功能是否在全局作用域可用:');
        console.log('importWordDocument:', typeof window.importWordDocument);
        console.log('exportToWord:', typeof window.exportToWord);
        console.log('previewWebFormat:', typeof window.previewWebFormat);
        console.log('showDebugInfo:', typeof window.showDebugInfo);

        // 如果函数不在全局作用域，尝试添加
        if (typeof window.importWordDocument !== 'function' && typeof importWordDocument === 'function') {
          window.importWordDocument = importWordDocument;
        }
        if (typeof window.exportToWord !== 'function' && typeof exportToWord === 'function') {
          window.exportToWord = exportToWord;
        }
        if (typeof window.previewWebFormat !== 'function' && typeof previewWebFormat === 'function') {
          window.previewWebFormat = previewWebFormat;
        }
        if (typeof window.showDebugInfo !== 'function' && typeof showDebugInfo === 'function') {
          window.showDebugInfo = showDebugInfo;
        }
      }, 1000);
    });
