var express = require('express');
var path = require('path');
var router = express.Router();
var authRestApiProxy = require("../libs/authRestApiProxy");
var log4js = require("../libs/log4jsUtil.js");
var constants = require("../libs/constants.js");
var merchantUtil = require("../libs/merchantUtil.js");
var logger = log4js.getLogger('index');
var async = require('async');
var config = require('../libs/config');
var imgDomain = config.ImageService.domain;
var title = '首页';

router.get('/', function (req, res, next) {
	var pageTabsUrl = "homepage";
	async.waterfall([
		function (callback) {
			merchantUtil.getMerchantAllInfo(req,function (err, merchantData) {
				callback(err, merchantData);
			});
		},
		function (merchantData, callback) {
	        var pageTabsId ='', err = null;
			if(merchantData.pageTabs && merchantData.pageTabs.length > 0) {
				merchantData.pageTabs.forEach(function (item) {
				    if(item.tabsName == '首页' && item.modulesUrl == pageTabsUrl){
				        pageTabsId = item.tabsId;
				    }
				});
				if(pageTabsId){
					callback(err, merchantData, pageTabsId);
                }else{
					err = {pageTabsId: undefined};
					callback(err, null);
                }

			}else{
				err = {pageTabsId: undefined};
				callback(err, null);
            }
		},
		function (merchantData, pageTabsId, callback) {
			var reqVo = {
				tabsId : pageTabsId
			};
			authRestApiProxy.post('RetailerService', '/page/tabs/q', reqVo, function resultData(err, tabData) {
				callback(err, merchantData, pageTabsId, tabData);
			});
		},
		function (merchantData, pageTabsId, tabData, callback) {
			var innerReqVo = {};
			innerReqVo.tabsId = pageTabsId;
			innerReqVo.showMark = '1';
			authRestApiProxy.post('RetailerService', '/content/block/q', innerReqVo, function resultData(err, contentData) {
				callback(err, merchantData, pageTabsId, tabData, contentData);
			});
		},
		function (merchantData, pageTabsId, tabData, contentData, callback) {
            var ptType = pageTabsUrl;
            if(ptType == 'homepage'){
                authRestApiProxy.post('RetailerService', '/news/n/l/5', {}, function resultData(err, nData) {
                    callback(err, merchantData, pageTabsId, tabData, contentData, nData);
                });
            }else{
                callback(null,merchantData, pageTabsId, tabData, contentData, null);
            }
		},
		function (merchantData, pageTabsId, tabData, contentData, nData, callback) {
			authRestApiProxy.post('RetailerService', '/page/tabs/pq', {'tabsId':pageTabsId}, function resultData(err, kData) {
				callback(err, merchantData, pageTabsId, tabData,contentData,nData, kData);
			});
		},
        function (merchantData, pageTabsId, tabData, contentData, nData, kData, callback) {
            authRestApiProxy.post('RetailerService', '/page/tabs/rrtn', {'tabsId':pageTabsId,newsLimit:8,proLimit:6}, function resultData(err, electData) {
                merchantData.electData = electData;
                callback(err, merchantData, pageTabsId, tabData,contentData, nData, kData);
            });
        }
	],function (err, merchantData, pageTabsId, tabData, contentData, nData, kData) {
		if(err){
			logger.info(err.message);
			logger.info(err.code);
			res.render('errordiv');
		}else {
			merchantData.kData = kData;
            merchantData.title = kData.title;
			merchantData.unitModuleList = contentData;
			merchantData.pagetabsID = pageTabsId;
			merchantData.newsData = nData;
            merchantData.topTabsId = pageTabsId;
			if(tabData){
				merchantData.ptType = tabData[0].modulesUrl;
				merchantData.pageTabsUrl = tabData[0].modulesUrl;
			}
            merchantData.imgDomainLO = imgDomain;
			logger.debug(merchantData);
			res.render("layout", merchantData);
		}

	});
});

//转发跳转
router.get('/index/:url/', function (req, res, next) {
    merchantUtil.getMerchantAllInfo(req, function (err, menuData) {
        logger.info("原始:" + req.params.url);
        var url = req.params.url.replace(/[.]/g, "/");
        logger.info("替换:" + url);
        var data = menuData;
        data.topTabsId = req.query.topTabsId;
        res.render("website/" + url, data);
    });
});
//英文版
router.get('/en', function (req, res, next) {
    res.render("public/dest/en/index.html");
});

// Vue 3 test routes
router.get('/vue3-test', function (req, res, next) {
    res.render("website/vue3-test");
});

router.get('/vue3-home', function (req, res, next) {
    res.render("website/vue3-home");
});

router.get('/vue3-simple', function (req, res, next) {
    res.render("website/vue3-simple");
});

router.get('/word-editor-test', function (req, res, next) {
    console.log('访问word-editor-test路由');
    res.render("admin/word-editor-test");
});

router.get('/test-news-edit', function (req, res, next) {
    console.log('访问test-news-edit路由');
    res.sendFile(path.join(__dirname, '../test-news-edit.html'));
});

router.get('/test-word-buttons', function (req, res, next) {
    console.log('访问test-word-buttons路由');
    res.sendFile(path.join(__dirname, '../test-word-buttons.html'));
});

module.exports = router;
