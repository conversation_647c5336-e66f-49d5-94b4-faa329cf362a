#!/bin/bash

# 修复502错误 - Node.js升级到20.x
# 在dl380主机上直接执行的脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

echo -e "${CYAN}🔧 修复502错误 - Node.js升级到20.x${NC}"
echo -e "${CYAN}======================================${NC}"
echo "执行主机: $(hostname)"
echo "当前用户: $(whoami)"
echo "当前时间: $(date)"
echo ""

# 步骤1: 停止现有进程
log_step "停止现有Node.js进程"
log_info "查找并停止Node.js相关进程..."

# 显示当前运行的Node.js进程
EXISTING_PROCESSES=$(ps aux | grep -E '(node|npm)' | grep -v grep || echo "")
if [ -n "$EXISTING_PROCESSES" ]; then
    log_info "发现以下Node.js/npm进程:"
    echo "$EXISTING_PROCESSES"
fi

# 停止进程
pkill -f 'node' 2>/dev/null || true
pkill -f 'npm' 2>/dev/null || true
sleep 3

log_success "Node.js进程已停止"

# 步骤2: 检查当前Node.js版本
log_step "检查当前Node.js版本"

if command -v node &> /dev/null; then
    CURRENT_NODE_VERSION=$(node --version 2>/dev/null || echo "未知")
    log_info "当前Node.js版本: $CURRENT_NODE_VERSION"
else
    log_info "Node.js未安装或不在PATH中"
fi

# 步骤3: 安装NVM
log_step "安装/配置NVM"

if [ ! -d "$HOME/.nvm" ]; then
    log_info "NVM未安装，正在下载安装..."
    curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
    log_success "NVM安装完成"
else
    log_info "NVM已存在，跳过安装"
fi

# 加载NVM环境
log_info "加载NVM环境..."
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"

# 验证NVM是否可用
if command -v nvm &> /dev/null; then
    log_success "NVM加载成功"
    log_info "NVM版本: $(nvm --version)"
else
    log_error "NVM加载失败，尝试手动加载..."
    source ~/.bashrc 2>/dev/null || true
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    
    if command -v nvm &> /dev/null; then
        log_success "NVM手动加载成功"
    else
        log_error "NVM加载失败，请手动执行: source ~/.bashrc"
        exit 1
    fi
fi

# 步骤4: 安装Node.js 20.x
log_step "安装Node.js 20.x"

log_info "查看可用的Node.js版本..."
nvm list-remote --lts | tail -10

log_info "安装Node.js 20.x LTS..."
nvm install 20
nvm use 20
nvm alias default 20

# 验证安装
NEW_NODE_VERSION=$(node --version)
NEW_NPM_VERSION=$(npm --version)

log_success "Node.js安装完成"
echo "新的Node.js版本: $NEW_NODE_VERSION"
echo "新的npm版本: $NEW_NPM_VERSION"
echo "Node.js路径: $(which node)"
echo "npm路径: $(which npm)"

# 检查版本是否符合要求
if [[ $NEW_NODE_VERSION =~ ^v20\. ]]; then
    log_success "Node.js版本符合要求"
else
    log_warning "Node.js版本可能不符合要求: $NEW_NODE_VERSION"
fi

# 步骤5: 进入应用目录
log_step "进入应用目录"

APP_DIR="/home/<USER>/ronglianweb"
if [ ! -d "$APP_DIR" ]; then
    log_error "应用目录不存在: $APP_DIR"
    exit 1
fi

cd "$APP_DIR"
log_info "当前目录: $(pwd)"

# 检查关键文件
if [ ! -f "package.json" ]; then
    log_error "package.json文件不存在"
    exit 1
fi

log_info "应用目录检查完成"

# 步骤6: 重新安装依赖
log_step "重新安装应用依赖"

log_info "清理旧的依赖文件..."
rm -rf node_modules package-lock.json 2>/dev/null || true

log_info "安装应用依赖..."
npm install --production

if [ $? -eq 0 ]; then
    log_success "依赖安装成功"
else
    log_error "依赖安装失败"
    exit 1
fi

# 步骤7: 设置权限
log_step "设置文件权限"

mkdir -p logs
chmod +x run.sh deploy.sh deploy-https.sh 2>/dev/null || true
chmod 755 logs

# 检查SSL证书
if [ -f "ssl/private.key" ] && [ -f "ssl/certificate.crt" ]; then
    chmod 600 ssl/private.key
    chmod 644 ssl/certificate.crt
    log_info "SSL证书权限已设置"
fi

log_success "文件权限设置完成"

# 步骤8: 启动应用
log_step "启动Node.js应用"

log_info "启动应用..."
nohup npm start > logs/app.log 2>&1 &

# 获取启动的进程ID
sleep 2
APP_PID=$(pgrep -f 'node.*bin/www' || echo "")

if [ -n "$APP_PID" ]; then
    log_info "应用已启动，PID: $APP_PID"
else
    log_warning "应用可能启动失败，等待更长时间..."
fi

# 等待应用完全启动
log_info "等待应用完全启动..."
for i in {1..15}; do
    echo -n "."
    sleep 1
done
echo ""

# 步骤9: 检查应用状态
log_step "检查应用状态"

# 检查进程
NODE_PROCESSES=$(ps aux | grep 'node.*bin/www' | grep -v grep || echo "")
if [ -n "$NODE_PROCESSES" ]; then
    log_success "Node.js应用正在运行"
    echo "$NODE_PROCESSES"
else
    log_error "Node.js应用未运行"
    echo ""
    log_info "查看启动日志:"
    tail -20 logs/app.log 2>/dev/null || echo "无法读取日志文件"
    exit 1
fi

# 检查端口3000
PORT_CHECK=$(netstat -tlnp | grep :3000 || echo "")
if [ -n "$PORT_CHECK" ]; then
    log_success "端口3000正在监听"
    echo "$PORT_CHECK"
else
    log_error "端口3000未监听"
fi

# 步骤10: 测试访问
log_step "测试应用访问"

# 测试本地3000端口
log_info "测试本地3000端口..."
HTTP_CODE=$(curl -s -o /dev/null -w '%{http_code}' http://localhost:3000 2>/dev/null || echo "Failed")
echo "HTTP 3000端口测试: $HTTP_CODE"

# 测试HTTPS访问
log_info "测试HTTPS访问..."
HTTPS_CODE=$(curl -s -o /dev/null -w '%{http_code}' -k https://localhost 2>/dev/null || echo "Failed")
echo "HTTPS访问测试: $HTTPS_CODE"

# 测试健康检查
log_info "测试健康检查..."
HEALTH_CHECK=$(curl -s -k https://localhost/health 2>/dev/null || echo "Failed")
echo "健康检查结果: $HEALTH_CHECK"

# 步骤11: 最终结果
echo ""
echo -e "${CYAN}========================================${NC}"
echo -e "${CYAN}🎯 修复结果总结${NC}"
echo -e "${CYAN}========================================${NC}"

# 判断修复是否成功
if [[ $NEW_NODE_VERSION =~ ^v20\. ]] && [ -n "$NODE_PROCESSES" ] && [ -n "$PORT_CHECK" ] && ([ "$HTTPS_CODE" = "200" ] || [ "$HTTPS_CODE" = "301" ] || [ "$HTTPS_CODE" = "302" ]); then
    echo -e "${GREEN}🎉 502错误修复完全成功！${NC}"
    echo ""
    echo -e "${GREEN}✅ 修复状态:${NC}"
    echo "   Node.js版本: $NEW_NODE_VERSION"
    echo "   npm版本: $NEW_NPM_VERSION"
    echo "   应用状态: 正在运行"
    echo "   端口3000: 正在监听"
    echo "   HTTPS访问: 正常 ($HTTPS_CODE)"
    echo ""
    echo -e "${CYAN}🌐 访问地址:${NC}"
    echo "   🔒 主站 (HTTPS): https://*********"
    echo "   📰 新闻页面: https://*********/news/index"
    echo "   🔐 管理登录: https://*********/admin/login"
    echo "   🔧 直接访问: http://*********:3000"
    echo ""
    echo -e "${CYAN}🔐 登录信息:${NC}"
    echo "   管理员: admin / admin123"
    echo "   编辑员: user01 / user123"
    echo ""
    echo -e "${GREEN}🎊 现在可以正常访问网站了！${NC}"
    
elif [[ $NEW_NODE_VERSION =~ ^v20\. ]] && [ -n "$NODE_PROCESSES" ]; then
    echo -e "${YELLOW}⚠️ Node.js升级成功，应用已启动，但HTTPS访问可能有问题${NC}"
    echo ""
    echo "请检查Nginx配置或手动测试访问"
    
elif [[ $NEW_NODE_VERSION =~ ^v20\. ]]; then
    echo -e "${YELLOW}⚠️ Node.js升级成功，但应用未正常启动${NC}"
    echo ""
    echo "请查看错误日志: tail -50 logs/app.log"
    echo "或手动启动: npm start"
    
else
    echo -e "${RED}❌ 修复失败${NC}"
    echo ""
    echo "问题诊断:"
    echo "Node.js版本: $NEW_NODE_VERSION"
    echo "应用进程: $([ -n "$NODE_PROCESSES" ] && echo '运行中' || echo '未运行')"
    echo "端口3000: $([ -n "$PORT_CHECK" ] && echo '监听中' || echo '未监听')"
    echo "HTTPS访问: $HTTPS_CODE"
fi

echo ""
echo -e "${BLUE}📝 重要提示:${NC}"
echo "1. Node.js现在通过NVM管理"
echo "2. 重启系统后需要重新加载NVM: source ~/.bashrc"
echo "3. 应用日志位置: $APP_DIR/logs/app.log"
echo "4. 如需手动操作，请先运行: source ~/.bashrc"

echo ""
echo -e "${CYAN}修复脚本执行完成！${NC}"
