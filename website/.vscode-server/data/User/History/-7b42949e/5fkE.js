// 新闻编辑JavaScript - Word兼容版本
$(document).ready(function() {
    let editor;
    let isEditing = !!window.newsId;

    // 初始化
    init();

    function init() {
        initTinyMCE();
        bindEvents();

        // 数据加载现在在编辑器初始化完成后进行
    }

    // 初始化TinyMCE编辑器 (替换Quill)
    function initTinyMCE() {
        tinymce.init({
            selector: '#editor',
            height: 300,
            language: 'zh_CN',
            plugins: [
                'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                'insertdatetime', 'media', 'table', 'help', 'wordcount', 'paste'
            ],
            toolbar: [
                'undo redo | blocks | bold italic underline strikethrough | alignleft aligncenter alignright alignjustify',
                'bullist numlist outdent indent | removeformat | table link image media | code preview fullscreen'
            ].join(' | '),
            paste_data_images: true,
            paste_word_valid_elements: "b,strong,i,em,h1,h2,h3,h4,h5,h6,p,ol,ul,li,a[href],span,color,font-size,font-family,mark,table,tr,td,th",
            paste_retain_style_properties: "all",
            paste_merge_formats: true,
            automatic_uploads: true,
            file_picker_types: 'image',
            file_picker_callback: function(callback, value, meta) {
                if (meta.filetype === 'image') {
                    const input = document.createElement('input');
                    input.setAttribute('type', 'file');
                    input.setAttribute('accept', 'image/*');
                    input.onchange = function() {
                        const file = this.files[0];
                        uploadImage(file, callback);
                    };
                    input.click();
                }
            },
            setup: function(ed) {
                editor = ed;
                ed.on('change', function() {
                    $('#content').val(ed.getContent());
                });

                // 编辑器初始化完成后加载数据
                ed.on('init', function() {
                    console.log('TinyMCE编辑器初始化完成');
                    editor.initialized = true;
                    if (isEditing && window.newsId) {
                        console.log('编辑模式，开始加载新闻数据');
                        loadNewsData();
                    }
                });
            }
        });
    }

    // Word文档导入功能
    function importWordDocument(file) {
        if (!file) return;
        
        showMessage('正在导入Word文档，请稍候...', 'info');
        
        const reader = new FileReader();
        reader.onload = function(e) {
            const arrayBuffer = e.target.result;
            
            mammoth.convertToHtml({arrayBuffer: arrayBuffer})
                .then(function(result) {
                    const html = result.value;
                    
                    // 将Word内容插入到编辑器
                    if (editor) {
                        editor.setContent(html);
                        $('#content').val(html);
                    }
                    
                    // 显示转换消息
                    if (result.messages.length > 0) {
                        console.log('Word导入消息:', result.messages);
                    }
                    
                    showMessage('Word文档导入成功！', 'success');
                })
                .catch(function(error) {
                    console.error('Word导入错误:', error);
                    showMessage('Word文档导入失败：' + error.message, 'error');
                });
        };
        
        reader.readAsArrayBuffer(file);
    }

    // Word文档导出功能
    function exportToWord() {
        if (!editor) return;
        
        const content = editor.getContent();
        const title = $('#title').val() || '新闻文档';
        
        // 创建完整的HTML文档
        const htmlContent = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>${title}</title>
                <style>
                    body { font-family: "Microsoft YaHei", Arial, sans-serif; line-height: 1.6; margin: 40px; }
                    h1, h2, h3, h4, h5, h6 { color: #333; margin: 20px 0 10px 0; }
                    p { margin: 10px 0; }
                    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
                    table, th, td { border: 1px solid #ddd; }
                    th, td { padding: 8px; text-align: left; }
                    th { background-color: #f2f2f2; }
                    img { max-width: 100%; height: auto; }
                </style>
            </head>
            <body>
                <h1>${title}</h1>
                ${content}
            </body>
            </html>
        `;
        
        try {
            // 转换为Word文档
            const converted = htmlDocx.asBlob(htmlContent);
            
            // 下载文件
            const link = document.createElement('a');
            link.href = URL.createObjectURL(converted);
            link.download = `${title}.docx`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            showMessage('Word文档导出成功！', 'success');
        } catch (error) {
            console.error('Word导出错误:', error);
            showMessage('Word文档导出失败：' + error.message, 'error');
        }
    }

    // 绑定事件 (保持原有事件 + 新增Word功能)
    function bindEvents() {
        console.log('开始绑定事件...');

        // Word导入按钮
        $('#importWordBtn').on('click', function(e) {
            e.preventDefault();
            console.log('点击了导入按钮');
            $('#wordFileInput').click();
        });

        // Word文件选择
        $('#wordFileInput').on('change', function() {
            const file = this.files[0];
            console.log('选择了文件:', file);
            if (file) {
                importWordDocument(file);
            }
        });

        // Word导出按钮
        $('#exportWordBtn').on('click', function(e) {
            e.preventDefault();
            console.log('点击了导出按钮');
            exportToWord();
        });

        // 网页预览按钮
        $('#previewWebBtn').on('click', function(e) {
            e.preventDefault();
            console.log('点击了预览按钮');
            previewWebFormat();
        });
        
        // 保持原有的图片上传等事件
        $('#uploadArea').on('click', function() {
            $('#imageFile').click();
        });

        $('#imageFile').on('change', function() {
            const file = this.files[0];
            if (file) {
                uploadCoverImage(file);
            }
        });

        // 删除图片
        $('#removeImage').on('click', function() {
            $('#imagePreview').hide();
            $('#coverImage').val('');
            $('#uploadArea').show();
        });

        // 表单提交
        $('#newsForm').on('submit', function(e) {
            e.preventDefault();
            saveNews();
        });

        // 保存并发布
        $('#publishBtn').on('click', function() {
            $('#status').val('published');
            saveNews();
        });

        // 预览
        $('#previewBtn').on('click', function() {
            previewNews();
        });
    }

    // 加载新闻数据
    function loadNewsData() {
        if (!window.newsId) {
            console.log('没有新闻ID，跳过数据加载');
            return;
        }

        console.log('开始加载新闻数据，ID:', window.newsId);
        showMessage('正在加载新闻数据...', 'info');

        $.get(`/api/admin/news/${window.newsId}`)
            .done(function(response) {
                console.log('新闻数据加载响应:', response);
                if (response.success) {
                    const news = response.data;
                    console.log('新闻数据:', news);

                    // 填充表单数据
                    $('#title').val(news.title || '');
                    $('#status').val(news.status || 'draft');
                    $('#author').val(news.author || '');

                    // 设置编辑器内容
                    if (editor && editor.initialized) {
                        console.log('设置编辑器内容:', news.content);
                        editor.setContent(news.content || '');
                    } else {
                        console.log('编辑器未初始化，稍后重试');
                        // 如果编辑器还没初始化，等待一下再试
                        setTimeout(function() {
                            if (editor && editor.initialized) {
                                editor.setContent(news.content || '');
                            }
                        }, 1000);
                    }
                    $('#content').val(news.content || '');

                    // 设置封面图片
                    if (news.coverImage) {
                        $('#coverImage').val(news.coverImage);
                        $('#previewImg').attr('src', news.coverImage);
                        $('#imagePreview').show();
                        $('#uploadArea').hide();
                    }

                    showMessage('新闻数据加载成功', 'success');
                } else {
                    console.error('加载新闻数据失败:', response.message);
                    showMessage('加载新闻数据失败：' + response.message, 'error');
                }
            })
            .fail(function(xhr, status, error) {
                console.error('新闻数据加载请求失败:', xhr, status, error);
                showMessage('加载新闻数据失败：' + error, 'error');
            });
    }

    // 保存新闻
    function saveNews() {
        // 确保从编辑器获取最新内容
        if (editor) {
            $('#content').val(editor.getContent());
        }

        const formData = {
            title: $('#title').val(),
            content: $('#content').val(), // 这里保存的是HTML格式，用于网页显示
            status: $('#status').val(),
            author: $('#author').val(),
            coverImage: $('#coverImage').val()
        };

        // 验证必填字段
        if (!formData.title.trim()) {
            showMessage('请输入新闻标题', 'error');
            return;
        }

        if (!formData.content.trim()) {
            showMessage('请输入新闻内容', 'error');
            return;
        }

        // 显示保存状态
        showMessage('正在保存新闻...', 'info');

        const url = isEditing ? `/api/admin/news/${window.newsId}` : '/api/admin/news';
        const method = isEditing ? 'PUT' : 'POST';

        $.ajax({
            url: url,
            type: method,
            data: formData,
            success: function(response) {
                if (response.success) {
                    showMessage('新闻保存成功！内容已保存为网页格式', 'success');

                    // 如果是新建，跳转到编辑页面
                    if (!isEditing && response.data && response.data.id) {
                        setTimeout(function() {
                            window.location.href = `/admin/news/edit/${response.data.id}`;
                        }, 1500);
                    }
                } else {
                    showMessage('保存失败：' + response.message, 'error');
                }
            },
            error: function() {
                showMessage('保存失败，请重试', 'error');
            }
        });
    }

    // 预览新闻
    function previewNews() {
        const content = editor ? editor.getContent() : $('#content').val();
        const title = $('#title').val();

        if (!title || !content) {
            showMessage('请先填写标题和内容', 'error');
            return;
        }

        // 打开预览窗口
        const previewWindow = window.open('', '_blank', 'width=800,height=600');
        previewWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>预览 - ${title}</title>
                <link rel="stylesheet" href="/plugins/bootstrap/css/bootstrap.min.css">
                <style>
                    body { padding: 20px; font-family: "Microsoft YaHei", Arial, sans-serif; }
                    .preview-header { border-bottom: 1px solid #ddd; padding-bottom: 20px; margin-bottom: 20px; }
                    .preview-content { line-height: 1.8; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="preview-header">
                        <h1>${title}</h1>
                        <p class="text-muted">作者：${$('#author').val()} | 状态：${$('#status option:selected').text()}</p>
                    </div>
                    <div class="preview-content">
                        ${content}
                    </div>
                </div>
            </body>
            </html>
        `);
    }

    // 网页格式预览 (新增功能)
    function previewWebFormat() {
        const content = editor ? editor.getContent() : $('#content').val();
        const title = $('#title').val();

        if (!title || !content) {
            showMessage('请先填写标题和内容', 'error');
            return;
        }

        // 打开网页格式预览窗口
        const previewWindow = window.open('', '_blank', 'width=1000,height=700');
        previewWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>网页格式预览 - ${title}</title>
                <link rel="stylesheet" href="/plugins/bootstrap/css/bootstrap.min.css">
                <style>
                    body {
                        font-family: "Microsoft YaHei", Arial, sans-serif;
                        background: #f5f5f5;
                        margin: 0;
                        padding: 20px;
                    }
                    .news-container {
                        max-width: 800px;
                        margin: 0 auto;
                        background: white;
                        padding: 30px;
                        border-radius: 8px;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    }
                    .news-header {
                        text-align: center;
                        border-bottom: 2px solid #007bff;
                        padding-bottom: 20px;
                        margin-bottom: 30px;
                    }
                    .news-title {
                        font-size: 28px;
                        font-weight: bold;
                        color: #333;
                        margin-bottom: 15px;
                        line-height: 1.4;
                    }
                    .news-meta {
                        color: #666;
                        font-size: 14px;
                    }
                    .news-content {
                        line-height: 1.8;
                        font-size: 16px;
                        color: #444;
                    }
                    .news-content h1, .news-content h2, .news-content h3 {
                        color: #333;
                        margin: 25px 0 15px 0;
                    }
                    .news-content p {
                        margin: 15px 0;
                        text-indent: 2em;
                    }
                    .news-content table {
                        width: 100%;
                        border-collapse: collapse;
                        margin: 20px 0;
                    }
                    .news-content table th,
                    .news-content table td {
                        border: 1px solid #ddd;
                        padding: 12px;
                        text-align: left;
                    }
                    .news-content table th {
                        background-color: #f8f9fa;
                        font-weight: bold;
                    }
                    .news-content img {
                        max-width: 100%;
                        height: auto;
                        display: block;
                        margin: 20px auto;
                        border-radius: 4px;
                    }
                    .preview-notice {
                        position: fixed;
                        top: 10px;
                        right: 10px;
                        background: #007bff;
                        color: white;
                        padding: 10px 15px;
                        border-radius: 4px;
                        font-size: 12px;
                        z-index: 1000;
                    }
                </style>
            </head>
            <body>
                <div class="preview-notice">
                    📱 网页格式预览 - 发布后的显示效果
                </div>
                <div class="news-container">
                    <div class="news-header">
                        <h1 class="news-title">${title}</h1>
                        <div class="news-meta">
                            <span>作者：${$('#author').val()}</span> |
                            <span>发布时间：${new Date().toLocaleDateString()}</span> |
                            <span>状态：${$('#status option:selected').text()}</span>
                        </div>
                    </div>
                    <div class="news-content">
                        ${content}
                    </div>
                </div>
                <script>
                    // 3秒后隐藏预览提示
                    setTimeout(function() {
                        document.querySelector('.preview-notice').style.display = 'none';
                    }, 3000);
                </script>
            </body>
            </html>
        `);

        showMessage('已打开网页格式预览窗口，这是新闻发布后的显示效果', 'success');
    }

    // 上传封面图片
    function uploadCoverImage(file) {
        const formData = new FormData();
        formData.append('image', file);
        
        $.ajax({
            url: '/api/upload/image',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    $('#coverImage').val(response.data.url);
                    $('#previewImg').attr('src', response.data.url);
                    $('#imagePreview').show();
                    $('#uploadArea').hide();
                    showMessage('封面图片上传成功', 'success');
                } else {
                    showMessage('图片上传失败：' + response.message, 'error');
                }
            },
            error: function() {
                showMessage('图片上传失败', 'error');
            }
        });
    }

    // 编辑器内图片上传
    function uploadImage(file, callback) {
        const formData = new FormData();
        formData.append('image', file);
        
        $.ajax({
            url: '/api/upload/image',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    callback(response.data.url);
                } else {
                    showMessage('图片上传失败', 'error');
                }
            },
            error: function() {
                showMessage('图片上传失败', 'error');
            }
        });
    }

    // 显示消息函数
    function showMessage(message, type) {
        let alertClass = 'alert-info';
        if (type === 'success') alertClass = 'alert-success';
        if (type === 'error') alertClass = 'alert-danger';
        if (type === 'warning') alertClass = 'alert-warning';
        
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible" role="alert">
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
                ${message}
            </div>
        `;
        
        // 移除现有的alert
        $('.alert').remove();
        
        // 添加新的alert
        $('.panel-body').first().prepend(alertHtml);
        
        // 3秒后自动消失
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 3000);
    }

    // 暴露全局函数 (保持API兼容性)
    window.loadNewsData = loadNewsData;
    window.saveNews = saveNews;
    window.previewNews = previewNews;
});
