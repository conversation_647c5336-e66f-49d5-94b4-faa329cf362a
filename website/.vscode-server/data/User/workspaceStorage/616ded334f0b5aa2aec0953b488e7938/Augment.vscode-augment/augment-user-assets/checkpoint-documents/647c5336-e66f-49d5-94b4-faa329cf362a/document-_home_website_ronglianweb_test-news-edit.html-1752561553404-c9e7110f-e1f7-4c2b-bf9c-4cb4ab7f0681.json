{"path": {"rootPath": "/home", "relPath": "website/ronglianweb/test-news-edit.html"}, "modifiedCode": "<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <title>新闻编辑功能测试</title>\n    <link rel=\"stylesheet\" href=\"/plugins/bootstrap/css/bootstrap.min.css\">\n    <style>\n        .container { margin-top: 20px; }\n        .test-section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 4px; }\n        .test-result { margin-top: 10px; padding: 10px; border-radius: 4px; }\n        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }\n        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }\n        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <h1>新闻编辑功能测试</h1>\n        \n        <div class=\"test-section\">\n            <h3>1. 测试新闻API连接</h3>\n            <button class=\"btn btn-primary\" onclick=\"testNewsAPI()\">测试API连接</button>\n            <div id=\"apiResult\" class=\"test-result\"></div>\n        </div>\n        \n        <div class=\"test-section\">\n            <h3>2. 测试新闻列表获取</h3>\n            <button class=\"btn btn-primary\" onclick=\"testNewsList()\">获取新闻列表</button>\n            <div id=\"listResult\" class=\"test-result\"></div>\n        </div>\n        \n        <div class=\"test-section\">\n            <h3>3. 测试新闻详情获取</h3>\n            <input type=\"text\" id=\"newsIdInput\" placeholder=\"输入新闻ID\" class=\"form-control\" style=\"width: 200px; display: inline-block;\">\n            <button class=\"btn btn-primary\" onclick=\"testNewsDetail()\">获取新闻详情</button>\n            <div id=\"detailResult\" class=\"test-result\"></div>\n        </div>\n        \n        <div class=\"test-section\">\n            <h3>4. 测试编辑器初始化</h3>\n            <button class=\"btn btn-primary\" onclick=\"testEditorInit()\">初始化编辑器</button>\n            <div id=\"editorResult\" class=\"test-result\"></div>\n            <div id=\"testEditor\" style=\"height: 200px; margin-top: 10px; border: 1px solid #ddd;\"></div>\n        </div>\n        \n        <div class=\"test-section\">\n            <h3>5. 快速访问链接</h3>\n            <div class=\"btn-group\">\n                <a href=\"https://*********/admin/login\" class=\"btn btn-info\" target=\"_blank\">管理员登录</a>\n                <a href=\"https://*********/admin/news\" class=\"btn btn-info\" target=\"_blank\">新闻管理</a>\n                <a href=\"https://*********/admin/news/edit\" class=\"btn btn-info\" target=\"_blank\">创建新闻</a>\n                <a href=\"https://*********/word-editor-test\" class=\"btn btn-info\" target=\"_blank\">Word编辑器测试</a>\n            </div>\n        </div>\n    </div>\n\n    <script src=\"/plugins/jquery/jquery-1.11.3.js\"></script>\n    <script src=\"/plugins/bootstrap/js/bootstrap.min.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/tinymce@6/tinymce.min.js\"></script>\n    \n    <script>\n        let testEditor;\n        \n        function showResult(elementId, message, type) {\n            const element = document.getElementById(elementId);\n            element.className = 'test-result ' + type;\n            element.innerHTML = message;\n        }\n        \n        function testNewsAPI() {\n            showResult('apiResult', '正在测试API连接...', 'info');\n            \n            $.ajax({\n                url: '/api/admin/news/list',\n                type: 'GET',\n                success: function(response) {\n                    if (response.success) {\n                        showResult('apiResult', '✅ API连接成功！返回数据: ' + JSON.stringify(response, null, 2), 'success');\n                    } else {\n                        showResult('apiResult', '❌ API返回错误: ' + response.message, 'error');\n                    }\n                },\n                error: function(xhr, status, error) {\n                    showResult('apiResult', '❌ API连接失败: ' + error + ' (状态码: ' + xhr.status + ')', 'error');\n                }\n            });\n        }\n        \n        function testNewsList() {\n            showResult('listResult', '正在获取新闻列表...', 'info');\n            \n            $.ajax({\n                url: '/api/admin/news/list',\n                type: 'GET',\n                data: { pageNo: 1, pageSize: 5 },\n                success: function(response) {\n                    if (response.success && response.data && response.data.records) {\n                        const news = response.data.records;\n                        let html = '✅ 获取到 ' + news.length + ' 条新闻:<br>';\n                        news.forEach(function(item, index) {\n                            html += `${index + 1}. ID: ${item.id}, 标题: ${item.title}, 状态: ${item.status}<br>`;\n                        });\n                        showResult('listResult', html, 'success');\n                        \n                        // 自动填充第一个新闻ID到测试框\n                        if (news.length > 0) {\n                            document.getElementById('newsIdInput').value = news[0].id;\n                        }\n                    } else {\n                        showResult('listResult', '❌ 获取新闻列表失败: ' + (response.message || '未知错误'), 'error');\n                    }\n                },\n                error: function(xhr, status, error) {\n                    showResult('listResult', '❌ 请求失败: ' + error, 'error');\n                }\n            });\n        }\n        \n        function testNewsDetail() {\n            const newsId = document.getElementById('newsIdInput').value;\n            if (!newsId) {\n                showResult('detailResult', '❌ 请输入新闻ID', 'error');\n                return;\n            }\n            \n            showResult('detailResult', '正在获取新闻详情...', 'info');\n            \n            $.ajax({\n                url: `/api/admin/news/${newsId}`,\n                type: 'GET',\n                success: function(response) {\n                    if (response.success) {\n                        const news = response.data;\n                        let html = '✅ 获取新闻详情成功:<br>';\n                        html += `标题: ${news.title}<br>`;\n                        html += `作者: ${news.author}<br>`;\n                        html += `状态: ${news.status}<br>`;\n                        html += `内容长度: ${(news.content || '').length} 字符<br>`;\n                        html += `创建时间: ${news.createTime}<br>`;\n                        showResult('detailResult', html, 'success');\n                    } else {\n                        showResult('detailResult', '❌ 获取新闻详情失败: ' + response.message, 'error');\n                    }\n                },\n                error: function(xhr, status, error) {\n                    showResult('detailResult', '❌ 请求失败: ' + error + ' (状态码: ' + xhr.status + ')', 'error');\n                }\n            });\n        }\n        \n        function testEditorInit() {\n            showResult('editorResult', '正在初始化TinyMCE编辑器...', 'info');\n            \n            if (testEditor) {\n                testEditor.remove();\n            }\n            \n            tinymce.init({\n                selector: '#testEditor',\n                height: 200,\n                language: 'zh_CN',\n                plugins: [\n                    'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',\n                    'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',\n                    'insertdatetime', 'media', 'table', 'help', 'wordcount', 'paste'\n                ],\n                toolbar: [\n                    'undo redo | blocks | bold italic underline strikethrough',\n                    'alignleft aligncenter alignright alignjustify | bullist numlist outdent indent'\n                ].join(' | '),\n                setup: function(ed) {\n                    testEditor = ed;\n                    ed.on('init', function() {\n                        showResult('editorResult', '✅ TinyMCE编辑器初始化成功！', 'success');\n                        ed.setContent('<p>这是一个测试内容，编辑器工作正常！</p>');\n                    });\n                    ed.on('change', function() {\n                        console.log('编辑器内容已更改');\n                    });\n                }\n            });\n        }\n        \n        // 页面加载完成后自动测试API\n        $(document).ready(function() {\n            setTimeout(function() {\n                testNewsAPI();\n            }, 1000);\n        });\n    </script>\n</body>\n</html>\n"}