{"path": {"rootPath": "/home", "relPath": "website/fix-502.sh"}, "originalCode": "#!/bin/bash\n\n# 修复502错误 - Node.js升级到20.x\n# 在dl380主机上直接执行的脚本\n\nset -e\n\n# 颜色定义\nRED='\\033[0;31m'\nGREEN='\\033[0;32m'\nYELLOW='\\033[1;33m'\nBLUE='\\033[0;34m'\nPURPLE='\\033[0;35m'\nCYAN='\\033[0;36m'\nNC='\\033[0m' # No Color\n\n# 日志函数\nlog_info() {\n    echo -e \"${BLUE}[INFO]${NC} $1\"\n}\n\nlog_success() {\n    echo -e \"${GREEN}[SUCCESS]${NC} $1\"\n}\n\nlog_warning() {\n    echo -e \"${YELLOW}[WARNING]${NC} $1\"\n}\n\nlog_error() {\n    echo -e \"${RED}[ERROR]${NC} $1\"\n}\n\nlog_step() {\n    echo -e \"${PURPLE}[STEP]${NC} $1\"\n}\n\necho -e \"${CYAN}🔧 修复502错误 - Node.js升级到20.x${NC}\"\necho -e \"${CYAN}======================================${NC}\"\necho \"执行主机: $(hostname)\"\necho \"当前用户: $(whoami)\"\necho \"当前时间: $(date)\"\necho \"\"\n\n# 步骤1: 停止现有进程\nlog_step \"停止现有Node.js进程\"\nlog_info \"查找并停止Node.js相关进程...\"\n\n# 显示当前运行的Node.js进程\nEXISTING_PROCESSES=$(ps aux | grep -E '(node|npm)' | grep -v grep || echo \"\")\nif [ -n \"$EXISTING_PROCESSES\" ]; then\n    log_info \"发现以下Node.js/npm进程:\"\n    echo \"$EXISTING_PROCESSES\"\nfi\n\n# 停止进程\npkill -f 'node' 2>/dev/null || true\npkill -f 'npm' 2>/dev/null || true\nsleep 3\n\nlog_success \"Node.js进程已停止\"\n\n# 步骤2: 检查当前Node.js版本\nlog_step \"检查当前Node.js版本\"\n\nif command -v node &> /dev/null; then\n    CURRENT_NODE_VERSION=$(node --version 2>/dev/null || echo \"未知\")\n    log_info \"当前Node.js版本: $CURRENT_NODE_VERSION\"\nelse\n    log_info \"Node.js未安装或不在PATH中\"\nfi\n\n# 步骤3: 安装NVM\nlog_step \"安装/配置NVM\"\n\nif [ ! -d \"$HOME/.nvm\" ]; then\n    log_info \"NVM未安装，正在下载安装...\"\n    curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash\n    log_success \"NVM安装完成\"\nelse\n    log_info \"NVM已存在，跳过安装\"\nfi\n\n# 加载NVM环境\nlog_info \"加载NVM环境...\"\nexport NVM_DIR=\"$HOME/.nvm\"\n[ -s \"$NVM_DIR/nvm.sh\" ] && \\. \"$NVM_DIR/nvm.sh\"\n[ -s \"$NVM_DIR/bash_completion\" ] && \\. \"$NVM_DIR/bash_completion\"\n\n# 验证NVM是否可用\nif command -v nvm &> /dev/null; then\n    log_success \"NVM加载成功\"\n    log_info \"NVM版本: $(nvm --version)\"\nelse\n    log_error \"NVM加载失败，尝试手动加载...\"\n    source ~/.bashrc 2>/dev/null || true\n    export NVM_DIR=\"$HOME/.nvm\"\n    [ -s \"$NVM_DIR/nvm.sh\" ] && \\. \"$NVM_DIR/nvm.sh\"\n    \n    if command -v nvm &> /dev/null; then\n        log_success \"NVM手动加载成功\"\n    else\n        log_error \"NVM加载失败，请手动执行: source ~/.bashrc\"\n        exit 1\n    fi\nfi\n\n# 步骤4: 安装Node.js 20.x\nlog_step \"安装Node.js 20.x\"\n\nlog_info \"查看可用的Node.js版本...\"\nnvm list-remote --lts | tail -10\n\nlog_info \"安装Node.js 20.x LTS...\"\nnvm install 20\nnvm use 20\nnvm alias default 20\n\n# 验证安装\nNEW_NODE_VERSION=$(node --version)\nNEW_NPM_VERSION=$(npm --version)\n\nlog_success \"Node.js安装完成\"\necho \"新的Node.js版本: $NEW_NODE_VERSION\"\necho \"新的npm版本: $NEW_NPM_VERSION\"\necho \"Node.js路径: $(which node)\"\necho \"npm路径: $(which npm)\"\n\n# 检查版本是否符合要求\nif [[ $NEW_NODE_VERSION =~ ^v20\\. ]]; then\n    log_success \"Node.js版本符合要求\"\nelse\n    log_warning \"Node.js版本可能不符合要求: $NEW_NODE_VERSION\"\nfi\n\n# 步骤5: 进入应用目录\nlog_step \"进入应用目录\"\n\nAPP_DIR=\"/home/<USER>/ronglianweb\"\nif [ ! -d \"$APP_DIR\" ]; then\n    log_error \"应用目录不存在: $APP_DIR\"\n    exit 1\nfi\n\ncd \"$APP_DIR\"\nlog_info \"当前目录: $(pwd)\"\n\n# 检查关键文件\nif [ ! -f \"package.json\" ]; then\n    log_error \"package.json文件不存在\"\n    exit 1\nfi\n\nlog_info \"应用目录检查完成\"\n\n# 步骤6: 重新安装依赖\nlog_step \"重新安装应用依赖\"\n\nlog_info \"清理旧的依赖文件...\"\nrm -rf node_modules package-lock.json 2>/dev/null || true\n\nlog_info \"安装应用依赖...\"\nnpm install --production\n\nif [ $? -eq 0 ]; then\n    log_success \"依赖安装成功\"\nelse\n    log_error \"依赖安装失败\"\n    exit 1\nfi\n\n# 步骤7: 设置权限\nlog_step \"设置文件权限\"\n\nmkdir -p logs\nchmod +x run.sh deploy.sh deploy-https.sh 2>/dev/null || true\nchmod 755 logs\n\n# 检查SSL证书\nif [ -f \"ssl/private.key\" ] && [ -f \"ssl/certificate.crt\" ]; then\n    chmod 600 ssl/private.key\n    chmod 644 ssl/certificate.crt\n    log_info \"SSL证书权限已设置\"\nfi\n\nlog_success \"文件权限设置完成\"\n\n# 步骤8: 启动应用\nlog_step \"启动Node.js应用\"\n\nlog_info \"启动应用...\"\nnohup npm start > logs/app.log 2>&1 &\n\n# 获取启动的进程ID\nsleep 2\nAPP_PID=$(pgrep -f 'node.*bin/www' || echo \"\")\n\nif [ -n \"$APP_PID\" ]; then\n    log_info \"应用已启动，PID: $APP_PID\"\nelse\n    log_warning \"应用可能启动失败，等待更长时间...\"\nfi\n\n# 等待应用完全启动\nlog_info \"等待应用完全启动...\"\nfor i in {1..15}; do\n    echo -n \".\"\n    sleep 1\ndone\necho \"\"\n\n# 步骤9: 检查应用状态\nlog_step \"检查应用状态\"\n\n# 检查进程\nNODE_PROCESSES=$(ps aux | grep 'node.*bin/www' | grep -v grep || echo \"\")\nif [ -n \"$NODE_PROCESSES\" ]; then\n    log_success \"Node.js应用正在运行\"\n    echo \"$NODE_PROCESSES\"\nelse\n    log_error \"Node.js应用未运行\"\n    echo \"\"\n    log_info \"查看启动日志:\"\n    tail -20 logs/app.log 2>/dev/null || echo \"无法读取日志文件\"\n    exit 1\nfi\n\n# 检查端口3000\nPORT_CHECK=$(netstat -tlnp | grep :3000 || echo \"\")\nif [ -n \"$PORT_CHECK\" ]; then\n    log_success \"端口3000正在监听\"\n    echo \"$PORT_CHECK\"\nelse\n    log_error \"端口3000未监听\"\nfi\n\n# 步骤10: 测试访问\nlog_step \"测试应用访问\"\n\n# 测试本地3000端口\nlog_info \"测试本地3000端口...\"\nHTTP_CODE=$(curl -s -o /dev/null -w '%{http_code}' http://localhost:3000 2>/dev/null || echo \"Failed\")\necho \"HTTP 3000端口测试: $HTTP_CODE\"\n\n# 测试HTTPS访问\nlog_info \"测试HTTPS访问...\"\nHTTPS_CODE=$(curl -s -o /dev/null -w '%{http_code}' -k https://localhost 2>/dev/null || echo \"Failed\")\necho \"HTTPS访问测试: $HTTPS_CODE\"\n\n# 测试健康检查\nlog_info \"测试健康检查...\"\nHEALTH_CHECK=$(curl -s -k https://localhost/health 2>/dev/null || echo \"Failed\")\necho \"健康检查结果: $HEALTH_CHECK\"\n\n# 步骤11: 最终结果\necho \"\"\necho -e \"${CYAN}========================================${NC}\"\necho -e \"${CYAN}🎯 修复结果总结${NC}\"\necho -e \"${CYAN}========================================${NC}\"\n\n# 判断修复是否成功\nif [[ $NEW_NODE_VERSION =~ ^v20\\. ]] && [ -n \"$NODE_PROCESSES\" ] && [ -n \"$PORT_CHECK\" ] && ([ \"$HTTPS_CODE\" = \"200\" ] || [ \"$HTTPS_CODE\" = \"301\" ] || [ \"$HTTPS_CODE\" = \"302\" ]); then\n    echo -e \"${GREEN}🎉 502错误修复完全成功！${NC}\"\n    echo \"\"\n    echo -e \"${GREEN}✅ 修复状态:${NC}\"\n    echo \"   Node.js版本: $NEW_NODE_VERSION\"\n    echo \"   npm版本: $NEW_NPM_VERSION\"\n    echo \"   应用状态: 正在运行\"\n    echo \"   端口3000: 正在监听\"\n    echo \"   HTTPS访问: 正常 ($HTTPS_CODE)\"\n    echo \"\"\n    echo -e \"${CYAN}🌐 访问地址:${NC}\"\n    echo \"   主站首页: https://dl380\"\n    echo \"   新闻页面: https://dl380/news/index\"\n    echo \"   管理登录: https://dl380/admin/login\"\n    echo \"   新闻管理: https://dl380/admin/news\"\n    echo \"\"\n    echo -e \"${CYAN}🔐 登录信息:${NC}\"\n    echo \"   管理员: admin / admin123\"\n    echo \"   编辑员: user01 / user123\"\n    echo \"\"\n    echo -e \"${GREEN}🎊 现在可以正常访问网站了！${NC}\"\n    \nelif [[ $NEW_NODE_VERSION =~ ^v20\\. ]] && [ -n \"$NODE_PROCESSES\" ]; then\n    echo -e \"${YELLOW}⚠️ Node.js升级成功，应用已启动，但HTTPS访问可能有问题${NC}\"\n    echo \"\"\n    echo \"请检查Nginx配置或手动测试访问\"\n    \nelif [[ $NEW_NODE_VERSION =~ ^v20\\. ]]; then\n    echo -e \"${YELLOW}⚠️ Node.js升级成功，但应用未正常启动${NC}\"\n    echo \"\"\n    echo \"请查看错误日志: tail -50 logs/app.log\"\n    echo \"或手动启动: npm start\"\n    \nelse\n    echo -e \"${RED}❌ 修复失败${NC}\"\n    echo \"\"\n    echo \"问题诊断:\"\n    echo \"Node.js版本: $NEW_NODE_VERSION\"\n    echo \"应用进程: $([ -n \"$NODE_PROCESSES\" ] && echo '运行中' || echo '未运行')\"\n    echo \"端口3000: $([ -n \"$PORT_CHECK\" ] && echo '监听中' || echo '未监听')\"\n    echo \"HTTPS访问: $HTTPS_CODE\"\nfi\n\necho \"\"\necho -e \"${BLUE}📝 重要提示:${NC}\"\necho \"1. Node.js现在通过NVM管理\"\necho \"2. 重启系统后需要重新加载NVM: source ~/.bashrc\"\necho \"3. 应用日志位置: $APP_DIR/logs/app.log\"\necho \"4. 如需手动操作，请先运行: source ~/.bashrc\"\n\necho \"\"\necho -e \"${CYAN}修复脚本执行完成！${NC}\"\n", "modifiedCode": "#!/bin/bash\n\n# 修复502错误 - Node.js升级到20.x\n# 在dl380主机上直接执行的脚本\n\nset -e\n\n# 颜色定义\nRED='\\033[0;31m'\nGREEN='\\033[0;32m'\nYELLOW='\\033[1;33m'\nBLUE='\\033[0;34m'\nPURPLE='\\033[0;35m'\nCYAN='\\033[0;36m'\nNC='\\033[0m' # No Color\n\n# 日志函数\nlog_info() {\n    echo -e \"${BLUE}[INFO]${NC} $1\"\n}\n\nlog_success() {\n    echo -e \"${GREEN}[SUCCESS]${NC} $1\"\n}\n\nlog_warning() {\n    echo -e \"${YELLOW}[WARNING]${NC} $1\"\n}\n\nlog_error() {\n    echo -e \"${RED}[ERROR]${NC} $1\"\n}\n\nlog_step() {\n    echo -e \"${PURPLE}[STEP]${NC} $1\"\n}\n\necho -e \"${CYAN}🔧 修复502错误 - Node.js升级到20.x${NC}\"\necho -e \"${CYAN}======================================${NC}\"\necho \"执行主机: $(hostname)\"\necho \"当前用户: $(whoami)\"\necho \"当前时间: $(date)\"\necho \"\"\n\n# 步骤1: 停止现有进程\nlog_step \"停止现有Node.js进程\"\nlog_info \"查找并停止Node.js相关进程...\"\n\n# 显示当前运行的Node.js进程\nEXISTING_PROCESSES=$(ps aux | grep -E '(node|npm)' | grep -v grep || echo \"\")\nif [ -n \"$EXISTING_PROCESSES\" ]; then\n    log_info \"发现以下Node.js/npm进程:\"\n    echo \"$EXISTING_PROCESSES\"\nfi\n\n# 停止进程\npkill -f 'node' 2>/dev/null || true\npkill -f 'npm' 2>/dev/null || true\nsleep 3\n\nlog_success \"Node.js进程已停止\"\n\n# 步骤2: 检查当前Node.js版本\nlog_step \"检查当前Node.js版本\"\n\nif command -v node &> /dev/null; then\n    CURRENT_NODE_VERSION=$(node --version 2>/dev/null || echo \"未知\")\n    log_info \"当前Node.js版本: $CURRENT_NODE_VERSION\"\nelse\n    log_info \"Node.js未安装或不在PATH中\"\nfi\n\n# 步骤3: 安装NVM\nlog_step \"安装/配置NVM\"\n\nif [ ! -d \"$HOME/.nvm\" ]; then\n    log_info \"NVM未安装，正在下载安装...\"\n    curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash\n    log_success \"NVM安装完成\"\nelse\n    log_info \"NVM已存在，跳过安装\"\nfi\n\n# 加载NVM环境\nlog_info \"加载NVM环境...\"\nexport NVM_DIR=\"$HOME/.nvm\"\n[ -s \"$NVM_DIR/nvm.sh\" ] && \\. \"$NVM_DIR/nvm.sh\"\n[ -s \"$NVM_DIR/bash_completion\" ] && \\. \"$NVM_DIR/bash_completion\"\n\n# 验证NVM是否可用\nif command -v nvm &> /dev/null; then\n    log_success \"NVM加载成功\"\n    log_info \"NVM版本: $(nvm --version)\"\nelse\n    log_error \"NVM加载失败，尝试手动加载...\"\n    source ~/.bashrc 2>/dev/null || true\n    export NVM_DIR=\"$HOME/.nvm\"\n    [ -s \"$NVM_DIR/nvm.sh\" ] && \\. \"$NVM_DIR/nvm.sh\"\n    \n    if command -v nvm &> /dev/null; then\n        log_success \"NVM手动加载成功\"\n    else\n        log_error \"NVM加载失败，请手动执行: source ~/.bashrc\"\n        exit 1\n    fi\nfi\n\n# 步骤4: 安装Node.js 20.x\nlog_step \"安装Node.js 20.x\"\n\nlog_info \"查看可用的Node.js版本...\"\nnvm list-remote --lts | tail -10\n\nlog_info \"安装Node.js 20.x LTS...\"\nnvm install 20\nnvm use 20\nnvm alias default 20\n\n# 验证安装\nNEW_NODE_VERSION=$(node --version)\nNEW_NPM_VERSION=$(npm --version)\n\nlog_success \"Node.js安装完成\"\necho \"新的Node.js版本: $NEW_NODE_VERSION\"\necho \"新的npm版本: $NEW_NPM_VERSION\"\necho \"Node.js路径: $(which node)\"\necho \"npm路径: $(which npm)\"\n\n# 检查版本是否符合要求\nif [[ $NEW_NODE_VERSION =~ ^v20\\. ]]; then\n    log_success \"Node.js版本符合要求\"\nelse\n    log_warning \"Node.js版本可能不符合要求: $NEW_NODE_VERSION\"\nfi\n\n# 步骤5: 进入应用目录\nlog_step \"进入应用目录\"\n\nAPP_DIR=\"/home/<USER>/ronglianweb\"\nif [ ! -d \"$APP_DIR\" ]; then\n    log_error \"应用目录不存在: $APP_DIR\"\n    exit 1\nfi\n\ncd \"$APP_DIR\"\nlog_info \"当前目录: $(pwd)\"\n\n# 检查关键文件\nif [ ! -f \"package.json\" ]; then\n    log_error \"package.json文件不存在\"\n    exit 1\nfi\n\nlog_info \"应用目录检查完成\"\n\n# 步骤6: 重新安装依赖\nlog_step \"重新安装应用依赖\"\n\nlog_info \"清理旧的依赖文件...\"\nrm -rf node_modules package-lock.json 2>/dev/null || true\n\nlog_info \"安装应用依赖...\"\nnpm install --production\n\nif [ $? -eq 0 ]; then\n    log_success \"依赖安装成功\"\nelse\n    log_error \"依赖安装失败\"\n    exit 1\nfi\n\n# 步骤7: 设置权限\nlog_step \"设置文件权限\"\n\nmkdir -p logs\nchmod +x run.sh deploy.sh deploy-https.sh 2>/dev/null || true\nchmod 755 logs\n\n# 检查SSL证书\nif [ -f \"ssl/private.key\" ] && [ -f \"ssl/certificate.crt\" ]; then\n    chmod 600 ssl/private.key\n    chmod 644 ssl/certificate.crt\n    log_info \"SSL证书权限已设置\"\nfi\n\nlog_success \"文件权限设置完成\"\n\n# 步骤8: 启动应用\nlog_step \"启动Node.js应用\"\n\nlog_info \"启动应用...\"\nnohup npm start > logs/app.log 2>&1 &\n\n# 获取启动的进程ID\nsleep 2\nAPP_PID=$(pgrep -f 'node.*bin/www' || echo \"\")\n\nif [ -n \"$APP_PID\" ]; then\n    log_info \"应用已启动，PID: $APP_PID\"\nelse\n    log_warning \"应用可能启动失败，等待更长时间...\"\nfi\n\n# 等待应用完全启动\nlog_info \"等待应用完全启动...\"\nfor i in {1..15}; do\n    echo -n \".\"\n    sleep 1\ndone\necho \"\"\n\n# 步骤9: 检查应用状态\nlog_step \"检查应用状态\"\n\n# 检查进程\nNODE_PROCESSES=$(ps aux | grep 'node.*bin/www' | grep -v grep || echo \"\")\nif [ -n \"$NODE_PROCESSES\" ]; then\n    log_success \"Node.js应用正在运行\"\n    echo \"$NODE_PROCESSES\"\nelse\n    log_error \"Node.js应用未运行\"\n    echo \"\"\n    log_info \"查看启动日志:\"\n    tail -20 logs/app.log 2>/dev/null || echo \"无法读取日志文件\"\n    exit 1\nfi\n\n# 检查端口3000\nPORT_CHECK=$(netstat -tlnp | grep :3000 || echo \"\")\nif [ -n \"$PORT_CHECK\" ]; then\n    log_success \"端口3000正在监听\"\n    echo \"$PORT_CHECK\"\nelse\n    log_error \"端口3000未监听\"\nfi\n\n# 步骤10: 测试访问\nlog_step \"测试应用访问\"\n\n# 测试本地3000端口\nlog_info \"测试本地3000端口...\"\nHTTP_CODE=$(curl -s -o /dev/null -w '%{http_code}' http://localhost:3000 2>/dev/null || echo \"Failed\")\necho \"HTTP 3000端口测试: $HTTP_CODE\"\n\n# 测试HTTPS访问\nlog_info \"测试HTTPS访问...\"\nHTTPS_CODE=$(curl -s -o /dev/null -w '%{http_code}' -k https://localhost 2>/dev/null || echo \"Failed\")\necho \"HTTPS访问测试: $HTTPS_CODE\"\n\n# 测试健康检查\nlog_info \"测试健康检查...\"\nHEALTH_CHECK=$(curl -s -k https://localhost/health 2>/dev/null || echo \"Failed\")\necho \"健康检查结果: $HEALTH_CHECK\"\n\n# 步骤11: 最终结果\necho \"\"\necho -e \"${CYAN}========================================${NC}\"\necho -e \"${CYAN}🎯 修复结果总结${NC}\"\necho -e \"${CYAN}========================================${NC}\"\n\n# 判断修复是否成功\nif [[ $NEW_NODE_VERSION =~ ^v20\\. ]] && [ -n \"$NODE_PROCESSES\" ] && [ -n \"$PORT_CHECK\" ] && ([ \"$HTTPS_CODE\" = \"200\" ] || [ \"$HTTPS_CODE\" = \"301\" ] || [ \"$HTTPS_CODE\" = \"302\" ]); then\n    echo -e \"${GREEN}🎉 502错误修复完全成功！${NC}\"\n    echo \"\"\n    echo -e \"${GREEN}✅ 修复状态:${NC}\"\n    echo \"   Node.js版本: $NEW_NODE_VERSION\"\n    echo \"   npm版本: $NEW_NPM_VERSION\"\n    echo \"   应用状态: 正在运行\"\n    echo \"   端口3000: 正在监听\"\n    echo \"   HTTPS访问: 正常 ($HTTPS_CODE)\"\n    echo \"\"\n    echo -e \"${CYAN}🌐 访问地址:${NC}\"\n    echo \"   主站首页: https://dl380\"\n    echo \"   新闻页面: https://dl380/news/index\"\n    echo \"   管理登录: https://dl380/admin/login\"\n    echo \"   新闻管理: https://dl380/admin/news\"\n    echo \"\"\n    echo -e \"${CYAN}🔐 登录信息:${NC}\"\n    echo \"   管理员: admin / admin123\"\n    echo \"   编辑员: user01 / user123\"\n    echo \"\"\n    echo -e \"${GREEN}🎊 现在可以正常访问网站了！${NC}\"\n    \nelif [[ $NEW_NODE_VERSION =~ ^v20\\. ]] && [ -n \"$NODE_PROCESSES\" ]; then\n    echo -e \"${YELLOW}⚠️ Node.js升级成功，应用已启动，但HTTPS访问可能有问题${NC}\"\n    echo \"\"\n    echo \"请检查Nginx配置或手动测试访问\"\n    \nelif [[ $NEW_NODE_VERSION =~ ^v20\\. ]]; then\n    echo -e \"${YELLOW}⚠️ Node.js升级成功，但应用未正常启动${NC}\"\n    echo \"\"\n    echo \"请查看错误日志: tail -50 logs/app.log\"\n    echo \"或手动启动: npm start\"\n    \nelse\n    echo -e \"${RED}❌ 修复失败${NC}\"\n    echo \"\"\n    echo \"问题诊断:\"\n    echo \"Node.js版本: $NEW_NODE_VERSION\"\n    echo \"应用进程: $([ -n \"$NODE_PROCESSES\" ] && echo '运行中' || echo '未运行')\"\n    echo \"端口3000: $([ -n \"$PORT_CHECK\" ] && echo '监听中' || echo '未监听')\"\n    echo \"HTTPS访问: $HTTPS_CODE\"\nfi\n\necho \"\"\necho -e \"${BLUE}📝 重要提示:${NC}\"\necho \"1. Node.js现在通过NVM管理\"\necho \"2. 重启系统后需要重新加载NVM: source ~/.bashrc\"\necho \"3. 应用日志位置: $APP_DIR/logs/app.log\"\necho \"4. 如需手动操作，请先运行: source ~/.bashrc\"\n\necho \"\"\necho -e \"${CYAN}修复脚本执行完成！${NC}\"\n"}