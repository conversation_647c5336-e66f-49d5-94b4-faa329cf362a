{"path": {"rootPath": "/home", "relPath": "website/ronglianweb/ONLYOFFICE_INTEGRATION_PLAN.md"}, "modifiedCode": "# OnlyOffice 集成方案 - MS Office兼容在线编辑器\n\n## 🎯 方案概述\n\nOnlyOffice Document Server 是目前最佳的MS Office兼容在线编辑解决方案，提供100%兼容的Word、Excel、PowerPoint在线编辑功能。\n\n## 🏗️ 技术架构\n\n### 核心组件\n```\n前端应用 (Vue 3)\n    ↓\nOnlyOffice Document Server\n    ↓\n文件存储系统\n    ↓\n后端API (Node.js)\n```\n\n### 技术栈\n- **前端**: Vue 3.4+ + TypeScript + Element Plus\n- **编辑器**: OnlyOffice Document Server 7.5+\n- **后端**: Node.js + Express\n- **数据库**: MongoDB (文档元数据)\n- **文件存储**: 本地存储 + 阿里云OSS\n- **认证**: JWT Token\n\n## 📦 安装部署\n\n### 1. OnlyOffice Document Server 安装\n\n#### Docker 部署 (推荐)\n```bash\n# 拉取OnlyOffice镜像\ndocker pull onlyoffice/documentserver:latest\n\n# 启动Document Server\ndocker run -i -t -d -p 8080:80 \\\n  -e JWT_ENABLED=true \\\n  -e JWT_SECRET=your_jwt_secret \\\n  -v /app/onlyoffice/DocumentServer/logs:/var/log/onlyoffice \\\n  -v /app/onlyoffice/DocumentServer/data:/var/www/onlyoffice/Data \\\n  -v /app/onlyoffice/DocumentServer/lib:/var/lib/onlyoffice \\\n  -v /app/onlyoffice/DocumentServer/db:/var/lib/postgresql \\\n  --name documentserver \\\n  onlyoffice/documentserver\n```\n\n#### Ubuntu 直接安装\n```bash\n# 添加OnlyOffice仓库\necho \"deb https://download.onlyoffice.com/repo/debian squeeze main\" | sudo tee /etc/apt/sources.list.d/onlyoffice.list\nwget -qO - https://download.onlyoffice.com/GPG-KEY-ONLYOFFICE | sudo apt-key add -\n\n# 安装Document Server\nsudo apt-get update\nsudo apt-get install onlyoffice-documentserver\n```\n\n### 2. 前端集成\n\n#### Vue 3 组件\n```vue\n<template>\n  <div class=\"document-editor\">\n    <div id=\"placeholder\" style=\"height: 600px;\"></div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, onMounted, onUnmounted } from 'vue'\n\ninterface DocumentConfig {\n  document: {\n    fileType: string\n    key: string\n    title: string\n    url: string\n    permissions: {\n      edit: boolean\n      download: boolean\n      review: boolean\n    }\n  }\n  documentType: string\n  editorConfig: {\n    user: {\n      id: string\n      name: string\n    }\n    callbackUrl: string\n    lang: string\n  }\n  width: string\n  height: string\n}\n\nconst props = defineProps<{\n  documentUrl: string\n  documentKey: string\n  documentTitle: string\n  canEdit: boolean\n}>()\n\nlet docEditor: any = null\n\nonMounted(() => {\n  initEditor()\n})\n\nconst initEditor = () => {\n  const config: DocumentConfig = {\n    document: {\n      fileType: getFileExtension(props.documentTitle),\n      key: props.documentKey,\n      title: props.documentTitle,\n      url: props.documentUrl,\n      permissions: {\n        edit: props.canEdit,\n        download: true,\n        review: true\n      }\n    },\n    documentType: getDocumentType(props.documentTitle),\n    editorConfig: {\n      user: {\n        id: getCurrentUserId(),\n        name: getCurrentUserName()\n      },\n      callbackUrl: '/api/documents/callback',\n      lang: 'zh-CN'\n    },\n    width: '100%',\n    height: '600px'\n  }\n\n  // 初始化OnlyOffice编辑器\n  docEditor = new DocsAPI.DocEditor('placeholder', config)\n}\n\nconst getFileExtension = (filename: string): string => {\n  return filename.split('.').pop()?.toLowerCase() || 'docx'\n}\n\nconst getDocumentType = (filename: string): string => {\n  const ext = getFileExtension(filename)\n  if (['doc', 'docx', 'odt', 'rtf', 'txt'].includes(ext)) return 'text'\n  if (['xls', 'xlsx', 'ods', 'csv'].includes(ext)) return 'spreadsheet'\n  if (['ppt', 'pptx', 'odp'].includes(ext)) return 'presentation'\n  return 'text'\n}\n\nonUnmounted(() => {\n  if (docEditor) {\n    docEditor.destroyEditor()\n  }\n})\n</script>\n```\n\n### 3. 后端API实现\n\n#### 文档管理API\n```javascript\n// routes/documents.js\nconst express = require('express')\nconst multer = require('multer')\nconst path = require('path')\nconst fs = require('fs')\nconst jwt = require('jsonwebtoken')\n\nconst router = express.Router()\n\n// 文件上传配置\nconst storage = multer.diskStorage({\n  destination: (req, file, cb) => {\n    cb(null, 'uploads/documents/')\n  },\n  filename: (req, file, cb) => {\n    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9)\n    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname))\n  }\n})\n\nconst upload = multer({ storage })\n\n// 上传文档\nrouter.post('/upload', upload.single('document'), async (req, res) => {\n  try {\n    const { originalname, filename, path: filePath } = req.file\n    \n    // 生成文档密钥\n    const documentKey = generateDocumentKey(filename)\n    \n    // 保存文档信息到数据库\n    const document = await saveDocumentInfo({\n      title: originalname,\n      filename,\n      path: filePath,\n      key: documentKey,\n      uploadedBy: req.user.id,\n      uploadedAt: new Date()\n    })\n    \n    res.json({\n      success: true,\n      data: {\n        id: document.id,\n        title: document.title,\n        key: document.key,\n        url: `/api/documents/file/${document.filename}`\n      }\n    })\n  } catch (error) {\n    res.status(500).json({ success: false, message: error.message })\n  }\n})\n\n// 获取文档文件\nrouter.get('/file/:filename', (req, res) => {\n  const filePath = path.join(__dirname, '../uploads/documents', req.params.filename)\n  \n  if (fs.existsSync(filePath)) {\n    res.sendFile(path.resolve(filePath))\n  } else {\n    res.status(404).json({ success: false, message: '文件不存在' })\n  }\n})\n\n// OnlyOffice回调处理\nrouter.post('/callback', express.json(), async (req, res) => {\n  try {\n    const { key, status, url, users } = req.body\n    \n    // 验证JWT签名\n    if (!verifyJWTSignature(req.body)) {\n      return res.status(403).json({ error: 'Invalid signature' })\n    }\n    \n    switch (status) {\n      case 1: // 文档正在编辑\n        console.log(`文档 ${key} 正在被编辑`)\n        break\n        \n      case 2: // 文档准备保存\n        if (url) {\n          await downloadAndSaveDocument(key, url)\n          console.log(`文档 ${key} 已保存`)\n        }\n        break\n        \n      case 3: // 文档保存出错\n        console.error(`文档 ${key} 保存出错`)\n        break\n        \n      case 4: // 文档关闭，无修改\n        console.log(`文档 ${key} 已关闭`)\n        break\n        \n      case 6: // 文档正在编辑，但当前用户已断开连接\n        console.log(`用户已断开文档 ${key} 的连接`)\n        break\n        \n      case 7: // 强制保存文档\n        if (url) {\n          await downloadAndSaveDocument(key, url)\n          console.log(`文档 ${key} 已强制保存`)\n        }\n        break\n    }\n    \n    res.json({ error: 0 })\n  } catch (error) {\n    console.error('回调处理错误:', error)\n    res.status(500).json({ error: 1, message: error.message })\n  }\n})\n\n// 生成文档密钥\nfunction generateDocumentKey(filename) {\n  const timestamp = Date.now()\n  const random = Math.random().toString(36).substring(2)\n  return `${filename}_${timestamp}_${random}`\n}\n\n// 下载并保存文档\nasync function downloadAndSaveDocument(key, url) {\n  const axios = require('axios')\n  const response = await axios.get(url, { responseType: 'stream' })\n  \n  // 根据key找到原始文件路径\n  const document = await findDocumentByKey(key)\n  if (document) {\n    const filePath = path.join(__dirname, '../uploads/documents', document.filename)\n    const writer = fs.createWriteStream(filePath)\n    response.data.pipe(writer)\n    \n    return new Promise((resolve, reject) => {\n      writer.on('finish', resolve)\n      writer.on('error', reject)\n    })\n  }\n}\n\nmodule.exports = router\n```\n\n### 4. 数据库模型\n\n#### 文档模型 (MongoDB)\n```javascript\n// models/Document.js\nconst mongoose = require('mongoose')\n\nconst documentSchema = new mongoose.Schema({\n  title: {\n    type: String,\n    required: true\n  },\n  filename: {\n    type: String,\n    required: true,\n    unique: true\n  },\n  path: {\n    type: String,\n    required: true\n  },\n  key: {\n    type: String,\n    required: true,\n    unique: true\n  },\n  fileType: {\n    type: String,\n    required: true\n  },\n  size: {\n    type: Number,\n    default: 0\n  },\n  uploadedBy: {\n    type: mongoose.Schema.Types.ObjectId,\n    ref: 'User',\n    required: true\n  },\n  uploadedAt: {\n    type: Date,\n    default: Date.now\n  },\n  lastModified: {\n    type: Date,\n    default: Date.now\n  },\n  version: {\n    type: Number,\n    default: 1\n  },\n  permissions: {\n    canEdit: { type: Boolean, default: true },\n    canDownload: { type: Boolean, default: true },\n    canReview: { type: Boolean, default: true }\n  },\n  collaborators: [{\n    user: {\n      type: mongoose.Schema.Types.ObjectId,\n      ref: 'User'\n    },\n    permission: {\n      type: String,\n      enum: ['read', 'edit', 'admin'],\n      default: 'read'\n    }\n  }]\n}, {\n  timestamps: true\n})\n\nmodule.exports = mongoose.model('Document', documentSchema)\n```\n\n## 🔧 配置说明\n\n### OnlyOffice 配置文件\n```javascript\n// config/onlyoffice.js\nmodule.exports = {\n  documentServer: {\n    url: process.env.ONLYOFFICE_URL || 'http://localhost:8080',\n    jwtSecret: process.env.JWT_SECRET || 'your_jwt_secret',\n    jwtHeader: 'Authorization',\n    timeout: 120000\n  },\n  fileStorage: {\n    path: process.env.UPLOAD_PATH || './uploads/documents',\n    maxSize: 50 * 1024 * 1024, // 50MB\n    allowedTypes: ['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.pdf', '.txt', '.rtf', '.odt', '.ods', '.odp']\n  },\n  features: {\n    collaboration: true,\n    comments: true,\n    review: true,\n    chat: true,\n    plugins: true\n  }\n}\n```\n\n## 🚀 部署步骤\n\n### 1. 环境准备\n```bash\n# 创建项目目录\nmkdir onlyoffice-integration\ncd onlyoffice-integration\n\n# 初始化项目\nnpm init -y\n\n# 安装依赖\nnpm install express multer mongoose jsonwebtoken axios cors helmet\nnpm install -D nodemon typescript @types/node\n```\n\n### 2. Docker Compose 部署\n```yaml\n# docker-compose.yml\nversion: '3.8'\n\nservices:\n  documentserver:\n    image: onlyoffice/documentserver:latest\n    container_name: onlyoffice-documentserver\n    ports:\n      - \"8080:80\"\n    environment:\n      - JWT_ENABLED=true\n      - JWT_SECRET=your_jwt_secret\n    volumes:\n      - ./data/onlyoffice/logs:/var/log/onlyoffice\n      - ./data/onlyoffice/data:/var/www/onlyoffice/Data\n      - ./data/onlyoffice/lib:/var/lib/onlyoffice\n      - ./data/onlyoffice/db:/var/lib/postgresql\n    restart: unless-stopped\n\n  mongodb:\n    image: mongo:latest\n    container_name: mongodb\n    ports:\n      - \"27017:27017\"\n    volumes:\n      - ./data/mongodb:/data/db\n    restart: unless-stopped\n\n  app:\n    build: .\n    container_name: news-app\n    ports:\n      - \"3000:3000\"\n    environment:\n      - NODE_ENV=production\n      - MONGODB_URL=mongodb://mongodb:27017/newsdb\n      - ONLYOFFICE_URL=http://documentserver\n      - JWT_SECRET=your_jwt_secret\n    depends_on:\n      - documentserver\n      - mongodb\n    volumes:\n      - ./uploads:/app/uploads\n    restart: unless-stopped\n```\n\n### 3. 启动服务\n```bash\n# 启动所有服务\ndocker-compose up -d\n\n# 查看服务状态\ndocker-compose ps\n\n# 查看日志\ndocker-compose logs -f\n```\n\n## 📊 功能特性\n\n### 支持的文件格式\n- **文档**: .doc, .docx, .odt, .rtf, .txt\n- **表格**: .xls, .xlsx, .ods, .csv\n- **演示**: .ppt, .pptx, .odp\n- **PDF**: 查看模式\n\n### 编辑功能\n- ✅ **完整格式支持** - 字体、样式、表格、图片\n- ✅ **实时协作** - 多人同时编辑\n- ✅ **评论和审阅** - 文档审批流程\n- ✅ **版本控制** - 自动保存历史版本\n- ✅ **插件支持** - 扩展功能\n\n### 集成优势\n- ✅ **100% MS Office兼容** - 无格式丢失\n- ✅ **云端存储** - 支持各种存储后端\n- ✅ **权限控制** - 细粒度权限管理\n- ✅ **API丰富** - 完整的开发接口\n\n## 💰 成本分析\n\n### 开源版本 (免费)\n- ✅ 最多20个并发连接\n- ✅ 基础编辑功能\n- ✅ 协作编辑\n- ❌ 无技术支持\n\n### 企业版本 (付费)\n- ✅ 无连接数限制\n- ✅ 高级功能\n- ✅ 技术支持\n- ✅ 品牌定制\n\n## 🎯 实施建议\n\n1. **先部署测试环境** - 验证功能和性能\n2. **逐步迁移** - 从新文档开始使用\n3. **用户培训** - 熟悉在线编辑操作\n4. **备份策略** - 确保数据安全\n5. **性能监控** - 监控服务器资源使用\n\n这个方案提供了**完整的MS Office兼容在线编辑解决方案**，是目前市场上最成熟的选择！\n"}