{"path": {"rootPath": "/home", "relPath": "website/one-click-fix.sh"}, "originalCode": "#!/bin/bash\n\n# 一键修复502错误脚本 - 在dl380上执行\n# 解决express-session模块缺失和依赖问题\n\nset -e\n\n# 颜色定义\nRED='\\033[0;31m'\nGREEN='\\033[0;32m'\nYELLOW='\\033[1;33m'\nBLUE='\\033[0;34m'\nPURPLE='\\033[0;35m'\nCYAN='\\033[0;36m'\nNC='\\033[0m' # No Color\n\n# 日志函数\nlog_info() {\n    echo -e \"${BLUE}[INFO]${NC} $1\"\n}\n\nlog_success() {\n    echo -e \"${GREEN}[SUCCESS]${NC} $1\"\n}\n\nlog_warning() {\n    echo -e \"${YELLOW}[WARNING]${NC} $1\"\n}\n\nlog_error() {\n    echo -e \"${RED}[ERROR]${NC} $1\"\n}\n\nlog_step() {\n    echo -e \"${PURPLE}[STEP]${NC} $1\"\n}\n\necho -e \"${CYAN}🔧 一键修复502错误脚本${NC}\"\necho -e \"${CYAN}========================${NC}\"\necho \"执行主机: $(hostname)\"\necho \"当前用户: $(whoami)\"\necho \"当前时间: $(date)\"\necho \"Node.js版本: $(node --version)\"\necho \"npm版本: $(npm --version)\"\necho \"\"\n\n# 步骤1: 进入应用目录\nlog_step \"进入应用目录\"\nAPP_DIR=\"/home/<USER>/ronglianweb\"\n\nif [ ! -d \"$APP_DIR\" ]; then\n    log_error \"应用目录不存在: $APP_DIR\"\n    exit 1\nfi\n\ncd \"$APP_DIR\"\nlog_info \"当前目录: $(pwd)\"\n\n# 检查关键文件\nif [ ! -f \"package.json\" ]; then\n    log_error \"package.json文件不存在\"\n    exit 1\nfi\n\nif [ ! -f \"app.js\" ]; then\n    log_error \"app.js文件不存在\"\n    exit 1\nfi\n\nif [ ! -f \"bin/www\" ]; then\n    log_error \"bin/www文件不存在\"\n    exit 1\nfi\n\nlog_success \"关键文件检查完成\"\n\n# 步骤2: 停止现有进程\nlog_step \"停止现有Node.js进程\"\npkill -f 'node.*bin/www' 2>/dev/null || true\npkill -f 'npm.*start' 2>/dev/null || true\nsleep 3\nlog_success \"现有进程已停止\"\n\n# 步骤3: 清理和重新安装依赖\nlog_step \"清理npm缓存和依赖\"\n\nlog_info \"清理npm缓存...\"\nnpm cache clean --force\n\nlog_info \"删除node_modules和package-lock.json...\"\nrm -rf node_modules package-lock.json\n\nlog_success \"清理完成\"\n\n# 步骤4: 重新安装依赖\nlog_step \"重新安装应用依赖\"\n\nlog_info \"安装package.json中的依赖...\"\nnpm install\n\nif [ $? -ne 0 ]; then\n    log_warning \"npm install失败，尝试使用--legacy-peer-deps...\"\n    npm install --legacy-peer-deps\nfi\n\nlog_success \"基础依赖安装完成\"\n\n# 步骤5: 手动安装关键模块\nlog_step \"手动安装关键模块\"\n\n# 关键模块列表\nCRITICAL_MODULES=(\n    \"express\"\n    \"express-session\"\n    \"jade\"\n    \"body-parser\"\n    \"cookie-parser\"\n    \"morgan\"\n    \"serve-favicon\"\n    \"multer\"\n    \"debug\"\n    \"http-errors\"\n)\n\nlog_info \"安装关键模块...\"\nfor module in \"${CRITICAL_MODULES[@]}\"; do\n    if [ ! -d \"node_modules/$module\" ]; then\n        log_info \"安装缺失模块: $module\"\n        npm install \"$module\" --save\n    else\n        log_info \"模块已存在: $module\"\n    fi\ndone\n\nlog_success \"关键模块安装完成\"\n\n# 步骤6: 验证关键模块\nlog_step \"验证关键模块安装\"\n\nMISSING_MODULES=()\nfor module in \"${CRITICAL_MODULES[@]}\"; do\n    if [ ! -d \"node_modules/$module\" ]; then\n        MISSING_MODULES+=(\"$module\")\n        log_error \"模块缺失: $module\"\n    else\n        log_success \"模块已安装: $module\"\n    fi\ndone\n\nif [ ${#MISSING_MODULES[@]} -gt 0 ]; then\n    log_warning \"发现缺失模块，尝试重新安装...\"\n    for module in \"${MISSING_MODULES[@]}\"; do\n        npm install \"$module\" --save --force\n    done\nfi\n\n# 步骤7: 设置权限\nlog_step \"设置文件权限\"\n\nchmod +x bin/www\nchmod +x run.sh deploy.sh deploy-https.sh 2>/dev/null || true\nmkdir -p logs uploads\nchmod 755 logs uploads\n\n# 设置SSL证书权限（如果存在）\nif [ -f \"ssl/private.key\" ] && [ -f \"ssl/certificate.crt\" ]; then\n    chmod 600 ssl/private.key\n    chmod 644 ssl/certificate.crt\n    log_info \"SSL证书权限已设置\"\nfi\n\nlog_success \"文件权限设置完成\"\n\n# 步骤8: 测试应用启动\nlog_step \"测试应用启动\"\n\nlog_info \"尝试直接启动应用进行测试...\"\ntimeout 10 node bin/www 2>&1 | head -10 || log_warning \"直接启动测试完成（可能有错误但继续）\"\n\n# 步骤9: 启动应用\nlog_step \"启动Node.js应用\"\n\nlog_info \"使用npm start启动应用...\"\nnohup npm start > logs/app.log 2>&1 &\n\n# 获取启动的进程ID\nsleep 3\nAPP_PID=$(pgrep -f 'node.*bin/www' || echo \"\")\n\nif [ -n \"$APP_PID\" ]; then\n    log_info \"应用已启动，PID: $APP_PID\"\nelse\n    log_warning \"应用可能启动失败，等待更长时间...\"\nfi\n\n# 等待应用完全启动\nlog_info \"等待应用完全启动...\"\nfor i in {1..20}; do\n    echo -n \".\"\n    sleep 1\ndone\necho \"\"\n\nlog_success \"应用启动命令已执行\"\n\n# 步骤10: 验证应用状态\nlog_step \"验证应用状态\"\n\n# 检查进程\nNODE_PROCESSES=$(ps aux | grep 'node.*bin/www' | grep -v grep || echo \"\")\nif [ -n \"$NODE_PROCESSES\" ]; then\n    log_success \"Node.js应用正在运行\"\n    echo \"$NODE_PROCESSES\"\nelse\n    log_error \"Node.js应用未运行\"\n    echo \"\"\n    log_info \"查看启动日志:\"\n    tail -30 logs/app.log\n    echo \"\"\n    log_info \"查看package.json scripts:\"\n    cat package.json | grep -A 5 '\"scripts\"' || echo \"无法读取scripts\"\n    exit 1\nfi\n\n# 检查端口3000\nPORT_CHECK=$(netstat -tlnp | grep :3000 || echo \"\")\nif [ -n \"$PORT_CHECK\" ]; then\n    log_success \"端口3000正在监听\"\n    echo \"$PORT_CHECK\"\nelse\n    log_warning \"端口3000未监听，应用可能还在初始化\"\nfi\n\n# 步骤11: 测试访问\nlog_step \"测试应用访问\"\n\n# 等待端口监听\nlog_info \"等待端口3000开始监听...\"\nfor i in {1..30}; do\n    if netstat -tlnp | grep :3000 > /dev/null; then\n        log_success \"端口3000已开始监听\"\n        break\n    fi\n    echo -n \".\"\n    sleep 1\ndone\necho \"\"\n\n# 测试本地3000端口\nlog_info \"测试本地3000端口...\"\nHTTP_CODE=$(curl -s -o /dev/null -w '%{http_code}' http://localhost:3000 2>/dev/null || echo \"Failed\")\necho \"HTTP 3000端口测试: $HTTP_CODE\"\n\n# 测试HTTPS访问\nlog_info \"测试HTTPS访问...\"\nHTTPS_CODE=$(curl -s -o /dev/null -w '%{http_code}' -k https://localhost 2>/dev/null || echo \"Failed\")\necho \"HTTPS访问测试: $HTTPS_CODE\"\n\n# 测试健康检查\nlog_info \"测试健康检查...\"\nHEALTH_CHECK=$(curl -s -k https://localhost/health 2>/dev/null || echo \"Failed\")\necho \"健康检查结果: $HEALTH_CHECK\"\n\n# 步骤12: 最终结果\necho \"\"\necho -e \"${CYAN}========================================${NC}\"\necho -e \"${CYAN}🎯 修复结果总结${NC}\"\necho -e \"${CYAN}========================================${NC}\"\n\n# 最终状态检查\nFINAL_NODE_PROCESS=$(ps aux | grep 'node.*bin/www' | grep -v grep || echo \"\")\nFINAL_PORT_CHECK=$(netstat -tlnp | grep :3000 || echo \"\")\n\n# 判断修复是否成功\nif [ -n \"$FINAL_NODE_PROCESS\" ] && [ -n \"$FINAL_PORT_CHECK\" ] && ([ \"$HTTPS_CODE\" = \"200\" ] || [ \"$HTTPS_CODE\" = \"301\" ] || [ \"$HTTPS_CODE\" = \"302\" ]); then\n    echo -e \"${GREEN}🎉 502错误修复完全成功！${NC}\"\n    echo \"\"\n    echo -e \"${GREEN}✅ 修复状态:${NC}\"\n    echo \"   Node.js版本: $(node --version)\"\n    echo \"   npm版本: $(npm --version)\"\n    echo \"   应用状态: 正在运行\"\n    echo \"   端口3000: 正在监听\"\n    echo \"   HTTPS访问: 正常 ($HTTPS_CODE)\"\n    echo \"\"\n    echo -e \"${CYAN}🌐 访问地址:${NC}\"\n    echo \"   主站首页: https://dl380\"\n    echo \"   新闻页面: https://dl380/news/index\"\n    echo \"   管理登录: https://dl380/admin/login\"\n    echo \"   新闻管理: https://dl380/admin/news\"\n    echo \"\"\n    echo -e \"${CYAN}🔐 登录信息:${NC}\"\n    echo \"   管理员: admin / admin123\"\n    echo \"   编辑员: user01 / user123\"\n    echo \"\"\n    echo -e \"${GREEN}🎊 现在可以正常访问网站了！${NC}\"\n    \nelif [ -n \"$FINAL_NODE_PROCESS\" ] && [ -n \"$FINAL_PORT_CHECK\" ]; then\n    echo -e \"${YELLOW}⚠️ 应用已启动，端口正在监听，但HTTPS访问可能需要时间${NC}\"\n    echo \"\"\n    echo \"请等待1-2分钟后再次测试访问\"\n    echo \"或检查Nginx配置是否正确\"\n    \nelif [ -n \"$FINAL_NODE_PROCESS\" ]; then\n    echo -e \"${YELLOW}⚠️ 应用已启动，但端口可能还在初始化${NC}\"\n    echo \"\"\n    echo \"请等待片刻后再次检查端口状态\"\n    \nelse\n    echo -e \"${RED}❌ 修复失败，应用未能正常启动${NC}\"\n    echo \"\"\n    echo \"问题诊断:\"\n    echo \"应用进程: $([ -n \"$FINAL_NODE_PROCESS\" ] && echo '运行中' || echo '未运行')\"\n    echo \"端口3000: $([ -n \"$FINAL_PORT_CHECK\" ] && echo '监听中' || echo '未监听')\"\n    echo \"HTTPS访问: $HTTPS_CODE\"\n    echo \"\"\n    echo \"请查看错误日志: tail -50 logs/app.log\"\nfi\n\necho \"\"\necho -e \"${BLUE}📝 重要提示:${NC}\"\necho \"1. 应用日志位置: $APP_DIR/logs/app.log\"\necho \"2. 如需重启应用: cd $APP_DIR && ./run.sh restart\"\necho \"3. 查看实时日志: tail -f $APP_DIR/logs/app.log\"\necho \"4. 检查应用状态: ps aux | grep node\"\n\necho \"\"\necho -e \"${CYAN}一键修复脚本执行完成！${NC}\"\n", "modifiedCode": "#!/bin/bash\n\n# 一键修复502错误脚本 - 在dl380上执行\n# 解决express-session模块缺失和依赖问题\n\nset -e\n\n# 颜色定义\nRED='\\033[0;31m'\nGREEN='\\033[0;32m'\nYELLOW='\\033[1;33m'\nBLUE='\\033[0;34m'\nPURPLE='\\033[0;35m'\nCYAN='\\033[0;36m'\nNC='\\033[0m' # No Color\n\n# 日志函数\nlog_info() {\n    echo -e \"${BLUE}[INFO]${NC} $1\"\n}\n\nlog_success() {\n    echo -e \"${GREEN}[SUCCESS]${NC} $1\"\n}\n\nlog_warning() {\n    echo -e \"${YELLOW}[WARNING]${NC} $1\"\n}\n\nlog_error() {\n    echo -e \"${RED}[ERROR]${NC} $1\"\n}\n\nlog_step() {\n    echo -e \"${PURPLE}[STEP]${NC} $1\"\n}\n\necho -e \"${CYAN}🔧 一键修复502错误脚本${NC}\"\necho -e \"${CYAN}========================${NC}\"\necho \"执行主机: $(hostname)\"\necho \"当前用户: $(whoami)\"\necho \"当前时间: $(date)\"\necho \"Node.js版本: $(node --version)\"\necho \"npm版本: $(npm --version)\"\necho \"\"\n\n# 步骤1: 进入应用目录\nlog_step \"进入应用目录\"\nAPP_DIR=\"/home/<USER>/ronglianweb\"\n\nif [ ! -d \"$APP_DIR\" ]; then\n    log_error \"应用目录不存在: $APP_DIR\"\n    exit 1\nfi\n\ncd \"$APP_DIR\"\nlog_info \"当前目录: $(pwd)\"\n\n# 检查关键文件\nif [ ! -f \"package.json\" ]; then\n    log_error \"package.json文件不存在\"\n    exit 1\nfi\n\nif [ ! -f \"app.js\" ]; then\n    log_error \"app.js文件不存在\"\n    exit 1\nfi\n\nif [ ! -f \"bin/www\" ]; then\n    log_error \"bin/www文件不存在\"\n    exit 1\nfi\n\nlog_success \"关键文件检查完成\"\n\n# 步骤2: 停止现有进程\nlog_step \"停止现有Node.js进程\"\npkill -f 'node.*bin/www' 2>/dev/null || true\npkill -f 'npm.*start' 2>/dev/null || true\nsleep 3\nlog_success \"现有进程已停止\"\n\n# 步骤3: 清理和重新安装依赖\nlog_step \"清理npm缓存和依赖\"\n\nlog_info \"清理npm缓存...\"\nnpm cache clean --force\n\nlog_info \"删除node_modules和package-lock.json...\"\nrm -rf node_modules package-lock.json\n\nlog_success \"清理完成\"\n\n# 步骤4: 重新安装依赖\nlog_step \"重新安装应用依赖\"\n\nlog_info \"安装package.json中的依赖...\"\nnpm install\n\nif [ $? -ne 0 ]; then\n    log_warning \"npm install失败，尝试使用--legacy-peer-deps...\"\n    npm install --legacy-peer-deps\nfi\n\nlog_success \"基础依赖安装完成\"\n\n# 步骤5: 手动安装关键模块\nlog_step \"手动安装关键模块\"\n\n# 关键模块列表\nCRITICAL_MODULES=(\n    \"express\"\n    \"express-session\"\n    \"jade\"\n    \"body-parser\"\n    \"cookie-parser\"\n    \"morgan\"\n    \"serve-favicon\"\n    \"multer\"\n    \"debug\"\n    \"http-errors\"\n)\n\nlog_info \"安装关键模块...\"\nfor module in \"${CRITICAL_MODULES[@]}\"; do\n    if [ ! -d \"node_modules/$module\" ]; then\n        log_info \"安装缺失模块: $module\"\n        npm install \"$module\" --save\n    else\n        log_info \"模块已存在: $module\"\n    fi\ndone\n\nlog_success \"关键模块安装完成\"\n\n# 步骤6: 验证关键模块\nlog_step \"验证关键模块安装\"\n\nMISSING_MODULES=()\nfor module in \"${CRITICAL_MODULES[@]}\"; do\n    if [ ! -d \"node_modules/$module\" ]; then\n        MISSING_MODULES+=(\"$module\")\n        log_error \"模块缺失: $module\"\n    else\n        log_success \"模块已安装: $module\"\n    fi\ndone\n\nif [ ${#MISSING_MODULES[@]} -gt 0 ]; then\n    log_warning \"发现缺失模块，尝试重新安装...\"\n    for module in \"${MISSING_MODULES[@]}\"; do\n        npm install \"$module\" --save --force\n    done\nfi\n\n# 步骤7: 设置权限\nlog_step \"设置文件权限\"\n\nchmod +x bin/www\nchmod +x run.sh deploy.sh deploy-https.sh 2>/dev/null || true\nmkdir -p logs uploads\nchmod 755 logs uploads\n\n# 设置SSL证书权限（如果存在）\nif [ -f \"ssl/private.key\" ] && [ -f \"ssl/certificate.crt\" ]; then\n    chmod 600 ssl/private.key\n    chmod 644 ssl/certificate.crt\n    log_info \"SSL证书权限已设置\"\nfi\n\nlog_success \"文件权限设置完成\"\n\n# 步骤8: 测试应用启动\nlog_step \"测试应用启动\"\n\nlog_info \"尝试直接启动应用进行测试...\"\ntimeout 10 node bin/www 2>&1 | head -10 || log_warning \"直接启动测试完成（可能有错误但继续）\"\n\n# 步骤9: 启动应用\nlog_step \"启动Node.js应用\"\n\nlog_info \"使用npm start启动应用...\"\nnohup npm start > logs/app.log 2>&1 &\n\n# 获取启动的进程ID\nsleep 3\nAPP_PID=$(pgrep -f 'node.*bin/www' || echo \"\")\n\nif [ -n \"$APP_PID\" ]; then\n    log_info \"应用已启动，PID: $APP_PID\"\nelse\n    log_warning \"应用可能启动失败，等待更长时间...\"\nfi\n\n# 等待应用完全启动\nlog_info \"等待应用完全启动...\"\nfor i in {1..20}; do\n    echo -n \".\"\n    sleep 1\ndone\necho \"\"\n\nlog_success \"应用启动命令已执行\"\n\n# 步骤10: 验证应用状态\nlog_step \"验证应用状态\"\n\n# 检查进程\nNODE_PROCESSES=$(ps aux | grep 'node.*bin/www' | grep -v grep || echo \"\")\nif [ -n \"$NODE_PROCESSES\" ]; then\n    log_success \"Node.js应用正在运行\"\n    echo \"$NODE_PROCESSES\"\nelse\n    log_error \"Node.js应用未运行\"\n    echo \"\"\n    log_info \"查看启动日志:\"\n    tail -30 logs/app.log\n    echo \"\"\n    log_info \"查看package.json scripts:\"\n    cat package.json | grep -A 5 '\"scripts\"' || echo \"无法读取scripts\"\n    exit 1\nfi\n\n# 检查端口3000\nPORT_CHECK=$(netstat -tlnp | grep :3000 || echo \"\")\nif [ -n \"$PORT_CHECK\" ]; then\n    log_success \"端口3000正在监听\"\n    echo \"$PORT_CHECK\"\nelse\n    log_warning \"端口3000未监听，应用可能还在初始化\"\nfi\n\n# 步骤11: 测试访问\nlog_step \"测试应用访问\"\n\n# 等待端口监听\nlog_info \"等待端口3000开始监听...\"\nfor i in {1..30}; do\n    if netstat -tlnp | grep :3000 > /dev/null; then\n        log_success \"端口3000已开始监听\"\n        break\n    fi\n    echo -n \".\"\n    sleep 1\ndone\necho \"\"\n\n# 测试本地3000端口\nlog_info \"测试本地3000端口...\"\nHTTP_CODE=$(curl -s -o /dev/null -w '%{http_code}' http://localhost:3000 2>/dev/null || echo \"Failed\")\necho \"HTTP 3000端口测试: $HTTP_CODE\"\n\n# 测试HTTPS访问\nlog_info \"测试HTTPS访问...\"\nHTTPS_CODE=$(curl -s -o /dev/null -w '%{http_code}' -k https://localhost 2>/dev/null || echo \"Failed\")\necho \"HTTPS访问测试: $HTTPS_CODE\"\n\n# 测试健康检查\nlog_info \"测试健康检查...\"\nHEALTH_CHECK=$(curl -s -k https://localhost/health 2>/dev/null || echo \"Failed\")\necho \"健康检查结果: $HEALTH_CHECK\"\n\n# 步骤12: 最终结果\necho \"\"\necho -e \"${CYAN}========================================${NC}\"\necho -e \"${CYAN}🎯 修复结果总结${NC}\"\necho -e \"${CYAN}========================================${NC}\"\n\n# 最终状态检查\nFINAL_NODE_PROCESS=$(ps aux | grep 'node.*bin/www' | grep -v grep || echo \"\")\nFINAL_PORT_CHECK=$(netstat -tlnp | grep :3000 || echo \"\")\n\n# 判断修复是否成功\nif [ -n \"$FINAL_NODE_PROCESS\" ] && [ -n \"$FINAL_PORT_CHECK\" ] && ([ \"$HTTPS_CODE\" = \"200\" ] || [ \"$HTTPS_CODE\" = \"301\" ] || [ \"$HTTPS_CODE\" = \"302\" ]); then\n    echo -e \"${GREEN}🎉 502错误修复完全成功！${NC}\"\n    echo \"\"\n    echo -e \"${GREEN}✅ 修复状态:${NC}\"\n    echo \"   Node.js版本: $(node --version)\"\n    echo \"   npm版本: $(npm --version)\"\n    echo \"   应用状态: 正在运行\"\n    echo \"   端口3000: 正在监听\"\n    echo \"   HTTPS访问: 正常 ($HTTPS_CODE)\"\n    echo \"\"\n    echo -e \"${CYAN}🌐 访问地址:${NC}\"\n    echo \"   主站首页: https://dl380\"\n    echo \"   新闻页面: https://dl380/news/index\"\n    echo \"   管理登录: https://dl380/admin/login\"\n    echo \"   新闻管理: https://dl380/admin/news\"\n    echo \"\"\n    echo -e \"${CYAN}🔐 登录信息:${NC}\"\n    echo \"   管理员: admin / admin123\"\n    echo \"   编辑员: user01 / user123\"\n    echo \"\"\n    echo -e \"${GREEN}🎊 现在可以正常访问网站了！${NC}\"\n    \nelif [ -n \"$FINAL_NODE_PROCESS\" ] && [ -n \"$FINAL_PORT_CHECK\" ]; then\n    echo -e \"${YELLOW}⚠️ 应用已启动，端口正在监听，但HTTPS访问可能需要时间${NC}\"\n    echo \"\"\n    echo \"请等待1-2分钟后再次测试访问\"\n    echo \"或检查Nginx配置是否正确\"\n    \nelif [ -n \"$FINAL_NODE_PROCESS\" ]; then\n    echo -e \"${YELLOW}⚠️ 应用已启动，但端口可能还在初始化${NC}\"\n    echo \"\"\n    echo \"请等待片刻后再次检查端口状态\"\n    \nelse\n    echo -e \"${RED}❌ 修复失败，应用未能正常启动${NC}\"\n    echo \"\"\n    echo \"问题诊断:\"\n    echo \"应用进程: $([ -n \"$FINAL_NODE_PROCESS\" ] && echo '运行中' || echo '未运行')\"\n    echo \"端口3000: $([ -n \"$FINAL_PORT_CHECK\" ] && echo '监听中' || echo '未监听')\"\n    echo \"HTTPS访问: $HTTPS_CODE\"\n    echo \"\"\n    echo \"请查看错误日志: tail -50 logs/app.log\"\nfi\n\necho \"\"\necho -e \"${BLUE}📝 重要提示:${NC}\"\necho \"1. 应用日志位置: $APP_DIR/logs/app.log\"\necho \"2. 如需重启应用: cd $APP_DIR && ./run.sh restart\"\necho \"3. 查看实时日志: tail -f $APP_DIR/logs/app.log\"\necho \"4. 检查应用状态: ps aux | grep node\"\n\necho \"\"\necho -e \"${CYAN}一键修复脚本执行完成！${NC}\"\n"}