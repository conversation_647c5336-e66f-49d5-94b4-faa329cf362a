{"path": {"rootPath": "/home", "relPath": "website/ronglianweb/libs/config.js"}, "originalCode": "//所有的配置，都需要写在这个文件中，如果需要用zookeeper进行集中化配置，将此文件的逻辑重新实现\r\n/**\r\n * [config description]\r\n * @type {Object}\r\n */\r\nvar config = {\r\n    RetailerService: {\r\n        host: '**********',// //*********\r\n        port: 8101,\r\n        context: '',\r\n        encrypt: true,//数据是否加密\r\n        subsystemName: 'shopping',//系统名字\r\n        subsystemKey: '2cfca5a66e8e409ab85d2f400b2d4e2a',//系统密钥\r\n        retailersId: \"255fa8683f6d4693a2b8d616b240a93e\"//商户编号\r\n    },\r\n    ImageService: {\r\n        host: '**********',\r\n        port: 8103,\r\n        context: '',\r\n        encrypt: true,//数据是否加密\r\n        subsystemName: 'shopping',//系统名字\r\n        subsystemKey: '2cfca5a66e8e409ab85d2f400b2d4e2a',//系统密钥\r\n        retailersId: \"255fa8683f6d4693a2b8d616b240a93e\",//商户编号\r\n        domain:\"http://im1.ronglian.com:8103\"\r\n    },\r\n    LogService: {\r\n        host: '**********',\r\n        port: 8092,\r\n        context: '',\r\n        encrypt: true,//数据是否加密\r\n        subsystemName: 'shopping',//系统名字\r\n        subsystemKey: '2cfca5a66e8e409ab85d2f400b2d4e2a',//系统密钥\r\n        retailersId: \"255fa8683f6d4693a2b8d616b240a93e\",//商户编号\r\n        system: 'retailer'\r\n    },\r\n    DistrictService: {\r\n        host: '**********',\r\n        port: 8091,\r\n        context: '',\r\n        encrypt: true,//数据是否加密\r\n        subsystemName: 'shopping',//系统名字\r\n        subsystemKey: '2cfca5a66e8e409ab85d2f400b2d4e2a',//系统密钥\r\n        retailersId: \"255fa8683f6d4693a2b8d616b240a93e\",//商户编号\r\n        superId: '4cd36dbcc8b011e584680050569b1c58'//国家id\r\n    },\r\n    EmailService: {\r\n        host: '**********',\r\n        port: 8093,\r\n        context: '',\r\n        encrypt: true,//数据是否加密\r\n        subsystemName: 'retailersnode',\r\n        subsystemKey: 'erewfewfwe',\r\n        retailersId: \"11\"//商户编号\r\n    },\r\n    AlipayService: {\r\n        url: 'https://mapi.alipay.com/gateway.do?',//支付宝接口url\r\n        payment_type: '1',\r\n        _input_charset: 'UTF-8',\r\n        service: 'create_direct_pay_by_user',\r\n        //服务器异步通知页面路径,需http://格式的完整路径，不能加?id=123这类自定义参数\r\n        notify_url: 'http://shopping.ronglian.com:8080/buy/ar',//支付宝异步返回接口url\r\n        //页面跳转同步通知页面路径,需http://格式的完整路径，不能加?id=123这类自定义参数，不能写成http://localhost/\r\n        return_url: 'http://shopping.ronglian.com:8080/buy/r',//支付宝同步返回接口url\r\n        show_url: 'https://mail.ronglian.com',//商品展示的url,非必须\r\n        verify_url: 'https://mapi.alipay.com/gateway.do?service=notify_verify&'//支付宝消息验证地址\r\n    },\r\n    WeChatService:{\r\n        prePayUrl:'https://api.mch.weixin.qq.com/pay/unifiedorder',//预支付接口url\r\n        notify_url:'http://weixin.bio.ronglian.com/buy/wr'//回调url\r\n    },\r\n    smsService:{//阿里短信平台\r\n        appkey:'23440508',\r\n        appsecret:'e16b1ffe621c5cc9ef585f3d4f766547',\r\n        sms_free_sign_name: '荣联科技生物云',//头部签名\r\n        verify_template_code: 'SMS_13890355',//验证码，模板ID\r\n        retailersId:'255fa8683f6d4693a2b8d616b240a93e'//商户ID\r\n    },\r\n    redis: {\r\n        port: '6379',\r\n        ip: '**********',\r\n        password:'Uec62602000!@#$',\r\n        timeOut: '1296000' //单位秒\r\n    },\r\n    mongodb: {\r\n        db: 'retailers',\r\n        host: '**********',\r\n        port:27017,\r\n        dbRoot:'file',\r\n        url:'mongodb://**********:27017/retailers',\r\n        encoding:'utf-8'\r\n    }\r\n\r\n};\r\nconfig.SHOPPINGURL=(process.env.SHOPPING_URL || \"http://test1.ronglian.com\");\r\n//商户\r\nconfig.RetailerService.host = (process.env.RetailerService_SERVICE_HOST || config.RetailerService.host);\r\nconfig.RetailerService.port = (process.env.RetailerService_SERVICE_PORT || config.RetailerService.port);\r\nconfig.RetailerService.encrypt = (process.env.RetailerService_SERVICE_ENCRYPT || config.RetailerService.port);\r\nconfig.RetailerService.subsystem_name = (process.env.RetailerService_SERVICE_SUBSYSTEM_NAME || config.RetailerService.subsystem_name);\r\nconfig.RetailerService.subsystem_key = (process.env.RetailerService_SERVICE_SUBSYSTEM_KEY || config.RetailerService.subsystem_key);\r\nconfig.RetailerService.retailersId = (process.env.RetailerService_SERVICE_RETAILERSID || config.RetailerService.retailersId);\r\n\r\n//图片\r\nconfig.ImageService.host = (process.env.ImageService_SERVICE_HOST || config.ImageService.host);\r\nconfig.ImageService.port = (process.env.ImageService_SERVICE_PORT || config.ImageService.port);\r\nconfig.ImageService.encrypt = (process.env.ImageService_SERVICE_ENCRYPT || config.ImageService.port);\r\nconfig.ImageService.subsystem_name = (process.env.ImageService_SERVICE_SUBSYSTEM_NAME || config.ImageService.subsystem_name);\r\nconfig.ImageService.subsystem_key = (process.env.ImageService_SERVICE_SUBSYSTEM_KEY || config.ImageService.subsystem_key);\r\nconfig.ImageService.retailersId = (process.env.ImageService_SERVICE_RETAILERSID || config.ImageService.retailersId);\r\n\r\n//log\r\nconfig.LogService.host = (process.env.LogService_SERVICE_HOST || config.LogService.host);\r\nconfig.LogService.port = (process.env.LogService_SERVICE_PORT || config.LogService.port);\r\nconfig.LogService.encrypt = (process.env.LogService_SERVICE_ENCRYPT || config.LogService.port);\r\nconfig.LogService.subsystem_name = (process.env.LogService_SERVICE_SUBSYSTEM_NAME || config.LogService.subsystem_name);\r\nconfig.LogService.subsystem_key = (process.env.LogService_SERVICE_SUBSYSTEM_KEY || config.LogService.subsystem_key);\r\nconfig.LogService.retailersId = (process.env.LogService_SERVICE_RETAILERSID || config.LogService.retailersId);\r\n\r\n\r\n//地区\r\nconfig.DistrictService.host = (process.env.DistrictService_SERVICE_HOST || config.DistrictService.host);\r\nconfig.DistrictService.port = (process.env.DistrictService_SERVICE_PORT || config.DistrictService.port);\r\nconfig.DistrictService.encrypt = (process.env.DistrictService_SERVICE_ENCRYPT || config.DistrictService.port);\r\nconfig.DistrictService.subsystem_name = (process.env.DistrictService_SERVICE_SUBSYSTEM_NAME || config.DistrictService.subsystem_name);\r\nconfig.DistrictService.subsystem_key = (process.env.DistrictService_SERVICE_SUBSYSTEM_KEY || config.DistrictService.subsystem_key);\r\nconfig.DistrictService.retailersId = (process.env.DistrictService_SERVICE_RETAILERSID || config.DistrictService.retailersId);\r\nconfig.DistrictService.superId = (process.env.DISTRICTSERVICE_SERVICE_SUPERID || config.DistrictService.superId);\r\n\r\n//email\r\nconfig.EmailService.host = (process.env.EmailService_SERVICE_HOST || config.EmailService.host);\r\nconfig.EmailService.port = (process.env.EmailService_SERVICE_PORT || config.EmailService.port);\r\nconfig.EmailService.encrypt = (process.env.EmailService_SERVICE_ENCRYPT || config.EmailService.port);\r\nconfig.EmailService.subsystem_name = (process.env.EmailService_SERVICE_SUBSYSTEM_NAME || config.EmailService.subsystem_name);\r\nconfig.EmailService.subsystem_key = (process.env.EmailService_SERVICE_SUBSYSTEM_KEY || config.EmailService.subsystem_key);\r\nconfig.EmailService.retailersId = (process.env.EmailService_SERVICE_RETAILERSID || config.EmailService.retailersId);\r\n\r\n\r\n//redis\r\nconfig.redis.port = (process.env.REDIS_PORT || config.redis.port);\r\nconfig.redis.ip = (process.env.REDIS_IP || config.redis.ip);\r\nconfig.redis.timeOut = (process.env.REDIS_TIMEOUT || config.redis.timeOut);\r\nconfig.redis.password = (process.env.REDIS_PASSWORD || config.redis.password);\r\n//mongodb\r\nconfig.mongodb.db = (process.env.MONGODB_DB || config.mongodb.db);\r\nconfig.mongodb.host = (process.env.MONGODB_HOST || config.mongodb.host);\r\nconfig.mongodb.port = (process.env.MONGODB_PORT || config.mongodb.port);\r\nconfig.mongodb.dbRoot = (process.env.MONGODB_DBROOT || config.mongodb.dbRoot);\r\nconfig.mongodb.url = (process.env.MONGODB_URL || config.mongodb.url);\r\nconfig.mongodb.encoding = (process.env.MONGODB_ENCODING || config.mongodb.encoding);\r\n//支付宝\r\nconfig.AlipayService.url = (process.env.ALIPAYSERVICE_URL || config.AlipayService.url);\r\nconfig.AlipayService.payment_type = (process.env.ALIPAYSERVICE_PAYMENT_TYPE || config.AlipayService.payment_type);\r\nconfig.AlipayService._input_charset = (process.env.ALIPAYSERVICE_INPUT_CHARSET || config.AlipayService._input_charset);\r\nconfig.AlipayService.notify_url = (process.env.ALIPAYSERVICE_NOTIFY_URL || config.AlipayService.notify_url);\r\nconfig.AlipayService.return_url = (process.env.ALIPAYSERVICE_RETURN_URL || config.AlipayService.return_url);\r\nconfig.AlipayService.show_url = (process.env.ALIPAYSERVICE_SHOW_URL || config.AlipayService.show_url);\r\nconfig.AlipayService.verify_url = (process.env.ALIPAYSERVICE_VERIFY_URL || config.AlipayService.verify_url);\r\n\r\n\r\n//微信WeChatService\r\nconfig.WeChatService.notify_url = (process.env.WeChatService_NOTIFY_URL || config.WeChatService.notify_url);\r\n\r\nmodule.exports = config;\r\n", "modifiedCode": "//所有的配置，都需要写在这个文件中，如果需要用zookeeper进行集中化配置，将此文件的逻辑重新实现\r\n/**\r\n * [config description]\r\n * @type {Object}\r\n */\r\nvar config = {\r\n    RetailerService: {\r\n        host: '**********',// //*********\r\n        port: 8101,\r\n        context: '',\r\n        encrypt: true,//数据是否加密\r\n        subsystemName: 'shopping',//系统名字\r\n        subsystemKey: '2cfca5a66e8e409ab85d2f400b2d4e2a',//系统密钥\r\n        retailersId: \"255fa8683f6d4693a2b8d616b240a93e\"//商户编号\r\n    },\r\n    ImageService: {\r\n        host: '**********',\r\n        port: 8103,\r\n        context: '',\r\n        encrypt: true,//数据是否加密\r\n        subsystemName: 'shopping',//系统名字\r\n        subsystemKey: '2cfca5a66e8e409ab85d2f400b2d4e2a',//系统密钥\r\n        retailersId: \"255fa8683f6d4693a2b8d616b240a93e\",//商户编号\r\n        domain:\"http://im1.ronglian.com:8103\"\r\n    },\r\n    LogService: {\r\n        host: '**********',\r\n        port: 8092,\r\n        context: '',\r\n        encrypt: true,//数据是否加密\r\n        subsystemName: 'shopping',//系统名字\r\n        subsystemKey: '2cfca5a66e8e409ab85d2f400b2d4e2a',//系统密钥\r\n        retailersId: \"255fa8683f6d4693a2b8d616b240a93e\",//商户编号\r\n        system: 'retailer'\r\n    },\r\n    DistrictService: {\r\n        host: '**********',\r\n        port: 8091,\r\n        context: '',\r\n        encrypt: true,//数据是否加密\r\n        subsystemName: 'shopping',//系统名字\r\n        subsystemKey: '2cfca5a66e8e409ab85d2f400b2d4e2a',//系统密钥\r\n        retailersId: \"255fa8683f6d4693a2b8d616b240a93e\",//商户编号\r\n        superId: '4cd36dbcc8b011e584680050569b1c58'//国家id\r\n    },\r\n    EmailService: {\r\n        host: '**********',\r\n        port: 8093,\r\n        context: '',\r\n        encrypt: true,//数据是否加密\r\n        subsystemName: 'retailersnode',\r\n        subsystemKey: 'erewfewfwe',\r\n        retailersId: \"11\"//商户编号\r\n    },\r\n    AlipayService: {\r\n        url: 'https://mapi.alipay.com/gateway.do?',//支付宝接口url\r\n        payment_type: '1',\r\n        _input_charset: 'UTF-8',\r\n        service: 'create_direct_pay_by_user',\r\n        //服务器异步通知页面路径,需http://格式的完整路径，不能加?id=123这类自定义参数\r\n        notify_url: 'http://shopping.ronglian.com:8080/buy/ar',//支付宝异步返回接口url\r\n        //页面跳转同步通知页面路径,需http://格式的完整路径，不能加?id=123这类自定义参数，不能写成http://localhost/\r\n        return_url: 'http://shopping.ronglian.com:8080/buy/r',//支付宝同步返回接口url\r\n        show_url: 'https://mail.ronglian.com',//商品展示的url,非必须\r\n        verify_url: 'https://mapi.alipay.com/gateway.do?service=notify_verify&'//支付宝消息验证地址\r\n    },\r\n    WeChatService:{\r\n        prePayUrl:'https://api.mch.weixin.qq.com/pay/unifiedorder',//预支付接口url\r\n        notify_url:'http://weixin.bio.ronglian.com/buy/wr'//回调url\r\n    },\r\n    smsService:{//阿里短信平台\r\n        appkey:'23440508',\r\n        appsecret:'e16b1ffe621c5cc9ef585f3d4f766547',\r\n        sms_free_sign_name: '荣联科技生物云',//头部签名\r\n        verify_template_code: 'SMS_13890355',//验证码，模板ID\r\n        retailersId:'255fa8683f6d4693a2b8d616b240a93e'//商户ID\r\n    },\r\n    redis: {\r\n        port: '6379',\r\n        ip: '**********',\r\n        password:'Uec62602000!@#$',\r\n        timeOut: '1296000' //单位秒\r\n    },\r\n    mongodb: {\r\n        db: 'retailers',\r\n        host: '**********',\r\n        port:27017,\r\n        dbRoot:'file',\r\n        url:'mongodb://**********:27017/retailers',\r\n        encoding:'utf-8'\r\n    }\r\n\r\n};\r\nconfig.SHOPPINGURL=(process.env.SHOPPING_URL || \"http://test1.ronglian.com\");\r\n//商户\r\nconfig.RetailerService.host = (process.env.RetailerService_SERVICE_HOST || config.RetailerService.host);\r\nconfig.RetailerService.port = (process.env.RetailerService_SERVICE_PORT || config.RetailerService.port);\r\nconfig.RetailerService.encrypt = (process.env.RetailerService_SERVICE_ENCRYPT || config.RetailerService.port);\r\nconfig.RetailerService.subsystem_name = (process.env.RetailerService_SERVICE_SUBSYSTEM_NAME || config.RetailerService.subsystem_name);\r\nconfig.RetailerService.subsystem_key = (process.env.RetailerService_SERVICE_SUBSYSTEM_KEY || config.RetailerService.subsystem_key);\r\nconfig.RetailerService.retailersId = (process.env.RetailerService_SERVICE_RETAILERSID || config.RetailerService.retailersId);\r\n\r\n//图片\r\nconfig.ImageService.host = (process.env.ImageService_SERVICE_HOST || config.ImageService.host);\r\nconfig.ImageService.port = (process.env.ImageService_SERVICE_PORT || config.ImageService.port);\r\nconfig.ImageService.encrypt = (process.env.ImageService_SERVICE_ENCRYPT || config.ImageService.port);\r\nconfig.ImageService.subsystem_name = (process.env.ImageService_SERVICE_SUBSYSTEM_NAME || config.ImageService.subsystem_name);\r\nconfig.ImageService.subsystem_key = (process.env.ImageService_SERVICE_SUBSYSTEM_KEY || config.ImageService.subsystem_key);\r\nconfig.ImageService.retailersId = (process.env.ImageService_SERVICE_RETAILERSID || config.ImageService.retailersId);\r\n\r\n//log\r\nconfig.LogService.host = (process.env.LogService_SERVICE_HOST || config.LogService.host);\r\nconfig.LogService.port = (process.env.LogService_SERVICE_PORT || config.LogService.port);\r\nconfig.LogService.encrypt = (process.env.LogService_SERVICE_ENCRYPT || config.LogService.port);\r\nconfig.LogService.subsystem_name = (process.env.LogService_SERVICE_SUBSYSTEM_NAME || config.LogService.subsystem_name);\r\nconfig.LogService.subsystem_key = (process.env.LogService_SERVICE_SUBSYSTEM_KEY || config.LogService.subsystem_key);\r\nconfig.LogService.retailersId = (process.env.LogService_SERVICE_RETAILERSID || config.LogService.retailersId);\r\n\r\n\r\n//地区\r\nconfig.DistrictService.host = (process.env.DistrictService_SERVICE_HOST || config.DistrictService.host);\r\nconfig.DistrictService.port = (process.env.DistrictService_SERVICE_PORT || config.DistrictService.port);\r\nconfig.DistrictService.encrypt = (process.env.DistrictService_SERVICE_ENCRYPT || config.DistrictService.port);\r\nconfig.DistrictService.subsystem_name = (process.env.DistrictService_SERVICE_SUBSYSTEM_NAME || config.DistrictService.subsystem_name);\r\nconfig.DistrictService.subsystem_key = (process.env.DistrictService_SERVICE_SUBSYSTEM_KEY || config.DistrictService.subsystem_key);\r\nconfig.DistrictService.retailersId = (process.env.DistrictService_SERVICE_RETAILERSID || config.DistrictService.retailersId);\r\nconfig.DistrictService.superId = (process.env.DISTRICTSERVICE_SERVICE_SUPERID || config.DistrictService.superId);\r\n\r\n//email\r\nconfig.EmailService.host = (process.env.EmailService_SERVICE_HOST || config.EmailService.host);\r\nconfig.EmailService.port = (process.env.EmailService_SERVICE_PORT || config.EmailService.port);\r\nconfig.EmailService.encrypt = (process.env.EmailService_SERVICE_ENCRYPT || config.EmailService.port);\r\nconfig.EmailService.subsystem_name = (process.env.EmailService_SERVICE_SUBSYSTEM_NAME || config.EmailService.subsystem_name);\r\nconfig.EmailService.subsystem_key = (process.env.EmailService_SERVICE_SUBSYSTEM_KEY || config.EmailService.subsystem_key);\r\nconfig.EmailService.retailersId = (process.env.EmailService_SERVICE_RETAILERSID || config.EmailService.retailersId);\r\n\r\n\r\n//redis\r\nconfig.redis.port = (process.env.REDIS_PORT || config.redis.port);\r\nconfig.redis.ip = (process.env.REDIS_IP || config.redis.ip);\r\nconfig.redis.timeOut = (process.env.REDIS_TIMEOUT || config.redis.timeOut);\r\nconfig.redis.password = (process.env.REDIS_PASSWORD || config.redis.password);\r\n//mongodb\r\nconfig.mongodb.db = (process.env.MONGODB_DB || config.mongodb.db);\r\nconfig.mongodb.host = (process.env.MONGODB_HOST || config.mongodb.host);\r\nconfig.mongodb.port = (process.env.MONGODB_PORT || config.mongodb.port);\r\nconfig.mongodb.dbRoot = (process.env.MONGODB_DBROOT || config.mongodb.dbRoot);\r\nconfig.mongodb.url = (process.env.MONGODB_URL || config.mongodb.url);\r\nconfig.mongodb.encoding = (process.env.MONGODB_ENCODING || config.mongodb.encoding);\r\n//支付宝\r\nconfig.AlipayService.url = (process.env.ALIPAYSERVICE_URL || config.AlipayService.url);\r\nconfig.AlipayService.payment_type = (process.env.ALIPAYSERVICE_PAYMENT_TYPE || config.AlipayService.payment_type);\r\nconfig.AlipayService._input_charset = (process.env.ALIPAYSERVICE_INPUT_CHARSET || config.AlipayService._input_charset);\r\nconfig.AlipayService.notify_url = (process.env.ALIPAYSERVICE_NOTIFY_URL || config.AlipayService.notify_url);\r\nconfig.AlipayService.return_url = (process.env.ALIPAYSERVICE_RETURN_URL || config.AlipayService.return_url);\r\nconfig.AlipayService.show_url = (process.env.ALIPAYSERVICE_SHOW_URL || config.AlipayService.show_url);\r\nconfig.AlipayService.verify_url = (process.env.ALIPAYSERVICE_VERIFY_URL || config.AlipayService.verify_url);\r\n\r\n\r\n//微信WeChatService\r\nconfig.WeChatService.notify_url = (process.env.WeChatService_NOTIFY_URL || config.WeChatService.notify_url);\r\n\r\nmodule.exports = config;\r\n"}