{"path": {"rootPath": "/home", "relPath": "website/ronglianweb/views/admin/newsEdit_new.jade"}, "originalCode": "extends layout\n\nblock css\n  link(rel=\"stylesheet\", href=\"/plugins/admin/css/admin.css\")\n  // TinyMCE样式 (替换Quill样式)\n  style.\n    .tox-tinymce {\n      border: 1px solid #ddd !important;\n      border-radius: 4px !important;\n    }\n    .tox .tox-toolbar {\n      background: #f8f9fa !important;\n    }\n    // Word导入/导出按钮样式\n    .word-actions {\n      margin-bottom: 10px;\n      padding: 10px;\n      background: #f0f8ff;\n      border: 1px solid #d1ecf1;\n      border-radius: 4px;\n    }\n    .word-actions .btn {\n      margin-right: 10px;\n    }\n\nblock content\n  .row\n    .col-md-12\n      .panel.panel-default\n        .panel-heading\n          h3.panel-title\n            i.glyphicon.glyphicon-edit\n            if newsId\n              |  编辑新闻\n            else\n              |  创建新闻\n          .pull-right\n            a.btn.btn-default.btn-sm(href=\"/admin/news\")\n              i.glyphicon.glyphicon-arrow-left\n              |  返回列表\n        .panel-body\n          form#newsForm\n            // 基本信息区域\n            .row\n              .col-md-12\n                .panel.panel-info\n                  .panel-heading\n                    h4.panel-title\n                      i.glyphicon.glyphicon-pencil\n                      |  📝 基本信息\n                  .panel-body\n                    .form-group\n                      label.control-label(for=\"title\") 新闻标题 *\n                      input.form-control#title(type=\"text\", name=\"title\", required, placeholder=\"请输入新闻标题\")\n                    \n                    .form-group\n                      label.control-label(for=\"content\") 新闻内容 *\n\n                      // Word导入/导出功能区 (新增)\n                      .word-actions\n                        button.btn.btn-info.btn-sm#importWordBtn(type=\"button\")\n                          i.glyphicon.glyphicon-import\n                          |  导入Word文档\n                        button.btn.btn-success.btn-sm#exportWordBtn(type=\"button\")\n                          i.glyphicon.glyphicon-export\n                          |  导出为Word\n                        input#wordFileInput(type=\"file\", accept=\".doc,.docx\", style=\"display: none;\")\n                        small.text-muted 支持导入.docx格式，保持原有格式和样式\n\n                      // 编辑器容器 (保持原有尺寸)\n                      #editor(style=\"height: 300px;\")\n                      textarea#content(name=\"content\", style=\"display: none;\")\n            \n            // 发布设置和封面图片\n            .row\n              .col-md-6\n                .panel.panel-success\n                  .panel-heading\n                    h4.panel-title\n                      i.glyphicon.glyphicon-cog\n                      |  🚀 发布设置\n                  .panel-body\n                    .form-group\n                      label.control-label(for=\"status\") 状态\n                      select.form-control#status(name=\"status\")\n                        option(value=\"draft\") 草稿\n                        option(value=\"published\") 发布\n                        option(value=\"unpublished\") 下架\n                        option(value=\"archived\") 归档\n                    \n                    .form-group\n                      label.control-label(for=\"author\") 作者\n                      input.form-control#author(type=\"text\", name=\"author\", readonly)\n              \n              .col-md-6\n                .panel.panel-warning\n                  .panel-heading\n                    h4.panel-title\n                      i.glyphicon.glyphicon-picture\n                      |  🖼️ 封面图片\n                  .panel-body\n                    .form-group\n                      label.control-label 上传封面图片\n                      .upload-area#uploadArea\n                        .upload-placeholder\n                          i.glyphicon.glyphicon-cloud-upload\n                          p 点击或拖拽图片到此处上传\n                          small 支持 JPG、PNG、GIF 格式，大小不超过 5MB\n                      input#imageFile(type=\"file\", accept=\"image/*\", style=\"display: none;\")\n                      input#coverImage(type=\"hidden\", name=\"coverImage\")\n                    \n                    .image-preview#imagePreview(style=\"display: none;\")\n                      img#previewImg(style=\"max-width: 100%; height: auto; border-radius: 4px;\")\n                      .image-actions\n                        button.btn.btn-danger.btn-sm#removeImage(type=\"button\") 删除图片\n            \n            // 操作按钮\n            .row\n              .col-md-12\n                .form-actions\n                  button.btn.btn-primary#saveBtn(type=\"submit\")\n                    i.glyphicon.glyphicon-floppy-disk\n                    |  保存\n                  button.btn.btn-success#publishBtn(type=\"button\", style=\"margin-left: 10px;\")\n                    i.glyphicon.glyphicon-ok\n                    |  保存并发布\n                  button.btn.btn-default#previewBtn(type=\"button\", style=\"margin-left: 10px;\")\n                    i.glyphicon.glyphicon-eye-open\n                    |  预览\n                  a.btn.btn-secondary(href=\"/admin/news\", style=\"margin-left: 10px;\")\n                    i.glyphicon.glyphicon-remove\n                    |  取消\n\nblock scripts\n  // TinyMCE编辑器 (替换Quill)\n  script(src=\"https://cdn.jsdelivr.net/npm/tinymce@6/tinymce.min.js\")\n  // Word处理库\n  script(src=\"https://cdn.jsdelivr.net/npm/mammoth@1.6.0/mammoth.browser.min.js\")\n  script(src=\"https://cdn.jsdelivr.net/npm/html-docx-js@0.3.1/dist/html-docx.js\")\n  // 升级后的编辑器脚本\n  script(src=\"/plugins/admin/js/newsEdit-word.js\")\n  script.\n    // 传递新闻ID到前端\n    window.newsId = '#{newsId}';\n    window.currentUser = !{JSON.stringify(user || {})};\n    \n    // 初始化页面\n    $(document).ready(function() {\n      // 设置作者为当前用户\n      if (window.currentUser && window.currentUser.name) {\n        $('#author').val(window.currentUser.name);\n      }\n      \n      // 如果是编辑模式，加载新闻数据\n      if (window.newsId) {\n        loadNewsData(window.newsId);\n      }\n    });\n", "modifiedCode": "extends layout\n\nblock css\n  link(rel=\"stylesheet\", href=\"/plugins/admin/css/admin.css\")\n  // TinyMCE样式 (替换Quill样式)\n  style.\n    .tox-tinymce {\n      border: 1px solid #ddd !important;\n      border-radius: 4px !important;\n    }\n    .tox .tox-toolbar {\n      background: #f8f9fa !important;\n    }\n    // Word导入/导出按钮样式\n    .word-actions {\n      margin-bottom: 10px;\n      padding: 10px;\n      background: #f0f8ff;\n      border: 1px solid #d1ecf1;\n      border-radius: 4px;\n    }\n    .word-actions .btn {\n      margin-right: 10px;\n    }\n\nblock content\n  .row\n    .col-md-12\n      .panel.panel-default\n        .panel-heading\n          h3.panel-title\n            i.glyphicon.glyphicon-edit\n            if newsId\n              |  编辑新闻\n            else\n              |  创建新闻\n          .pull-right\n            a.btn.btn-default.btn-sm(href=\"/admin/news\")\n              i.glyphicon.glyphicon-arrow-left\n              |  返回列表\n        .panel-body\n          form#newsForm\n            // 基本信息区域\n            .row\n              .col-md-12\n                .panel.panel-info\n                  .panel-heading\n                    h4.panel-title\n                      i.glyphicon.glyphicon-pencil\n                      |  📝 基本信息\n                  .panel-body\n                    .form-group\n                      label.control-label(for=\"title\") 新闻标题 *\n                      input.form-control#title(type=\"text\", name=\"title\", required, placeholder=\"请输入新闻标题\")\n                    \n                    .form-group\n                      label.control-label(for=\"content\") 新闻内容 *\n\n                      // Word导入/导出功能区 (新增)\n                      .word-actions\n                        button.btn.btn-info.btn-sm#importWordBtn(type=\"button\")\n                          i.glyphicon.glyphicon-import\n                          |  导入Word文档\n                        button.btn.btn-success.btn-sm#exportWordBtn(type=\"button\")\n                          i.glyphicon.glyphicon-export\n                          |  导出为Word\n                        input#wordFileInput(type=\"file\", accept=\".doc,.docx\", style=\"display: none;\")\n                        small.text-muted 支持导入.docx格式，保持原有格式和样式\n\n                      // 编辑器容器 (保持原有尺寸)\n                      #editor(style=\"height: 300px;\")\n                      textarea#content(name=\"content\", style=\"display: none;\")\n            \n            // 发布设置和封面图片\n            .row\n              .col-md-6\n                .panel.panel-success\n                  .panel-heading\n                    h4.panel-title\n                      i.glyphicon.glyphicon-cog\n                      |  🚀 发布设置\n                  .panel-body\n                    .form-group\n                      label.control-label(for=\"status\") 状态\n                      select.form-control#status(name=\"status\")\n                        option(value=\"draft\") 草稿\n                        option(value=\"published\") 发布\n                        option(value=\"unpublished\") 下架\n                        option(value=\"archived\") 归档\n                    \n                    .form-group\n                      label.control-label(for=\"author\") 作者\n                      input.form-control#author(type=\"text\", name=\"author\", readonly)\n              \n              .col-md-6\n                .panel.panel-warning\n                  .panel-heading\n                    h4.panel-title\n                      i.glyphicon.glyphicon-picture\n                      |  🖼️ 封面图片\n                  .panel-body\n                    .form-group\n                      label.control-label 上传封面图片\n                      .upload-area#uploadArea\n                        .upload-placeholder\n                          i.glyphicon.glyphicon-cloud-upload\n                          p 点击或拖拽图片到此处上传\n                          small 支持 JPG、PNG、GIF 格式，大小不超过 5MB\n                      input#imageFile(type=\"file\", accept=\"image/*\", style=\"display: none;\")\n                      input#coverImage(type=\"hidden\", name=\"coverImage\")\n                    \n                    .image-preview#imagePreview(style=\"display: none;\")\n                      img#previewImg(style=\"max-width: 100%; height: auto; border-radius: 4px;\")\n                      .image-actions\n                        button.btn.btn-danger.btn-sm#removeImage(type=\"button\") 删除图片\n            \n            // 操作按钮\n            .row\n              .col-md-12\n                .form-actions\n                  button.btn.btn-primary#saveBtn(type=\"submit\")\n                    i.glyphicon.glyphicon-floppy-disk\n                    |  保存\n                  button.btn.btn-success#publishBtn(type=\"button\", style=\"margin-left: 10px;\")\n                    i.glyphicon.glyphicon-ok\n                    |  保存并发布\n                  button.btn.btn-default#previewBtn(type=\"button\", style=\"margin-left: 10px;\")\n                    i.glyphicon.glyphicon-eye-open\n                    |  预览\n                  a.btn.btn-secondary(href=\"/admin/news\", style=\"margin-left: 10px;\")\n                    i.glyphicon.glyphicon-remove\n                    |  取消\n\nblock scripts\n  // TinyMCE编辑器 (替换Quill)\n  script(src=\"https://cdn.jsdelivr.net/npm/tinymce@6/tinymce.min.js\")\n  // Word处理库\n  script(src=\"https://cdn.jsdelivr.net/npm/mammoth@1.6.0/mammoth.browser.min.js\")\n  script(src=\"https://cdn.jsdelivr.net/npm/html-docx-js@0.3.1/dist/html-docx.js\")\n  // 升级后的编辑器脚本\n  script(src=\"/plugins/admin/js/newsEdit-word.js\")\n  script.\n    // 传递新闻ID到前端\n    window.newsId = '#{newsId}';\n    window.currentUser = !{JSON.stringify(user || {})};\n    \n    // 初始化页面\n    $(document).ready(function() {\n      // 设置作者为当前用户\n      if (window.currentUser && window.currentUser.name) {\n        $('#author').val(window.currentUser.name);\n      }\n      \n      // 如果是编辑模式，加载新闻数据\n      if (window.newsId) {\n        loadNewsData(window.newsId);\n      }\n    });\n"}