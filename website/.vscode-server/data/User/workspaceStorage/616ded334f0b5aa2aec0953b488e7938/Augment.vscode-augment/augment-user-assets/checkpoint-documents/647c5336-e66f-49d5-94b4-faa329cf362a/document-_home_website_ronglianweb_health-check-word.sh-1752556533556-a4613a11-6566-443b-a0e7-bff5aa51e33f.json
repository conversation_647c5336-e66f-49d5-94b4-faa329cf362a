{"path": {"rootPath": "/home", "relPath": "website/ronglianweb/health-check-word.sh"}, "modifiedCode": "#!/bin/bash\n\n# Word兼容编辑器健康检查脚本\n\necho \"=== Word兼容编辑器健康检查 ===\"\necho \"\"\n\n# 检查Node.js应用状态\necho \"🔍 检查Node.js应用状态...\"\nif pgrep -f \"node.*bin/www\" > /dev/null; then\n    echo \"✅ Node.js应用正在运行\"\n    NODE_STATUS=\"✅ 运行中\"\n    NODE_PID=$(pgrep -f \"node.*bin/www\")\n    echo \"   进程ID: $NODE_PID\"\nelse\n    echo \"❌ Node.js应用未运行\"\n    NODE_STATUS=\"❌ 未运行\"\nfi\n\n# 检查Nginx状态\necho \"🔍 检查Nginx状态...\"\nif sudo systemctl is-active --quiet nginx; then\n    echo \"✅ Nginx正在运行\"\n    NGINX_STATUS=\"✅ 运行中\"\nelse\n    echo \"❌ Nginx未运行\"\n    NGINX_STATUS=\"❌ 未运行\"\nfi\n\n# 检查HTTPS主站访问\necho \"🔍 检查HTTPS主站访问...\"\nif curl -s -I -k https://********* > /dev/null; then\n    echo \"✅ HTTPS主站可访问 (IP: *********)\"\n    HTTPS_STATUS=\"✅ 可访问\"\nelse\n    echo \"❌ HTTPS主站不可访问 (IP: *********)\"\n    HTTPS_STATUS=\"❌ 不可访问\"\nfi\n\n# 检查Word编辑器测试页面\necho \"🔍 检查Word编辑器测试页面...\"\nif curl -s -I -k https://*********/word-editor-test > /dev/null; then\n    echo \"✅ Word编辑器测试页面正常 (IP: *********)\"\n    WORD_EDITOR_STATUS=\"✅ 正常\"\nelse\n    echo \"❌ Word编辑器测试页面异常 (IP: *********)\"\n    WORD_EDITOR_STATUS=\"❌ 异常\"\nfi\n\n# 检查新闻管理页面\necho \"🔍 检查新闻管理页面...\"\nif curl -s -I -k https://*********/admin/login > /dev/null; then\n    echo \"✅ 新闻管理登录页面正常\"\n    ADMIN_STATUS=\"✅ 正常\"\nelse\n    echo \"❌ 新闻管理登录页面异常\"\n    ADMIN_STATUS=\"❌ 异常\"\nfi\n\n# 检查Vue 3页面\necho \"🔍 检查Vue 3页面...\"\nif curl -s -I -k https://*********/vue3-demo > /dev/null; then\n    echo \"✅ Vue 3演示页面正常\"\n    VUE3_STATUS=\"✅ 正常\"\nelse\n    echo \"❌ Vue 3演示页面异常\"\n    VUE3_STATUS=\"❌ 异常\"\nfi\n\n# 显示总结\necho \"\"\necho \"📊 健康检查总结:\"\necho \"   Node.js应用:   $NODE_STATUS\"\necho \"   Nginx服务:     $NGINX_STATUS\"\necho \"   HTTPS主站:     $HTTPS_STATUS\"\necho \"   Word编辑器:    $WORD_EDITOR_STATUS\"\necho \"   新闻管理:      $ADMIN_STATUS\"\necho \"   Vue 3页面:     $VUE3_STATUS\"\necho \"\"\n\n# 显示访问地址\necho \"🌐 访问地址:\"\necho \"   🔒 主站 (HTTPS):    https://*********\"\necho \"   📰 新闻页面:        https://*********/news/index\"\necho \"   🔐 管理登录:        https://*********/admin/login\"\necho \"   📝 Word编辑器:      https://*********/word-editor-test\"\necho \"   🎯 Vue 3演示:       https://*********/vue3-demo\"\necho \"   🧪 Vue 3测试:       https://*********/vue3-test\"\necho \"\"\n\n# 显示Word编辑器功能\necho \"📝 Word编辑器功能:\"\necho \"   ✅ Word文档导入 (.docx格式)\"\necho \"   ✅ Word文档导出 (.docx格式)\"\necho \"   ✅ 格式保持 (样式、表格、图片)\"\necho \"   ✅ 在线编辑 (TinyMCE 6.x)\"\necho \"   ✅ 图片上传和处理\"\necho \"   ✅ 表格编辑功能\"\necho \"\"\n\n# 显示技术栈\necho \"🔧 技术栈信息:\"\necho \"   前端框架:     Vue 3.3.4 + TinyMCE 6.x\"\necho \"   后端框架:     Node.js + Express\"\necho \"   模板引擎:     Jade/Pug\"\necho \"   Web服务器:    Nginx (反向代理)\"\necho \"   Word处理:     Mammoth.js + html-docx-js\"\necho \"\"\n\n# 显示日志命令\necho \"📝 查看日志:\"\necho \"   Nginx访问日志: sudo tail -f /var/log/nginx/access.log\"\necho \"   Nginx错误日志: sudo tail -f /var/log/nginx/error.log\"\necho \"   应用日志:      tail -f logs/app.log\"\necho \"\"\n\n# 显示管理命令\necho \"🛠️  管理命令:\"\necho \"   启动应用:      ./run.sh start\"\necho \"   停止应用:      ./run.sh stop\"\necho \"   重启应用:      ./run.sh restart\"\necho \"   查看状态:      ./run.sh status\"\necho \"\"\n\n# 检查是否所有服务都正常\nif [[ \"$NODE_STATUS\" == *\"✅\"* ]] && [[ \"$NGINX_STATUS\" == *\"✅\"* ]] && [[ \"$HTTPS_STATUS\" == *\"✅\"* ]] && [[ \"$WORD_EDITOR_STATUS\" == *\"✅\"* ]]; then\n    echo \"🎉 所有服务运行正常！Word兼容编辑器已就绪！\"\n    exit 0\nelse\n    echo \"⚠️  部分服务存在问题，请检查上述状态\"\n    echo \"\"\n    echo \"🔧 故障排除建议:\"\n    \n    if [[ \"$NODE_STATUS\" == *\"❌\"* ]]; then\n        echo \"   - Node.js应用未运行: 执行 ./run.sh start\"\n    fi\n    \n    if [[ \"$NGINX_STATUS\" == *\"❌\"* ]]; then\n        echo \"   - Nginx未运行: 执行 sudo systemctl start nginx\"\n    fi\n    \n    if [[ \"$HTTPS_STATUS\" == *\"❌\"* ]]; then\n        echo \"   - HTTPS访问异常: 检查SSL证书和Nginx配置\"\n    fi\n    \n    if [[ \"$WORD_EDITOR_STATUS\" == *\"❌\"* ]]; then\n        echo \"   - Word编辑器异常: 检查路由配置和模板文件\"\n    fi\n    \n    exit 1\nfi\n"}