{"path": {"rootPath": "/home", "relPath": "website/ronglianweb/views/admin/newsEdit.jade"}, "originalCode": "extends layout\n\nblock css\n  link(rel='stylesheet', href='/plugins/admin/css/admin.css')\n  // TinyMCE样式 (替换Quill样式)\n  style.\n    .tox-tinymce {\n      border: 1px solid #ddd !important;\n      border-radius: 4px !important;\n    }\n    .tox .tox-toolbar {\n      background: #f8f9fa !important;\n    }\n    // Word导入/导出按钮样式\n    .word-actions {\n      margin-bottom: 10px;\n      padding: 10px;\n      background: #f0f8ff;\n      border: 1px solid #d1ecf1;\n      border-radius: 4px;\n    }\n    .word-actions .btn {\n      margin-right: 10px;\n    }\n  style.\n    .image-preview {\n      max-width: 200px;\n      max-height: 200px;\n      border: 1px solid #ddd;\n      border-radius: 4px;\n      margin-top: 10px;\n    }\n    .upload-area {\n      border: 2px dashed #ccc;\n      border-radius: 4px;\n      padding: 20px;\n      text-align: center;\n      cursor: pointer;\n      transition: border-color 0.3s;\n    }\n    .upload-area:hover {\n      border-color: #667eea;\n    }\n    .upload-area.dragover {\n        border-color: #667eea;\n        background-color: #f8f9fa;\n      }\n      #editor {\n        height: 300px;\n      }\n      .form-actions {\n        background-color: #f8f9fa;\n        padding: 20px;\n        margin: 20px -15px -15px -15px;\n        border-top: 1px solid #ddd;\n      }\n      /* 新增样式：优化上下布局 */\n      .panel {\n        margin-bottom: 20px;\n      }\n      .panel-title {\n        font-size: 16px;\n        font-weight: 600;\n      }\n      .row {\n        margin-bottom: 15px;\n      }\n      /* 确保发布设置和封面图片面板高度一致 */\n      .col-md-6 .panel {\n        height: 100%;\n      }\n      .col-md-6 .panel-body {\n        min-height: 200px;\n      }\n\nblock content\n  .container-fluid\n      .row\n        .col-md-12\n          h1.page-header \n            | ✏️ #{newsId ? '编辑新闻' : '创建新闻'}\n            .pull-right\n              a.btn.btn-default(href=\"/admin/news\") \n                i.glyphicon.glyphicon-arrow-left\n                |  返回列表\n\n      form#newsForm\n        // 基本信息 - 上半部分\n        .row\n          .col-md-12\n            .panel.panel-default\n              .panel-heading\n                h3.panel-title 📝 基本信息\n              .panel-body\n                .form-group\n                  label(for=\"title\") 新闻标题 *\n                  input.form-control#title(type=\"text\", name=\"title\", required, placeholder=\"请输入新闻标题\")\n\n                .form-group\n                  label(for=\"content\") 新闻内容 *\n\n                  // Word导入/导出功能区 (新增)\n                  .word-actions\n                    button.btn.btn-info.btn-sm#importWordBtn(type=\"button\")\n                      i.glyphicon.glyphicon-import\n                      |  导入Word文档\n                    button.btn.btn-success.btn-sm#exportWordBtn(type=\"button\")\n                      i.glyphicon.glyphicon-export\n                      |  导出为Word\n                    button.btn.btn-warning.btn-sm#previewWebBtn(type=\"button\")\n                      i.glyphicon.glyphicon-globe\n                      |  预览网页效果\n                    input#wordFileInput(type=\"file\", accept=\".doc,.docx\", style=\"display: none;\")\n                    br\n                    small.text-muted\n                      strong 功能说明：\n                      | 导入Word文档进行编辑，\n                      strong 发布后自动转为网页格式\n                      | 显示。Word导出仅用于备份编辑内容。\n\n                  // 编辑器容器 (保持原有尺寸)\n                  #editor(style=\"height: 300px;\")\n                  textarea#content(name=\"content\", style=\"display: none;\")\n\n        // 发布设置和封面图片 - 下半部分\n        .row\n          .col-md-6\n            // 发布设置\n            .panel.panel-default\n              .panel-heading\n                h3.panel-title 🚀 发布设置\n              .panel-body\n                .form-group\n                  label(for=\"status\") 状态\n                  select.form-control#status(name=\"status\")\n                    option(value=\"draft\") 草稿\n                    option(value=\"published\") 发布\n                    option(value=\"unpublished\") 下架\n                    option(value=\"archived\") 归档\n\n                .form-group\n                  label(for=\"author\") 作者\n                  input.form-control#author(type=\"text\", name=\"author\", value=\"admin\")\n\n          .col-md-6\n            // 封面图片\n            .panel.panel-default\n              .panel-heading\n                h3.panel-title 🖼️ 封面图片\n              .panel-body\n                .form-group\n                  label 上传图片\n                  .upload-area#uploadArea\n                    i.glyphicon.glyphicon-cloud-upload(style=\"font-size: 2em; color: #ccc;\")\n                    p 点击或拖拽图片到此处上传\n                    p.text-muted 支持 JPG、PNG、GIF 格式，最大 5MB\n                  input#imageInput(type=\"file\", accept=\"image/*\", style=\"display: none;\")\n                  input#picMgid(type=\"hidden\", name=\"picMgid\")\n                  #imagePreview\n\n        .form-actions\n          .row\n            .col-md-12.text-right\n              button.btn.btn-default#saveDraftBtn(type=\"button\") 保存草稿\n              button.btn.btn-success#savePublishBtn(type=\"button\") 保存并发布\n              if newsId\n                button.btn.btn-danger#deleteBtn(type=\"button\") 删除新闻\n\n    // 确认删除模态框\n    .modal.fade#deleteModal(tabindex=\"-1\", role=\"dialog\")\n      .modal-dialog(role=\"document\")\n        .modal-content\n          .modal-header\n            button.close(type=\"button\", data-dismiss=\"modal\")\n              span &times;\n            h4.modal-title 确认删除\n          .modal-body\n            p 确定要删除这条新闻吗？此操作不可恢复。\n          .modal-footer\n            button.btn.btn-default(type=\"button\", data-dismiss=\"modal\") 取消\n            button.btn.btn-danger#confirmDelete 确认删除\n\nblock scripts\n  // TinyMCE编辑器 (替换Quill)\n  script(src=\"https://cdn.jsdelivr.net/npm/tinymce@6/tinymce.min.js\")\n  // Word处理库\n  script(src=\"https://cdn.jsdelivr.net/npm/mammoth@1.6.0/mammoth.browser.min.js\")\n  script(src=\"https://cdn.jsdelivr.net/npm/html-docx-js@0.3.1/dist/html-docx.js\")\n  // 升级后的编辑器脚本\n  script(src=\"/plugins/admin/js/newsEdit-word.js\")\n  script.\n    // 传递新闻ID到前端\n    window.newsId = '#{newsId}';\n    window.currentUser = !{JSON.stringify(user || {})};\n\n    // 初始化页面\n    $(document).ready(function() {\n      // 设置作者为当前用户\n      if (window.currentUser && window.currentUser.name) {\n        $('#author').val(window.currentUser.name);\n      }\n\n      // 如果是编辑模式，加载新闻数据\n      if (window.newsId) {\n        loadNewsData(window.newsId);\n      }\n    });\n", "modifiedCode": "extends layout\n\nblock css\n  link(rel='stylesheet', href='/plugins/admin/css/admin.css')\n  // TinyMCE样式 (替换Quill样式)\n  style.\n    .tox-tinymce {\n      border: 1px solid #ddd !important;\n      border-radius: 4px !important;\n    }\n    .tox .tox-toolbar {\n      background: #f8f9fa !important;\n    }\n    // Word导入/导出按钮样式\n    .word-actions {\n      margin-bottom: 10px;\n      padding: 10px;\n      background: #f0f8ff;\n      border: 1px solid #d1ecf1;\n      border-radius: 4px;\n    }\n    .word-actions .btn {\n      margin-right: 10px;\n    }\n  style.\n    .image-preview {\n      max-width: 200px;\n      max-height: 200px;\n      border: 1px solid #ddd;\n      border-radius: 4px;\n      margin-top: 10px;\n    }\n    .upload-area {\n      border: 2px dashed #ccc;\n      border-radius: 4px;\n      padding: 20px;\n      text-align: center;\n      cursor: pointer;\n      transition: border-color 0.3s;\n    }\n    .upload-area:hover {\n      border-color: #667eea;\n    }\n    .upload-area.dragover {\n        border-color: #667eea;\n        background-color: #f8f9fa;\n      }\n      #editor {\n        height: 300px;\n      }\n      .form-actions {\n        background-color: #f8f9fa;\n        padding: 20px;\n        margin: 20px -15px -15px -15px;\n        border-top: 1px solid #ddd;\n      }\n      /* 新增样式：优化上下布局 */\n      .panel {\n        margin-bottom: 20px;\n      }\n      .panel-title {\n        font-size: 16px;\n        font-weight: 600;\n      }\n      .row {\n        margin-bottom: 15px;\n      }\n      /* 确保发布设置和封面图片面板高度一致 */\n      .col-md-6 .panel {\n        height: 100%;\n      }\n      .col-md-6 .panel-body {\n        min-height: 200px;\n      }\n\nblock content\n  .container-fluid\n      .row\n        .col-md-12\n          h1.page-header \n            | ✏️ #{newsId ? '编辑新闻' : '创建新闻'}\n            .pull-right\n              a.btn.btn-default(href=\"/admin/news\") \n                i.glyphicon.glyphicon-arrow-left\n                |  返回列表\n\n      form#newsForm\n        // 基本信息 - 上半部分\n        .row\n          .col-md-12\n            .panel.panel-default\n              .panel-heading\n                h3.panel-title 📝 基本信息\n              .panel-body\n                .form-group\n                  label(for=\"title\") 新闻标题 *\n                  input.form-control#title(type=\"text\", name=\"title\", required, placeholder=\"请输入新闻标题\")\n\n                .form-group\n                  label(for=\"content\") 新闻内容 *\n\n                  // Word导入/导出功能区 (新增)\n                  .word-actions\n                    button.btn.btn-info.btn-sm#importWordBtn(type=\"button\")\n                      i.glyphicon.glyphicon-import\n                      |  导入Word文档\n                    button.btn.btn-success.btn-sm#exportWordBtn(type=\"button\")\n                      i.glyphicon.glyphicon-export\n                      |  导出为Word\n                    button.btn.btn-warning.btn-sm#previewWebBtn(type=\"button\")\n                      i.glyphicon.glyphicon-globe\n                      |  预览网页效果\n                    input#wordFileInput(type=\"file\", accept=\".doc,.docx\", style=\"display: none;\")\n                    br\n                    small.text-muted\n                      strong 功能说明：\n                      | 导入Word文档进行编辑，\n                      strong 发布后自动转为网页格式\n                      | 显示。Word导出仅用于备份编辑内容。\n\n                  // 编辑器容器 (保持原有尺寸)\n                  #editor(style=\"height: 300px;\")\n                  textarea#content(name=\"content\", style=\"display: none;\")\n\n        // 发布设置和封面图片 - 下半部分\n        .row\n          .col-md-6\n            // 发布设置\n            .panel.panel-default\n              .panel-heading\n                h3.panel-title 🚀 发布设置\n              .panel-body\n                .form-group\n                  label(for=\"status\") 状态\n                  select.form-control#status(name=\"status\")\n                    option(value=\"draft\") 草稿\n                    option(value=\"published\") 发布\n                    option(value=\"unpublished\") 下架\n                    option(value=\"archived\") 归档\n\n                .form-group\n                  label(for=\"author\") 作者\n                  input.form-control#author(type=\"text\", name=\"author\", value=\"admin\")\n\n          .col-md-6\n            // 封面图片\n            .panel.panel-default\n              .panel-heading\n                h3.panel-title 🖼️ 封面图片\n              .panel-body\n                .form-group\n                  label 上传图片\n                  .upload-area#uploadArea\n                    i.glyphicon.glyphicon-cloud-upload(style=\"font-size: 2em; color: #ccc;\")\n                    p 点击或拖拽图片到此处上传\n                    p.text-muted 支持 JPG、PNG、GIF 格式，最大 5MB\n                  input#imageInput(type=\"file\", accept=\"image/*\", style=\"display: none;\")\n                  input#picMgid(type=\"hidden\", name=\"picMgid\")\n                  #imagePreview\n\n        .form-actions\n          .row\n            .col-md-12.text-right\n              button.btn.btn-default#saveDraftBtn(type=\"button\") 保存草稿\n              button.btn.btn-success#savePublishBtn(type=\"button\") 保存并发布\n              if newsId\n                button.btn.btn-danger#deleteBtn(type=\"button\") 删除新闻\n\n    // 确认删除模态框\n    .modal.fade#deleteModal(tabindex=\"-1\", role=\"dialog\")\n      .modal-dialog(role=\"document\")\n        .modal-content\n          .modal-header\n            button.close(type=\"button\", data-dismiss=\"modal\")\n              span &times;\n            h4.modal-title 确认删除\n          .modal-body\n            p 确定要删除这条新闻吗？此操作不可恢复。\n          .modal-footer\n            button.btn.btn-default(type=\"button\", data-dismiss=\"modal\") 取消\n            button.btn.btn-danger#confirmDelete 确认删除\n\nblock scripts\n  // TinyMCE编辑器 (替换Quill)\n  script(src=\"https://cdn.jsdelivr.net/npm/tinymce@6/tinymce.min.js\")\n  // Word处理库\n  script(src=\"https://cdn.jsdelivr.net/npm/mammoth@1.6.0/mammoth.browser.min.js\")\n  script(src=\"https://cdn.jsdelivr.net/npm/html-docx-js@0.3.1/dist/html-docx.js\")\n  // 升级后的编辑器脚本\n  script(src=\"/plugins/admin/js/newsEdit-word.js\")\n  script.\n    // 传递新闻ID到前端\n    window.newsId = '#{newsId}';\n    window.currentUser = !{JSON.stringify(user || {})};\n\n    // 初始化页面\n    $(document).ready(function() {\n      // 设置作者为当前用户\n      if (window.currentUser && window.currentUser.name) {\n        $('#author').val(window.currentUser.name);\n      }\n\n      // 如果是编辑模式，加载新闻数据\n      if (window.newsId) {\n        loadNewsData(window.newsId);\n      }\n    });\n"}