{"path": {"rootPath": "/home", "relPath": "website/ronglianweb/test-word-buttons.html"}, "modifiedCode": "<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <title>Word按钮功能测试</title>\n    <link rel=\"stylesheet\" href=\"/plugins/bootstrap/css/bootstrap.min.css\">\n    <style>\n        .container { margin-top: 20px; }\n        .test-section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 4px; }\n        .help-block .btn-xs {\n            padding: 1px 5px;\n            font-size: 11px;\n            line-height: 1.5;\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <h1>Word按钮功能测试</h1>\n        \n        <div class=\"test-section\">\n            <h3>1. 按钮HTML结构测试</h3>\n            <div class=\"form-group\">\n                <label for=\"content\">新闻内容 *</label>\n                <div id=\"editor\" style=\"height: 300px;\"></div>\n                <textarea id=\"content\" name=\"content\" style=\"display: none;\"></textarea>\n                \n                <!-- 复制新闻编辑页面的按钮结构 -->\n                <div class=\"help-block small\" style=\"margin-top: 5px;\">\n                    <span class=\"text-muted\">支持Word文档: </span>\n                    <a class=\"btn btn-xs btn-info\" id=\"importWordBtn\" href=\"javascript:void(0);\" style=\"margin-right: 5px;\">\n                        <i class=\"glyphicon glyphicon-import\"></i> 导入\n                    </a>\n                    <a class=\"btn btn-xs btn-success\" id=\"exportWordBtn\" href=\"javascript:void(0);\" style=\"margin-right: 5px;\">\n                        <i class=\"glyphicon glyphicon-export\"></i> 导出\n                    </a>\n                    <a class=\"btn btn-xs btn-default\" id=\"previewWebBtn\" href=\"javascript:void(0);\">\n                        <i class=\"glyphicon glyphicon-eye-open\"></i> 预览\n                    </a>\n                    <input id=\"wordFileInput\" type=\"file\" accept=\".doc,.docx\" style=\"display: none;\">\n                </div>\n            </div>\n            \n            <div id=\"buttonTest\" class=\"alert alert-info\">\n                <strong>按钮测试结果:</strong>\n                <div id=\"testResults\"></div>\n            </div>\n        </div>\n        \n        <div class=\"test-section\">\n            <h3>2. 库加载测试</h3>\n            <button class=\"btn btn-primary\" onclick=\"testLibraries()\">测试库加载</button>\n            <div id=\"libraryResults\" class=\"alert alert-info\" style=\"margin-top: 10px;\"></div>\n        </div>\n        \n        <div class=\"test-section\">\n            <h3>3. 手动功能测试</h3>\n            <button class=\"btn btn-warning\" onclick=\"manualImportTest()\">手动测试导入</button>\n            <button class=\"btn btn-success\" onclick=\"manualExportTest()\">手动测试导出</button>\n            <button class=\"btn btn-info\" onclick=\"manualPreviewTest()\">手动测试预览</button>\n            <div id=\"manualResults\" class=\"alert alert-info\" style=\"margin-top: 10px;\"></div>\n        </div>\n    </div>\n\n    <script src=\"/plugins/jquery/jquery-1.11.3.js\"></script>\n    <script src=\"/plugins/bootstrap/js/bootstrap.min.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/tinymce@6/tinymce.min.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/mammoth@1.6.0/mammoth.browser.min.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/html-docx-js@0.3.1/dist/html-docx.js\"></script>\n    \n    <script>\n        let editor;\n        \n        // 初始化TinyMCE\n        tinymce.init({\n            selector: '#editor',\n            height: 200,\n            language: 'zh_CN',\n            plugins: ['advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview'],\n            toolbar: 'undo redo | bold italic | alignleft aligncenter alignright | bullist numlist',\n            setup: function(ed) {\n                editor = ed;\n                ed.on('init', function() {\n                    console.log('TinyMCE初始化完成');\n                    ed.setContent('<p>这是测试内容，可以编辑。</p>');\n                    bindEvents();\n                });\n            }\n        });\n        \n        function bindEvents() {\n            console.log('绑定事件...');\n            \n            // 检查按钮是否存在\n            const importBtn = $('#importWordBtn');\n            const exportBtn = $('#exportWordBtn');\n            const previewBtn = $('#previewWebBtn');\n            \n            let results = '';\n            results += '导入按钮存在: ' + (importBtn.length > 0) + '<br>';\n            results += '导出按钮存在: ' + (exportBtn.length > 0) + '<br>';\n            results += '预览按钮存在: ' + (previewBtn.length > 0) + '<br>';\n            \n            $('#testResults').html(results);\n            \n            // 绑定事件\n            importBtn.on('click', function(e) {\n                e.preventDefault();\n                console.log('点击了导入按钮');\n                alert('导入按钮点击成功！');\n                $('#wordFileInput').click();\n            });\n            \n            exportBtn.on('click', function(e) {\n                e.preventDefault();\n                console.log('点击了导出按钮');\n                alert('导出按钮点击成功！');\n                exportToWord();\n            });\n            \n            previewBtn.on('click', function(e) {\n                e.preventDefault();\n                console.log('点击了预览按钮');\n                alert('预览按钮点击成功！');\n                previewWebFormat();\n            });\n            \n            $('#wordFileInput').on('change', function() {\n                const file = this.files[0];\n                if (file) {\n                    console.log('选择了文件:', file.name);\n                    alert('选择了文件: ' + file.name);\n                    importWordDocument(file);\n                }\n            });\n        }\n        \n        function testLibraries() {\n            let results = '';\n            results += 'jQuery: ' + (typeof $ !== 'undefined') + '<br>';\n            results += 'TinyMCE: ' + (typeof tinymce !== 'undefined') + '<br>';\n            results += 'Mammoth: ' + (typeof mammoth !== 'undefined') + '<br>';\n            results += 'htmlDocx: ' + (typeof htmlDocx !== 'undefined') + '<br>';\n            results += 'Editor对象: ' + (editor !== undefined) + '<br>';\n            \n            $('#libraryResults').html(results);\n        }\n        \n        function manualImportTest() {\n            if (typeof mammoth === 'undefined') {\n                alert('Mammoth库未加载');\n                return;\n            }\n            alert('导入功能可用，请选择Word文件');\n            $('#wordFileInput').click();\n        }\n        \n        function manualExportTest() {\n            if (typeof htmlDocx === 'undefined') {\n                alert('htmlDocx库未加载');\n                return;\n            }\n            if (!editor) {\n                alert('编辑器未初始化');\n                return;\n            }\n            exportToWord();\n        }\n        \n        function manualPreviewTest() {\n            if (!editor) {\n                alert('编辑器未初始化');\n                return;\n            }\n            previewWebFormat();\n        }\n        \n        // Word导入功能\n        function importWordDocument(file) {\n            if (!file) return;\n            \n            const reader = new FileReader();\n            reader.onload = function(e) {\n                const arrayBuffer = e.target.result;\n                \n                mammoth.convertToHtml({arrayBuffer: arrayBuffer})\n                    .then(function(result) {\n                        const html = result.value;\n                        if (editor) {\n                            editor.setContent(html);\n                        }\n                        alert('Word文档导入成功！');\n                    })\n                    .catch(function(error) {\n                        console.error('Word导入错误:', error);\n                        alert('Word文档导入失败：' + error.message);\n                    });\n            };\n            reader.readAsArrayBuffer(file);\n        }\n        \n        // Word导出功能\n        function exportToWord() {\n            if (!editor) return;\n            \n            const content = editor.getContent();\n            const title = '测试文档';\n            \n            const htmlContent = `\n                <!DOCTYPE html>\n                <html>\n                <head>\n                    <meta charset=\"utf-8\">\n                    <title>${title}</title>\n                </head>\n                <body>\n                    <h1>${title}</h1>\n                    ${content}\n                </body>\n                </html>\n            `;\n            \n            try {\n                const converted = htmlDocx.asBlob(htmlContent);\n                const link = document.createElement('a');\n                link.href = URL.createObjectURL(converted);\n                link.download = `${title}.docx`;\n                document.body.appendChild(link);\n                link.click();\n                document.body.removeChild(link);\n                alert('Word文档导出成功！');\n            } catch (error) {\n                console.error('Word导出错误:', error);\n                alert('Word文档导出失败：' + error.message);\n            }\n        }\n        \n        // 网页预览功能\n        function previewWebFormat() {\n            const content = editor ? editor.getContent() : '';\n            const title = '预览测试';\n            \n            const previewWindow = window.open('', '_blank', 'width=800,height=600');\n            previewWindow.document.write(`\n                <!DOCTYPE html>\n                <html>\n                <head>\n                    <meta charset=\"utf-8\">\n                    <title>网页格式预览 - ${title}</title>\n                    <style>\n                        body { font-family: \"Microsoft YaHei\", Arial, sans-serif; margin: 40px; }\n                        h1 { color: #333; }\n                    </style>\n                </head>\n                <body>\n                    <h1>${title}</h1>\n                    ${content}\n                </body>\n                </html>\n            `);\n        }\n        \n        // 页面加载完成后自动测试库\n        $(document).ready(function() {\n            setTimeout(testLibraries, 1000);\n        });\n    </script>\n</body>\n</html>\n"}