{"path": {"rootPath": "/home", "relPath": "website/ronglianweb/views/website/vue3-demo.jade"}, "modifiedCode": "doctype html\nhtml\n    head\n        meta(charset='utf-8')\n        meta(http-equiv='X-UA-Compatible', content='IE=edge')\n        meta(name='viewport', content='width=device-width, initial-scale=1')\n        title Vue 3 Demo Page\n        link(rel='stylesheet', href='../plugins/bootstrap/css/bootstrap.min.css')\n        link(rel='stylesheet', href='../plugins/common/css/common.css')\n        style.\n            .demo-container {\n                padding: 40px 20px;\n                max-width: 1200px;\n                margin: 0 auto;\n            }\n            .demo-section {\n                margin-bottom: 40px;\n                padding: 20px;\n                border: 1px solid #ddd;\n                border-radius: 8px;\n                background: #f9f9f9;\n            }\n            .demo-title {\n                color: #333;\n                margin-bottom: 20px;\n                border-bottom: 2px solid #007bff;\n                padding-bottom: 10px;\n            }\n            .counter-demo {\n                text-align: center;\n                padding: 20px;\n            }\n            .counter-display {\n                font-size: 2em;\n                color: #007bff;\n                margin: 20px 0;\n            }\n            .todo-item {\n                padding: 10px;\n                margin: 5px 0;\n                background: white;\n                border: 1px solid #ddd;\n                border-radius: 4px;\n                display: flex;\n                justify-content: space-between;\n                align-items: center;\n            }\n            .todo-item.completed {\n                text-decoration: line-through;\n                opacity: 0.6;\n            }\n    body\n        .demo-container\n            h1.text-center Vue 3.0 框架演示页面\n            p.text-center.text-muted 这个页面展示了 Vue 3.0 的基本功能和组件系统\n            \n            #vue_app_demo\n                // 计数器演示\n                .demo-section\n                    h2.demo-title 1. 响应式数据演示 - 计数器\n                    .counter-demo\n                        .counter-display {{ counter }}\n                        button.btn.btn-primary.btn-lg(@click=\"increment\") 点击增加\n                        button.btn.btn-secondary.btn-lg.ml-2(@click=\"decrement\") 点击减少\n                        button.btn.btn-warning.btn-lg.ml-2(@click=\"reset\") 重置\n                \n                // 表单演示\n                .demo-section\n                    h2.demo-title 2. 双向数据绑定演示\n                    .row\n                        .col-md-6\n                            .form-group\n                                label 输入您的姓名:\n                                input.form-control(v-model=\"userName\" placeholder=\"请输入姓名\")\n                            .form-group\n                                label 选择您的城市:\n                                select.form-control(v-model=\"selectedCity\")\n                                    option(value=\"\") 请选择城市\n                                    option(v-for=\"city in cities\" :value=\"city\") {{ city }}\n                        .col-md-6\n                            h4 实时预览:\n                            .alert.alert-info\n                                p(v-if=\"userName\") 您好, {{ userName }}!\n                                p(v-else) 请输入您的姓名\n                                p(v-if=\"selectedCity\") 您来自: {{ selectedCity }}\n                                p(v-else) 请选择您的城市\n                \n                // 列表演示\n                .demo-section\n                    h2.demo-title 3. 列表渲染和条件渲染演示 - 待办事项\n                    .row\n                        .col-md-8\n                            .form-group\n                                .input-group\n                                    input.form-control(v-model=\"newTodo\" @keyup.enter=\"addTodo\" placeholder=\"输入新的待办事项\")\n                                    .input-group-btn\n                                        button.btn.btn-success(@click=\"addTodo\") 添加\n                            \n                            div(v-if=\"todos.length === 0\")\n                                .alert.alert-warning 暂无待办事项，请添加一些任务！\n                            \n                            div(v-else)\n                                .todo-item(v-for=\"(todo, index) in todos\" :key=\"todo.id\" :class=\"{completed: todo.completed}\")\n                                    span {{ todo.text }}\n                                    div\n                                        button.btn.btn-sm.btn-info(@click=\"toggleTodo(index)\") \n                                            span(v-if=\"todo.completed\") 取消完成\n                                            span(v-else) 标记完成\n                                        button.btn.btn-sm.btn-danger.ml-1(@click=\"removeTodo(index)\") 删除\n                        \n                        .col-md-4\n                            h4 统计信息:\n                            .alert.alert-success\n                                p 总任务数: {{ todos.length }}\n                                p 已完成: {{ completedCount }}\n                                p 未完成: {{ todos.length - completedCount }}\n                \n                // 组件演示\n                .demo-section\n                    h2.demo-title 4. 组件系统演示\n                    p 这里展示了自定义的 Vue 3 组件:\n                    demo-card(title=\"Vue 3 特性\" content=\"Vue 3 提供了更好的性能、更小的包体积和更好的 TypeScript 支持\")\n                    demo-card(title=\"Composition API\" content=\"新的 Composition API 让代码组织更加灵活\")\n                    demo-card(title=\"响应式系统\" content=\"全新的响应式系统基于 Proxy，提供更好的性能\")\n\n        // 基础库\n        script(src='../plugins/jquery/jquery-1.11.3.js')\n        script(src='../plugins/bootstrap/js/bootstrap.min.js')\n        \n        // Vue 3核心库\n        script(src='../plugins/vue3/vue.global.min.js')\n        \n        // Vue 3应用\n        script.\n            const { createApp } = Vue;\n            \n            // 创建Vue 3应用\n            const app = createApp({\n                data() {\n                    return {\n                        counter: 0,\n                        userName: '',\n                        selectedCity: '',\n                        cities: ['北京', '上海', '广州', '深圳', '杭州', '成都'],\n                        newTodo: '',\n                        todos: [\n                            { id: 1, text: '学习 Vue 3 基础', completed: true },\n                            { id: 2, text: '创建第一个 Vue 3 应用', completed: false },\n                            { id: 3, text: '掌握 Composition API', completed: false }\n                        ],\n                        nextTodoId: 4\n                    }\n                },\n                computed: {\n                    completedCount() {\n                        return this.todos.filter(todo => todo.completed).length;\n                    }\n                },\n                methods: {\n                    increment() {\n                        this.counter++;\n                    },\n                    decrement() {\n                        this.counter--;\n                    },\n                    reset() {\n                        this.counter = 0;\n                    },\n                    addTodo() {\n                        if (this.newTodo.trim()) {\n                            this.todos.push({\n                                id: this.nextTodoId++,\n                                text: this.newTodo.trim(),\n                                completed: false\n                            });\n                            this.newTodo = '';\n                        }\n                    },\n                    removeTodo(index) {\n                        this.todos.splice(index, 1);\n                    },\n                    toggleTodo(index) {\n                        this.todos[index].completed = !this.todos[index].completed;\n                    }\n                },\n                mounted() {\n                    console.log('Vue 3 Demo 应用已挂载');\n                }\n            });\n            \n            // 定义演示卡片组件\n            app.component('demo-card', {\n                props: ['title', 'content'],\n                template: `\n                    <div class=\"demo-card\" style=\"margin: 10px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: white;\">\n                        <h5 style=\"color: #007bff; margin-bottom: 10px;\">{{ title }}</h5>\n                        <p style=\"margin: 0; color: #666;\">{{ content }}</p>\n                    </div>\n                `\n            });\n            \n            // 挂载应用\n            app.mount('#vue_app_demo');\n"}