{"path": {"rootPath": "/home", "relPath": "website/ronglianweb/views/admin/word-editor-test.jade"}, "originalCode": "doctype html\nhtml\n    head\n        meta(charset='utf-8')\n        meta(http-equiv='X-UA-Compatible', content='IE=edge')\n        meta(name='viewport', content='width=device-width, initial-scale=1')\n        title 荣联科技 - Word兼容编辑器测试\n        link(rel='stylesheet', href='/plugins/bootstrap/css/bootstrap.min.css')\n        link(rel='stylesheet', href='/plugins/admin/css/admin.css')\n        style.\n            .container { margin-top: 20px; }\n            .tox-tinymce {\n                border: 1px solid #ddd !important;\n                border-radius: 4px !important;\n            }\n            .tox .tox-toolbar {\n                background: #f8f9fa !important;\n            }\n            .word-actions {\n                margin-bottom: 15px;\n                padding: 15px;\n                background: #f0f8ff;\n                border: 1px solid #d1ecf1;\n                border-radius: 4px;\n            }\n            .word-actions .btn {\n                margin-right: 10px;\n            }\n            .test-info {\n                background: #fff3cd;\n                border: 1px solid #ffeaa7;\n                border-radius: 4px;\n                padding: 15px;\n                margin-bottom: 20px;\n            }\n    body\n        .container\n            .row\n                .col-md-12\n                    .test-info\n                        h4 \n                            i.glyphicon.glyphicon-info-sign\n                            |  Word兼容编辑器测试页面\n                        p 这个页面用于测试新的Word兼容富文本编辑器功能：\n                        ul\n                            li ✅ 导入Word文档 (.docx格式)\n                            li ✅ 保持Word文档格式和样式\n                            li ✅ 在线编辑功能\n                            li ✅ 导出为Word文档\n                            li ✅ 图片上传和处理\n                    \n                    .panel.panel-default\n                        .panel-heading\n                            h3.panel-title\n                                i.glyphicon.glyphicon-edit\n                                |  Word兼容编辑器演示\n                        .panel-body\n                            form#testForm\n                                .form-group\n                                    label.control-label(for=\"title\") 文档标题\n                                    input.form-control#title(type=\"text\", placeholder=\"请输入文档标题\", value=\"Word兼容测试文档\")\n                                \n                                .form-group\n                                    label.control-label(for=\"content\") 文档内容\n                                    \n                                    // Word导入/导出功能区\n                                    .word-actions\n                                        button.btn.btn-info.btn-sm#importWordBtn(type=\"button\")\n                                            i.glyphicon.glyphicon-import\n                                            |  导入Word文档\n                                        button.btn.btn-success.btn-sm#exportWordBtn(type=\"button\")\n                                            i.glyphicon.glyphicon-export\n                                            |  导出为Word\n                                        button.btn.btn-warning.btn-sm#clearBtn(type=\"button\")\n                                            i.glyphicon.glyphicon-trash\n                                            |  清空内容\n                                        input#wordFileInput(type=\"file\", accept=\".doc,.docx\", style=\"display: none;\")\n                                        br\n                                        small.text-muted \n                                            strong 使用说明：\n                                            | 支持导入.docx格式文件，保持原有格式和样式。编辑完成后可导出为Word文档。\n                                    \n                                    // 编辑器容器\n                                    #editor(style=\"height: 400px;\")\n                                    textarea#content(name=\"content\", style=\"display: none;\")\n                                \n                                .form-group\n                                    .btn-group\n                                        button.btn.btn-primary#saveBtn(type=\"button\")\n                                            i.glyphicon.glyphicon-floppy-disk\n                                            |  保存内容\n                                        button.btn.btn-default#previewBtn(type=\"button\")\n                                            i.glyphicon.glyphicon-eye-open\n                                            |  预览\n                                        button.btn.btn-info#getContentBtn(type=\"button\")\n                                            i.glyphicon.glyphicon-list-alt\n                                            |  获取HTML\n                    \n                    // 预览区域\n                    .panel.panel-info#previewPanel(style=\"display: none;\")\n                        .panel-heading\n                            h4.panel-title\n                                i.glyphicon.glyphicon-eye-open\n                                |  内容预览\n                        .panel-body#previewContent\n                    \n                    // HTML内容显示区域\n                    .panel.panel-warning#htmlPanel(style=\"display: none;\")\n                        .panel-heading\n                            h4.panel-title\n                                i.glyphicon.glyphicon-code\n                                |  HTML源码\n                        .panel-body\n                            pre#htmlContent(style=\"max-height: 300px; overflow-y: auto;\")\n\n        // 基础库\n        script(src='/plugins/jquery/jquery-1.11.3.js')\n        script(src='/plugins/bootstrap/js/bootstrap.min.js')\n        \n        // TinyMCE编辑器\n        script(src='https://cdn.jsdelivr.net/npm/tinymce@6/tinymce.min.js')\n        \n        // Word处理库\n        script(src='https://cdn.jsdelivr.net/npm/mammoth@1.6.0/mammoth.browser.min.js')\n        script(src='https://cdn.jsdelivr.net/npm/html-docx-js@0.3.1/dist/html-docx.js')\n        \n        // 测试脚本\n        script.\n            $(document).ready(function() {\n                let editor;\n                \n                // 初始化TinyMCE编辑器\n                tinymce.init({\n                    selector: '#editor',\n                    height: 400,\n                    language: 'zh_CN',\n                    plugins: [\n                        'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',\n                        'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',\n                        'insertdatetime', 'media', 'table', 'help', 'wordcount', 'paste'\n                    ],\n                    toolbar: [\n                        'undo redo | blocks | bold italic underline strikethrough | alignleft aligncenter alignright alignjustify',\n                        'bullist numlist outdent indent | removeformat | table link image media | code preview fullscreen'\n                    ].join(' | '),\n                    paste_data_images: true,\n                    paste_word_valid_elements: \"b,strong,i,em,h1,h2,h3,h4,h5,h6,p,ol,ul,li,a[href],span,color,font-size,font-family,mark,table,tr,td,th\",\n                    paste_retain_style_properties: \"all\",\n                    paste_merge_formats: true,\n                    automatic_uploads: false,\n                    content_style: 'body { font-family: \"Microsoft YaHei\", Arial, sans-serif; font-size: 14px; line-height: 1.6; }',\n                    setup: function(ed) {\n                        editor = ed;\n                        ed.on('change', function() {\n                            $('#content').val(ed.getContent());\n                        });\n                        \n                        // 编辑器加载完成后设置默认内容\n                        ed.on('init', function() {\n                            ed.setContent(`\n                                <h1>Word兼容编辑器测试</h1>\n                                <p>这是一个支持MS Office Word格式的在线编辑器。</p>\n                                <h2>主要功能</h2>\n                                <ul>\n                                    <li><strong>Word文档导入</strong> - 支持.docx格式文件导入</li>\n                                    <li><strong>格式保持</strong> - 保留Word文档的样式和布局</li>\n                                    <li><strong>在线编辑</strong> - 浏览器内直接编辑</li>\n                                    <li><strong>Word导出</strong> - 导出为.docx格式文件</li>\n                                </ul>\n                                <h2>测试表格</h2>\n                                <table border=\"1\" style=\"border-collapse: collapse; width: 100%;\">\n                                    <tr>\n                                        <th style=\"background-color: #f2f2f2; padding: 8px;\">功能</th>\n                                        <th style=\"background-color: #f2f2f2; padding: 8px;\">状态</th>\n                                        <th style=\"background-color: #f2f2f2; padding: 8px;\">说明</th>\n                                    </tr>\n                                    <tr>\n                                        <td style=\"padding: 8px;\">Word导入</td>\n                                        <td style=\"padding: 8px; color: green;\">✅ 支持</td>\n                                        <td style=\"padding: 8px;\">支持.docx格式</td>\n                                    </tr>\n                                    <tr>\n                                        <td style=\"padding: 8px;\">Word导出</td>\n                                        <td style=\"padding: 8px; color: green;\">✅ 支持</td>\n                                        <td style=\"padding: 8px;\">导出为.docx格式</td>\n                                    </tr>\n                                </table>\n                                <p><em>请尝试导入Word文档或编辑内容，然后导出测试！</em></p>\n                            `);\n                        });\n                    }\n                });\n                \n                // Word导入功能\n                $('#importWordBtn').on('click', function() {\n                    $('#wordFileInput').click();\n                });\n                \n                $('#wordFileInput').on('change', function() {\n                    const file = this.files[0];\n                    if (file) {\n                        importWordDocument(file);\n                    }\n                });\n                \n                // Word导出功能\n                $('#exportWordBtn').on('click', function() {\n                    exportToWord();\n                });\n                \n                // 清空内容\n                $('#clearBtn').on('click', function() {\n                    if (confirm('确定要清空所有内容吗？')) {\n                        editor.setContent('');\n                    }\n                });\n                \n                // 保存内容\n                $('#saveBtn').on('click', function() {\n                    const content = editor.getContent();\n                    localStorage.setItem('wordEditorContent', content);\n                    showMessage('内容已保存到本地存储', 'success');\n                });\n                \n                // 预览\n                $('#previewBtn').on('click', function() {\n                    const content = editor.getContent();\n                    const title = $('#title').val();\n                    $('#previewContent').html('<h2>' + title + '</h2>' + content);\n                    $('#previewPanel').show();\n                });\n                \n                // 获取HTML\n                $('#getContentBtn').on('click', function() {\n                    const content = editor.getContent();\n                    $('#htmlContent').text(content);\n                    $('#htmlPanel').show();\n                });\n                \n                // Word文档导入\n                function importWordDocument(file) {\n                    showMessage('正在导入Word文档，请稍候...', 'info');\n                    \n                    const reader = new FileReader();\n                    reader.onload = function(e) {\n                        const arrayBuffer = e.target.result;\n                        \n                        mammoth.convertToHtml({arrayBuffer: arrayBuffer})\n                            .then(function(result) {\n                                const html = result.value;\n                                editor.setContent(html);\n                                \n                                if (result.messages.length > 0) {\n                                    console.log('Word导入消息:', result.messages);\n                                }\n                                \n                                showMessage('Word文档导入成功！', 'success');\n                            })\n                            .catch(function(error) {\n                                console.error('Word导入错误:', error);\n                                showMessage('Word文档导入失败：' + error.message, 'error');\n                            });\n                    };\n                    \n                    reader.readAsArrayBuffer(file);\n                }\n                \n                // Word文档导出\n                function exportToWord() {\n                    const content = editor.getContent();\n                    const title = $('#title').val() || 'Word兼容测试文档';\n                    \n                    const htmlContent = `\n                        <!DOCTYPE html>\n                        <html>\n                        <head>\n                            <meta charset=\"utf-8\">\n                            <title>${title}</title>\n                            <style>\n                                body { font-family: \"Microsoft YaHei\", Arial, sans-serif; line-height: 1.6; margin: 40px; }\n                                h1, h2, h3, h4, h5, h6 { color: #333; margin: 20px 0 10px 0; }\n                                p { margin: 10px 0; }\n                                table { border-collapse: collapse; width: 100%; margin: 10px 0; }\n                                table, th, td { border: 1px solid #ddd; }\n                                th, td { padding: 8px; text-align: left; }\n                                th { background-color: #f2f2f2; }\n                                img { max-width: 100%; height: auto; }\n                            </style>\n                        </head>\n                        <body>\n                            ${content}\n                        </body>\n                        </html>\n                    `;\n                    \n                    try {\n                        const converted = htmlDocx.asBlob(htmlContent);\n                        const link = document.createElement('a');\n                        link.href = URL.createObjectURL(converted);\n                        link.download = `${title}.docx`;\n                        document.body.appendChild(link);\n                        link.click();\n                        document.body.removeChild(link);\n                        \n                        showMessage('Word文档导出成功！', 'success');\n                    } catch (error) {\n                        console.error('Word导出错误:', error);\n                        showMessage('Word文档导出失败：' + error.message, 'error');\n                    }\n                }\n                \n                // 显示消息\n                function showMessage(message, type) {\n                    let alertClass = 'alert-info';\n                    if (type === 'success') alertClass = 'alert-success';\n                    if (type === 'error') alertClass = 'alert-danger';\n                    \n                    const alertHtml = `\n                        <div class=\"alert ${alertClass} alert-dismissible\" role=\"alert\">\n                            <button type=\"button\" class=\"close\" data-dismiss=\"alert\">\n                                <span>&times;</span>\n                            </button>\n                            ${message}\n                        </div>\n                    `;\n                    \n                    $('.alert').remove();\n                    $('.container').prepend(alertHtml);\n                    \n                    setTimeout(function() {\n                        $('.alert').fadeOut();\n                    }, 3000);\n                }\n                \n                // 加载保存的内容\n                const savedContent = localStorage.getItem('wordEditorContent');\n                if (savedContent) {\n                    setTimeout(function() {\n                        if (confirm('发现本地保存的内容，是否加载？')) {\n                            editor.setContent(savedContent);\n                        }\n                    }, 1000);\n                }\n            });\n", "modifiedCode": "doctype html\nhtml\n    head\n        meta(charset='utf-8')\n        meta(http-equiv='X-UA-Compatible', content='IE=edge')\n        meta(name='viewport', content='width=device-width, initial-scale=1')\n        title 荣联科技 - Word兼容编辑器测试\n        link(rel='stylesheet', href='/plugins/bootstrap/css/bootstrap.min.css')\n        link(rel='stylesheet', href='/plugins/admin/css/admin.css')\n        style.\n            .container { margin-top: 20px; }\n            .tox-tinymce {\n                border: 1px solid #ddd !important;\n                border-radius: 4px !important;\n            }\n            .tox .tox-toolbar {\n                background: #f8f9fa !important;\n            }\n            .word-actions {\n                margin-bottom: 15px;\n                padding: 15px;\n                background: #f0f8ff;\n                border: 1px solid #d1ecf1;\n                border-radius: 4px;\n            }\n            .word-actions .btn {\n                margin-right: 10px;\n            }\n            .test-info {\n                background: #fff3cd;\n                border: 1px solid #ffeaa7;\n                border-radius: 4px;\n                padding: 15px;\n                margin-bottom: 20px;\n            }\n    body\n        .container\n            .row\n                .col-md-12\n                    .test-info\n                        h4 \n                            i.glyphicon.glyphicon-info-sign\n                            |  Word兼容编辑器测试页面\n                        p 这个页面用于测试新的Word兼容富文本编辑器功能：\n                        ul\n                            li ✅ 导入Word文档 (.docx格式)\n                            li ✅ 保持Word文档格式和样式\n                            li ✅ 在线编辑功能\n                            li ✅ 导出为Word文档\n                            li ✅ 图片上传和处理\n                    \n                    .panel.panel-default\n                        .panel-heading\n                            h3.panel-title\n                                i.glyphicon.glyphicon-edit\n                                |  Word兼容编辑器演示\n                        .panel-body\n                            form#testForm\n                                .form-group\n                                    label.control-label(for=\"title\") 文档标题\n                                    input.form-control#title(type=\"text\", placeholder=\"请输入文档标题\", value=\"Word兼容测试文档\")\n                                \n                                .form-group\n                                    label.control-label(for=\"content\") 文档内容\n                                    \n                                    // Word导入/导出功能区\n                                    .word-actions\n                                        button.btn.btn-info.btn-sm#importWordBtn(type=\"button\")\n                                            i.glyphicon.glyphicon-import\n                                            |  导入Word文档\n                                        button.btn.btn-success.btn-sm#exportWordBtn(type=\"button\")\n                                            i.glyphicon.glyphicon-export\n                                            |  导出为Word\n                                        button.btn.btn-warning.btn-sm#clearBtn(type=\"button\")\n                                            i.glyphicon.glyphicon-trash\n                                            |  清空内容\n                                        input#wordFileInput(type=\"file\", accept=\".doc,.docx\", style=\"display: none;\")\n                                        br\n                                        small.text-muted \n                                            strong 使用说明：\n                                            | 支持导入.docx格式文件，保持原有格式和样式。编辑完成后可导出为Word文档。\n                                    \n                                    // 编辑器容器\n                                    #editor(style=\"height: 400px;\")\n                                    textarea#content(name=\"content\", style=\"display: none;\")\n                                \n                                .form-group\n                                    .btn-group\n                                        button.btn.btn-primary#saveBtn(type=\"button\")\n                                            i.glyphicon.glyphicon-floppy-disk\n                                            |  保存内容\n                                        button.btn.btn-default#previewBtn(type=\"button\")\n                                            i.glyphicon.glyphicon-eye-open\n                                            |  预览\n                                        button.btn.btn-info#getContentBtn(type=\"button\")\n                                            i.glyphicon.glyphicon-list-alt\n                                            |  获取HTML\n                    \n                    // 预览区域\n                    .panel.panel-info#previewPanel(style=\"display: none;\")\n                        .panel-heading\n                            h4.panel-title\n                                i.glyphicon.glyphicon-eye-open\n                                |  内容预览\n                        .panel-body#previewContent\n                    \n                    // HTML内容显示区域\n                    .panel.panel-warning#htmlPanel(style=\"display: none;\")\n                        .panel-heading\n                            h4.panel-title\n                                i.glyphicon.glyphicon-code\n                                |  HTML源码\n                        .panel-body\n                            pre#htmlContent(style=\"max-height: 300px; overflow-y: auto;\")\n\n        // 基础库\n        script(src='/plugins/jquery/jquery-1.11.3.js')\n        script(src='/plugins/bootstrap/js/bootstrap.min.js')\n        \n        // TinyMCE编辑器\n        script(src='https://cdn.jsdelivr.net/npm/tinymce@6/tinymce.min.js')\n        \n        // Word处理库\n        script(src='https://cdn.jsdelivr.net/npm/mammoth@1.6.0/mammoth.browser.min.js')\n        script(src='https://cdn.jsdelivr.net/npm/html-docx-js@0.3.1/dist/html-docx.js')\n        \n        // 测试脚本\n        script.\n            $(document).ready(function() {\n                let editor;\n                \n                // 初始化TinyMCE编辑器\n                tinymce.init({\n                    selector: '#editor',\n                    height: 400,\n                    language: 'zh_CN',\n                    plugins: [\n                        'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',\n                        'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',\n                        'insertdatetime', 'media', 'table', 'help', 'wordcount', 'paste'\n                    ],\n                    toolbar: [\n                        'undo redo | blocks | bold italic underline strikethrough | alignleft aligncenter alignright alignjustify',\n                        'bullist numlist outdent indent | removeformat | table link image media | code preview fullscreen'\n                    ].join(' | '),\n                    paste_data_images: true,\n                    paste_word_valid_elements: \"b,strong,i,em,h1,h2,h3,h4,h5,h6,p,ol,ul,li,a[href],span,color,font-size,font-family,mark,table,tr,td,th\",\n                    paste_retain_style_properties: \"all\",\n                    paste_merge_formats: true,\n                    automatic_uploads: false,\n                    content_style: 'body { font-family: \"Microsoft YaHei\", Arial, sans-serif; font-size: 14px; line-height: 1.6; }',\n                    setup: function(ed) {\n                        editor = ed;\n                        ed.on('change', function() {\n                            $('#content').val(ed.getContent());\n                        });\n                        \n                        // 编辑器加载完成后设置默认内容\n                        ed.on('init', function() {\n                            ed.setContent(`\n                                <h1>Word兼容编辑器测试</h1>\n                                <p>这是一个支持MS Office Word格式的在线编辑器。</p>\n                                <h2>主要功能</h2>\n                                <ul>\n                                    <li><strong>Word文档导入</strong> - 支持.docx格式文件导入</li>\n                                    <li><strong>格式保持</strong> - 保留Word文档的样式和布局</li>\n                                    <li><strong>在线编辑</strong> - 浏览器内直接编辑</li>\n                                    <li><strong>Word导出</strong> - 导出为.docx格式文件</li>\n                                </ul>\n                                <h2>测试表格</h2>\n                                <table border=\"1\" style=\"border-collapse: collapse; width: 100%;\">\n                                    <tr>\n                                        <th style=\"background-color: #f2f2f2; padding: 8px;\">功能</th>\n                                        <th style=\"background-color: #f2f2f2; padding: 8px;\">状态</th>\n                                        <th style=\"background-color: #f2f2f2; padding: 8px;\">说明</th>\n                                    </tr>\n                                    <tr>\n                                        <td style=\"padding: 8px;\">Word导入</td>\n                                        <td style=\"padding: 8px; color: green;\">✅ 支持</td>\n                                        <td style=\"padding: 8px;\">支持.docx格式</td>\n                                    </tr>\n                                    <tr>\n                                        <td style=\"padding: 8px;\">Word导出</td>\n                                        <td style=\"padding: 8px; color: green;\">✅ 支持</td>\n                                        <td style=\"padding: 8px;\">导出为.docx格式</td>\n                                    </tr>\n                                </table>\n                                <p><em>请尝试导入Word文档或编辑内容，然后导出测试！</em></p>\n                            `);\n                        });\n                    }\n                });\n                \n                // Word导入功能\n                $('#importWordBtn').on('click', function() {\n                    $('#wordFileInput').click();\n                });\n                \n                $('#wordFileInput').on('change', function() {\n                    const file = this.files[0];\n                    if (file) {\n                        importWordDocument(file);\n                    }\n                });\n                \n                // Word导出功能\n                $('#exportWordBtn').on('click', function() {\n                    exportToWord();\n                });\n                \n                // 清空内容\n                $('#clearBtn').on('click', function() {\n                    if (confirm('确定要清空所有内容吗？')) {\n                        editor.setContent('');\n                    }\n                });\n                \n                // 保存内容\n                $('#saveBtn').on('click', function() {\n                    const content = editor.getContent();\n                    localStorage.setItem('wordEditorContent', content);\n                    showMessage('内容已保存到本地存储', 'success');\n                });\n                \n                // 预览\n                $('#previewBtn').on('click', function() {\n                    const content = editor.getContent();\n                    const title = $('#title').val();\n                    $('#previewContent').html('<h2>' + title + '</h2>' + content);\n                    $('#previewPanel').show();\n                });\n                \n                // 获取HTML\n                $('#getContentBtn').on('click', function() {\n                    const content = editor.getContent();\n                    $('#htmlContent').text(content);\n                    $('#htmlPanel').show();\n                });\n                \n                // Word文档导入\n                function importWordDocument(file) {\n                    showMessage('正在导入Word文档，请稍候...', 'info');\n                    \n                    const reader = new FileReader();\n                    reader.onload = function(e) {\n                        const arrayBuffer = e.target.result;\n                        \n                        mammoth.convertToHtml({arrayBuffer: arrayBuffer})\n                            .then(function(result) {\n                                const html = result.value;\n                                editor.setContent(html);\n                                \n                                if (result.messages.length > 0) {\n                                    console.log('Word导入消息:', result.messages);\n                                }\n                                \n                                showMessage('Word文档导入成功！', 'success');\n                            })\n                            .catch(function(error) {\n                                console.error('Word导入错误:', error);\n                                showMessage('Word文档导入失败：' + error.message, 'error');\n                            });\n                    };\n                    \n                    reader.readAsArrayBuffer(file);\n                }\n                \n                // Word文档导出\n                function exportToWord() {\n                    const content = editor.getContent();\n                    const title = $('#title').val() || 'Word兼容测试文档';\n                    \n                    const htmlContent = `\n                        <!DOCTYPE html>\n                        <html>\n                        <head>\n                            <meta charset=\"utf-8\">\n                            <title>${title}</title>\n                            <style>\n                                body { font-family: \"Microsoft YaHei\", Arial, sans-serif; line-height: 1.6; margin: 40px; }\n                                h1, h2, h3, h4, h5, h6 { color: #333; margin: 20px 0 10px 0; }\n                                p { margin: 10px 0; }\n                                table { border-collapse: collapse; width: 100%; margin: 10px 0; }\n                                table, th, td { border: 1px solid #ddd; }\n                                th, td { padding: 8px; text-align: left; }\n                                th { background-color: #f2f2f2; }\n                                img { max-width: 100%; height: auto; }\n                            </style>\n                        </head>\n                        <body>\n                            ${content}\n                        </body>\n                        </html>\n                    `;\n                    \n                    try {\n                        const converted = htmlDocx.asBlob(htmlContent);\n                        const link = document.createElement('a');\n                        link.href = URL.createObjectURL(converted);\n                        link.download = `${title}.docx`;\n                        document.body.appendChild(link);\n                        link.click();\n                        document.body.removeChild(link);\n                        \n                        showMessage('Word文档导出成功！', 'success');\n                    } catch (error) {\n                        console.error('Word导出错误:', error);\n                        showMessage('Word文档导出失败：' + error.message, 'error');\n                    }\n                }\n                \n                // 显示消息\n                function showMessage(message, type) {\n                    let alertClass = 'alert-info';\n                    if (type === 'success') alertClass = 'alert-success';\n                    if (type === 'error') alertClass = 'alert-danger';\n                    \n                    const alertHtml = `\n                        <div class=\"alert ${alertClass} alert-dismissible\" role=\"alert\">\n                            <button type=\"button\" class=\"close\" data-dismiss=\"alert\">\n                                <span>&times;</span>\n                            </button>\n                            ${message}\n                        </div>\n                    `;\n                    \n                    $('.alert').remove();\n                    $('.container').prepend(alertHtml);\n                    \n                    setTimeout(function() {\n                        $('.alert').fadeOut();\n                    }, 3000);\n                }\n                \n                // 加载保存的内容\n                const savedContent = localStorage.getItem('wordEditorContent');\n                if (savedContent) {\n                    setTimeout(function() {\n                        if (confirm('发现本地保存的内容，是否加载？')) {\n                            editor.setContent(savedContent);\n                        }\n                    }, 1000);\n                }\n            });\n"}