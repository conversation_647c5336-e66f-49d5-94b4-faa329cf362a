{"path": {"rootPath": "/home", "relPath": "website/ronglianweb/nginx-ronglian-https.conf"}, "originalCode": "# Nginx配置 - 荣联科技网站 HTTPS封装 (80端口对外服务)\n# 使用HTTPS协议封装整个网站，通过80端口提供对外服务\n\n# HTTP服务器 - 重定向到HTTPS\nserver {\n    listen 80;\n    server_name ronglian.com www.ronglian.com ********* localhost;\n\n    # 重定向所有HTTP请求到HTTPS\n    return 301 https://$host$request_uri;\n}\n\n# HTTPS服务器 - 主要服务\nserver {\n    listen 443 ssl http2;\n    server_name ronglian.com www.ronglian.com ********* localhost;\n    \n    # SSL证书配置\n    ssl_certificate /home/<USER>/ronglianweb/ssl/certificate.crt;\n    ssl_certificate_key /home/<USER>/ronglianweb/ssl/private.key;\n    \n    # SSL安全配置\n    ssl_protocols TLSv1.2 TLSv1.3;\n    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;\n    ssl_prefer_server_ciphers off;\n    ssl_session_cache shared:SSL:10m;\n    ssl_session_timeout 10m;\n    \n    # 安全头设置\n    add_header Strict-Transport-Security \"max-age=31536000; includeSubDomains\" always;\n    add_header X-Frame-Options DENY;\n    add_header X-Content-Type-Options nosniff;\n    add_header X-XSS-Protection \"1; mode=block\";\n    add_header Referrer-Policy \"strict-origin-when-cross-origin\";\n    \n    # 日志配置\n    access_log /var/log/nginx/ronglian_https_access.log;\n    error_log /var/log/nginx/ronglian_https_error.log;\n    \n    # Gzip压缩\n    gzip on;\n    gzip_vary on;\n    gzip_min_length 1024;\n    gzip_proxied any;\n    gzip_comp_level 6;\n    gzip_types\n        text/plain\n        text/css\n        text/xml\n        text/javascript\n        application/json\n        application/javascript\n        application/xml+rss\n        application/atom+xml\n        image/svg+xml;\n\n    # 主站首页\n    location / {\n        proxy_pass http://localhost:3000;\n        proxy_http_version 1.1;\n        proxy_set_header Upgrade $http_upgrade;\n        proxy_set_header Connection 'upgrade';\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto https;\n        proxy_set_header X-Forwarded-Port 443;\n        proxy_cache_bypass $http_upgrade;\n        \n        # 超时设置\n        proxy_connect_timeout 30s;\n        proxy_send_timeout 30s;\n        proxy_read_timeout 30s;\n    }\n    \n    # 新闻展示页面（公开访问）\n    location /news/ {\n        proxy_pass http://localhost:3000/news/;\n        proxy_http_version 1.1;\n        proxy_set_header Upgrade $http_upgrade;\n        proxy_set_header Connection 'upgrade';\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto https;\n        proxy_set_header X-Forwarded-Port 443;\n        proxy_cache_bypass $http_upgrade;\n    }\n    \n    # 静态资源代理（CSS、JS、图片等）\n    location /plugins/ {\n        proxy_pass http://localhost:3000/plugins/;\n        proxy_http_version 1.1;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto https;\n        proxy_set_header X-Forwarded-Port 443;\n        \n        # 静态资源缓存\n        expires 1y;\n        add_header Cache-Control \"public, immutable\";\n    }\n    \n    location /images/ {\n        proxy_pass http://localhost:3000/images/;\n        proxy_http_version 1.1;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto https;\n        proxy_set_header X-Forwarded-Port 443;\n        \n        # 图片缓存\n        expires 30d;\n        add_header Cache-Control \"public\";\n    }\n    \n    location /component-news/ {\n        proxy_pass http://localhost:3000/component-news/;\n        proxy_http_version 1.1;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto https;\n        proxy_set_header X-Forwarded-Port 443;\n        \n        # 组件缓存\n        expires 1d;\n        add_header Cache-Control \"public\";\n    }\n    \n    # 新闻管理系统（需要认证）\n    location /admin/ {\n        proxy_pass http://localhost:3000/admin/;\n        proxy_http_version 1.1;\n        proxy_set_header Upgrade $http_upgrade;\n        proxy_set_header Connection 'upgrade';\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto https;\n        proxy_set_header X-Forwarded-Port 443;\n        proxy_cache_bypass $http_upgrade;\n        \n        # 额外的安全头\n        add_header X-Frame-Options SAMEORIGIN;\n    }\n    \n    # 新闻管理API\n    location /api/admin/ {\n        proxy_pass http://localhost:3000/api/admin/;\n        proxy_http_version 1.1;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto https;\n        proxy_set_header X-Forwarded-Port 443;\n        proxy_set_header Content-Type application/json;\n    }\n    \n    # 其他API路由\n    location /api/ {\n        proxy_pass http://localhost:3000/api/;\n        proxy_http_version 1.1;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto https;\n        proxy_set_header X-Forwarded-Port 443;\n    }\n    \n    # 健康检查\n    location /health {\n        access_log off;\n        return 200 \"healthy\\n\";\n        add_header Content-Type text/plain;\n    }\n    \n    # 禁止访问隐藏文件\n    location ~ /\\. {\n        deny all;\n        access_log off;\n        log_not_found off;\n    }\n    \n    # 禁止访问备份文件\n    location ~ ~$ {\n        deny all;\n        access_log off;\n        log_not_found off;\n    }\n    \n    # 错误页面\n    error_page 404 /404.html;\n    error_page 500 502 503 504 /50x.html;\n    \n    location = /404.html {\n        proxy_pass http://localhost:3000/404.html;\n    }\n    \n    location = /50x.html {\n        proxy_pass http://localhost:3000/50x.html;\n    }\n}\n\n# 额外的80端口服务器（用于nginx状态监控等）\nserver {\n    listen 8080;\n    server_name localhost;\n    \n    # nginx状态页面\n    location /nginx_status {\n        stub_status on;\n        access_log off;\n        allow 127.0.0.1;\n        allow ::1;\n        deny all;\n    }\n    \n    # 健康检查\n    location /health {\n        access_log off;\n        return 200 \"nginx healthy\\n\";\n        add_header Content-Type text/plain;\n    }\n}\n", "modifiedCode": "# Nginx配置 - 荣联科技网站 HTTPS封装 (80端口对外服务)\n# 使用HTTPS协议封装整个网站，通过80端口提供对外服务\n\n# HTTP服务器 - 重定向到HTTPS\nserver {\n    listen 80;\n    server_name ronglian.com www.ronglian.com ********* localhost;\n\n    # 重定向所有HTTP请求到HTTPS\n    return 301 https://$host$request_uri;\n}\n\n# HTTPS服务器 - 主要服务\nserver {\n    listen 443 ssl http2;\n    server_name ronglian.com www.ronglian.com ********* localhost;\n    \n    # SSL证书配置\n    ssl_certificate /home/<USER>/ronglianweb/ssl/certificate.crt;\n    ssl_certificate_key /home/<USER>/ronglianweb/ssl/private.key;\n    \n    # SSL安全配置\n    ssl_protocols TLSv1.2 TLSv1.3;\n    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;\n    ssl_prefer_server_ciphers off;\n    ssl_session_cache shared:SSL:10m;\n    ssl_session_timeout 10m;\n    \n    # 安全头设置\n    add_header Strict-Transport-Security \"max-age=31536000; includeSubDomains\" always;\n    add_header X-Frame-Options DENY;\n    add_header X-Content-Type-Options nosniff;\n    add_header X-XSS-Protection \"1; mode=block\";\n    add_header Referrer-Policy \"strict-origin-when-cross-origin\";\n    \n    # 日志配置\n    access_log /var/log/nginx/ronglian_https_access.log;\n    error_log /var/log/nginx/ronglian_https_error.log;\n    \n    # Gzip压缩\n    gzip on;\n    gzip_vary on;\n    gzip_min_length 1024;\n    gzip_proxied any;\n    gzip_comp_level 6;\n    gzip_types\n        text/plain\n        text/css\n        text/xml\n        text/javascript\n        application/json\n        application/javascript\n        application/xml+rss\n        application/atom+xml\n        image/svg+xml;\n\n    # 主站首页\n    location / {\n        proxy_pass http://localhost:3000;\n        proxy_http_version 1.1;\n        proxy_set_header Upgrade $http_upgrade;\n        proxy_set_header Connection 'upgrade';\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto https;\n        proxy_set_header X-Forwarded-Port 443;\n        proxy_cache_bypass $http_upgrade;\n        \n        # 超时设置\n        proxy_connect_timeout 30s;\n        proxy_send_timeout 30s;\n        proxy_read_timeout 30s;\n    }\n    \n    # 新闻展示页面（公开访问）\n    location /news/ {\n        proxy_pass http://localhost:3000/news/;\n        proxy_http_version 1.1;\n        proxy_set_header Upgrade $http_upgrade;\n        proxy_set_header Connection 'upgrade';\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto https;\n        proxy_set_header X-Forwarded-Port 443;\n        proxy_cache_bypass $http_upgrade;\n    }\n    \n    # 静态资源代理（CSS、JS、图片等）\n    location /plugins/ {\n        proxy_pass http://localhost:3000/plugins/;\n        proxy_http_version 1.1;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto https;\n        proxy_set_header X-Forwarded-Port 443;\n        \n        # 静态资源缓存\n        expires 1y;\n        add_header Cache-Control \"public, immutable\";\n    }\n    \n    location /images/ {\n        proxy_pass http://localhost:3000/images/;\n        proxy_http_version 1.1;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto https;\n        proxy_set_header X-Forwarded-Port 443;\n        \n        # 图片缓存\n        expires 30d;\n        add_header Cache-Control \"public\";\n    }\n    \n    location /component-news/ {\n        proxy_pass http://localhost:3000/component-news/;\n        proxy_http_version 1.1;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto https;\n        proxy_set_header X-Forwarded-Port 443;\n        \n        # 组件缓存\n        expires 1d;\n        add_header Cache-Control \"public\";\n    }\n    \n    # 新闻管理系统（需要认证）\n    location /admin/ {\n        proxy_pass http://localhost:3000/admin/;\n        proxy_http_version 1.1;\n        proxy_set_header Upgrade $http_upgrade;\n        proxy_set_header Connection 'upgrade';\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto https;\n        proxy_set_header X-Forwarded-Port 443;\n        proxy_cache_bypass $http_upgrade;\n        \n        # 额外的安全头\n        add_header X-Frame-Options SAMEORIGIN;\n    }\n    \n    # 新闻管理API\n    location /api/admin/ {\n        proxy_pass http://localhost:3000/api/admin/;\n        proxy_http_version 1.1;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto https;\n        proxy_set_header X-Forwarded-Port 443;\n        proxy_set_header Content-Type application/json;\n    }\n    \n    # 其他API路由\n    location /api/ {\n        proxy_pass http://localhost:3000/api/;\n        proxy_http_version 1.1;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto https;\n        proxy_set_header X-Forwarded-Port 443;\n    }\n    \n    # 健康检查\n    location /health {\n        access_log off;\n        return 200 \"healthy\\n\";\n        add_header Content-Type text/plain;\n    }\n    \n    # 禁止访问隐藏文件\n    location ~ /\\. {\n        deny all;\n        access_log off;\n        log_not_found off;\n    }\n    \n    # 禁止访问备份文件\n    location ~ ~$ {\n        deny all;\n        access_log off;\n        log_not_found off;\n    }\n    \n    # 错误页面\n    error_page 404 /404.html;\n    error_page 500 502 503 504 /50x.html;\n    \n    location = /404.html {\n        proxy_pass http://localhost:3000/404.html;\n    }\n    \n    location = /50x.html {\n        proxy_pass http://localhost:3000/50x.html;\n    }\n}\n\n# 额外的80端口服务器（用于nginx状态监控等）\nserver {\n    listen 8080;\n    server_name localhost;\n    \n    # nginx状态页面\n    location /nginx_status {\n        stub_status on;\n        access_log off;\n        allow 127.0.0.1;\n        allow ::1;\n        deny all;\n    }\n    \n    # 健康检查\n    location /health {\n        access_log off;\n        return 200 \"nginx healthy\\n\";\n        add_header Content-Type text/plain;\n    }\n}\n"}