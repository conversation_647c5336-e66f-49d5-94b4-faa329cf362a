{"path": {"rootPath": "/home", "relPath": "website/ACCESS_ADDRESSES_UPDATED.md"}, "originalCode": "# 访问地址更新总结\n\n## 更新内容\n\n已将所有脚本和文档中的localhost访问地址更新为IP地址 `*********`。\n\n## 已更新的文件\n\n### 1. fix-502.sh\n**更新前:**\n```\n主站首页: https://dl380\n新闻页面: https://dl380/news/index\n管理登录: https://dl380/admin/login\n新闻管理: https://dl380/admin/news\n```\n\n**更新后:**\n```\n🔒 主站 (HTTPS): https://*********\n📰 新闻页面: https://*********/news/index\n🔐 管理登录: https://*********/admin/login\n🔧 直接访问: http://*********:3000\n```\n\n### 2. one-click-fix.sh\n**更新前:**\n```\n主站首页: https://dl380\n新闻页面: https://dl380/news/index\n管理登录: https://dl380/admin/login\n新闻管理: https://dl380/admin/news\n```\n\n**更新后:**\n```\n🔒 主站 (HTTPS): https://*********\n📰 新闻页面: https://*********/news/index\n🔐 管理登录: https://*********/admin/login\n🔧 直接访问: http://*********:3000\n```\n\n## 当前访问地址\n\n### 主要访问地址 (HTTPS)\n- 🔒 **主站 (HTTPS)**: `https://*********`\n- 📰 **新闻页面**: `https://*********/news/index`\n- 🔐 **管理登录**: `https://*********/admin/login`\n\n### 直接访问 (HTTP)\n- 🔧 **直接访问**: `http://*********:3000`\n\n### Vue 3.0 应用访问地址\n- 🏠 **主页**: `http://*********`\n- 🎯 **Vue 3 演示**: `http://*********/vue3-demo`\n- 🧪 **Vue 3 测试**: `http://*********/vue3-test`\n- 🏡 **Vue 3 主页**: `http://*********/vue3-home`\n- 📝 **Vue 3 简单**: `http://*********/vue3-simple`\n\n## 网络架构\n\n### HTTPS 服务 (主站)\n```\n用户 → *********:443 (HTTPS) → 内部服务:3000\n```\n\n### HTTP 服务 (Vue 3.0)\n```\n用户 → *********:80 (Nginx) → localhost:8080 (Node.js)\n```\n\n## 服务状态检查\n\n### 检查HTTPS服务\n```bash\ncurl -I -k https://*********\ncurl -I -k https://*********/news/index\ncurl -I -k https://*********/admin/login\n```\n\n### 检查HTTP服务 (Vue 3.0)\n```bash\ncurl -I http://*********\ncurl -I http://*********/vue3-demo\ncurl -I http://*********/vue3-test\n```\n\n### 检查直接访问\n```bash\ncurl -I http://*********:3000\n```\n\n## 登录信息\n\n### 管理员账户\n- **用户名**: admin\n- **密码**: admin123\n\n### 编辑员账户\n- **用户名**: user01\n- **密码**: user123\n\n## 使用说明\n\n### 1. 主站访问 (HTTPS)\n- 用于生产环境的主要网站\n- 支持新闻管理、用户管理等功能\n- 使用HTTPS加密连接\n\n### 2. Vue 3.0 应用 (HTTP)\n- 用于Vue 3.0框架演示\n- 展示现代前端技术\n- 通过Nginx反向代理提供服务\n\n### 3. 直接访问 (HTTP:3000)\n- 用于开发和调试\n- 直接访问Node.js应用\n- 绕过代理服务器\n\n## 故障排除\n\n### HTTPS服务无法访问\n1. 检查SSL证书配置\n2. 检查防火墙443端口\n3. 检查Nginx HTTPS配置\n\n### HTTP服务无法访问\n1. 检查Nginx状态: `sudo systemctl status nginx`\n2. 检查Node.js进程: `ps aux | grep node`\n3. 运行健康检查: `./health-check.sh`\n\n### 端口3000无法访问\n1. 检查Node.js应用状态\n2. 检查防火墙3000端口\n3. 检查进程监听: `netstat -tlnp | grep :3000`\n\n## 测试结果\n\n### HTTPS服务测试 ✅\n- ✅ **主站**: `https://*********` - 200 OK\n- ✅ **新闻页面**: `https://*********/news/index` - 200 OK\n- ✅ **管理登录**: `https://*********/admin/login` - 200 OK\n\n### HTTP服务测试 ✅\n- ✅ **Vue 3主页**: `http://*********` - 301 重定向到HTTPS (正常)\n- ✅ **Vue 3演示**: `http://*********/vue3-demo` - 301 重定向到HTTPS (正常)\n\n### 直接访问测试\n- ❌ **端口3000**: `http://*********:3000` - 无法访问 (仅内部访问，正常)\n\n## 总结\n\n✅ **所有访问地址已成功更新为IP地址 ***********\n\n### 主要服务\n- **HTTPS服务**: 主站和管理功能 (443端口)\n- **HTTP重定向**: 自动重定向到HTTPS (80端口)\n- **内部服务**: Node.js应用运行在3000端口 (仅内部访问)\n\n### 网络架构\n```\n外部用户 → *********:80 → 301重定向 → *********:443 → localhost:3000\n```\n\n### 安全特性\n- ✅ SSL/TLS加密\n- ✅ 自动HTTPS重定向\n- ✅ 安全头设置\n- ✅ 内部服务保护\n\n现在您可以通过IP地址 `*********` 安全地访问所有服务了！🔒\n", "modifiedCode": "# 访问地址更新总结\n\n## 更新内容\n\n已将所有脚本和文档中的localhost访问地址更新为IP地址 `*********`。\n\n## 已更新的文件\n\n### 1. fix-502.sh\n**更新前:**\n```\n主站首页: https://dl380\n新闻页面: https://dl380/news/index\n管理登录: https://dl380/admin/login\n新闻管理: https://dl380/admin/news\n```\n\n**更新后:**\n```\n🔒 主站 (HTTPS): https://*********\n📰 新闻页面: https://*********/news/index\n🔐 管理登录: https://*********/admin/login\n🔧 直接访问: http://*********:3000\n```\n\n### 2. one-click-fix.sh\n**更新前:**\n```\n主站首页: https://dl380\n新闻页面: https://dl380/news/index\n管理登录: https://dl380/admin/login\n新闻管理: https://dl380/admin/news\n```\n\n**更新后:**\n```\n🔒 主站 (HTTPS): https://*********\n📰 新闻页面: https://*********/news/index\n🔐 管理登录: https://*********/admin/login\n🔧 直接访问: http://*********:3000\n```\n\n## 当前访问地址\n\n### 主要访问地址 (HTTPS)\n- 🔒 **主站 (HTTPS)**: `https://*********`\n- 📰 **新闻页面**: `https://*********/news/index`\n- 🔐 **管理登录**: `https://*********/admin/login`\n\n### 直接访问 (HTTP)\n- 🔧 **直接访问**: `http://*********:3000`\n\n### Vue 3.0 应用访问地址\n- 🏠 **主页**: `http://*********`\n- 🎯 **Vue 3 演示**: `http://*********/vue3-demo`\n- 🧪 **Vue 3 测试**: `http://*********/vue3-test`\n- 🏡 **Vue 3 主页**: `http://*********/vue3-home`\n- 📝 **Vue 3 简单**: `http://*********/vue3-simple`\n\n## 网络架构\n\n### HTTPS 服务 (主站)\n```\n用户 → *********:443 (HTTPS) → 内部服务:3000\n```\n\n### HTTP 服务 (Vue 3.0)\n```\n用户 → *********:80 (Nginx) → localhost:8080 (Node.js)\n```\n\n## 服务状态检查\n\n### 检查HTTPS服务\n```bash\ncurl -I -k https://*********\ncurl -I -k https://*********/news/index\ncurl -I -k https://*********/admin/login\n```\n\n### 检查HTTP服务 (Vue 3.0)\n```bash\ncurl -I http://*********\ncurl -I http://*********/vue3-demo\ncurl -I http://*********/vue3-test\n```\n\n### 检查直接访问\n```bash\ncurl -I http://*********:3000\n```\n\n## 登录信息\n\n### 管理员账户\n- **用户名**: admin\n- **密码**: admin123\n\n### 编辑员账户\n- **用户名**: user01\n- **密码**: user123\n\n## 使用说明\n\n### 1. 主站访问 (HTTPS)\n- 用于生产环境的主要网站\n- 支持新闻管理、用户管理等功能\n- 使用HTTPS加密连接\n\n### 2. Vue 3.0 应用 (HTTP)\n- 用于Vue 3.0框架演示\n- 展示现代前端技术\n- 通过Nginx反向代理提供服务\n\n### 3. 直接访问 (HTTP:3000)\n- 用于开发和调试\n- 直接访问Node.js应用\n- 绕过代理服务器\n\n## 故障排除\n\n### HTTPS服务无法访问\n1. 检查SSL证书配置\n2. 检查防火墙443端口\n3. 检查Nginx HTTPS配置\n\n### HTTP服务无法访问\n1. 检查Nginx状态: `sudo systemctl status nginx`\n2. 检查Node.js进程: `ps aux | grep node`\n3. 运行健康检查: `./health-check.sh`\n\n### 端口3000无法访问\n1. 检查Node.js应用状态\n2. 检查防火墙3000端口\n3. 检查进程监听: `netstat -tlnp | grep :3000`\n\n## 测试结果\n\n### HTTPS服务测试 ✅\n- ✅ **主站**: `https://*********` - 200 OK\n- ✅ **新闻页面**: `https://*********/news/index` - 200 OK\n- ✅ **管理登录**: `https://*********/admin/login` - 200 OK\n\n### HTTP服务测试 ✅\n- ✅ **Vue 3主页**: `http://*********` - 301 重定向到HTTPS (正常)\n- ✅ **Vue 3演示**: `http://*********/vue3-demo` - 301 重定向到HTTPS (正常)\n\n### 直接访问测试\n- ❌ **端口3000**: `http://*********:3000` - 无法访问 (仅内部访问，正常)\n\n## 总结\n\n✅ **所有访问地址已成功更新为IP地址 ***********\n\n### 主要服务\n- **HTTPS服务**: 主站和管理功能 (443端口)\n- **HTTP重定向**: 自动重定向到HTTPS (80端口)\n- **内部服务**: Node.js应用运行在3000端口 (仅内部访问)\n\n### 网络架构\n```\n外部用户 → *********:80 → 301重定向 → *********:443 → localhost:3000\n```\n\n### 安全特性\n- ✅ SSL/TLS加密\n- ✅ 自动HTTPS重定向\n- ✅ 安全头设置\n- ✅ 内部服务保护\n\n现在您可以通过IP地址 `*********` 安全地访问所有服务了！🔒\n"}