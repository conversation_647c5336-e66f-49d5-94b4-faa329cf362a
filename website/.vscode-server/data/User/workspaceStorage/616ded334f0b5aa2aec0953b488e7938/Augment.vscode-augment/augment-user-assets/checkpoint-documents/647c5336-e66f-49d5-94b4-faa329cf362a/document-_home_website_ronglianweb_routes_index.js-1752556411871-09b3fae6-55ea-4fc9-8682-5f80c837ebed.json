{"path": {"rootPath": "/home", "relPath": "website/ronglianweb/routes/index.js"}, "originalCode": "var express = require('express');\r\nvar router = express.Router();\r\nvar authRestApiProxy = require(\"../libs/authRestApiProxy\");\r\nvar log4js = require(\"../libs/log4jsUtil.js\");\r\nvar constants = require(\"../libs/constants.js\");\r\nvar merchantUtil = require(\"../libs/merchantUtil.js\");\r\nvar logger = log4js.getLogger('index');\r\nvar async = require('async');\r\nvar config = require('../libs/config');\r\nvar imgDomain = config.ImageService.domain;\r\nvar title = '首页';\r\n\r\nrouter.get('/', function (req, res, next) {\r\n\tvar pageTabsUrl = \"homepage\";\r\n\tasync.waterfall([\r\n\t\tfunction (callback) {\r\n\t\t\tmerchantUtil.getMerchantAllInfo(req,function (err, merchantData) {\r\n\t\t\t\tcallback(err, merchantData);\r\n\t\t\t});\r\n\t\t},\r\n\t\tfunction (merchantData, callback) {\r\n\t        var pageTabsId ='', err = null;\r\n\t\t\tif(merchantData.pageTabs && merchantData.pageTabs.length > 0) {\r\n\t\t\t\tmerchantData.pageTabs.forEach(function (item) {\r\n\t\t\t\t    if(item.tabsName == '首页' && item.modulesUrl == pageTabsUrl){\r\n\t\t\t\t        pageTabsId = item.tabsId;\r\n\t\t\t\t    }\r\n\t\t\t\t});\r\n\t\t\t\tif(pageTabsId){\r\n\t\t\t\t\tcallback(err, merchantData, pageTabsId);\r\n                }else{\r\n\t\t\t\t\terr = {pageTabsId: undefined};\r\n\t\t\t\t\tcallback(err, null);\r\n                }\r\n\r\n\t\t\t}else{\r\n\t\t\t\terr = {pageTabsId: undefined};\r\n\t\t\t\tcallback(err, null);\r\n            }\r\n\t\t},\r\n\t\tfunction (merchantData, pageTabsId, callback) {\r\n\t\t\tvar reqVo = {\r\n\t\t\t\ttabsId : pageTabsId\r\n\t\t\t};\r\n\t\t\tauthRestApiProxy.post('RetailerService', '/page/tabs/q', reqVo, function resultData(err, tabData) {\r\n\t\t\t\tcallback(err, merchantData, pageTabsId, tabData);\r\n\t\t\t});\r\n\t\t},\r\n\t\tfunction (merchantData, pageTabsId, tabData, callback) {\r\n\t\t\tvar innerReqVo = {};\r\n\t\t\tinnerReqVo.tabsId = pageTabsId;\r\n\t\t\tinnerReqVo.showMark = '1';\r\n\t\t\tauthRestApiProxy.post('RetailerService', '/content/block/q', innerReqVo, function resultData(err, contentData) {\r\n\t\t\t\tcallback(err, merchantData, pageTabsId, tabData, contentData);\r\n\t\t\t});\r\n\t\t},\r\n\t\tfunction (merchantData, pageTabsId, tabData, contentData, callback) {\r\n            var ptType = pageTabsUrl;\r\n            if(ptType == 'homepage'){\r\n                authRestApiProxy.post('RetailerService', '/news/n/l/5', {}, function resultData(err, nData) {\r\n                    callback(err, merchantData, pageTabsId, tabData, contentData, nData);\r\n                });\r\n            }else{\r\n                callback(null,merchantData, pageTabsId, tabData, contentData, null);\r\n            }\r\n\t\t},\r\n\t\tfunction (merchantData, pageTabsId, tabData, contentData, nData, callback) {\r\n\t\t\tauthRestApiProxy.post('RetailerService', '/page/tabs/pq', {'tabsId':pageTabsId}, function resultData(err, kData) {\r\n\t\t\t\tcallback(err, merchantData, pageTabsId, tabData,contentData,nData, kData);\r\n\t\t\t});\r\n\t\t},\r\n        function (merchantData, pageTabsId, tabData, contentData, nData, kData, callback) {\r\n            authRestApiProxy.post('RetailerService', '/page/tabs/rrtn', {'tabsId':pageTabsId,newsLimit:8,proLimit:6}, function resultData(err, electData) {\r\n                merchantData.electData = electData;\r\n                callback(err, merchantData, pageTabsId, tabData,contentData, nData, kData);\r\n            });\r\n        }\r\n\t],function (err, merchantData, pageTabsId, tabData, contentData, nData, kData) {\r\n\t\tif(err){\r\n\t\t\tlogger.info(err.message);\r\n\t\t\tlogger.info(err.code);\r\n\t\t\tres.render('errordiv');\r\n\t\t}else {\r\n\t\t\tmerchantData.kData = kData;\r\n            merchantData.title = kData.title;\r\n\t\t\tmerchantData.unitModuleList = contentData;\r\n\t\t\tmerchantData.pagetabsID = pageTabsId;\r\n\t\t\tmerchantData.newsData = nData;\r\n            merchantData.topTabsId = pageTabsId;\r\n\t\t\tif(tabData){\r\n\t\t\t\tmerchantData.ptType = tabData[0].modulesUrl;\r\n\t\t\t\tmerchantData.pageTabsUrl = tabData[0].modulesUrl;\r\n\t\t\t}\r\n            merchantData.imgDomainLO = imgDomain;\r\n\t\t\tlogger.debug(merchantData);\r\n\t\t\tres.render(\"layout\", merchantData);\r\n\t\t}\r\n\r\n\t});\r\n});\r\n\r\n//转发跳转\r\nrouter.get('/index/:url/', function (req, res, next) {\r\n    merchantUtil.getMerchantAllInfo(req, function (err, menuData) {\r\n        logger.info(\"原始:\" + req.params.url);\r\n        var url = req.params.url.replace(/[.]/g, \"/\");\r\n        logger.info(\"替换:\" + url);\r\n        var data = menuData;\r\n        data.topTabsId = req.query.topTabsId;\r\n        res.render(\"website/\" + url, data);\r\n    });\r\n});\r\n//英文版\r\nrouter.get('/en', function (req, res, next) {\r\n    res.render(\"public/dest/en/index.html\");\r\n});\r\n\r\n// Vue 3 test routes\r\nrouter.get('/vue3-test', function (req, res, next) {\r\n    res.render(\"website/vue3-test\");\r\n});\r\n\r\nrouter.get('/vue3-home', function (req, res, next) {\r\n    res.render(\"website/vue3-home\");\r\n});\r\n\r\nrouter.get('/vue3-simple', function (req, res, next) {\r\n    res.render(\"website/vue3-simple\");\r\n});\r\n\r\nmodule.exports = router;\r\n", "modifiedCode": "var express = require('express');\r\nvar router = express.Router();\r\nvar authRestApiProxy = require(\"../libs/authRestApiProxy\");\r\nvar log4js = require(\"../libs/log4jsUtil.js\");\r\nvar constants = require(\"../libs/constants.js\");\r\nvar merchantUtil = require(\"../libs/merchantUtil.js\");\r\nvar logger = log4js.getLogger('index');\r\nvar async = require('async');\r\nvar config = require('../libs/config');\r\nvar imgDomain = config.ImageService.domain;\r\nvar title = '首页';\r\n\r\nrouter.get('/', function (req, res, next) {\r\n\tvar pageTabsUrl = \"homepage\";\r\n\tasync.waterfall([\r\n\t\tfunction (callback) {\r\n\t\t\tmerchantUtil.getMerchantAllInfo(req,function (err, merchantData) {\r\n\t\t\t\tcallback(err, merchantData);\r\n\t\t\t});\r\n\t\t},\r\n\t\tfunction (merchantData, callback) {\r\n\t        var pageTabsId ='', err = null;\r\n\t\t\tif(merchantData.pageTabs && merchantData.pageTabs.length > 0) {\r\n\t\t\t\tmerchantData.pageTabs.forEach(function (item) {\r\n\t\t\t\t    if(item.tabsName == '首页' && item.modulesUrl == pageTabsUrl){\r\n\t\t\t\t        pageTabsId = item.tabsId;\r\n\t\t\t\t    }\r\n\t\t\t\t});\r\n\t\t\t\tif(pageTabsId){\r\n\t\t\t\t\tcallback(err, merchantData, pageTabsId);\r\n                }else{\r\n\t\t\t\t\terr = {pageTabsId: undefined};\r\n\t\t\t\t\tcallback(err, null);\r\n                }\r\n\r\n\t\t\t}else{\r\n\t\t\t\terr = {pageTabsId: undefined};\r\n\t\t\t\tcallback(err, null);\r\n            }\r\n\t\t},\r\n\t\tfunction (merchantData, pageTabsId, callback) {\r\n\t\t\tvar reqVo = {\r\n\t\t\t\ttabsId : pageTabsId\r\n\t\t\t};\r\n\t\t\tauthRestApiProxy.post('RetailerService', '/page/tabs/q', reqVo, function resultData(err, tabData) {\r\n\t\t\t\tcallback(err, merchantData, pageTabsId, tabData);\r\n\t\t\t});\r\n\t\t},\r\n\t\tfunction (merchantData, pageTabsId, tabData, callback) {\r\n\t\t\tvar innerReqVo = {};\r\n\t\t\tinnerReqVo.tabsId = pageTabsId;\r\n\t\t\tinnerReqVo.showMark = '1';\r\n\t\t\tauthRestApiProxy.post('RetailerService', '/content/block/q', innerReqVo, function resultData(err, contentData) {\r\n\t\t\t\tcallback(err, merchantData, pageTabsId, tabData, contentData);\r\n\t\t\t});\r\n\t\t},\r\n\t\tfunction (merchantData, pageTabsId, tabData, contentData, callback) {\r\n            var ptType = pageTabsUrl;\r\n            if(ptType == 'homepage'){\r\n                authRestApiProxy.post('RetailerService', '/news/n/l/5', {}, function resultData(err, nData) {\r\n                    callback(err, merchantData, pageTabsId, tabData, contentData, nData);\r\n                });\r\n            }else{\r\n                callback(null,merchantData, pageTabsId, tabData, contentData, null);\r\n            }\r\n\t\t},\r\n\t\tfunction (merchantData, pageTabsId, tabData, contentData, nData, callback) {\r\n\t\t\tauthRestApiProxy.post('RetailerService', '/page/tabs/pq', {'tabsId':pageTabsId}, function resultData(err, kData) {\r\n\t\t\t\tcallback(err, merchantData, pageTabsId, tabData,contentData,nData, kData);\r\n\t\t\t});\r\n\t\t},\r\n        function (merchantData, pageTabsId, tabData, contentData, nData, kData, callback) {\r\n            authRestApiProxy.post('RetailerService', '/page/tabs/rrtn', {'tabsId':pageTabsId,newsLimit:8,proLimit:6}, function resultData(err, electData) {\r\n                merchantData.electData = electData;\r\n                callback(err, merchantData, pageTabsId, tabData,contentData, nData, kData);\r\n            });\r\n        }\r\n\t],function (err, merchantData, pageTabsId, tabData, contentData, nData, kData) {\r\n\t\tif(err){\r\n\t\t\tlogger.info(err.message);\r\n\t\t\tlogger.info(err.code);\r\n\t\t\tres.render('errordiv');\r\n\t\t}else {\r\n\t\t\tmerchantData.kData = kData;\r\n            merchantData.title = kData.title;\r\n\t\t\tmerchantData.unitModuleList = contentData;\r\n\t\t\tmerchantData.pagetabsID = pageTabsId;\r\n\t\t\tmerchantData.newsData = nData;\r\n            merchantData.topTabsId = pageTabsId;\r\n\t\t\tif(tabData){\r\n\t\t\t\tmerchantData.ptType = tabData[0].modulesUrl;\r\n\t\t\t\tmerchantData.pageTabsUrl = tabData[0].modulesUrl;\r\n\t\t\t}\r\n            merchantData.imgDomainLO = imgDomain;\r\n\t\t\tlogger.debug(merchantData);\r\n\t\t\tres.render(\"layout\", merchantData);\r\n\t\t}\r\n\r\n\t});\r\n});\r\n\r\n//转发跳转\r\nrouter.get('/index/:url/', function (req, res, next) {\r\n    merchantUtil.getMerchantAllInfo(req, function (err, menuData) {\r\n        logger.info(\"原始:\" + req.params.url);\r\n        var url = req.params.url.replace(/[.]/g, \"/\");\r\n        logger.info(\"替换:\" + url);\r\n        var data = menuData;\r\n        data.topTabsId = req.query.topTabsId;\r\n        res.render(\"website/\" + url, data);\r\n    });\r\n});\r\n//英文版\r\nrouter.get('/en', function (req, res, next) {\r\n    res.render(\"public/dest/en/index.html\");\r\n});\r\n\r\n// Vue 3 test routes\r\nrouter.get('/vue3-test', function (req, res, next) {\r\n    res.render(\"website/vue3-test\");\r\n});\r\n\r\nrouter.get('/vue3-home', function (req, res, next) {\r\n    res.render(\"website/vue3-home\");\r\n});\r\n\r\nrouter.get('/vue3-simple', function (req, res, next) {\r\n    res.render(\"website/vue3-simple\");\r\n});\r\n\r\nrouter.get('/word-editor-test', function (req, res, next) {\r\n    console.log('访问word-editor-test路由');\r\n    res.render(\"admin/word-editor-test\");\r\n});\r\n\r\nmodule.exports = router;\r\n"}