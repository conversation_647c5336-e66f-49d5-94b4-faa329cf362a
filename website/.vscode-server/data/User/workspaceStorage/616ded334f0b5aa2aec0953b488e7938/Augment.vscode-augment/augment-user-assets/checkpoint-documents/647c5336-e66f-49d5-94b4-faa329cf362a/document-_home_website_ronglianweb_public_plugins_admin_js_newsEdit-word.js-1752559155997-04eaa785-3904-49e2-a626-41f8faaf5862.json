{"path": {"rootPath": "/home", "relPath": "website/ronglianweb/public/plugins/admin/js/newsEdit-word.js"}, "originalCode": "// 新闻编辑JavaScript - Word兼容版本\n$(document).ready(function() {\n    let editor;\n    let isEditing = !!window.newsId;\n\n    // 初始化\n    init();\n\n    function init() {\n        initTinyMCE();\n        bindEvents();\n        \n        if (isEditing) {\n            loadNewsData();\n        }\n    }\n\n    // 初始化TinyMCE编辑器 (替换Quill)\n    function initTinyMCE() {\n        tinymce.init({\n            selector: '#editor',\n            height: 300,\n            language: 'zh_CN',\n            plugins: [\n                'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',\n                'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',\n                'insertdatetime', 'media', 'table', 'help', 'wordcount', 'paste'\n            ],\n            toolbar: [\n                'undo redo | blocks | bold italic underline strikethrough | alignleft aligncenter alignright alignjustify',\n                'bullist numlist outdent indent | removeformat | table link image media | code preview fullscreen'\n            ].join(' | '),\n            paste_data_images: true,\n            paste_word_valid_elements: \"b,strong,i,em,h1,h2,h3,h4,h5,h6,p,ol,ul,li,a[href],span,color,font-size,font-family,mark,table,tr,td,th\",\n            paste_retain_style_properties: \"all\",\n            paste_merge_formats: true,\n            automatic_uploads: true,\n            file_picker_types: 'image',\n            file_picker_callback: function(callback, value, meta) {\n                if (meta.filetype === 'image') {\n                    const input = document.createElement('input');\n                    input.setAttribute('type', 'file');\n                    input.setAttribute('accept', 'image/*');\n                    input.onchange = function() {\n                        const file = this.files[0];\n                        uploadImage(file, callback);\n                    };\n                    input.click();\n                }\n            },\n            setup: function(ed) {\n                editor = ed;\n                ed.on('change', function() {\n                    $('#content').val(ed.getContent());\n                });\n            }\n        });\n    }\n\n    // Word文档导入功能\n    function importWordDocument(file) {\n        if (!file) return;\n        \n        showMessage('正在导入Word文档，请稍候...', 'info');\n        \n        const reader = new FileReader();\n        reader.onload = function(e) {\n            const arrayBuffer = e.target.result;\n            \n            mammoth.convertToHtml({arrayBuffer: arrayBuffer})\n                .then(function(result) {\n                    const html = result.value;\n                    \n                    // 将Word内容插入到编辑器\n                    if (editor) {\n                        editor.setContent(html);\n                        $('#content').val(html);\n                    }\n                    \n                    // 显示转换消息\n                    if (result.messages.length > 0) {\n                        console.log('Word导入消息:', result.messages);\n                    }\n                    \n                    showMessage('Word文档导入成功！', 'success');\n                })\n                .catch(function(error) {\n                    console.error('Word导入错误:', error);\n                    showMessage('Word文档导入失败：' + error.message, 'error');\n                });\n        };\n        \n        reader.readAsArrayBuffer(file);\n    }\n\n    // Word文档导出功能\n    function exportToWord() {\n        if (!editor) return;\n        \n        const content = editor.getContent();\n        const title = $('#title').val() || '新闻文档';\n        \n        // 创建完整的HTML文档\n        const htmlContent = `\n            <!DOCTYPE html>\n            <html>\n            <head>\n                <meta charset=\"utf-8\">\n                <title>${title}</title>\n                <style>\n                    body { font-family: \"Microsoft YaHei\", Arial, sans-serif; line-height: 1.6; margin: 40px; }\n                    h1, h2, h3, h4, h5, h6 { color: #333; margin: 20px 0 10px 0; }\n                    p { margin: 10px 0; }\n                    table { border-collapse: collapse; width: 100%; margin: 10px 0; }\n                    table, th, td { border: 1px solid #ddd; }\n                    th, td { padding: 8px; text-align: left; }\n                    th { background-color: #f2f2f2; }\n                    img { max-width: 100%; height: auto; }\n                </style>\n            </head>\n            <body>\n                <h1>${title}</h1>\n                ${content}\n            </body>\n            </html>\n        `;\n        \n        try {\n            // 转换为Word文档\n            const converted = htmlDocx.asBlob(htmlContent);\n            \n            // 下载文件\n            const link = document.createElement('a');\n            link.href = URL.createObjectURL(converted);\n            link.download = `${title}.docx`;\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            \n            showMessage('Word文档导出成功！', 'success');\n        } catch (error) {\n            console.error('Word导出错误:', error);\n            showMessage('Word文档导出失败：' + error.message, 'error');\n        }\n    }\n\n    // 绑定事件 (保持原有事件 + 新增Word功能)\n    function bindEvents() {\n        // Word导入按钮\n        $('#importWordBtn').on('click', function() {\n            $('#wordFileInput').click();\n        });\n        \n        // Word文件选择\n        $('#wordFileInput').on('change', function() {\n            const file = this.files[0];\n            if (file) {\n                importWordDocument(file);\n            }\n        });\n        \n        // Word导出按钮\n        $('#exportWordBtn').on('click', function() {\n            exportToWord();\n        });\n\n        // 网页预览按钮\n        $('#previewWebBtn').on('click', function() {\n            previewWebFormat();\n        });\n        \n        // 保持原有的图片上传等事件\n        $('#uploadArea').on('click', function() {\n            $('#imageFile').click();\n        });\n\n        $('#imageFile').on('change', function() {\n            const file = this.files[0];\n            if (file) {\n                uploadCoverImage(file);\n            }\n        });\n\n        // 删除图片\n        $('#removeImage').on('click', function() {\n            $('#imagePreview').hide();\n            $('#coverImage').val('');\n            $('#uploadArea').show();\n        });\n\n        // 表单提交\n        $('#newsForm').on('submit', function(e) {\n            e.preventDefault();\n            saveNews();\n        });\n\n        // 保存并发布\n        $('#publishBtn').on('click', function() {\n            $('#status').val('published');\n            saveNews();\n        });\n\n        // 预览\n        $('#previewBtn').on('click', function() {\n            previewNews();\n        });\n    }\n\n    // 加载新闻数据\n    function loadNewsData() {\n        if (!window.newsId) return;\n        \n        $.get(`/api/admin/news/${window.newsId}`)\n            .done(function(response) {\n                if (response.success) {\n                    const news = response.data;\n                    \n                    // 填充表单数据\n                    $('#title').val(news.title);\n                    $('#status').val(news.status);\n                    $('#author').val(news.author);\n                    \n                    // 设置编辑器内容\n                    if (editor) {\n                        editor.setContent(news.content || '');\n                    }\n                    $('#content').val(news.content || '');\n                    \n                    // 设置封面图片\n                    if (news.coverImage) {\n                        $('#coverImage').val(news.coverImage);\n                        $('#previewImg').attr('src', news.coverImage);\n                        $('#imagePreview').show();\n                        $('#uploadArea').hide();\n                    }\n                } else {\n                    showMessage('加载新闻数据失败：' + response.message, 'error');\n                }\n            })\n            .fail(function() {\n                showMessage('加载新闻数据失败', 'error');\n            });\n    }\n\n    // 保存新闻\n    function saveNews() {\n        // 确保从编辑器获取最新内容\n        if (editor) {\n            $('#content').val(editor.getContent());\n        }\n\n        const formData = {\n            title: $('#title').val(),\n            content: $('#content').val(), // 这里保存的是HTML格式，用于网页显示\n            status: $('#status').val(),\n            author: $('#author').val(),\n            coverImage: $('#coverImage').val()\n        };\n\n        // 验证必填字段\n        if (!formData.title.trim()) {\n            showMessage('请输入新闻标题', 'error');\n            return;\n        }\n\n        if (!formData.content.trim()) {\n            showMessage('请输入新闻内容', 'error');\n            return;\n        }\n\n        // 显示保存状态\n        showMessage('正在保存新闻...', 'info');\n\n        const url = isEditing ? `/api/admin/news/${window.newsId}` : '/api/admin/news';\n        const method = isEditing ? 'PUT' : 'POST';\n\n        $.ajax({\n            url: url,\n            type: method,\n            data: formData,\n            success: function(response) {\n                if (response.success) {\n                    showMessage('新闻保存成功！内容已保存为网页格式', 'success');\n\n                    // 如果是新建，跳转到编辑页面\n                    if (!isEditing && response.data && response.data.id) {\n                        setTimeout(function() {\n                            window.location.href = `/admin/news/edit/${response.data.id}`;\n                        }, 1500);\n                    }\n                } else {\n                    showMessage('保存失败：' + response.message, 'error');\n                }\n            },\n            error: function() {\n                showMessage('保存失败，请重试', 'error');\n            }\n        });\n    }\n\n    // 预览新闻\n    function previewNews() {\n        const content = editor ? editor.getContent() : $('#content').val();\n        const title = $('#title').val();\n\n        if (!title || !content) {\n            showMessage('请先填写标题和内容', 'error');\n            return;\n        }\n\n        // 打开预览窗口\n        const previewWindow = window.open('', '_blank', 'width=800,height=600');\n        previewWindow.document.write(`\n            <!DOCTYPE html>\n            <html>\n            <head>\n                <meta charset=\"utf-8\">\n                <title>预览 - ${title}</title>\n                <link rel=\"stylesheet\" href=\"/plugins/bootstrap/css/bootstrap.min.css\">\n                <style>\n                    body { padding: 20px; font-family: \"Microsoft YaHei\", Arial, sans-serif; }\n                    .preview-header { border-bottom: 1px solid #ddd; padding-bottom: 20px; margin-bottom: 20px; }\n                    .preview-content { line-height: 1.8; }\n                </style>\n            </head>\n            <body>\n                <div class=\"container\">\n                    <div class=\"preview-header\">\n                        <h1>${title}</h1>\n                        <p class=\"text-muted\">作者：${$('#author').val()} | 状态：${$('#status option:selected').text()}</p>\n                    </div>\n                    <div class=\"preview-content\">\n                        ${content}\n                    </div>\n                </div>\n            </body>\n            </html>\n        `);\n    }\n\n    // 网页格式预览 (新增功能)\n    function previewWebFormat() {\n        const content = editor ? editor.getContent() : $('#content').val();\n        const title = $('#title').val();\n\n        if (!title || !content) {\n            showMessage('请先填写标题和内容', 'error');\n            return;\n        }\n\n        // 打开网页格式预览窗口\n        const previewWindow = window.open('', '_blank', 'width=1000,height=700');\n        previewWindow.document.write(`\n            <!DOCTYPE html>\n            <html>\n            <head>\n                <meta charset=\"utf-8\">\n                <title>网页格式预览 - ${title}</title>\n                <link rel=\"stylesheet\" href=\"/plugins/bootstrap/css/bootstrap.min.css\">\n                <style>\n                    body {\n                        font-family: \"Microsoft YaHei\", Arial, sans-serif;\n                        background: #f5f5f5;\n                        margin: 0;\n                        padding: 20px;\n                    }\n                    .news-container {\n                        max-width: 800px;\n                        margin: 0 auto;\n                        background: white;\n                        padding: 30px;\n                        border-radius: 8px;\n                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n                    }\n                    .news-header {\n                        text-align: center;\n                        border-bottom: 2px solid #007bff;\n                        padding-bottom: 20px;\n                        margin-bottom: 30px;\n                    }\n                    .news-title {\n                        font-size: 28px;\n                        font-weight: bold;\n                        color: #333;\n                        margin-bottom: 15px;\n                        line-height: 1.4;\n                    }\n                    .news-meta {\n                        color: #666;\n                        font-size: 14px;\n                    }\n                    .news-content {\n                        line-height: 1.8;\n                        font-size: 16px;\n                        color: #444;\n                    }\n                    .news-content h1, .news-content h2, .news-content h3 {\n                        color: #333;\n                        margin: 25px 0 15px 0;\n                    }\n                    .news-content p {\n                        margin: 15px 0;\n                        text-indent: 2em;\n                    }\n                    .news-content table {\n                        width: 100%;\n                        border-collapse: collapse;\n                        margin: 20px 0;\n                    }\n                    .news-content table th,\n                    .news-content table td {\n                        border: 1px solid #ddd;\n                        padding: 12px;\n                        text-align: left;\n                    }\n                    .news-content table th {\n                        background-color: #f8f9fa;\n                        font-weight: bold;\n                    }\n                    .news-content img {\n                        max-width: 100%;\n                        height: auto;\n                        display: block;\n                        margin: 20px auto;\n                        border-radius: 4px;\n                    }\n                    .preview-notice {\n                        position: fixed;\n                        top: 10px;\n                        right: 10px;\n                        background: #007bff;\n                        color: white;\n                        padding: 10px 15px;\n                        border-radius: 4px;\n                        font-size: 12px;\n                        z-index: 1000;\n                    }\n                </style>\n            </head>\n            <body>\n                <div class=\"preview-notice\">\n                    📱 网页格式预览 - 发布后的显示效果\n                </div>\n                <div class=\"news-container\">\n                    <div class=\"news-header\">\n                        <h1 class=\"news-title\">${title}</h1>\n                        <div class=\"news-meta\">\n                            <span>作者：${$('#author').val()}</span> |\n                            <span>发布时间：${new Date().toLocaleDateString()}</span> |\n                            <span>状态：${$('#status option:selected').text()}</span>\n                        </div>\n                    </div>\n                    <div class=\"news-content\">\n                        ${content}\n                    </div>\n                </div>\n                <script>\n                    // 3秒后隐藏预览提示\n                    setTimeout(function() {\n                        document.querySelector('.preview-notice').style.display = 'none';\n                    }, 3000);\n                </script>\n            </body>\n            </html>\n        `);\n\n        showMessage('已打开网页格式预览窗口，这是新闻发布后的显示效果', 'success');\n    }\n\n    // 上传封面图片\n    function uploadCoverImage(file) {\n        const formData = new FormData();\n        formData.append('image', file);\n        \n        $.ajax({\n            url: '/api/upload/image',\n            type: 'POST',\n            data: formData,\n            processData: false,\n            contentType: false,\n            success: function(response) {\n                if (response.success) {\n                    $('#coverImage').val(response.data.url);\n                    $('#previewImg').attr('src', response.data.url);\n                    $('#imagePreview').show();\n                    $('#uploadArea').hide();\n                    showMessage('封面图片上传成功', 'success');\n                } else {\n                    showMessage('图片上传失败：' + response.message, 'error');\n                }\n            },\n            error: function() {\n                showMessage('图片上传失败', 'error');\n            }\n        });\n    }\n\n    // 编辑器内图片上传\n    function uploadImage(file, callback) {\n        const formData = new FormData();\n        formData.append('image', file);\n        \n        $.ajax({\n            url: '/api/upload/image',\n            type: 'POST',\n            data: formData,\n            processData: false,\n            contentType: false,\n            success: function(response) {\n                if (response.success) {\n                    callback(response.data.url);\n                } else {\n                    showMessage('图片上传失败', 'error');\n                }\n            },\n            error: function() {\n                showMessage('图片上传失败', 'error');\n            }\n        });\n    }\n\n    // 显示消息函数\n    function showMessage(message, type) {\n        let alertClass = 'alert-info';\n        if (type === 'success') alertClass = 'alert-success';\n        if (type === 'error') alertClass = 'alert-danger';\n        if (type === 'warning') alertClass = 'alert-warning';\n        \n        const alertHtml = `\n            <div class=\"alert ${alertClass} alert-dismissible\" role=\"alert\">\n                <button type=\"button\" class=\"close\" data-dismiss=\"alert\">\n                    <span>&times;</span>\n                </button>\n                ${message}\n            </div>\n        `;\n        \n        // 移除现有的alert\n        $('.alert').remove();\n        \n        // 添加新的alert\n        $('.panel-body').first().prepend(alertHtml);\n        \n        // 3秒后自动消失\n        setTimeout(function() {\n            $('.alert').fadeOut();\n        }, 3000);\n    }\n\n    // 暴露全局函数 (保持API兼容性)\n    window.loadNewsData = loadNewsData;\n    window.saveNews = saveNews;\n    window.previewNews = previewNews;\n});\n", "modifiedCode": "// 新闻编辑JavaScript - Word兼容版本\n$(document).ready(function() {\n    let editor;\n    let isEditing = !!window.newsId;\n\n    // 初始化\n    init();\n\n    function init() {\n        initTinyMCE();\n        bindEvents();\n        \n        if (isEditing) {\n            loadNewsData();\n        }\n    }\n\n    // 初始化TinyMCE编辑器 (替换Quill)\n    function initTinyMCE() {\n        tinymce.init({\n            selector: '#editor',\n            height: 300,\n            language: 'zh_CN',\n            plugins: [\n                'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',\n                'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',\n                'insertdatetime', 'media', 'table', 'help', 'wordcount', 'paste'\n            ],\n            toolbar: [\n                'undo redo | blocks | bold italic underline strikethrough | alignleft aligncenter alignright alignjustify',\n                'bullist numlist outdent indent | removeformat | table link image media | code preview fullscreen'\n            ].join(' | '),\n            paste_data_images: true,\n            paste_word_valid_elements: \"b,strong,i,em,h1,h2,h3,h4,h5,h6,p,ol,ul,li,a[href],span,color,font-size,font-family,mark,table,tr,td,th\",\n            paste_retain_style_properties: \"all\",\n            paste_merge_formats: true,\n            automatic_uploads: true,\n            file_picker_types: 'image',\n            file_picker_callback: function(callback, value, meta) {\n                if (meta.filetype === 'image') {\n                    const input = document.createElement('input');\n                    input.setAttribute('type', 'file');\n                    input.setAttribute('accept', 'image/*');\n                    input.onchange = function() {\n                        const file = this.files[0];\n                        uploadImage(file, callback);\n                    };\n                    input.click();\n                }\n            },\n            setup: function(ed) {\n                editor = ed;\n                ed.on('change', function() {\n                    $('#content').val(ed.getContent());\n                });\n            }\n        });\n    }\n\n    // Word文档导入功能\n    function importWordDocument(file) {\n        if (!file) return;\n        \n        showMessage('正在导入Word文档，请稍候...', 'info');\n        \n        const reader = new FileReader();\n        reader.onload = function(e) {\n            const arrayBuffer = e.target.result;\n            \n            mammoth.convertToHtml({arrayBuffer: arrayBuffer})\n                .then(function(result) {\n                    const html = result.value;\n                    \n                    // 将Word内容插入到编辑器\n                    if (editor) {\n                        editor.setContent(html);\n                        $('#content').val(html);\n                    }\n                    \n                    // 显示转换消息\n                    if (result.messages.length > 0) {\n                        console.log('Word导入消息:', result.messages);\n                    }\n                    \n                    showMessage('Word文档导入成功！', 'success');\n                })\n                .catch(function(error) {\n                    console.error('Word导入错误:', error);\n                    showMessage('Word文档导入失败：' + error.message, 'error');\n                });\n        };\n        \n        reader.readAsArrayBuffer(file);\n    }\n\n    // Word文档导出功能\n    function exportToWord() {\n        if (!editor) return;\n        \n        const content = editor.getContent();\n        const title = $('#title').val() || '新闻文档';\n        \n        // 创建完整的HTML文档\n        const htmlContent = `\n            <!DOCTYPE html>\n            <html>\n            <head>\n                <meta charset=\"utf-8\">\n                <title>${title}</title>\n                <style>\n                    body { font-family: \"Microsoft YaHei\", Arial, sans-serif; line-height: 1.6; margin: 40px; }\n                    h1, h2, h3, h4, h5, h6 { color: #333; margin: 20px 0 10px 0; }\n                    p { margin: 10px 0; }\n                    table { border-collapse: collapse; width: 100%; margin: 10px 0; }\n                    table, th, td { border: 1px solid #ddd; }\n                    th, td { padding: 8px; text-align: left; }\n                    th { background-color: #f2f2f2; }\n                    img { max-width: 100%; height: auto; }\n                </style>\n            </head>\n            <body>\n                <h1>${title}</h1>\n                ${content}\n            </body>\n            </html>\n        `;\n        \n        try {\n            // 转换为Word文档\n            const converted = htmlDocx.asBlob(htmlContent);\n            \n            // 下载文件\n            const link = document.createElement('a');\n            link.href = URL.createObjectURL(converted);\n            link.download = `${title}.docx`;\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            \n            showMessage('Word文档导出成功！', 'success');\n        } catch (error) {\n            console.error('Word导出错误:', error);\n            showMessage('Word文档导出失败：' + error.message, 'error');\n        }\n    }\n\n    // 绑定事件 (保持原有事件 + 新增Word功能)\n    function bindEvents() {\n        // Word导入按钮\n        $('#importWordBtn').on('click', function() {\n            $('#wordFileInput').click();\n        });\n        \n        // Word文件选择\n        $('#wordFileInput').on('change', function() {\n            const file = this.files[0];\n            if (file) {\n                importWordDocument(file);\n            }\n        });\n        \n        // Word导出按钮\n        $('#exportWordBtn').on('click', function() {\n            exportToWord();\n        });\n\n        // 网页预览按钮\n        $('#previewWebBtn').on('click', function() {\n            previewWebFormat();\n        });\n        \n        // 保持原有的图片上传等事件\n        $('#uploadArea').on('click', function() {\n            $('#imageFile').click();\n        });\n\n        $('#imageFile').on('change', function() {\n            const file = this.files[0];\n            if (file) {\n                uploadCoverImage(file);\n            }\n        });\n\n        // 删除图片\n        $('#removeImage').on('click', function() {\n            $('#imagePreview').hide();\n            $('#coverImage').val('');\n            $('#uploadArea').show();\n        });\n\n        // 表单提交\n        $('#newsForm').on('submit', function(e) {\n            e.preventDefault();\n            saveNews();\n        });\n\n        // 保存并发布\n        $('#publishBtn').on('click', function() {\n            $('#status').val('published');\n            saveNews();\n        });\n\n        // 预览\n        $('#previewBtn').on('click', function() {\n            previewNews();\n        });\n    }\n\n    // 加载新闻数据\n    function loadNewsData() {\n        if (!window.newsId) return;\n        \n        $.get(`/api/admin/news/${window.newsId}`)\n            .done(function(response) {\n                if (response.success) {\n                    const news = response.data;\n                    \n                    // 填充表单数据\n                    $('#title').val(news.title);\n                    $('#status').val(news.status);\n                    $('#author').val(news.author);\n                    \n                    // 设置编辑器内容\n                    if (editor) {\n                        editor.setContent(news.content || '');\n                    }\n                    $('#content').val(news.content || '');\n                    \n                    // 设置封面图片\n                    if (news.coverImage) {\n                        $('#coverImage').val(news.coverImage);\n                        $('#previewImg').attr('src', news.coverImage);\n                        $('#imagePreview').show();\n                        $('#uploadArea').hide();\n                    }\n                } else {\n                    showMessage('加载新闻数据失败：' + response.message, 'error');\n                }\n            })\n            .fail(function() {\n                showMessage('加载新闻数据失败', 'error');\n            });\n    }\n\n    // 保存新闻\n    function saveNews() {\n        // 确保从编辑器获取最新内容\n        if (editor) {\n            $('#content').val(editor.getContent());\n        }\n\n        const formData = {\n            title: $('#title').val(),\n            content: $('#content').val(), // 这里保存的是HTML格式，用于网页显示\n            status: $('#status').val(),\n            author: $('#author').val(),\n            coverImage: $('#coverImage').val()\n        };\n\n        // 验证必填字段\n        if (!formData.title.trim()) {\n            showMessage('请输入新闻标题', 'error');\n            return;\n        }\n\n        if (!formData.content.trim()) {\n            showMessage('请输入新闻内容', 'error');\n            return;\n        }\n\n        // 显示保存状态\n        showMessage('正在保存新闻...', 'info');\n\n        const url = isEditing ? `/api/admin/news/${window.newsId}` : '/api/admin/news';\n        const method = isEditing ? 'PUT' : 'POST';\n\n        $.ajax({\n            url: url,\n            type: method,\n            data: formData,\n            success: function(response) {\n                if (response.success) {\n                    showMessage('新闻保存成功！内容已保存为网页格式', 'success');\n\n                    // 如果是新建，跳转到编辑页面\n                    if (!isEditing && response.data && response.data.id) {\n                        setTimeout(function() {\n                            window.location.href = `/admin/news/edit/${response.data.id}`;\n                        }, 1500);\n                    }\n                } else {\n                    showMessage('保存失败：' + response.message, 'error');\n                }\n            },\n            error: function() {\n                showMessage('保存失败，请重试', 'error');\n            }\n        });\n    }\n\n    // 预览新闻\n    function previewNews() {\n        const content = editor ? editor.getContent() : $('#content').val();\n        const title = $('#title').val();\n\n        if (!title || !content) {\n            showMessage('请先填写标题和内容', 'error');\n            return;\n        }\n\n        // 打开预览窗口\n        const previewWindow = window.open('', '_blank', 'width=800,height=600');\n        previewWindow.document.write(`\n            <!DOCTYPE html>\n            <html>\n            <head>\n                <meta charset=\"utf-8\">\n                <title>预览 - ${title}</title>\n                <link rel=\"stylesheet\" href=\"/plugins/bootstrap/css/bootstrap.min.css\">\n                <style>\n                    body { padding: 20px; font-family: \"Microsoft YaHei\", Arial, sans-serif; }\n                    .preview-header { border-bottom: 1px solid #ddd; padding-bottom: 20px; margin-bottom: 20px; }\n                    .preview-content { line-height: 1.8; }\n                </style>\n            </head>\n            <body>\n                <div class=\"container\">\n                    <div class=\"preview-header\">\n                        <h1>${title}</h1>\n                        <p class=\"text-muted\">作者：${$('#author').val()} | 状态：${$('#status option:selected').text()}</p>\n                    </div>\n                    <div class=\"preview-content\">\n                        ${content}\n                    </div>\n                </div>\n            </body>\n            </html>\n        `);\n    }\n\n    // 网页格式预览 (新增功能)\n    function previewWebFormat() {\n        const content = editor ? editor.getContent() : $('#content').val();\n        const title = $('#title').val();\n\n        if (!title || !content) {\n            showMessage('请先填写标题和内容', 'error');\n            return;\n        }\n\n        // 打开网页格式预览窗口\n        const previewWindow = window.open('', '_blank', 'width=1000,height=700');\n        previewWindow.document.write(`\n            <!DOCTYPE html>\n            <html>\n            <head>\n                <meta charset=\"utf-8\">\n                <title>网页格式预览 - ${title}</title>\n                <link rel=\"stylesheet\" href=\"/plugins/bootstrap/css/bootstrap.min.css\">\n                <style>\n                    body {\n                        font-family: \"Microsoft YaHei\", Arial, sans-serif;\n                        background: #f5f5f5;\n                        margin: 0;\n                        padding: 20px;\n                    }\n                    .news-container {\n                        max-width: 800px;\n                        margin: 0 auto;\n                        background: white;\n                        padding: 30px;\n                        border-radius: 8px;\n                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n                    }\n                    .news-header {\n                        text-align: center;\n                        border-bottom: 2px solid #007bff;\n                        padding-bottom: 20px;\n                        margin-bottom: 30px;\n                    }\n                    .news-title {\n                        font-size: 28px;\n                        font-weight: bold;\n                        color: #333;\n                        margin-bottom: 15px;\n                        line-height: 1.4;\n                    }\n                    .news-meta {\n                        color: #666;\n                        font-size: 14px;\n                    }\n                    .news-content {\n                        line-height: 1.8;\n                        font-size: 16px;\n                        color: #444;\n                    }\n                    .news-content h1, .news-content h2, .news-content h3 {\n                        color: #333;\n                        margin: 25px 0 15px 0;\n                    }\n                    .news-content p {\n                        margin: 15px 0;\n                        text-indent: 2em;\n                    }\n                    .news-content table {\n                        width: 100%;\n                        border-collapse: collapse;\n                        margin: 20px 0;\n                    }\n                    .news-content table th,\n                    .news-content table td {\n                        border: 1px solid #ddd;\n                        padding: 12px;\n                        text-align: left;\n                    }\n                    .news-content table th {\n                        background-color: #f8f9fa;\n                        font-weight: bold;\n                    }\n                    .news-content img {\n                        max-width: 100%;\n                        height: auto;\n                        display: block;\n                        margin: 20px auto;\n                        border-radius: 4px;\n                    }\n                    .preview-notice {\n                        position: fixed;\n                        top: 10px;\n                        right: 10px;\n                        background: #007bff;\n                        color: white;\n                        padding: 10px 15px;\n                        border-radius: 4px;\n                        font-size: 12px;\n                        z-index: 1000;\n                    }\n                </style>\n            </head>\n            <body>\n                <div class=\"preview-notice\">\n                    📱 网页格式预览 - 发布后的显示效果\n                </div>\n                <div class=\"news-container\">\n                    <div class=\"news-header\">\n                        <h1 class=\"news-title\">${title}</h1>\n                        <div class=\"news-meta\">\n                            <span>作者：${$('#author').val()}</span> |\n                            <span>发布时间：${new Date().toLocaleDateString()}</span> |\n                            <span>状态：${$('#status option:selected').text()}</span>\n                        </div>\n                    </div>\n                    <div class=\"news-content\">\n                        ${content}\n                    </div>\n                </div>\n                <script>\n                    // 3秒后隐藏预览提示\n                    setTimeout(function() {\n                        document.querySelector('.preview-notice').style.display = 'none';\n                    }, 3000);\n                </script>\n            </body>\n            </html>\n        `);\n\n        showMessage('已打开网页格式预览窗口，这是新闻发布后的显示效果', 'success');\n    }\n\n    // 上传封面图片\n    function uploadCoverImage(file) {\n        const formData = new FormData();\n        formData.append('image', file);\n        \n        $.ajax({\n            url: '/api/upload/image',\n            type: 'POST',\n            data: formData,\n            processData: false,\n            contentType: false,\n            success: function(response) {\n                if (response.success) {\n                    $('#coverImage').val(response.data.url);\n                    $('#previewImg').attr('src', response.data.url);\n                    $('#imagePreview').show();\n                    $('#uploadArea').hide();\n                    showMessage('封面图片上传成功', 'success');\n                } else {\n                    showMessage('图片上传失败：' + response.message, 'error');\n                }\n            },\n            error: function() {\n                showMessage('图片上传失败', 'error');\n            }\n        });\n    }\n\n    // 编辑器内图片上传\n    function uploadImage(file, callback) {\n        const formData = new FormData();\n        formData.append('image', file);\n        \n        $.ajax({\n            url: '/api/upload/image',\n            type: 'POST',\n            data: formData,\n            processData: false,\n            contentType: false,\n            success: function(response) {\n                if (response.success) {\n                    callback(response.data.url);\n                } else {\n                    showMessage('图片上传失败', 'error');\n                }\n            },\n            error: function() {\n                showMessage('图片上传失败', 'error');\n            }\n        });\n    }\n\n    // 显示消息函数\n    function showMessage(message, type) {\n        let alertClass = 'alert-info';\n        if (type === 'success') alertClass = 'alert-success';\n        if (type === 'error') alertClass = 'alert-danger';\n        if (type === 'warning') alertClass = 'alert-warning';\n        \n        const alertHtml = `\n            <div class=\"alert ${alertClass} alert-dismissible\" role=\"alert\">\n                <button type=\"button\" class=\"close\" data-dismiss=\"alert\">\n                    <span>&times;</span>\n                </button>\n                ${message}\n            </div>\n        `;\n        \n        // 移除现有的alert\n        $('.alert').remove();\n        \n        // 添加新的alert\n        $('.panel-body').first().prepend(alertHtml);\n        \n        // 3秒后自动消失\n        setTimeout(function() {\n            $('.alert').fadeOut();\n        }, 3000);\n    }\n\n    // 暴露全局函数 (保持API兼容性)\n    window.loadNewsData = loadNewsData;\n    window.saveNews = saveNews;\n    window.previewNews = previewNews;\n});\n"}