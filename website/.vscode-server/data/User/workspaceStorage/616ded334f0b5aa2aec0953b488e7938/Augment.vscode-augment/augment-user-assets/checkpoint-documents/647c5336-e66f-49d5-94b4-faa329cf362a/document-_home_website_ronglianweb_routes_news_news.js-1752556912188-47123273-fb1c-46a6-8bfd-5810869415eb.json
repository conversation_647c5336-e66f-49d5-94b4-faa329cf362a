{"path": {"rootPath": "/home", "relPath": "website/ronglianweb/routes/news/news.js"}, "originalCode": "var express = require('express');\r\nvar authRestApiProxy = require(\"../../libs/authRestApiProxy\");\r\nvar config = require('../../libs/config');\r\nvar log4js = require('../../libs/log4jsUtil.js');\r\nvar router = express.Router();\r\nvar logger = log4js.getLogger('contactUs');\r\nvar merchantUtil = require(\"../../libs/merchantUtil.js\");\r\nvar imgDomain = config.ImageService.domain;\r\nvar async = require(\"async\");\r\nvar title = \"荣联科技-新闻中心\";\r\nvar newsTopTabsId = \"news\";\r\nvar WechatUserUtils = require('../../libs/WechatUserUtils.js');\r\n// 引入新闻管理系统\r\nconst NewsManager = require('../../libs/newsManager');\r\nconst newsManager = new NewsManager();\r\n// 新闻首页 /news/index\r\nrouter.get('/', function (req, res, next) {\r\n    //var param = req.query.code;\r\n    //logger.info(next);\r\n    //logger.info(\"获取code\" + req.query.response_type);\r\n    merchantUtil.getMerchantAllInfo(req, function (err, menuData) {\r\n        //logger.info(\"加载企业微信用户信息开始\");\r\n        if (err == null && menuData != null) {\r\n            menuData.topTabsId = newsTopTabsId;\r\n            if (menuData.pageTabs) {\r\n                for (var i = 0; i < menuData.pageTabs.length; i++) {\r\n                    if (menuData.pageTabs[i].tabsId === menuData.topTabsId) {\r\n                        menuData.title = menuData.pageTabs[i].title;\r\n                    }\r\n                }\r\n            } else {\r\n                menuData.title = title;\r\n            }\r\n            //WechatUserUtils.getWechatUser(req.query.type, param, res, \"news/newsn\", menuData, req);\r\n\t\t\tres.render('news/newsn', menuData);\r\n        } else {\r\n            res.render('error');\r\n        }\r\n    });\r\n});\r\nrouter.get('/uplus', function (req, res, next) {\r\n    var param = req.query.code;\r\n    merchantUtil.getMerchantAllInfo(req, function (err, menuData) {\r\n        if (err == null && menuData != null) {\r\n            menuData.topTabsId = newsTopTabsId;\r\n            if (menuData.pageTabs) {\r\n                for (var i = 0; i < menuData.pageTabs.length; i++) {\r\n                    if (menuData.pageTabs[i].tabsId === menuData.topTabsId) {\r\n                        menuData.title = menuData.pageTabs[i].title;\r\n                    }\r\n                }\r\n            } else {\r\n                menuData.title = title;\r\n            }\r\n            menuData.infoType = req.query.type;\r\n            if(req.cookies.userid){\r\n                menuData.userid =req.cookies.userid;\r\n                res.render('news/unewsn', menuData);\r\n            }else {\r\n                WechatUserUtils.getWechatUser(req.query.type, param, res, \"news/unewsn\", menuData, req);\r\n            }\r\n\r\n        } else {\r\n            res.render('error');\r\n        }\r\n    });\r\n});\r\nrouter.get('/uDetail', function (req, res, next) {\r\n    logger.info(next);\r\n    logger.info(\"获取code\" + req.query.response_type);\r\n    merchantUtil.getMerchantAllInfo(req, function (err, menuData) {\r\n        if (err == null && menuData != null) {\r\n            menuData.topTabsId = newsTopTabsId;\r\n            if (menuData.pageTabs) {\r\n                for (var i = 0; i < menuData.pageTabs.length; i++) {\r\n                    if (menuData.pageTabs[i].tabsId === menuData.topTabsId) {\r\n                        menuData.title = menuData.pageTabs[i].title;\r\n                    }\r\n                }\r\n            } else {\r\n                menuData.title = title;\r\n            }\r\n            menuData.info_ID = req.query.info_ID;\r\n            menuData.infoType = req.query.infoType;\r\n            menuData.userid = req.cookies.userid;\r\n            res.render('news/udetail', menuData);\r\n        } else {\r\n            res.render('error');\r\n        }\r\n    });\r\n});\r\n// 新闻列表 /news/index/q\r\nrouter.post('/q', function (req, res, next) {\r\n    console.info(next);\r\n    req.body.sortIndex = \"n.create_on desc\";//修改时间降序\r\n    req.body.pageSize = 8;\r\n    req.body.frontFlag = \"1\";\r\n\r\n    try {\r\n        // 使用新闻管理系统获取已发布的新闻\r\n        const pageNo = req.body.pageNo || 1;\r\n        const pageSize = req.body.pageSize || 8;\r\n\r\n        const newsResult = newsManager.getPublishedNews(pageNo, pageSize);\r\n\r\n        console.log(`📄 前台分页查询: 第${pageNo}页，每页${pageSize}条，总计${newsResult.totalCount}条已发布新闻，共${newsResult.totalPages}页`);\r\n\r\n        // 返回符合前台期望格式的数据\r\n        const responseData = {\r\n            records: newsResult.records,\r\n            totalCount: newsResult.totalCount,\r\n            pageNo: newsResult.pageNo,\r\n            pageSize: newsResult.pageSize,\r\n            totalPages: newsResult.totalPages\r\n        };\r\n\r\n        res.send(responseData);\r\n        return;\r\n\r\n    } catch (error) {\r\n        console.error('新闻管理系统查询失败，使用备用数据:', error);\r\n    }\r\n\r\n    // 备用：荣联科技模拟新闻数据（仅在新闻管理系统失败时使用）\r\n    var mockNewsData = {\r\n        records: [\r\n            {\r\n                news_id: '1',\r\n                shortId: 'news001',\r\n                title: '荣联科技集团荣获\"海淀企业\"荣誉称号',\r\n                content: '近日，由海淀区人民政府主办的2025年海淀区经济社会高质量发展大会在京顺利召开，荣联科技集团受邀参会，并凭借在推动区域经济高质量发展中的卓越表现，荣获\"海淀企业\"荣誉称号。',\r\n                picMgid: '/images/news/haidian-enterprise.jpg',\r\n                year: '2025',\r\n                month: '02',\r\n                day: '17'\r\n            },\r\n            {\r\n                news_id: '2',\r\n                shortId: 'news002',\r\n                title: '荣联科技集团荣获\"2024大数据产业年度创新技术突破奖\"',\r\n                content: '荣联科技集团凭借在大数据技术创新方面的突出贡献，荣获\"2024大数据产业年度创新技术突破奖\"，这一荣誉充分体现了公司在大数据领域的技术实力和创新能力。',\r\n                picMgid: '/images/news/bigdata-award.jpg',\r\n                year: '2025',\r\n                month: '01',\r\n                day: '06'\r\n            },\r\n            {\r\n                news_id: '3',\r\n                shortId: 'news003',\r\n                title: '荣联科技集团出席2024数字经济领航者大会',\r\n                content: '荣联科技集团出席2024数字经济领航者大会，共话人工智能数字化创新应用。公司在会上分享了在AI赋能数字化转型方面的最新实践和成果，获得与会专家的高度认可。',\r\n                picMgid: '/images/news/digital-economy-summit.jpg',\r\n                year: '2025',\r\n                month: '01',\r\n                day: '02'\r\n            },\r\n            {\r\n                news_id: '4',\r\n                shortId: 'news004',\r\n                title: '荣联科技：2024年度净利润约2811万元，成功扭亏为盈',\r\n                content: '荣联科技发布2024年度业绩报告，实现营业收入20.22亿元，归属于上市公司股东的净利润2810.69万元，同比成功扭亏为盈，展现了公司强劲的经营韧性和发展潜力。',\r\n                picMgid: '/images/news/financial-report.jpg',\r\n                year: '2024',\r\n                month: '12',\r\n                day: '31'\r\n            },\r\n            {\r\n                news_id: '5',\r\n                shortId: 'news005',\r\n                title: '荣联科技集团荣登\"2024金融信创优秀服务商TOP50\"',\r\n                content: '由中国科学院主管权威媒体《互联网周刊》联合eNet研究院、德本咨询发布的\"2024金融信创优秀服务商TOP50\"榜单中，荣联科技集团凭借在金融信创领域的卓越表现成功入选。',\r\n                picMgid: '/images/news/fintech-top50.jpg',\r\n                year: '2024',\r\n                month: '08',\r\n                day: '15'\r\n            },\r\n            {\r\n                news_id: '6',\r\n                shortId: 'news006',\r\n                title: '天府人工智能大会医工交叉分论坛顺利举行',\r\n                content: '2024年9月29日，由电子科技大学医学院、四川省普通高校新医科建设教学指导委员会承办，荣联科技协办的天府人工智能大会医工交叉分论坛顺利举行，共同探讨AI在医疗健康领域的创新应用。',\r\n                picMgid: '/images/news/ai-medical-forum.jpg',\r\n                year: '2024',\r\n                month: '09',\r\n                day: '29'\r\n            },\r\n            {\r\n                news_id: '7',\r\n                shortId: 'news007',\r\n                title: '荣联科技前三季度净利润约1048万元，业绩稳步提升',\r\n                content: '荣联科技发布2024年前三季度业绩公告，实现净利润约1048万元，业绩稳步提升。公司在数字化服务领域持续深耕，为客户提供更加优质的解决方案和服务。',\r\n                picMgid: '/images/news/q3-report.jpg',\r\n                year: '2024',\r\n                month: '10',\r\n                day: '29'\r\n            },\r\n            {\r\n                news_id: '8',\r\n                shortId: 'news008',\r\n                title: '荣联科技深化数字化转型服务，助力千行百业智能升级',\r\n                content: '荣联科技作为专业的数字化服务提供商，围绕云计算、大数据等新一代信息技术，深入金融、政府公用、运营商、能源制造和生物医疗等优势行业核心应用，以先进的数字化服务赋能客户全面数字化转型升级。',\r\n                picMgid: '/images/news/digital-transformation.jpg',\r\n                year: '2024',\r\n                month: '11',\r\n                day: '15'\r\n            }\r\n        ],\r\n        totalCount: 6,\r\n        pageNo: req.body.pageNo || 1,\r\n        pageSize: req.body.pageSize || 8\r\n    };\r\n\r\n    // 尝试调用真实API，如果失败则使用模拟数据\r\n    authRestApiProxy.post('RetailerService', \"/news/q/l\", req.body, function (err, data) {\r\n        if (data != null && err == null) {\r\n            var tableData = data;\r\n            res.send(tableData);\r\n        } else {\r\n            // 使用模拟数据\r\n            console.log('使用模拟新闻数据');\r\n            res.send(mockNewsData);\r\n        }\r\n    });\r\n});\r\n\r\n//新闻详情  /news/index/qd/newsShortId\r\nrouter.get('/qd/:shortId', function (req, res, next) {\r\n    console.info(next);\r\n    async.waterfall([\r\n        function (callback) {\r\n            //加载头尾\r\n            merchantUtil.getMerchantAllInfo(req, function (menuErr, menuData) {\r\n                callback(menuErr, menuData);\r\n            });\r\n        },\r\n        function (menuData, callback) {\r\n            //查询详情\r\n            var newsRequestVo = {};\r\n            newsRequestVo.shortId = req.params.shortId;\r\n            authRestApiProxy.post('RetailerService', \"/news/n/s/x\", newsRequestVo, function (newsDetailErr, newsDetail) {\r\n                callback(newsDetailErr, menuData, newsDetail);\r\n            });\r\n        },\r\n        //查询最新5条新闻\r\n        function (menuData, newsDetail, callback) {\r\n            authRestApiProxy.post('RetailerService', \"/news/n/l/5\", {}, function (latestNewsErr, latestNews) {\r\n                callback(latestNewsErr, menuData, newsDetail, latestNews);\r\n            });\r\n        },\r\n    ], function (err, menuData, newsDetail, latestNews) {\r\n        if (err) {\r\n            res.render('error');\r\n        } else {\r\n            //kData.keywords,kData.description\r\n            var kData = {};//关键字和描述\r\n            kData.keywords = newsDetail.keywords;\r\n            kData.description = newsDetail.description;\r\n            menuData.kData = kData;\r\n            newsDetail.imgDomain = imgDomain;\r\n            menuData.newsDetail = newsDetail;\r\n            menuData.latestNews = latestNews;\r\n            menuData.title = menuData.newsDetail.title;\r\n            menuData.topTabsId = newsTopTabsId;\r\n            res.render('news/news-detail', menuData);\r\n        }\r\n    });\r\n});\r\n\r\n//新闻详情(老地址，做301跳转)  /news/index/qd?newsId=\r\nrouter.get('/qd', function (req, res, next) {\r\n    console.info(next);\r\n    var newAddr = '/news/index/qd/';\r\n    var reqVo = {\r\n        newsId: req.query.newsId\r\n    };\r\n    authRestApiProxy.post('RetailerService', '/news/n/x', reqVo, function resultData(err, data) {\r\n        if (data && data.shortId !== 'undefined') {\r\n            newAddr += data.shortId;\r\n            res.redirect(301, newAddr);\r\n        } else {\r\n            res.render('error');\r\n        }\r\n    });\r\n});\r\nmodule.exports = router;\r\n", "modifiedCode": "var express = require('express');\r\nvar authRestApiProxy = require(\"../../libs/authRestApiProxy\");\r\nvar config = require('../../libs/config');\r\nvar log4js = require('../../libs/log4jsUtil.js');\r\nvar router = express.Router();\r\nvar logger = log4js.getLogger('contactUs');\r\nvar merchantUtil = require(\"../../libs/merchantUtil.js\");\r\nvar imgDomain = config.ImageService.domain;\r\nvar async = require(\"async\");\r\nvar title = \"荣联科技-新闻中心\";\r\nvar newsTopTabsId = \"news\";\r\nvar WechatUserUtils = require('../../libs/WechatUserUtils.js');\r\n// 引入新闻管理系统\r\nconst NewsManager = require('../../libs/newsManager');\r\nconst newsManager = new NewsManager();\r\n// 新闻首页 /news/index\r\nrouter.get('/', function (req, res, next) {\r\n    //var param = req.query.code;\r\n    //logger.info(next);\r\n    //logger.info(\"获取code\" + req.query.response_type);\r\n    merchantUtil.getMerchantAllInfo(req, function (err, menuData) {\r\n        //logger.info(\"加载企业微信用户信息开始\");\r\n        if (err == null && menuData != null) {\r\n            menuData.topTabsId = newsTopTabsId;\r\n            if (menuData.pageTabs) {\r\n                for (var i = 0; i < menuData.pageTabs.length; i++) {\r\n                    if (menuData.pageTabs[i].tabsId === menuData.topTabsId) {\r\n                        menuData.title = menuData.pageTabs[i].title;\r\n                    }\r\n                }\r\n            } else {\r\n                menuData.title = title;\r\n            }\r\n            //WechatUserUtils.getWechatUser(req.query.type, param, res, \"news/newsn\", menuData, req);\r\n\t\t\tres.render('news/newsn', menuData);\r\n        } else {\r\n            res.render('error');\r\n        }\r\n    });\r\n});\r\nrouter.get('/uplus', function (req, res, next) {\r\n    var param = req.query.code;\r\n    merchantUtil.getMerchantAllInfo(req, function (err, menuData) {\r\n        if (err == null && menuData != null) {\r\n            menuData.topTabsId = newsTopTabsId;\r\n            if (menuData.pageTabs) {\r\n                for (var i = 0; i < menuData.pageTabs.length; i++) {\r\n                    if (menuData.pageTabs[i].tabsId === menuData.topTabsId) {\r\n                        menuData.title = menuData.pageTabs[i].title;\r\n                    }\r\n                }\r\n            } else {\r\n                menuData.title = title;\r\n            }\r\n            menuData.infoType = req.query.type;\r\n            if(req.cookies.userid){\r\n                menuData.userid =req.cookies.userid;\r\n                res.render('news/unewsn', menuData);\r\n            }else {\r\n                WechatUserUtils.getWechatUser(req.query.type, param, res, \"news/unewsn\", menuData, req);\r\n            }\r\n\r\n        } else {\r\n            res.render('error');\r\n        }\r\n    });\r\n});\r\nrouter.get('/uDetail', function (req, res, next) {\r\n    logger.info(next);\r\n    logger.info(\"获取code\" + req.query.response_type);\r\n    merchantUtil.getMerchantAllInfo(req, function (err, menuData) {\r\n        if (err == null && menuData != null) {\r\n            menuData.topTabsId = newsTopTabsId;\r\n            if (menuData.pageTabs) {\r\n                for (var i = 0; i < menuData.pageTabs.length; i++) {\r\n                    if (menuData.pageTabs[i].tabsId === menuData.topTabsId) {\r\n                        menuData.title = menuData.pageTabs[i].title;\r\n                    }\r\n                }\r\n            } else {\r\n                menuData.title = title;\r\n            }\r\n            menuData.info_ID = req.query.info_ID;\r\n            menuData.infoType = req.query.infoType;\r\n            menuData.userid = req.cookies.userid;\r\n            res.render('news/udetail', menuData);\r\n        } else {\r\n            res.render('error');\r\n        }\r\n    });\r\n});\r\n// 新闻列表 /news/index/q\r\nrouter.post('/q', function (req, res, next) {\r\n    console.info(next);\r\n    req.body.sortIndex = \"n.create_on desc\";//修改时间降序\r\n    req.body.pageSize = 8;\r\n    req.body.frontFlag = \"1\";\r\n\r\n    try {\r\n        // 使用新闻管理系统获取已发布的新闻\r\n        const pageNo = req.body.pageNo || 1;\r\n        const pageSize = req.body.pageSize || 8;\r\n\r\n        const newsResult = newsManager.getPublishedNews(pageNo, pageSize);\r\n\r\n        console.log(`📄 前台分页查询: 第${pageNo}页，每页${pageSize}条，总计${newsResult.totalCount}条已发布新闻，共${newsResult.totalPages}页`);\r\n\r\n        // 返回符合前台期望格式的数据\r\n        const responseData = {\r\n            records: newsResult.records,\r\n            totalCount: newsResult.totalCount,\r\n            pageNo: newsResult.pageNo,\r\n            pageSize: newsResult.pageSize,\r\n            totalPages: newsResult.totalPages\r\n        };\r\n\r\n        res.send(responseData);\r\n        return;\r\n\r\n    } catch (error) {\r\n        console.error('新闻管理系统查询失败，使用备用数据:', error);\r\n    }\r\n\r\n    // 备用：荣联科技模拟新闻数据（仅在新闻管理系统失败时使用）\r\n    var mockNewsData = {\r\n        records: [\r\n            {\r\n                news_id: '1',\r\n                shortId: 'news001',\r\n                title: '荣联科技集团荣获\"海淀企业\"荣誉称号',\r\n                content: '近日，由海淀区人民政府主办的2025年海淀区经济社会高质量发展大会在京顺利召开，荣联科技集团受邀参会，并凭借在推动区域经济高质量发展中的卓越表现，荣获\"海淀企业\"荣誉称号。',\r\n                picMgid: '/images/news/haidian-enterprise.jpg',\r\n                year: '2025',\r\n                month: '02',\r\n                day: '17'\r\n            },\r\n            {\r\n                news_id: '2',\r\n                shortId: 'news002',\r\n                title: '荣联科技集团荣获\"2024大数据产业年度创新技术突破奖\"',\r\n                content: '荣联科技集团凭借在大数据技术创新方面的突出贡献，荣获\"2024大数据产业年度创新技术突破奖\"，这一荣誉充分体现了公司在大数据领域的技术实力和创新能力。',\r\n                picMgid: '/images/news/bigdata-award.jpg',\r\n                year: '2025',\r\n                month: '01',\r\n                day: '06'\r\n            },\r\n            {\r\n                news_id: '3',\r\n                shortId: 'news003',\r\n                title: '荣联科技集团出席2024数字经济领航者大会',\r\n                content: '荣联科技集团出席2024数字经济领航者大会，共话人工智能数字化创新应用。公司在会上分享了在AI赋能数字化转型方面的最新实践和成果，获得与会专家的高度认可。',\r\n                picMgid: '/images/news/digital-economy-summit.jpg',\r\n                year: '2025',\r\n                month: '01',\r\n                day: '02'\r\n            },\r\n            {\r\n                news_id: '4',\r\n                shortId: 'news004',\r\n                title: '荣联科技：2024年度净利润约2811万元，成功扭亏为盈',\r\n                content: '荣联科技发布2024年度业绩报告，实现营业收入20.22亿元，归属于上市公司股东的净利润2810.69万元，同比成功扭亏为盈，展现了公司强劲的经营韧性和发展潜力。',\r\n                picMgid: '/images/news/financial-report.jpg',\r\n                year: '2024',\r\n                month: '12',\r\n                day: '31'\r\n            },\r\n            {\r\n                news_id: '5',\r\n                shortId: 'news005',\r\n                title: '荣联科技集团荣登\"2024金融信创优秀服务商TOP50\"',\r\n                content: '由中国科学院主管权威媒体《互联网周刊》联合eNet研究院、德本咨询发布的\"2024金融信创优秀服务商TOP50\"榜单中，荣联科技集团凭借在金融信创领域的卓越表现成功入选。',\r\n                picMgid: '/images/news/fintech-top50.jpg',\r\n                year: '2024',\r\n                month: '08',\r\n                day: '15'\r\n            },\r\n            {\r\n                news_id: '6',\r\n                shortId: 'news006',\r\n                title: '天府人工智能大会医工交叉分论坛顺利举行',\r\n                content: '2024年9月29日，由电子科技大学医学院、四川省普通高校新医科建设教学指导委员会承办，荣联科技协办的天府人工智能大会医工交叉分论坛顺利举行，共同探讨AI在医疗健康领域的创新应用。',\r\n                picMgid: '/images/news/ai-medical-forum.jpg',\r\n                year: '2024',\r\n                month: '09',\r\n                day: '29'\r\n            },\r\n            {\r\n                news_id: '7',\r\n                shortId: 'news007',\r\n                title: '荣联科技前三季度净利润约1048万元，业绩稳步提升',\r\n                content: '荣联科技发布2024年前三季度业绩公告，实现净利润约1048万元，业绩稳步提升。公司在数字化服务领域持续深耕，为客户提供更加优质的解决方案和服务。',\r\n                picMgid: '/images/news/q3-report.jpg',\r\n                year: '2024',\r\n                month: '10',\r\n                day: '29'\r\n            },\r\n            {\r\n                news_id: '8',\r\n                shortId: 'news008',\r\n                title: '荣联科技深化数字化转型服务，助力千行百业智能升级',\r\n                content: '荣联科技作为专业的数字化服务提供商，围绕云计算、大数据等新一代信息技术，深入金融、政府公用、运营商、能源制造和生物医疗等优势行业核心应用，以先进的数字化服务赋能客户全面数字化转型升级。',\r\n                picMgid: '/images/news/digital-transformation.jpg',\r\n                year: '2024',\r\n                month: '11',\r\n                day: '15'\r\n            }\r\n        ],\r\n        totalCount: 6,\r\n        pageNo: req.body.pageNo || 1,\r\n        pageSize: req.body.pageSize || 8\r\n    };\r\n\r\n    // 尝试调用真实API，如果失败则使用模拟数据\r\n    authRestApiProxy.post('RetailerService', \"/news/q/l\", req.body, function (err, data) {\r\n        if (data != null && err == null) {\r\n            var tableData = data;\r\n            res.send(tableData);\r\n        } else {\r\n            // 使用模拟数据\r\n            console.log('使用模拟新闻数据');\r\n            res.send(mockNewsData);\r\n        }\r\n    });\r\n});\r\n\r\n//新闻详情  /news/index/qd/newsShortId\r\nrouter.get('/qd/:shortId', function (req, res, next) {\r\n    console.info(next);\r\n    async.waterfall([\r\n        function (callback) {\r\n            //加载头尾\r\n            merchantUtil.getMerchantAllInfo(req, function (menuErr, menuData) {\r\n                callback(menuErr, menuData);\r\n            });\r\n        },\r\n        function (menuData, callback) {\r\n            //查询详情\r\n            var newsRequestVo = {};\r\n            newsRequestVo.shortId = req.params.shortId;\r\n            authRestApiProxy.post('RetailerService', \"/news/n/s/x\", newsRequestVo, function (newsDetailErr, newsDetail) {\r\n                callback(newsDetailErr, menuData, newsDetail);\r\n            });\r\n        },\r\n        //查询最新5条新闻\r\n        function (menuData, newsDetail, callback) {\r\n            authRestApiProxy.post('RetailerService', \"/news/n/l/5\", {}, function (latestNewsErr, latestNews) {\r\n                callback(latestNewsErr, menuData, newsDetail, latestNews);\r\n            });\r\n        },\r\n    ], function (err, menuData, newsDetail, latestNews) {\r\n        if (err) {\r\n            res.render('error');\r\n        } else {\r\n            //kData.keywords,kData.description\r\n            var kData = {};//关键字和描述\r\n            kData.keywords = newsDetail.keywords;\r\n            kData.description = newsDetail.description;\r\n            menuData.kData = kData;\r\n            newsDetail.imgDomain = imgDomain;\r\n            menuData.newsDetail = newsDetail;\r\n            menuData.latestNews = latestNews;\r\n            menuData.title = menuData.newsDetail.title;\r\n            menuData.topTabsId = newsTopTabsId;\r\n            res.render('news/news-detail', menuData);\r\n        }\r\n    });\r\n});\r\n\r\n//新闻详情(老地址，做301跳转)  /news/index/qd?newsId=\r\nrouter.get('/qd', function (req, res, next) {\r\n    console.info(next);\r\n    var newAddr = '/news/index/qd/';\r\n    var reqVo = {\r\n        newsId: req.query.newsId\r\n    };\r\n    authRestApiProxy.post('RetailerService', '/news/n/x', reqVo, function resultData(err, data) {\r\n        if (data && data.shortId !== 'undefined') {\r\n            newAddr += data.shortId;\r\n            res.redirect(301, newAddr);\r\n        } else {\r\n            res.render('error');\r\n        }\r\n    });\r\n});\r\nmodule.exports = router;\r\n"}