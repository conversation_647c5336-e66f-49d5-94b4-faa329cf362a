{"path": {"rootPath": "/home", "relPath": "website/ronglianweb/run.sh"}, "originalCode": "#!/bin/bash\n# 荣联科技网站运行脚本 (兼容原版本)\n# 新功能请使用 deploy.sh 脚本\n\n# 颜色定义\nGREEN='\\033[0;32m'\nYELLOW='\\033[1;33m'\nRED='\\033[0;31m'\nBLUE='\\033[0;34m'\nNC='\\033[0m'\n\nprint_message() {\n    local color=$1\n    local message=$2\n    echo -e \"${color}${message}${NC}\"\n}\n\nstart_app() {\n    # Install dependencies if node_modules directory doesn't exist\n    if [ ! -d \"node_modules\" ]; then\n        print_message $YELLOW \"📦 Installing dependencies...\"\n        npm install\n    fi\n\n    # 检查是否已有Node.js进程在运行\n    if pgrep -f \"node.*bin/www\" > /dev/null; then\n        print_message $YELLOW \"⚠️  Node.js应用已在运行\"\n        return 0\n    fi\n\n    # Start the application\n    print_message $GREEN \"🚀 Starting the application...\"\n    npm start &\n\n    # 等待应用启动\n    sleep 3\n\n    # 检查应用是否启动成功\n    if pgrep -f \"node.*bin/www\" > /dev/null; then\n        print_message $GREEN \"✅ Node.js应用启动成功 (端口3000)\"\n        print_message $BLUE \"📱 直接访问: http://localhost:3000\"\n        print_message $BLUE \"📱 Nginx代理: https://********* (推荐)\"\n    else\n        print_message $RED \"❌ Node.js应用启动失败\"\n        exit 1\n    fi\n}\n\ncase \"$1\" in\n    start)\n        print_message $GREEN \"🌐 启动荣联科技网站 (兼容模式)...\"\n        print_message $BLUE \"🔧 启动Nginx (HTTPS代理)...\"\n\n        # 测试nginx配置\n        if ! sudo nginx -t > /dev/null 2>&1; then\n            print_message $RED \"❌ Nginx配置测试失败\"\n            exit 1\n        fi\n\n        # 启动nginx\n        sudo systemctl start nginx\n        if systemctl is-active --quiet nginx; then\n            print_message $GREEN \"✅ Nginx启动成功 (HTTPS代理已启用)\"\n        else\n            print_message $RED \"❌ Nginx启动失败\"\n            exit 1\n        fi\n\n        start_app\n\n        print_message $GREEN \"\"\n        print_message $GREEN \"🎉 网站启动完成！\"\n        print_message $BLUE \"📱 主要访问地址:\"\n        echo \"   🔒 主站 (HTTPS): https://*********\"\n        echo \"   📰 新闻页面: https://*********/news/index\"\n        echo \"   🔐 管理登录: https://*********/admin/login\"\n        echo \"   🔧 直接访问: http://localhost:3000\"\n        print_message $YELLOW \"\"\n        print_message $YELLOW \"💡 提示: 使用 './deploy.sh' 获得更多高级功能\"\n        ;;\n    stop)\n        print_message $YELLOW \"🛑 停止荣联科技网站...\"\n\n        # 停止Node.js应用\n        print_message $BLUE \"🛑 停止Node.js应用...\"\n        pkill -f \"node.*bin/www\" 2>/dev/null\n        pkill -f \"npm.*start\" 2>/dev/null\n        print_message $GREEN \"✅ Node.js应用已停止\"\n\n        # 停止nginx\n        print_message $BLUE \"🛑 停止Nginx...\"\n        sudo systemctl stop nginx\n        print_message $GREEN \"✅ Nginx已停止\"\n\n        print_message $GREEN \"🎉 网站已完全停止\"\n        ;;\n    restart)\n        print_message $YELLOW \"🔄 重启荣联科技网站...\"\n\n        # 停止服务\n        print_message $BLUE \"🛑 停止现有服务...\"\n        pkill -f \"node.*bin/www\" 2>/dev/null\n        pkill -f \"npm.*start\" 2>/dev/null\n        sudo systemctl stop nginx\n\n        sleep 2\n\n        # 重新启动\n        print_message $BLUE \"🚀 重新启动服务...\"\n\n        # 测试nginx配置\n        if ! sudo nginx -t > /dev/null 2>&1; then\n            print_message $RED \"❌ Nginx配置测试失败\"\n            exit 1\n        fi\n\n        # 启动nginx\n        sudo systemctl start nginx\n        if systemctl is-active --quiet nginx; then\n            print_message $GREEN \"✅ Nginx重启成功\"\n        else\n            print_message $RED \"❌ Nginx重启失败\"\n            exit 1\n        fi\n\n        start_app\n\n        print_message $GREEN \"🎉 网站重启完成！\"\n        print_message $BLUE \"📱 访问地址: https://*********\"\n        ;;\n    status)\n        print_message $BLUE \"📊 系统状态检查...\"\n        print_message $BLUE \"==================\"\n\n        # 检查Node.js应用\n        if pgrep -f \"node.*bin/www\" > /dev/null; then\n            print_message $GREEN \"✅ Node.js应用 - 运行中 (端口3000)\"\n        else\n            print_message $RED \"❌ Node.js应用 - 未运行\"\n        fi\n\n        # 检查Nginx\n        if systemctl is-active --quiet nginx; then\n            print_message $GREEN \"✅ Nginx服务 - 运行中\"\n        else\n            print_message $RED \"❌ Nginx服务 - 未运行\"\n        fi\n\n        # 检查SSL证书\n        if [ -f \"ssl/private.key\" ] && [ -f \"ssl/certificate.crt\" ]; then\n            print_message $GREEN \"✅ SSL证书 - 已配置\"\n        else\n            print_message $YELLOW \"⚠️  SSL证书 - 未配置\"\n        fi\n\n        # 显示访问地址\n        print_message $BLUE \"\"\n        print_message $BLUE \"🌐 访问地址:\"\n        if systemctl is-active --quiet nginx; then\n            echo \"   🔒 主站 (HTTPS): https://*********\"\n            echo \"   📰 新闻页面: https://*********/news/index\"\n            echo \"   🔐 管理登录: https://*********/admin/login\"\n        fi\n        if pgrep -f \"node.*bin/www\" > /dev/null; then\n            echo \"   🔧 直接访问: http://localhost:3000\"\n        fi\n        ;;\n    *)\n        echo \"Usage: $0 {start|stop|restart|status}\"\n        echo \"\"\n        print_message $GREEN \"💡 荣联科技网站运行脚本 (兼容模式)\"\n        echo \"\"\n        echo \"基础命令:\"\n        echo \"  start    启动网站 (Nginx + Node.js)\"\n        echo \"  stop     停止网站\"\n        echo \"  restart  重启网站\"\n        echo \"  status   查看系统状态\"\n        echo \"\"\n        print_message $YELLOW \"🚀 高级功能:\"\n        echo \"   使用 './deploy.sh' 获得更多功能:\"\n        echo \"   - HTTPS证书管理\"\n        echo \"   - 详细的系统监控\"\n        echo \"   - 新闻管理系统部署\"\n        echo \"   - 运行 './deploy.sh help' 查看详细帮助\"\n        echo \"\"\n        print_message $BLUE \"📱 访问地址:\"\n        echo \"   🔒 主站: https://*********\"\n        echo \"   🔐 管理: https://*********/admin/login\"\n        echo \"\"\n        exit 1\n        ;;\nesac\n\nexit 0\n", "modifiedCode": "#!/bin/bash\n# 荣联科技网站运行脚本 (兼容原版本)\n# 新功能请使用 deploy.sh 脚本\n\n# 颜色定义\nGREEN='\\033[0;32m'\nYELLOW='\\033[1;33m'\nRED='\\033[0;31m'\nBLUE='\\033[0;34m'\nNC='\\033[0m'\n\nprint_message() {\n    local color=$1\n    local message=$2\n    echo -e \"${color}${message}${NC}\"\n}\n\nstart_app() {\n    # Install dependencies if node_modules directory doesn't exist\n    if [ ! -d \"node_modules\" ]; then\n        print_message $YELLOW \"📦 Installing dependencies...\"\n        npm install\n    fi\n\n    # 检查是否已有Node.js进程在运行\n    if pgrep -f \"node.*bin/www\" > /dev/null; then\n        print_message $YELLOW \"⚠️  Node.js应用已在运行\"\n        return 0\n    fi\n\n    # Start the application\n    print_message $GREEN \"🚀 Starting the application...\"\n    npm start &\n\n    # 等待应用启动\n    sleep 3\n\n    # 检查应用是否启动成功\n    if pgrep -f \"node.*bin/www\" > /dev/null; then\n        print_message $GREEN \"✅ Node.js应用启动成功 (端口3000)\"\n        print_message $BLUE \"📱 直接访问: http://localhost:3000\"\n        print_message $BLUE \"📱 Nginx代理: https://********* (推荐)\"\n    else\n        print_message $RED \"❌ Node.js应用启动失败\"\n        exit 1\n    fi\n}\n\ncase \"$1\" in\n    start)\n        print_message $GREEN \"🌐 启动荣联科技网站 (兼容模式)...\"\n        print_message $BLUE \"🔧 启动Nginx (HTTPS代理)...\"\n\n        # 测试nginx配置\n        if ! sudo nginx -t > /dev/null 2>&1; then\n            print_message $RED \"❌ Nginx配置测试失败\"\n            exit 1\n        fi\n\n        # 启动nginx\n        sudo systemctl start nginx\n        if systemctl is-active --quiet nginx; then\n            print_message $GREEN \"✅ Nginx启动成功 (HTTPS代理已启用)\"\n        else\n            print_message $RED \"❌ Nginx启动失败\"\n            exit 1\n        fi\n\n        start_app\n\n        print_message $GREEN \"\"\n        print_message $GREEN \"🎉 网站启动完成！\"\n        print_message $BLUE \"📱 主要访问地址:\"\n        echo \"   🔒 主站 (HTTPS): https://*********\"\n        echo \"   📰 新闻页面: https://*********/news/index\"\n        echo \"   🔐 管理登录: https://*********/admin/login\"\n        echo \"   🔧 直接访问: http://localhost:3000\"\n        print_message $YELLOW \"\"\n        print_message $YELLOW \"💡 提示: 使用 './deploy.sh' 获得更多高级功能\"\n        ;;\n    stop)\n        print_message $YELLOW \"🛑 停止荣联科技网站...\"\n\n        # 停止Node.js应用\n        print_message $BLUE \"🛑 停止Node.js应用...\"\n        pkill -f \"node.*bin/www\" 2>/dev/null\n        pkill -f \"npm.*start\" 2>/dev/null\n        print_message $GREEN \"✅ Node.js应用已停止\"\n\n        # 停止nginx\n        print_message $BLUE \"🛑 停止Nginx...\"\n        sudo systemctl stop nginx\n        print_message $GREEN \"✅ Nginx已停止\"\n\n        print_message $GREEN \"🎉 网站已完全停止\"\n        ;;\n    restart)\n        print_message $YELLOW \"🔄 重启荣联科技网站...\"\n\n        # 停止服务\n        print_message $BLUE \"🛑 停止现有服务...\"\n        pkill -f \"node.*bin/www\" 2>/dev/null\n        pkill -f \"npm.*start\" 2>/dev/null\n        sudo systemctl stop nginx\n\n        sleep 2\n\n        # 重新启动\n        print_message $BLUE \"🚀 重新启动服务...\"\n\n        # 测试nginx配置\n        if ! sudo nginx -t > /dev/null 2>&1; then\n            print_message $RED \"❌ Nginx配置测试失败\"\n            exit 1\n        fi\n\n        # 启动nginx\n        sudo systemctl start nginx\n        if systemctl is-active --quiet nginx; then\n            print_message $GREEN \"✅ Nginx重启成功\"\n        else\n            print_message $RED \"❌ Nginx重启失败\"\n            exit 1\n        fi\n\n        start_app\n\n        print_message $GREEN \"🎉 网站重启完成！\"\n        print_message $BLUE \"📱 访问地址: https://*********\"\n        ;;\n    status)\n        print_message $BLUE \"📊 系统状态检查...\"\n        print_message $BLUE \"==================\"\n\n        # 检查Node.js应用\n        if pgrep -f \"node.*bin/www\" > /dev/null; then\n            print_message $GREEN \"✅ Node.js应用 - 运行中 (端口3000)\"\n        else\n            print_message $RED \"❌ Node.js应用 - 未运行\"\n        fi\n\n        # 检查Nginx\n        if systemctl is-active --quiet nginx; then\n            print_message $GREEN \"✅ Nginx服务 - 运行中\"\n        else\n            print_message $RED \"❌ Nginx服务 - 未运行\"\n        fi\n\n        # 检查SSL证书\n        if [ -f \"ssl/private.key\" ] && [ -f \"ssl/certificate.crt\" ]; then\n            print_message $GREEN \"✅ SSL证书 - 已配置\"\n        else\n            print_message $YELLOW \"⚠️  SSL证书 - 未配置\"\n        fi\n\n        # 显示访问地址\n        print_message $BLUE \"\"\n        print_message $BLUE \"🌐 访问地址:\"\n        if systemctl is-active --quiet nginx; then\n            echo \"   🔒 主站 (HTTPS): https://*********\"\n            echo \"   📰 新闻页面: https://*********/news/index\"\n            echo \"   🔐 管理登录: https://*********/admin/login\"\n        fi\n        if pgrep -f \"node.*bin/www\" > /dev/null; then\n            echo \"   🔧 直接访问: http://localhost:3000\"\n        fi\n        ;;\n    *)\n        echo \"Usage: $0 {start|stop|restart|status}\"\n        echo \"\"\n        print_message $GREEN \"💡 荣联科技网站运行脚本 (兼容模式)\"\n        echo \"\"\n        echo \"基础命令:\"\n        echo \"  start    启动网站 (Nginx + Node.js)\"\n        echo \"  stop     停止网站\"\n        echo \"  restart  重启网站\"\n        echo \"  status   查看系统状态\"\n        echo \"\"\n        print_message $YELLOW \"🚀 高级功能:\"\n        echo \"   使用 './deploy.sh' 获得更多功能:\"\n        echo \"   - HTTPS证书管理\"\n        echo \"   - 详细的系统监控\"\n        echo \"   - 新闻管理系统部署\"\n        echo \"   - 运行 './deploy.sh help' 查看详细帮助\"\n        echo \"\"\n        print_message $BLUE \"📱 访问地址:\"\n        echo \"   🔒 主站: https://*********\"\n        echo \"   🔐 管理: https://*********/admin/login\"\n        echo \"\"\n        exit 1\n        ;;\nesac\n\nexit 0\n"}