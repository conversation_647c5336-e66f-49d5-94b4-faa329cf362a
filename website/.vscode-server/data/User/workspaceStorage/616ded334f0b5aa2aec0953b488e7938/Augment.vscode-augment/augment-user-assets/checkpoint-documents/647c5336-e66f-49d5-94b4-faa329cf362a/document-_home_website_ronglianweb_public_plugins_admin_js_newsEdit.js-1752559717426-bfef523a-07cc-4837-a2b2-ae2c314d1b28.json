{"path": {"rootPath": "/home", "relPath": "website/ronglianweb/public/plugins/admin/js/newsEdit.js"}, "originalCode": "// 新闻编辑JavaScript - Word兼容版本\n$(document).ready(function() {\n    let editor;\n    let isEditing = !!window.newsId;\n\n    // 初始化\n    init();\n\n    function init() {\n        initTinyMCE();\n        bindEvents();\n\n        if (isEditing) {\n            loadNewsData();\n        }\n    }\n\n    // 初始化TinyMCE编辑器 (替换Quill)\n    function initTinyMCE() {\n        tinymce.init({\n            selector: '#editor',\n            height: 300,\n            language: 'zh_CN',\n            plugins: [\n                'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',\n                'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',\n                'insertdatetime', 'media', 'table', 'help', 'wordcount', 'paste'\n            ],\n            toolbar: [\n                'undo redo | blocks | bold italic underline strikethrough | alignleft aligncenter alignright alignjustify',\n                'bullist numlist outdent indent | removeformat | table link image media | code preview fullscreen'\n            ].join(' | '),\n            paste_data_images: true,\n            paste_word_valid_elements: \"b,strong,i,em,h1,h2,h3,h4,h5,h6,p,ol,ul,li,a[href],span,color,font-size,font-family,mark,table,tr,td,th\",\n            paste_retain_style_properties: \"all\",\n            paste_merge_formats: true,\n            automatic_uploads: true,\n            file_picker_types: 'image',\n            file_picker_callback: function(callback, value, meta) {\n                if (meta.filetype === 'image') {\n                    const input = document.createElement('input');\n                    input.setAttribute('type', 'file');\n                    input.setAttribute('accept', 'image/*');\n                    input.onchange = function() {\n                        const file = this.files[0];\n                        uploadImage(file, callback);\n                    };\n                    input.click();\n                }\n            },\n            setup: function(ed) {\n                editor = ed;\n                ed.on('change', function() {\n                    $('#content').val(ed.getContent());\n                });\n            }\n        });\n    }\n\n    // 绑定事件\n    function bindEvents() {\n        // 图片上传\n        $('#uploadArea').on('click', function() {\n            $('#imageInput').click();\n        });\n\n        $('#imageInput').on('change', function() {\n            const file = this.files[0];\n            if (file) {\n                uploadImage(file);\n            }\n        });\n\n        // 拖拽上传\n        $('#uploadArea').on('dragover', function(e) {\n            e.preventDefault();\n            $(this).addClass('dragover');\n        });\n\n        $('#uploadArea').on('dragleave', function(e) {\n            e.preventDefault();\n            $(this).removeClass('dragover');\n        });\n\n        $('#uploadArea').on('drop', function(e) {\n            e.preventDefault();\n            $(this).removeClass('dragover');\n            \n            const files = e.originalEvent.dataTransfer.files;\n            if (files.length > 0) {\n                uploadImage(files[0]);\n            }\n        });\n\n        // 保存按钮\n        $('#saveDraftBtn').on('click', function() {\n            saveNews('draft');\n        });\n\n        $('#savePublishBtn').on('click', function() {\n            saveNews('published');\n        });\n\n        // 删除按钮\n        $('#deleteBtn').on('click', function() {\n            $('#deleteModal').modal('show');\n        });\n\n        $('#confirmDelete').on('click', function() {\n            deleteNews();\n        });\n    }\n\n    // 加载新闻数据（编辑模式）\n    function loadNewsData() {\n        $.get(`/api/admin/news/${window.newsId}`)\n            .done(function(response) {\n                if (response.success) {\n                    const news = response.data;\n                    $('#title').val(news.title);\n                    $('#status').val(news.status);\n                    $('#author').val(news.author);\n                    $('#picMgid').val(news.picMgid);\n                    \n                    // 设置编辑器内容\n                    quill.root.innerHTML = news.content;\n                    $('#content').val(news.content);\n                    \n                    // 显示图片预览\n                    if (news.picMgid) {\n                        showImagePreview(news.picMgid);\n                    }\n                } else {\n                    showMessage('加载新闻数据失败: ' + response.message, 'error');\n                }\n            })\n            .fail(function() {\n                showMessage('加载新闻数据失败', 'error');\n            });\n    }\n\n    // 上传图片\n    function uploadImage(file) {\n        // 验证文件类型\n        if (!file.type.match(/^image\\/(jpeg|jpg|png|gif|webp)$/)) {\n            showMessage('请选择有效的图片文件', 'error');\n            return;\n        }\n\n        // 验证文件大小\n        if (file.size > 5 * 1024 * 1024) {\n            showMessage('图片大小不能超过5MB', 'error');\n            return;\n        }\n\n        const formData = new FormData();\n        formData.append('image', file);\n\n        // 显示上传进度\n        showMessage('正在上传图片...', 'info');\n\n        $.ajax({\n            url: '/api/admin/news/upload',\n            type: 'POST',\n            data: formData,\n            processData: false,\n            contentType: false\n        })\n        .done(function(response) {\n            if (response.success) {\n                $('#picMgid').val(response.data.url);\n                showImagePreview(response.data.url);\n                showMessage('图片上传成功', 'success');\n            } else {\n                showMessage('图片上传失败: ' + response.message, 'error');\n            }\n        })\n        .fail(function() {\n            showMessage('图片上传失败', 'error');\n        });\n    }\n\n    // 显示图片预览\n    function showImagePreview(imageUrl) {\n        const preview = `\n            <img src=\"${imageUrl}\" class=\"image-preview\" alt=\"封面图片\">\n            <br>\n            <button type=\"button\" class=\"btn btn-sm btn-danger\" onclick=\"removeImage()\">\n                <i class=\"glyphicon glyphicon-trash\"></i> 删除图片\n            </button>\n        `;\n        $('#imagePreview').html(preview);\n    }\n\n    // 删除图片\n    window.removeImage = function() {\n        $('#picMgid').val('');\n        $('#imagePreview').empty();\n        showMessage('图片已删除', 'info');\n    };\n\n    // 保存新闻\n    function saveNews(status) {\n        // 验证表单\n        const title = $('#title').val().trim();\n        if (!title) {\n            showMessage('请输入新闻标题', 'error');\n            $('#title').focus();\n            return;\n        }\n\n        const content = quill.root.innerHTML.trim();\n        if (!content || content === '<p><br></p>') {\n            showMessage('请输入新闻内容', 'error');\n            quill.focus();\n            return;\n        }\n\n        // 准备数据\n        const newsData = {\n            title: title,\n            content: content,\n            status: status,\n            author: $('#author').val() || 'admin',\n            picMgid: $('#picMgid').val() || '/images/news/default.jpg'\n        };\n\n        // 显示保存进度\n        const saveBtn = status === 'draft' ? $('#saveDraftBtn') : $('#savePublishBtn');\n        const originalText = saveBtn.text();\n        saveBtn.prop('disabled', true).text('保存中...');\n\n        // 发送请求\n        const url = isEditing ? `/api/admin/news/${window.newsId}` : '/api/admin/news/create';\n        const method = isEditing ? 'PUT' : 'POST';\n\n        $.ajax({\n            url: url,\n            type: method,\n            data: JSON.stringify(newsData),\n            contentType: 'application/json'\n        })\n        .done(function(response) {\n            if (response.success) {\n                showMessage('新闻保存成功', 'success');\n                \n                // 如果是新建，跳转到编辑页面\n                if (!isEditing) {\n                    setTimeout(function() {\n                        window.location.href = `/admin/news/edit/${response.data.news_id}`;\n                    }, 1000);\n                }\n            } else {\n                showMessage('保存失败: ' + response.message, 'error');\n            }\n        })\n        .fail(function() {\n            showMessage('保存失败', 'error');\n        })\n        .always(function() {\n            saveBtn.prop('disabled', false).text(originalText);\n        });\n    }\n\n    // 删除新闻\n    function deleteNews() {\n        if (!isEditing) return;\n\n        $.ajax({\n            url: `/api/admin/news/${window.newsId}`,\n            type: 'DELETE'\n        })\n        .done(function(response) {\n            if (response.success) {\n                showMessage('新闻删除成功', 'success');\n                setTimeout(function() {\n                    window.location.href = '/admin/news';\n                }, 1000);\n            } else {\n                showMessage('删除失败: ' + response.message, 'error');\n            }\n        })\n        .fail(function() {\n            showMessage('删除失败', 'error');\n        });\n    }\n\n    // 显示消息\n    function showMessage(message, type = 'info') {\n        const alertClass = {\n            'success': 'alert-success',\n            'error': 'alert-danger',\n            'info': 'alert-info'\n        }[type] || 'alert-info';\n\n        const alertHtml = `\n            <div class=\"alert ${alertClass} alert-dismissible\" role=\"alert\">\n                <button type=\"button\" class=\"close\" data-dismiss=\"alert\">\n                    <span>&times;</span>\n                </button>\n                ${message}\n            </div>\n        `;\n        \n        // 移除现有的alert\n        $('.alert').remove();\n        // 在页面顶部添加新的alert\n        $('.container-fluid').prepend(alertHtml);\n        \n        // 3秒后自动消失\n        setTimeout(function() {\n            $('.alert').fadeOut();\n        }, 3000);\n    }\n});\n", "modifiedCode": "// 新闻编辑JavaScript - Word兼容版本\n$(document).ready(function() {\n    let editor;\n    let isEditing = !!window.newsId;\n\n    // 初始化\n    init();\n\n    function init() {\n        initTinyMCE();\n        bindEvents();\n\n        if (isEditing) {\n            loadNewsData();\n        }\n    }\n\n    // 初始化TinyMCE编辑器 (替换Quill)\n    function initTinyMCE() {\n        tinymce.init({\n            selector: '#editor',\n            height: 300,\n            language: 'zh_CN',\n            plugins: [\n                'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',\n                'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',\n                'insertdatetime', 'media', 'table', 'help', 'wordcount', 'paste'\n            ],\n            toolbar: [\n                'undo redo | blocks | bold italic underline strikethrough | alignleft aligncenter alignright alignjustify',\n                'bullist numlist outdent indent | removeformat | table link image media | code preview fullscreen'\n            ].join(' | '),\n            paste_data_images: true,\n            paste_word_valid_elements: \"b,strong,i,em,h1,h2,h3,h4,h5,h6,p,ol,ul,li,a[href],span,color,font-size,font-family,mark,table,tr,td,th\",\n            paste_retain_style_properties: \"all\",\n            paste_merge_formats: true,\n            automatic_uploads: true,\n            file_picker_types: 'image',\n            file_picker_callback: function(callback, value, meta) {\n                if (meta.filetype === 'image') {\n                    const input = document.createElement('input');\n                    input.setAttribute('type', 'file');\n                    input.setAttribute('accept', 'image/*');\n                    input.onchange = function() {\n                        const file = this.files[0];\n                        uploadImage(file, callback);\n                    };\n                    input.click();\n                }\n            },\n            setup: function(ed) {\n                editor = ed;\n                ed.on('change', function() {\n                    $('#content').val(ed.getContent());\n                });\n            }\n        });\n    }\n\n    // 绑定事件\n    function bindEvents() {\n        // 图片上传\n        $('#uploadArea').on('click', function() {\n            $('#imageInput').click();\n        });\n\n        $('#imageInput').on('change', function() {\n            const file = this.files[0];\n            if (file) {\n                uploadImage(file);\n            }\n        });\n\n        // 拖拽上传\n        $('#uploadArea').on('dragover', function(e) {\n            e.preventDefault();\n            $(this).addClass('dragover');\n        });\n\n        $('#uploadArea').on('dragleave', function(e) {\n            e.preventDefault();\n            $(this).removeClass('dragover');\n        });\n\n        $('#uploadArea').on('drop', function(e) {\n            e.preventDefault();\n            $(this).removeClass('dragover');\n            \n            const files = e.originalEvent.dataTransfer.files;\n            if (files.length > 0) {\n                uploadImage(files[0]);\n            }\n        });\n\n        // 保存按钮\n        $('#saveDraftBtn').on('click', function() {\n            saveNews('draft');\n        });\n\n        $('#savePublishBtn').on('click', function() {\n            saveNews('published');\n        });\n\n        // 删除按钮\n        $('#deleteBtn').on('click', function() {\n            $('#deleteModal').modal('show');\n        });\n\n        $('#confirmDelete').on('click', function() {\n            deleteNews();\n        });\n    }\n\n    // 加载新闻数据（编辑模式）\n    function loadNewsData() {\n        $.get(`/api/admin/news/${window.newsId}`)\n            .done(function(response) {\n                if (response.success) {\n                    const news = response.data;\n                    $('#title').val(news.title);\n                    $('#status').val(news.status);\n                    $('#author').val(news.author);\n                    $('#picMgid').val(news.picMgid);\n                    \n                    // 设置编辑器内容\n                    quill.root.innerHTML = news.content;\n                    $('#content').val(news.content);\n                    \n                    // 显示图片预览\n                    if (news.picMgid) {\n                        showImagePreview(news.picMgid);\n                    }\n                } else {\n                    showMessage('加载新闻数据失败: ' + response.message, 'error');\n                }\n            })\n            .fail(function() {\n                showMessage('加载新闻数据失败', 'error');\n            });\n    }\n\n    // 上传图片\n    function uploadImage(file) {\n        // 验证文件类型\n        if (!file.type.match(/^image\\/(jpeg|jpg|png|gif|webp)$/)) {\n            showMessage('请选择有效的图片文件', 'error');\n            return;\n        }\n\n        // 验证文件大小\n        if (file.size > 5 * 1024 * 1024) {\n            showMessage('图片大小不能超过5MB', 'error');\n            return;\n        }\n\n        const formData = new FormData();\n        formData.append('image', file);\n\n        // 显示上传进度\n        showMessage('正在上传图片...', 'info');\n\n        $.ajax({\n            url: '/api/admin/news/upload',\n            type: 'POST',\n            data: formData,\n            processData: false,\n            contentType: false\n        })\n        .done(function(response) {\n            if (response.success) {\n                $('#picMgid').val(response.data.url);\n                showImagePreview(response.data.url);\n                showMessage('图片上传成功', 'success');\n            } else {\n                showMessage('图片上传失败: ' + response.message, 'error');\n            }\n        })\n        .fail(function() {\n            showMessage('图片上传失败', 'error');\n        });\n    }\n\n    // 显示图片预览\n    function showImagePreview(imageUrl) {\n        const preview = `\n            <img src=\"${imageUrl}\" class=\"image-preview\" alt=\"封面图片\">\n            <br>\n            <button type=\"button\" class=\"btn btn-sm btn-danger\" onclick=\"removeImage()\">\n                <i class=\"glyphicon glyphicon-trash\"></i> 删除图片\n            </button>\n        `;\n        $('#imagePreview').html(preview);\n    }\n\n    // 删除图片\n    window.removeImage = function() {\n        $('#picMgid').val('');\n        $('#imagePreview').empty();\n        showMessage('图片已删除', 'info');\n    };\n\n    // 保存新闻\n    function saveNews(status) {\n        // 验证表单\n        const title = $('#title').val().trim();\n        if (!title) {\n            showMessage('请输入新闻标题', 'error');\n            $('#title').focus();\n            return;\n        }\n\n        const content = quill.root.innerHTML.trim();\n        if (!content || content === '<p><br></p>') {\n            showMessage('请输入新闻内容', 'error');\n            quill.focus();\n            return;\n        }\n\n        // 准备数据\n        const newsData = {\n            title: title,\n            content: content,\n            status: status,\n            author: $('#author').val() || 'admin',\n            picMgid: $('#picMgid').val() || '/images/news/default.jpg'\n        };\n\n        // 显示保存进度\n        const saveBtn = status === 'draft' ? $('#saveDraftBtn') : $('#savePublishBtn');\n        const originalText = saveBtn.text();\n        saveBtn.prop('disabled', true).text('保存中...');\n\n        // 发送请求\n        const url = isEditing ? `/api/admin/news/${window.newsId}` : '/api/admin/news/create';\n        const method = isEditing ? 'PUT' : 'POST';\n\n        $.ajax({\n            url: url,\n            type: method,\n            data: JSON.stringify(newsData),\n            contentType: 'application/json'\n        })\n        .done(function(response) {\n            if (response.success) {\n                showMessage('新闻保存成功', 'success');\n                \n                // 如果是新建，跳转到编辑页面\n                if (!isEditing) {\n                    setTimeout(function() {\n                        window.location.href = `/admin/news/edit/${response.data.news_id}`;\n                    }, 1000);\n                }\n            } else {\n                showMessage('保存失败: ' + response.message, 'error');\n            }\n        })\n        .fail(function() {\n            showMessage('保存失败', 'error');\n        })\n        .always(function() {\n            saveBtn.prop('disabled', false).text(originalText);\n        });\n    }\n\n    // 删除新闻\n    function deleteNews() {\n        if (!isEditing) return;\n\n        $.ajax({\n            url: `/api/admin/news/${window.newsId}`,\n            type: 'DELETE'\n        })\n        .done(function(response) {\n            if (response.success) {\n                showMessage('新闻删除成功', 'success');\n                setTimeout(function() {\n                    window.location.href = '/admin/news';\n                }, 1000);\n            } else {\n                showMessage('删除失败: ' + response.message, 'error');\n            }\n        })\n        .fail(function() {\n            showMessage('删除失败', 'error');\n        });\n    }\n\n    // 显示消息\n    function showMessage(message, type = 'info') {\n        const alertClass = {\n            'success': 'alert-success',\n            'error': 'alert-danger',\n            'info': 'alert-info'\n        }[type] || 'alert-info';\n\n        const alertHtml = `\n            <div class=\"alert ${alertClass} alert-dismissible\" role=\"alert\">\n                <button type=\"button\" class=\"close\" data-dismiss=\"alert\">\n                    <span>&times;</span>\n                </button>\n                ${message}\n            </div>\n        `;\n        \n        // 移除现有的alert\n        $('.alert').remove();\n        // 在页面顶部添加新的alert\n        $('.container-fluid').prepend(alertHtml);\n        \n        // 3秒后自动消失\n        setTimeout(function() {\n            $('.alert').fadeOut();\n        }, 3000);\n    }\n});\n"}