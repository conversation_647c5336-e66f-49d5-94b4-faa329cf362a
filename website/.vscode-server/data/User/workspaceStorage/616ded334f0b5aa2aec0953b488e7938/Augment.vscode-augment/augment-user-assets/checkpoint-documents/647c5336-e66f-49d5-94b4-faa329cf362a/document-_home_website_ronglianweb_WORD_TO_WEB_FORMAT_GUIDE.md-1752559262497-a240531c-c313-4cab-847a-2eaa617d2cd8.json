{"path": {"rootPath": "/home", "relPath": "website/ronglianweb/WORD_TO_WEB_FORMAT_GUIDE.md"}, "modifiedCode": "# Word兼容编辑器 - 网页格式发布指南\n\n## 🎯 功能说明\n\n荣联科技新闻管理系统的Word兼容编辑器支持**双重格式**：\n1. **编辑阶段**: 支持Word文档导入/导出 (.docx格式)\n2. **发布阶段**: 自动转换为网页HTML格式显示\n\n## 📝 工作流程\n\n### 1. 编辑阶段 - Word兼容\n```\nWord文档 (.docx) → 导入编辑器 → 在线编辑 → 导出Word备份\n```\n\n### 2. 发布阶段 - 网页格式\n```\n编辑器内容 → 保存/发布 → HTML格式 → 网页显示\n```\n\n## 🔧 功能详解\n\n### Word导入功能\n- **支持格式**: .docx文件\n- **保持样式**: 字体、颜色、对齐、列表、表格\n- **图片处理**: 自动处理Word中的图片\n- **用途**: 将现有Word文档导入编辑器进行编辑\n\n### Word导出功能\n- **导出格式**: .docx文件\n- **用途**: 备份编辑内容，离线查看或分享\n- **注意**: 仅用于备份，不影响网页发布\n\n### 网页发布功能\n- **保存格式**: HTML格式\n- **显示位置**: 网站新闻页面\n- **样式保持**: 完整保留编辑器中的格式\n- **响应式**: 自动适配各种设备屏幕\n\n## 🌐 网页格式预览\n\n### 新增预览功能\n- **预览按钮**: \"预览网页效果\"\n- **显示效果**: 模拟新闻发布后的网页显示\n- **样式完整**: 包含标题、作者、时间、内容等\n\n### 预览特性\n- **专业布局**: 新闻网页标准布局\n- **响应式设计**: 适配不同屏幕尺寸\n- **样式优化**: 针对网页阅读优化的样式\n- **实时预览**: 即时查看发布效果\n\n## 📊 格式对比\n\n### Word格式 (.docx)\n| 特性 | 说明 | 用途 |\n|------|------|------|\n| 文件格式 | Microsoft Word文档 | 编辑、备份、分享 |\n| 样式支持 | 完整Word样式 | 保持原始格式 |\n| 兼容性 | Office软件 | 离线编辑 |\n| 文件大小 | 较大 | 完整文档 |\n\n### 网页格式 (HTML)\n| 特性 | 说明 | 用途 |\n|------|------|------|\n| 文件格式 | HTML网页代码 | 网站显示 |\n| 样式支持 | CSS网页样式 | 网页优化显示 |\n| 兼容性 | 所有浏览器 | 在线访问 |\n| 文件大小 | 较小 | 快速加载 |\n\n## 🎨 样式转换\n\n### Word样式 → 网页样式\n```\nWord标题 → HTML <h1>, <h2>, <h3>\nWord段落 → HTML <p>\nWord表格 → HTML <table>\nWord列表 → HTML <ul>, <ol>\nWord图片 → HTML <img>\nWord链接 → HTML <a>\n```\n\n### 样式保持\n- ✅ **字体样式**: 粗体、斜体、下划线\n- ✅ **文字颜色**: 前景色、背景色\n- ✅ **对齐方式**: 左对齐、居中、右对齐\n- ✅ **列表格式**: 有序列表、无序列表\n- ✅ **表格样式**: 边框、背景、对齐\n- ✅ **图片显示**: 尺寸、对齐、边距\n\n## 🚀 使用指南\n\n### 1. 导入Word文档\n1. 点击\"导入Word文档\"按钮\n2. 选择.docx格式文件\n3. 系统自动转换并保持格式\n4. 在编辑器中继续编辑\n\n### 2. 在线编辑\n- 使用丰富的工具栏进行格式化\n- 插入图片、表格、链接等\n- 实时预览编辑效果\n- 支持撤销/重做操作\n\n### 3. 预览网页效果\n1. 点击\"预览网页效果\"按钮\n2. 查看新闻发布后的显示效果\n3. 确认样式和布局是否满意\n4. 返回继续编辑或直接发布\n\n### 4. 保存发布\n1. 点击\"保存\"按钮保存为草稿\n2. 点击\"保存并发布\"直接发布\n3. 内容自动转换为HTML格式\n4. 在网站新闻页面显示\n\n### 5. 导出Word备份\n1. 点击\"导出为Word\"按钮\n2. 下载.docx格式文件\n3. 用于备份或离线查看\n4. 可在Word中进一步编辑\n\n## 📱 网页显示效果\n\n### 新闻页面布局\n```\n┌─────────────────────────────────┐\n│           新闻标题               │\n│    作者 | 发布时间 | 状态        │\n├─────────────────────────────────┤\n│                                 │\n│         新闻内容                │\n│     (HTML格式显示)              │\n│                                 │\n│   • 段落格式                    │\n│   • 标题层级                    │\n│   • 表格数据                    │\n│   • 图片展示                    │\n│                                 │\n└─────────────────────────────────┘\n```\n\n### 响应式特性\n- **桌面端**: 最大宽度800px，居中显示\n- **平板端**: 自适应屏幕宽度\n- **手机端**: 单列布局，优化阅读\n\n## 🔍 技术实现\n\n### 编辑器技术栈\n- **TinyMCE 6.x**: 现代化富文本编辑器\n- **Mammoth.js**: Word文档导入处理\n- **html-docx-js**: HTML转Word文档导出\n\n### 格式转换流程\n```\nWord文档 → Mammoth.js → HTML → TinyMCE编辑器\n编辑器内容 → HTML → 数据库存储 → 网页显示\n编辑器内容 → html-docx-js → Word文档下载\n```\n\n### 数据存储\n- **数据库字段**: content (TEXT类型)\n- **存储格式**: HTML代码\n- **显示方式**: 直接渲染HTML\n\n## ⚠️ 注意事项\n\n### Word导入限制\n- **文件大小**: 建议不超过10MB\n- **复杂格式**: 部分高级格式可能简化\n- **字体支持**: 使用网页安全字体\n- **图片处理**: 自动压缩和优化\n\n### 网页显示限制\n- **样式统一**: 遵循网站整体样式\n- **安全过滤**: 过滤不安全的HTML标签\n- **性能优化**: 图片自动压缩\n- **兼容性**: 确保各浏览器正常显示\n\n### 最佳实践\n1. **先导入Word**: 如有现成文档，先导入再编辑\n2. **及时预览**: 编辑过程中多次预览网页效果\n3. **定期备份**: 使用Word导出功能备份内容\n4. **样式简洁**: 避免过于复杂的格式\n5. **图片优化**: 使用适当尺寸的图片\n\n## 🎉 优势总结\n\n### 编辑优势\n- ✅ **Word兼容**: 无缝导入现有Word文档\n- ✅ **格式保持**: 最大程度保留原始格式\n- ✅ **在线编辑**: 强大的在线编辑功能\n- ✅ **实时预览**: 即时查看发布效果\n\n### 发布优势\n- ✅ **网页优化**: 专门优化的网页显示效果\n- ✅ **响应式**: 完美适配各种设备\n- ✅ **加载快速**: HTML格式快速加载\n- ✅ **SEO友好**: 搜索引擎优化\n\n### 管理优势\n- ✅ **双重备份**: Word文件 + 数据库存储\n- ✅ **版本控制**: 编辑历史和版本管理\n- ✅ **权限控制**: 完整的用户权限系统\n- ✅ **批量管理**: 支持批量操作和管理\n\n## 📞 技术支持\n\n### 常见问题\n1. **Word导入失败**: 检查文件格式和大小\n2. **格式丢失**: 使用网页预览确认效果\n3. **图片不显示**: 确认图片格式和大小\n4. **保存失败**: 检查网络连接和权限\n\n### 联系方式\n- **技术支持**: 系统管理员\n- **使用指导**: 查看在线帮助文档\n- **问题反馈**: 通过系统反馈功能\n\n---\n\n**荣联科技新闻管理系统** - 专业的Word兼容在线编辑解决方案 🚀\n"}