{"path": {"rootPath": "/home", "relPath": "website/ronglianweb/bin/www"}, "originalCode": "#!/usr/bin/env node\r\n\r\n/**\r\n * Module dependencies.\r\n */\r\n\r\nvar app = require('../app');\r\nvar debug = require('debug')('ronglian-website:server');\r\nvar http = require('http');\r\n\r\n/**\r\n * Get port from environment and store in Express.\r\n */\r\n\r\nvar port = normalizePort(process.env.PORT || '8080');\r\napp.set('port', port);\r\n\r\n/**\r\n * Create HTTP server.\r\n */\r\n\r\nvar server = http.createServer(app);\r\n\r\n/**\r\n * Listen on provided port, on all network interfaces.\r\n */\r\n\r\nserver.listen(port);\r\nserver.on('error', onError);\r\nserver.on('listening', onListening);\r\n\r\n/**\r\n * Normalize a port into a number, string, or false.\r\n */\r\n\r\nfunction normalizePort(val) {\r\n  var port = parseInt(val, 10);\r\n\r\n  if (isNaN(port)) {\r\n    // named pipe\r\n    return val;\r\n  }\r\n\r\n  if (port >= 0) {\r\n    // port number\r\n    return port;\r\n  }\r\n\r\n  return false;\r\n}\r\n\r\n/**\r\n * Event listener for HTTP server \"error\" event.\r\n */\r\n\r\nfunction onError(error) {\r\n  if (error.syscall !== 'listen') {\r\n    throw error;\r\n  }\r\n\r\n  var bind = typeof port === 'string'\r\n    ? 'Pipe ' + port\r\n    : 'Port ' + port;\r\n\r\n  // handle specific listen errors with friendly messages\r\n  switch (error.code) {\r\n    case 'EACCES':\r\n      console.error(bind + ' requires elevated privileges');\r\n      process.exit(1);\r\n      break;\r\n    case 'EADDRINUSE':\r\n      console.error(bind + ' is already in use');\r\n      process.exit(1);\r\n      break;\r\n    default:\r\n      throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Event listener for HTTP server \"listening\" event.\r\n */\r\n\r\nfunction onListening() {\r\n  var addr = server.address();\r\n  var bind = typeof addr === 'string'\r\n    ? 'pipe ' + addr\r\n    : 'port ' + addr.port;\r\n  debug('Listening on ' + bind);\r\n}\r\n", "modifiedCode": "#!/usr/bin/env node\r\n\r\n/**\r\n * Module dependencies.\r\n */\r\n\r\nvar app = require('../app');\r\nvar debug = require('debug')('ronglian-website:server');\r\nvar http = require('http');\r\n\r\n/**\r\n * Get port from environment and store in Express.\r\n */\r\n\r\nvar port = normalizePort(process.env.PORT || '3000');\r\napp.set('port', port);\r\n\r\n/**\r\n * Create HTTP server.\r\n */\r\n\r\nvar server = http.createServer(app);\r\n\r\n/**\r\n * Listen on provided port, on all network interfaces.\r\n */\r\n\r\nserver.listen(port, '127.0.0.1');\r\nserver.on('error', onError);\r\nserver.on('listening', onListening);\r\n\r\n/**\r\n * Normalize a port into a number, string, or false.\r\n */\r\n\r\nfunction normalizePort(val) {\r\n  var port = parseInt(val, 10);\r\n\r\n  if (isNaN(port)) {\r\n    // named pipe\r\n    return val;\r\n  }\r\n\r\n  if (port >= 0) {\r\n    // port number\r\n    return port;\r\n  }\r\n\r\n  return false;\r\n}\r\n\r\n/**\r\n * Event listener for HTTP server \"error\" event.\r\n */\r\n\r\nfunction onError(error) {\r\n  if (error.syscall !== 'listen') {\r\n    throw error;\r\n  }\r\n\r\n  var bind = typeof port === 'string'\r\n    ? 'Pipe ' + port\r\n    : 'Port ' + port;\r\n\r\n  // handle specific listen errors with friendly messages\r\n  switch (error.code) {\r\n    case 'EACCES':\r\n      console.error(bind + ' requires elevated privileges');\r\n      process.exit(1);\r\n      break;\r\n    case 'EADDRINUSE':\r\n      console.error(bind + ' is already in use');\r\n      process.exit(1);\r\n      break;\r\n    default:\r\n      throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Event listener for HTTP server \"listening\" event.\r\n */\r\n\r\nfunction onListening() {\r\n  var addr = server.address();\r\n  var bind = typeof addr === 'string'\r\n    ? 'pipe ' + addr\r\n    : 'port ' + addr.port;\r\n  debug('Listening on ' + bind);\r\n}\r\n"}