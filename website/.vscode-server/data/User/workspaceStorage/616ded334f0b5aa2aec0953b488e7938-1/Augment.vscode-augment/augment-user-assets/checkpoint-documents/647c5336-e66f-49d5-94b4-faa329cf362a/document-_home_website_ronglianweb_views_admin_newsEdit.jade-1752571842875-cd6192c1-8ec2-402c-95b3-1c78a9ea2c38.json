{"path": {"rootPath": "/home", "relPath": "website/ronglianweb/views/admin/newsEdit.jade"}, "originalCode": "extends layout\n\nblock css\n  link(rel='stylesheet', href='/plugins/admin/css/admin.css')\n  // TinyMCE样式 (替换Quill样式，保持原有编辑器外观)\n  style.\n    .tox-tinymce {\n      border: 1px solid #ddd !important;\n      border-radius: 4px !important;\n    }\n    .tox .tox-toolbar {\n      background: #f8f9fa !important;\n    }\n    // 保持原有布局，Word功能按钮样式最小化\n    .help-block .btn-xs {\n      padding: 1px 5px;\n      font-size: 11px;\n      line-height: 1.5;\n    }\n  style.\n    .image-preview {\n      max-width: 200px;\n      max-height: 200px;\n      border: 1px solid #ddd;\n      border-radius: 4px;\n      margin-top: 10px;\n    }\n    .upload-area {\n      border: 2px dashed #ccc;\n      border-radius: 4px;\n      padding: 20px;\n      text-align: center;\n      cursor: pointer;\n      transition: border-color 0.3s;\n    }\n    .upload-area:hover {\n      border-color: #667eea;\n    }\n    .upload-area.dragover {\n        border-color: #667eea;\n        background-color: #f8f9fa;\n      }\n      #editor {\n        height: 300px;\n      }\n      .form-actions {\n        background-color: #f8f9fa;\n        padding: 20px;\n        margin: 20px -15px -15px -15px;\n        border-top: 1px solid #ddd;\n      }\n      /* 新增样式：优化上下布局 */\n      .panel {\n        margin-bottom: 20px;\n      }\n      .panel-title {\n        font-size: 16px;\n        font-weight: 600;\n      }\n      .row {\n        margin-bottom: 15px;\n      }\n      /* 确保发布设置和封面图片面板高度一致 */\n      .col-md-6 .panel {\n        height: 100%;\n      }\n      .col-md-6 .panel-body {\n        min-height: 200px;\n      }\n\nblock content\n  .container-fluid\n      .row\n        .col-md-12\n          h1.page-header \n            | ✏️ #{newsId ? '编辑新闻' : '创建新闻'}\n            .pull-right\n              a.btn.btn-default(href=\"/admin/news\") \n                i.glyphicon.glyphicon-arrow-left\n                |  返回列表\n\n      form#newsForm\n        // 基本信息 - 上半部分\n        .row\n          .col-md-12\n            .panel.panel-default\n              .panel-heading\n                h3.panel-title 📝 基本信息\n              .panel-body\n                .form-group\n                  label(for=\"title\") 新闻标题 *\n                  input.form-control#title(type=\"text\", name=\"title\", required, placeholder=\"请输入新闻标题\")\n\n                .form-group\n                  label(for=\"content\") 新闻内容 *\n\n                  // 编辑器容器 (保持原有尺寸)\n                  #editor(style=\"height: 300px;\")\n                  textarea#content(name=\"content\", style=\"display: none;\")\n\n                  // Word功能区 (以小字体提示形式添加，不改变原有布局)\n                  .help-block.small(style=\"margin-top: 5px;\")\n                    span.text-muted 支持Word文档:\n                    a.btn.btn-xs.btn-info#importWordBtn(href=\"javascript:void(0);\", style=\"margin-right: 5px;\", onclick=\"document.getElementById('wordFileInput').click(); return false;\")\n                      i.glyphicon.glyphicon-import\n                      |  导入\n                    a.btn.btn-xs.btn-success#exportWordBtn(href=\"javascript:void(0);\", style=\"margin-right: 5px;\", onclick=\"if(typeof exportToWord === 'function') { exportToWord(); } else if(typeof backupExportToWord === 'function') { backupExportToWord(); } else { alert('导出功能未加载'); } return false;\")\n                      i.glyphicon.glyphicon-export\n                      |  导出\n                    a.btn.btn-xs.btn-default#previewWebBtn(href=\"javascript:void(0);\", onclick=\"if(typeof previewWebFormat === 'function') { previewWebFormat(); } else if(typeof backupPreviewWebFormat === 'function') { backupPreviewWebFormat(); } else { alert('预览功能未加载'); } return false;\")\n                      i.glyphicon.glyphicon-eye-open\n                      |  预览\n                    button.btn.btn-xs.btn-warning#debugBtn(type=\"button\", style=\"margin-left: 10px;\", onclick=\"if(typeof showDebugInfo === 'function') { showDebugInfo(); } else if(typeof backupShowDebugInfo === 'function') { backupShowDebugInfo(); } else { alert('调试功能未加载'); }\")\n                      i.glyphicon.glyphicon-wrench\n                      |  调试\n                    input#wordFileInput(type=\"file\", accept=\".doc,.docx\", style=\"display: none;\", onchange=\"if(this.files[0] && typeof importWordDocument === 'function') { importWordDocument(this.files[0]); } else if(this.files[0] && typeof backupImportWordDocument === 'function') { backupImportWordDocument(this.files[0]); } else if(this.files[0]) { alert('Word导入功能未加载'); }\")\n\n        // 发布设置和封面图片 - 下半部分\n        .row\n          .col-md-6\n            // 发布设置\n            .panel.panel-default\n              .panel-heading\n                h3.panel-title 🚀 发布设置\n              .panel-body\n                .form-group\n                  label(for=\"status\") 状态\n                  select.form-control#status(name=\"status\")\n                    option(value=\"draft\") 草稿\n                    option(value=\"published\") 发布\n                    option(value=\"unpublished\") 下架\n                    option(value=\"archived\") 归档\n\n                .form-group\n                  label(for=\"author\") 作者\n                  input.form-control#author(type=\"text\", name=\"author\", value=\"admin\")\n\n          .col-md-6\n            // 封面图片\n            .panel.panel-default\n              .panel-heading\n                h3.panel-title 🖼️ 封面图片\n              .panel-body\n                .form-group\n                  label 上传图片\n                  .upload-area#uploadArea\n                    i.glyphicon.glyphicon-cloud-upload(style=\"font-size: 2em; color: #ccc;\")\n                    p 点击或拖拽图片到此处上传\n                    p.text-muted 支持 JPG、PNG、GIF 格式，最大 5MB\n                  input#imageInput(type=\"file\", accept=\"image/*\", style=\"display: none;\")\n                  input#picMgid(type=\"hidden\", name=\"picMgid\")\n                  #imagePreview\n\n        .form-actions\n          .row\n            .col-md-12.text-right\n              button.btn.btn-default#saveDraftBtn(type=\"button\", onclick=\"if(typeof saveNews === 'function') { $('#status').val('draft'); saveNews(); } else { alert('保存功能未加载'); }\") 保存草稿\n              button.btn.btn-success#savePublishBtn(type=\"button\", onclick=\"if(typeof saveNews === 'function') { $('#status').val('published'); saveNews(); } else { alert('发布功能未加载'); }\") 保存并发布\n              if newsId\n                button.btn.btn-danger#deleteBtn(type=\"button\", onclick=\"$('#deleteModal').modal('show');\") 删除新闻\n\n    // 确认删除模态框\n    .modal.fade#deleteModal(tabindex=\"-1\", role=\"dialog\")\n      .modal-dialog(role=\"document\")\n        .modal-content\n          .modal-header\n            button.close(type=\"button\", data-dismiss=\"modal\")\n              span &times;\n            h4.modal-title 确认删除\n          .modal-body\n            p 确定要删除这条新闻吗？此操作不可恢复。\n          .modal-footer\n            button.btn.btn-default(type=\"button\", data-dismiss=\"modal\") 取消\n            button.btn.btn-danger#confirmDelete(onclick=\"if(typeof deleteNews === 'function') { deleteNews(); } else { alert('删除功能未加载'); }\") 确认删除\n\nblock scripts\n  // TinyMCE编辑器 (替换Quill)\n  script(src=\"https://cdn.jsdelivr.net/npm/tinymce@6/tinymce.min.js\")\n  // Word处理库\n  script(src=\"https://cdn.jsdelivr.net/npm/mammoth@1.6.0/mammoth.browser.min.js\")\n  script(src=\"https://cdn.jsdelivr.net/npm/html-docx-js@0.3.1/dist/html-docx.js\")\n  // 升级后的编辑器脚本\n  script(src=\"/plugins/admin/js/newsEdit.js\")\n\n  // 内联备份函数 (以防主脚本中的函数不可用)\n  script.\n    // 备份Word导入函数\n    function backupImportWordDocument(file) {\n      if (!file) return;\n\n      console.log('使用备份导入函数');\n      alert('正在导入Word文档，请稍候...');\n\n      const reader = new FileReader();\n      reader.onload = function(e) {\n        const arrayBuffer = e.target.result;\n\n        mammoth.convertToHtml({arrayBuffer: arrayBuffer})\n          .then(function(result) {\n            const html = result.value;\n\n            // 将Word内容插入到编辑器\n            if (window.tinymce && tinymce.activeEditor) {\n              tinymce.activeEditor.setContent(html);\n              $('#content').val(html);\n              alert('Word文档导入成功！');\n            } else {\n              alert('编辑器未初始化，无法导入内容');\n            }\n          })\n          .catch(function(error) {\n            console.error('Word导入错误:', error);\n            alert('Word文档导入失败：' + error.message);\n          });\n      };\n\n      reader.readAsArrayBuffer(file);\n    }\n\n    // 备份Word导出函数\n    function backupExportToWord() {\n      console.log('使用备份导出函数');\n\n      let content = '';\n      let title = $('#title').val() || '新闻文档';\n\n      // 尝试从编辑器获取内容\n      if (window.tinymce && tinymce.activeEditor) {\n        content = tinymce.activeEditor.getContent();\n      } else {\n        content = $('#content').val() || '<p>无法获取编辑器内容</p>';\n      }\n\n      // 创建完整的HTML文档\n      const htmlContent = `\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset=\"utf-8\">\n          <title>${title}</title>\n          <style>\n            body { font-family: \"Microsoft YaHei\", Arial, sans-serif; line-height: 1.6; margin: 40px; }\n            h1, h2, h3, h4, h5, h6 { color: #333; margin: 20px 0 10px 0; }\n            p { margin: 10px 0; }\n            table { border-collapse: collapse; width: 100%; margin: 10px 0; }\n            table, th, td { border: 1px solid #ddd; }\n            th, td { padding: 8px; text-align: left; }\n            th { background-color: #f2f2f2; }\n            img { max-width: 100%; height: auto; }\n          </style>\n        </head>\n        <body>\n          <h1>${title}</h1>\n          ${content}\n        </body>\n        </html>\n      `;\n\n      try {\n        // 转换为Word文档\n        const converted = htmlDocx.asBlob(htmlContent);\n\n        // 下载文件\n        const link = document.createElement('a');\n        link.href = URL.createObjectURL(converted);\n        link.download = `${title}.docx`;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n\n        alert('Word文档导出成功！');\n      } catch (error) {\n        console.error('Word导出错误:', error);\n        alert('Word文档导出失败：' + error.message);\n      }\n    }\n\n    // 备份网页预览函数\n    function backupPreviewWebFormat() {\n      console.log('使用备份预览函数');\n\n      let content = '';\n      let title = $('#title').val() || '预览';\n\n      // 尝试从编辑器获取内容\n      if (window.tinymce && tinymce.activeEditor) {\n        content = tinymce.activeEditor.getContent();\n      } else {\n        content = $('#content').val() || '<p>无法获取编辑器内容</p>';\n      }\n\n      // 打开预览窗口\n      const previewWindow = window.open('', '_blank', 'width=1000,height=700');\n      previewWindow.document.write(`\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset=\"utf-8\">\n          <title>网页格式预览 - ${title}</title>\n          <link rel=\"stylesheet\" href=\"/plugins/bootstrap/css/bootstrap.min.css\">\n          <style>\n            body {\n              font-family: \"Microsoft YaHei\", Arial, sans-serif;\n              line-height: 1.6;\n              color: #333;\n              max-width: 1000px;\n              margin: 0 auto;\n              padding: 20px;\n            }\n            .preview-header {\n              background-color: #f8f9fa;\n              padding: 20px;\n              margin-bottom: 20px;\n              border-bottom: 1px solid #ddd;\n            }\n            .preview-content {\n              padding: 20px;\n            }\n            img {\n              max-width: 100%;\n              height: auto;\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"preview-header\">\n            <h1>${title}</h1>\n            <p class=\"text-muted\">预览模式 - 显示网页发布后的效果</p>\n          </div>\n          <div class=\"preview-content\">\n            ${content}\n          </div>\n        </body>\n        </html>\n      `);\n    }\n\n    // 备份调试信息函数\n    function backupShowDebugInfo() {\n      console.log('使用备份调试函数');\n\n      const debugInfo = {\n        '编辑器状态': (window.tinymce && tinymce.activeEditor) ? '已初始化' : '未初始化',\n        'jQuery版本': (typeof $ !== 'undefined') ? $.fn.jquery : '未加载',\n        'TinyMCE可用': (typeof tinymce !== 'undefined') ? '是' : '否',\n        'Mammoth可用': (typeof mammoth !== 'undefined') ? '是' : '否',\n        'htmlDocx可用': (typeof htmlDocx !== 'undefined') ? '是' : '否',\n        '导入按钮存在': $('#importWordBtn').length > 0 ? '是' : '否',\n        '导出按钮存在': $('#exportWordBtn').length > 0 ? '是' : '否',\n        '预览按钮存在': $('#previewWebBtn').length > 0 ? '是' : '否',\n        '文件输入存在': $('#wordFileInput').length > 0 ? '是' : '否',\n        '当前新闻ID': window.newsId || '无'\n      };\n\n      let debugHtml = '<h4>调试信息</h4><table class=\"table table-bordered table-condensed\"><tbody>';\n      for (const key in debugInfo) {\n        debugHtml += `<tr><td><strong>${key}</strong></td><td>${debugInfo[key]}</td></tr>`;\n      }\n      debugHtml += '</tbody></table>';\n\n      alert('调试信息已输出到控制台');\n      console.table(debugInfo);\n    }\n\n    // 备份保存新闻函数\n    function backupSaveNews() {\n      console.log('使用备份保存函数');\n\n      // 获取编辑器内容\n      let content = '';\n      if (window.tinymce && tinymce.activeEditor) {\n        content = tinymce.activeEditor.getContent();\n      } else {\n        content = $('#content').val() || '';\n      }\n\n      const formData = {\n        title: $('#title').val(),\n        content: content,\n        status: $('#status').val(),\n        author: $('#author').val(),\n        coverImage: $('#coverImage').val()\n      };\n\n      // 验证必填字段\n      if (!formData.title.trim()) {\n        alert('请输入新闻标题');\n        return;\n      }\n\n      if (!formData.content.trim()) {\n        alert('请输入新闻内容');\n        return;\n      }\n\n      // 显示保存状态\n      alert('正在保存新闻...');\n\n      const url = window.newsId ? `/api/admin/news/${window.newsId}` : '/api/admin/news';\n      const method = window.newsId ? 'PUT' : 'POST';\n\n      $.ajax({\n        url: url,\n        type: method,\n        data: formData,\n        success: function(response) {\n          if (response.success) {\n            alert('新闻保存成功！内容已保存为网页格式');\n\n            // 如果是新建，跳转到编辑页面\n            if (!window.newsId && response.data && response.data.id) {\n              window.location.href = `/admin/news/edit/${response.data.id}`;\n            }\n          } else {\n            alert('保存失败：' + (response.message || '未知错误'));\n          }\n        },\n        error: function(xhr, status, error) {\n          alert('保存失败：' + error);\n        }\n      });\n    }\n\n    // 备份删除新闻函数\n    function backupDeleteNews() {\n      console.log('使用备份删除函数');\n\n      if (!window.newsId) {\n        alert('无法删除：新闻ID不存在');\n        return;\n      }\n\n      if (!confirm('确定要删除这条新闻吗？此操作不可恢复。')) {\n        return;\n      }\n\n      $.ajax({\n        url: `/api/admin/news/${window.newsId}`,\n        type: 'DELETE',\n        success: function(response) {\n          if (response.success) {\n            alert('新闻删除成功！');\n            window.location.href = '/admin/news';\n          } else {\n            alert('删除失败：' + (response.message || '未知错误'));\n          }\n        },\n        error: function(xhr, status, error) {\n          alert('删除失败：' + error);\n        }\n      });\n    }\n  script.\n    // 传递新闻ID到前端\n    window.newsId = '#{newsId}';\n    window.currentUser = !{JSON.stringify(user || {})};\n\n    // 初始化页面\n    $(document).ready(function() {\n      console.log('页面DOM加载完成');\n      console.log('newsId:', window.newsId);\n      console.log('currentUser:', window.currentUser);\n\n      // 设置作者为当前用户\n      if (window.currentUser && window.currentUser.name) {\n        $('#author').val(window.currentUser.name);\n      }\n\n      // 数据加载由newsEdit.js中的编辑器初始化完成后处理\n      // 这里不再重复调用loadNewsData\n\n      // 确保Word功能在全局作用域可用\n      setTimeout(function() {\n        console.log('检查Word功能是否在全局作用域可用:');\n        console.log('importWordDocument:', typeof window.importWordDocument);\n        console.log('exportToWord:', typeof window.exportToWord);\n        console.log('previewWebFormat:', typeof window.previewWebFormat);\n        console.log('showDebugInfo:', typeof window.showDebugInfo);\n\n        // 如果函数不在全局作用域，尝试添加\n        if (typeof window.importWordDocument !== 'function' && typeof importWordDocument === 'function') {\n          window.importWordDocument = importWordDocument;\n        }\n        if (typeof window.exportToWord !== 'function' && typeof exportToWord === 'function') {\n          window.exportToWord = exportToWord;\n        }\n        if (typeof window.previewWebFormat !== 'function' && typeof previewWebFormat === 'function') {\n          window.previewWebFormat = previewWebFormat;\n        }\n        if (typeof window.showDebugInfo !== 'function' && typeof showDebugInfo === 'function') {\n          window.showDebugInfo = showDebugInfo;\n        }\n      }, 1000);\n    });\n", "modifiedCode": "extends layout\n\nblock css\n  link(rel='stylesheet', href='/plugins/admin/css/admin.css')\n  // TinyMCE样式 (替换Quill样式，保持原有编辑器外观)\n  style.\n    .tox-tinymce {\n      border: 1px solid #ddd !important;\n      border-radius: 4px !important;\n    }\n    .tox .tox-toolbar {\n      background: #f8f9fa !important;\n    }\n    // 保持原有布局，Word功能按钮样式最小化\n    .help-block .btn-xs {\n      padding: 1px 5px;\n      font-size: 11px;\n      line-height: 1.5;\n    }\n  style.\n    .image-preview {\n      max-width: 200px;\n      max-height: 200px;\n      border: 1px solid #ddd;\n      border-radius: 4px;\n      margin-top: 10px;\n    }\n    .upload-area {\n      border: 2px dashed #ccc;\n      border-radius: 4px;\n      padding: 20px;\n      text-align: center;\n      cursor: pointer;\n      transition: border-color 0.3s;\n    }\n    .upload-area:hover {\n      border-color: #667eea;\n    }\n    .upload-area.dragover {\n        border-color: #667eea;\n        background-color: #f8f9fa;\n      }\n      #editor {\n        height: 300px;\n      }\n      .form-actions {\n        background-color: #f8f9fa;\n        padding: 20px;\n        margin: 20px -15px -15px -15px;\n        border-top: 1px solid #ddd;\n      }\n      /* 新增样式：优化上下布局 */\n      .panel {\n        margin-bottom: 20px;\n      }\n      .panel-title {\n        font-size: 16px;\n        font-weight: 600;\n      }\n      .row {\n        margin-bottom: 15px;\n      }\n      /* 确保发布设置和封面图片面板高度一致 */\n      .col-md-6 .panel {\n        height: 100%;\n      }\n      .col-md-6 .panel-body {\n        min-height: 200px;\n      }\n\nblock content\n  .container-fluid\n      .row\n        .col-md-12\n          h1.page-header \n            | ✏️ #{newsId ? '编辑新闻' : '创建新闻'}\n            .pull-right\n              a.btn.btn-default(href=\"/admin/news\") \n                i.glyphicon.glyphicon-arrow-left\n                |  返回列表\n\n      form#newsForm\n        // 基本信息 - 上半部分\n        .row\n          .col-md-12\n            .panel.panel-default\n              .panel-heading\n                h3.panel-title 📝 基本信息\n              .panel-body\n                .form-group\n                  label(for=\"title\") 新闻标题 *\n                  input.form-control#title(type=\"text\", name=\"title\", required, placeholder=\"请输入新闻标题\")\n\n                .form-group\n                  label(for=\"content\") 新闻内容 *\n\n                  // 编辑器容器 (保持原有尺寸)\n                  #editor(style=\"height: 300px;\")\n                  textarea#content(name=\"content\", style=\"display: none;\")\n\n                  // Word功能区 (以小字体提示形式添加，不改变原有布局)\n                  .help-block.small(style=\"margin-top: 5px;\")\n                    span.text-muted 支持Word文档:\n                    a.btn.btn-xs.btn-info#importWordBtn(href=\"javascript:void(0);\", style=\"margin-right: 5px;\", onclick=\"document.getElementById('wordFileInput').click(); return false;\")\n                      i.glyphicon.glyphicon-import\n                      |  导入\n                    a.btn.btn-xs.btn-success#exportWordBtn(href=\"javascript:void(0);\", style=\"margin-right: 5px;\", onclick=\"if(typeof exportToWord === 'function') { exportToWord(); } else if(typeof backupExportToWord === 'function') { backupExportToWord(); } else { alert('导出功能未加载'); } return false;\")\n                      i.glyphicon.glyphicon-export\n                      |  导出\n                    a.btn.btn-xs.btn-default#previewWebBtn(href=\"javascript:void(0);\", onclick=\"if(typeof previewWebFormat === 'function') { previewWebFormat(); } else if(typeof backupPreviewWebFormat === 'function') { backupPreviewWebFormat(); } else { alert('预览功能未加载'); } return false;\")\n                      i.glyphicon.glyphicon-eye-open\n                      |  预览\n                    button.btn.btn-xs.btn-warning#debugBtn(type=\"button\", style=\"margin-left: 10px;\", onclick=\"if(typeof showDebugInfo === 'function') { showDebugInfo(); } else if(typeof backupShowDebugInfo === 'function') { backupShowDebugInfo(); } else { alert('调试功能未加载'); }\")\n                      i.glyphicon.glyphicon-wrench\n                      |  调试\n                    input#wordFileInput(type=\"file\", accept=\".doc,.docx\", style=\"display: none;\", onchange=\"if(this.files[0] && typeof importWordDocument === 'function') { importWordDocument(this.files[0]); } else if(this.files[0] && typeof backupImportWordDocument === 'function') { backupImportWordDocument(this.files[0]); } else if(this.files[0]) { alert('Word导入功能未加载'); }\")\n\n        // 发布设置和封面图片 - 下半部分\n        .row\n          .col-md-6\n            // 发布设置\n            .panel.panel-default\n              .panel-heading\n                h3.panel-title 🚀 发布设置\n              .panel-body\n                .form-group\n                  label(for=\"status\") 状态\n                  select.form-control#status(name=\"status\")\n                    option(value=\"draft\") 草稿\n                    option(value=\"published\") 发布\n                    option(value=\"unpublished\") 下架\n                    option(value=\"archived\") 归档\n\n                .form-group\n                  label(for=\"author\") 作者\n                  input.form-control#author(type=\"text\", name=\"author\", value=\"admin\")\n\n          .col-md-6\n            // 封面图片\n            .panel.panel-default\n              .panel-heading\n                h3.panel-title 🖼️ 封面图片\n              .panel-body\n                .form-group\n                  label 上传图片\n                  .upload-area#uploadArea\n                    i.glyphicon.glyphicon-cloud-upload(style=\"font-size: 2em; color: #ccc;\")\n                    p 点击或拖拽图片到此处上传\n                    p.text-muted 支持 JPG、PNG、GIF 格式，最大 5MB\n                  input#imageInput(type=\"file\", accept=\"image/*\", style=\"display: none;\")\n                  input#picMgid(type=\"hidden\", name=\"picMgid\")\n                  #imagePreview\n\n        .form-actions\n          .row\n            .col-md-12.text-right\n              button.btn.btn-default#saveDraftBtn(type=\"button\", onclick=\"if(typeof saveNews === 'function') { $('#status').val('draft'); saveNews(); } else { alert('保存功能未加载'); }\") 保存草稿\n              button.btn.btn-success#savePublishBtn(type=\"button\", onclick=\"if(typeof saveNews === 'function') { $('#status').val('published'); saveNews(); } else { alert('发布功能未加载'); }\") 保存并发布\n              if newsId\n                button.btn.btn-danger#deleteBtn(type=\"button\", onclick=\"$('#deleteModal').modal('show');\") 删除新闻\n\n    // 确认删除模态框\n    .modal.fade#deleteModal(tabindex=\"-1\", role=\"dialog\")\n      .modal-dialog(role=\"document\")\n        .modal-content\n          .modal-header\n            button.close(type=\"button\", data-dismiss=\"modal\")\n              span &times;\n            h4.modal-title 确认删除\n          .modal-body\n            p 确定要删除这条新闻吗？此操作不可恢复。\n          .modal-footer\n            button.btn.btn-default(type=\"button\", data-dismiss=\"modal\") 取消\n            button.btn.btn-danger#confirmDelete(onclick=\"if(typeof deleteNews === 'function') { deleteNews(); } else { alert('删除功能未加载'); }\") 确认删除\n\nblock scripts\n  // TinyMCE编辑器 (替换Quill)\n  script(src=\"https://cdn.jsdelivr.net/npm/tinymce@6/tinymce.min.js\")\n  // Word处理库\n  script(src=\"https://cdn.jsdelivr.net/npm/mammoth@1.6.0/mammoth.browser.min.js\")\n  script(src=\"https://cdn.jsdelivr.net/npm/html-docx-js@0.3.1/dist/html-docx.js\")\n  // 升级后的编辑器脚本\n  script(src=\"/plugins/admin/js/newsEdit.js\")\n\n  // 内联备份函数 (以防主脚本中的函数不可用)\n  script.\n    // 备份Word导入函数\n    function backupImportWordDocument(file) {\n      if (!file) return;\n\n      console.log('使用备份导入函数');\n      alert('正在导入Word文档，请稍候...');\n\n      const reader = new FileReader();\n      reader.onload = function(e) {\n        const arrayBuffer = e.target.result;\n\n        mammoth.convertToHtml({arrayBuffer: arrayBuffer})\n          .then(function(result) {\n            const html = result.value;\n\n            // 将Word内容插入到编辑器\n            if (window.tinymce && tinymce.activeEditor) {\n              tinymce.activeEditor.setContent(html);\n              $('#content').val(html);\n              alert('Word文档导入成功！');\n            } else {\n              alert('编辑器未初始化，无法导入内容');\n            }\n          })\n          .catch(function(error) {\n            console.error('Word导入错误:', error);\n            alert('Word文档导入失败：' + error.message);\n          });\n      };\n\n      reader.readAsArrayBuffer(file);\n    }\n\n    // 备份Word导出函数\n    function backupExportToWord() {\n      console.log('使用备份导出函数');\n\n      let content = '';\n      let title = $('#title').val() || '新闻文档';\n\n      // 尝试从编辑器获取内容\n      if (window.tinymce && tinymce.activeEditor) {\n        content = tinymce.activeEditor.getContent();\n      } else {\n        content = $('#content').val() || '<p>无法获取编辑器内容</p>';\n      }\n\n      // 创建完整的HTML文档\n      const htmlContent = `\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset=\"utf-8\">\n          <title>${title}</title>\n          <style>\n            body { font-family: \"Microsoft YaHei\", Arial, sans-serif; line-height: 1.6; margin: 40px; }\n            h1, h2, h3, h4, h5, h6 { color: #333; margin: 20px 0 10px 0; }\n            p { margin: 10px 0; }\n            table { border-collapse: collapse; width: 100%; margin: 10px 0; }\n            table, th, td { border: 1px solid #ddd; }\n            th, td { padding: 8px; text-align: left; }\n            th { background-color: #f2f2f2; }\n            img { max-width: 100%; height: auto; }\n          </style>\n        </head>\n        <body>\n          <h1>${title}</h1>\n          ${content}\n        </body>\n        </html>\n      `;\n\n      try {\n        // 转换为Word文档\n        const converted = htmlDocx.asBlob(htmlContent);\n\n        // 下载文件\n        const link = document.createElement('a');\n        link.href = URL.createObjectURL(converted);\n        link.download = `${title}.docx`;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n\n        alert('Word文档导出成功！');\n      } catch (error) {\n        console.error('Word导出错误:', error);\n        alert('Word文档导出失败：' + error.message);\n      }\n    }\n\n    // 备份网页预览函数\n    function backupPreviewWebFormat() {\n      console.log('使用备份预览函数');\n\n      let content = '';\n      let title = $('#title').val() || '预览';\n\n      // 尝试从编辑器获取内容\n      if (window.tinymce && tinymce.activeEditor) {\n        content = tinymce.activeEditor.getContent();\n      } else {\n        content = $('#content').val() || '<p>无法获取编辑器内容</p>';\n      }\n\n      // 打开预览窗口\n      const previewWindow = window.open('', '_blank', 'width=1000,height=700');\n      previewWindow.document.write(`\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset=\"utf-8\">\n          <title>网页格式预览 - ${title}</title>\n          <link rel=\"stylesheet\" href=\"/plugins/bootstrap/css/bootstrap.min.css\">\n          <style>\n            body {\n              font-family: \"Microsoft YaHei\", Arial, sans-serif;\n              line-height: 1.6;\n              color: #333;\n              max-width: 1000px;\n              margin: 0 auto;\n              padding: 20px;\n            }\n            .preview-header {\n              background-color: #f8f9fa;\n              padding: 20px;\n              margin-bottom: 20px;\n              border-bottom: 1px solid #ddd;\n            }\n            .preview-content {\n              padding: 20px;\n            }\n            img {\n              max-width: 100%;\n              height: auto;\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"preview-header\">\n            <h1>${title}</h1>\n            <p class=\"text-muted\">预览模式 - 显示网页发布后的效果</p>\n          </div>\n          <div class=\"preview-content\">\n            ${content}\n          </div>\n        </body>\n        </html>\n      `);\n    }\n\n    // 备份调试信息函数\n    function backupShowDebugInfo() {\n      console.log('使用备份调试函数');\n\n      const debugInfo = {\n        '编辑器状态': (window.tinymce && tinymce.activeEditor) ? '已初始化' : '未初始化',\n        'jQuery版本': (typeof $ !== 'undefined') ? $.fn.jquery : '未加载',\n        'TinyMCE可用': (typeof tinymce !== 'undefined') ? '是' : '否',\n        'Mammoth可用': (typeof mammoth !== 'undefined') ? '是' : '否',\n        'htmlDocx可用': (typeof htmlDocx !== 'undefined') ? '是' : '否',\n        '导入按钮存在': $('#importWordBtn').length > 0 ? '是' : '否',\n        '导出按钮存在': $('#exportWordBtn').length > 0 ? '是' : '否',\n        '预览按钮存在': $('#previewWebBtn').length > 0 ? '是' : '否',\n        '文件输入存在': $('#wordFileInput').length > 0 ? '是' : '否',\n        '当前新闻ID': window.newsId || '无'\n      };\n\n      let debugHtml = '<h4>调试信息</h4><table class=\"table table-bordered table-condensed\"><tbody>';\n      for (const key in debugInfo) {\n        debugHtml += `<tr><td><strong>${key}</strong></td><td>${debugInfo[key]}</td></tr>`;\n      }\n      debugHtml += '</tbody></table>';\n\n      alert('调试信息已输出到控制台');\n      console.table(debugInfo);\n    }\n\n    // 备份保存新闻函数\n    function backupSaveNews() {\n      console.log('使用备份保存函数');\n\n      // 获取编辑器内容\n      let content = '';\n      if (window.tinymce && tinymce.activeEditor) {\n        content = tinymce.activeEditor.getContent();\n      } else {\n        content = $('#content').val() || '';\n      }\n\n      const formData = {\n        title: $('#title').val(),\n        content: content,\n        status: $('#status').val(),\n        author: $('#author').val(),\n        coverImage: $('#coverImage').val()\n      };\n\n      // 验证必填字段\n      if (!formData.title.trim()) {\n        alert('请输入新闻标题');\n        return;\n      }\n\n      if (!formData.content.trim()) {\n        alert('请输入新闻内容');\n        return;\n      }\n\n      // 显示保存状态\n      alert('正在保存新闻...');\n\n      const url = window.newsId ? `/api/admin/news/${window.newsId}` : '/api/admin/news';\n      const method = window.newsId ? 'PUT' : 'POST';\n\n      $.ajax({\n        url: url,\n        type: method,\n        data: formData,\n        success: function(response) {\n          if (response.success) {\n            alert('新闻保存成功！内容已保存为网页格式');\n\n            // 如果是新建，跳转到编辑页面\n            if (!window.newsId && response.data && response.data.id) {\n              window.location.href = `/admin/news/edit/${response.data.id}`;\n            }\n          } else {\n            alert('保存失败：' + (response.message || '未知错误'));\n          }\n        },\n        error: function(xhr, status, error) {\n          alert('保存失败：' + error);\n        }\n      });\n    }\n\n    // 备份删除新闻函数\n    function backupDeleteNews() {\n      console.log('使用备份删除函数');\n\n      if (!window.newsId) {\n        alert('无法删除：新闻ID不存在');\n        return;\n      }\n\n      if (!confirm('确定要删除这条新闻吗？此操作不可恢复。')) {\n        return;\n      }\n\n      $.ajax({\n        url: `/api/admin/news/${window.newsId}`,\n        type: 'DELETE',\n        success: function(response) {\n          if (response.success) {\n            alert('新闻删除成功！');\n            window.location.href = '/admin/news';\n          } else {\n            alert('删除失败：' + (response.message || '未知错误'));\n          }\n        },\n        error: function(xhr, status, error) {\n          alert('删除失败：' + error);\n        }\n      });\n    }\n  script.\n    // 传递新闻ID到前端\n    window.newsId = '#{newsId}';\n    window.currentUser = !{JSON.stringify(user || {})};\n\n    // 初始化页面\n    $(document).ready(function() {\n      console.log('页面DOM加载完成');\n      console.log('newsId:', window.newsId);\n      console.log('currentUser:', window.currentUser);\n\n      // 设置作者为当前用户\n      if (window.currentUser && window.currentUser.name) {\n        $('#author').val(window.currentUser.name);\n      }\n\n      // 数据加载由newsEdit.js中的编辑器初始化完成后处理\n      // 这里不再重复调用loadNewsData\n\n      // 确保Word功能在全局作用域可用\n      setTimeout(function() {\n        console.log('检查Word功能是否在全局作用域可用:');\n        console.log('importWordDocument:', typeof window.importWordDocument);\n        console.log('exportToWord:', typeof window.exportToWord);\n        console.log('previewWebFormat:', typeof window.previewWebFormat);\n        console.log('showDebugInfo:', typeof window.showDebugInfo);\n\n        // 如果函数不在全局作用域，尝试添加\n        if (typeof window.importWordDocument !== 'function' && typeof importWordDocument === 'function') {\n          window.importWordDocument = importWordDocument;\n        }\n        if (typeof window.exportToWord !== 'function' && typeof exportToWord === 'function') {\n          window.exportToWord = exportToWord;\n        }\n        if (typeof window.previewWebFormat !== 'function' && typeof previewWebFormat === 'function') {\n          window.previewWebFormat = previewWebFormat;\n        }\n        if (typeof window.showDebugInfo !== 'function' && typeof showDebugInfo === 'function') {\n          window.showDebugInfo = showDebugInfo;\n        }\n      }, 1000);\n    });\n"}