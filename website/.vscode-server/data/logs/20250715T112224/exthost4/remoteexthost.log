2025-07-15 11:24:27.313 [info] Extension host with pid 5538 started
2025-07-15 11:24:27.313 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/vscode.lock': Lock acquired.
2025-07-15 11:24:28.080 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: false, activationEvent: '*', root cause: vscode.git
2025-07-15 11:24:28.083 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-07-15 11:24:28.878 [info] ExtensionService#_doActivateExtension vscode.git, startup: false, activationEvent: '*'
2025-07-15 11:24:28.878 [info] ExtensionService#_doActivateExtension vscode.github, startup: false, activationEvent: '*'
2025-07-15 11:24:28.957 [info] ExtensionService#_doActivateExtension vscode.tunnel-forwarding, startup: false, activationEvent: 'onTunnel'
2025-07-15 11:24:29.438 [info] Eager extensions activated
2025-07-15 11:24:29.438 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-07-15 11:24:29.439 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-07-15 11:24:29.439 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-07-15 11:25:28.879 [info] ExtensionService#_doActivateExtension vscode.typescript-language-features, startup: false, activationEvent: 'onLanguage:javascript'
2025-07-15 11:28:32.824 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107411)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:28:32.841 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:28:33.286 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:28:33.287 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at _W.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106566)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:28:48.461 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107411)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:28:48.466 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:28:48.865 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:28:48.866 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at _W.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106566)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:29:53.597 [info] ExtensionService#_doActivateExtension vscode.extension-editing, startup: false, activationEvent: 'onLanguage:markdown'
2025-07-15 11:29:53.598 [info] ExtensionService#_doActivateExtension vscode.markdown-language-features, startup: false, activationEvent: 'onLanguage:markdown'
2025-07-15 11:30:40.034 [info] ExtensionService#_doActivateExtension vscode.npm, startup: false, activationEvent: 'onTerminalQuickFixRequest:ms-vscode.npm-command'
2025-07-15 11:30:45.780 [info] ExtensionService#_doActivateExtension vscode.configuration-editing, startup: false, activationEvent: 'onLanguage:json'
2025-07-15 11:30:45.781 [info] ExtensionService#_doActivateExtension vscode.json-language-features, startup: false, activationEvent: 'onLanguage:json'
2025-07-15 11:31:51.051 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107411)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:31:51.054 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:31:51.413 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:31:51.414 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at _W.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106566)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:32:02.943 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107411)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:32:02.948 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:32:03.126 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:32:03.128 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at _W.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106566)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:32:50.192 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107411)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:32:50.197 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:32:50.424 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:32:50.425 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at _W.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106566)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:36:11.502 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107411)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:36:11.513 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:36:11.826 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:36:11.827 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at _W.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106566)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:36:36.806 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107411)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:36:36.811 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:36:37.086 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:36:37.088 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at _W.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106566)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:39:57.887 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107411)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:39:57.900 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:39:58.218 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:39:58.221 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at _W.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106566)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:40:14.314 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107411)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:40:14.321 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:40:15.187 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:40:15.188 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at _W.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106566)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:40:31.574 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107411)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:40:31.579 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:40:31.795 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:40:31.797 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at _W.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106566)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:40:48.319 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107411)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:40:48.324 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:40:48.581 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:40:48.582 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at _W.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106566)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:41:02.756 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107411)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:41:02.760 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:41:03.004 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 11:41:03.005 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at _W.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106566)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:10:27.805 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107411)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:10:27.813 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:10:28.114 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:10:28.116 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at _W.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106566)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:10:46.914 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107411)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:10:46.918 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:10:47.238 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:10:47.239 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at _W.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106566)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:11:04.054 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107411)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:11:04.057 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:11:04.296 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:11:04.298 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at _W.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106566)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:13:31.961 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107411)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:13:31.975 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:13:32.323 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:13:32.325 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at _W.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106566)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:18:22.996 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107411)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:18:23.001 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:18:23.272 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:18:23.274 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at _W.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106566)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:19:27.677 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107411)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:19:27.682 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:19:28.083 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:19:28.086 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at _W.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106566)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:19:42.825 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107411)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:19:42.830 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:19:43.029 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:19:43.031 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at _W.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106566)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:19:56.202 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107411)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:19:56.206 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:19:56.405 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:19:56.406 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at _W.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106566)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:20:11.066 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107411)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:20:11.071 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:20:11.301 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:20:11.302 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at _W.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106566)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:20:25.434 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107411)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:20:25.439 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:20:25.621 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:20:25.622 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at _W.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106566)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:21:10.510 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107411)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:21:10.516 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:21:11.179 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:21:11.181 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at _W.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106566)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:21:52.180 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107411)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:21:52.187 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:21:52.467 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:21:52.469 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at _W.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106566)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:22:16.367 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107411)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:22:16.373 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:22:16.664 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:22:16.666 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at _W.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106566)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:22:31.363 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptModelChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:107411)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:22:31.368 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:22:31.668 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:22:31.670 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:164:103730)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at _W.$acceptDirtyStateChanged (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106764)
	at _W.$acceptModelSaved (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:106566)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162694)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
