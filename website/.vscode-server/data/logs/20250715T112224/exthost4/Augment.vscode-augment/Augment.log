2025-07-15 11:24:30.216 [info] 'AugmentConfigListener' settings parsed successfully
2025-07-15 11:24:30.216 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"disableFocusOnAugmentPanel":false,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-07-15 11:24:30.216 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","enableNewThreadsList":false,"enableUntruncatedContentStorage":false,"maxLinesTerminalProcessOutput":0,"vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","vscodeMinVersion":"1.96.0","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeSupportToolUseStartMinVersion":"","enableAgentAutoMode":false,"enableAgentSwarmMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5,"historySummaryMinVersion":"","historySummaryMaxChars":0,"historySummaryLowerChars":0,"historySummaryPrompt":"","enableCommitIndexing":false,"maxCommitsToIndex":0,"enableExchangeStorage":false,"conversationHistorySizeThresholdBytes":268435456,"enableToolUseStateStorage":false,"retryChatStreamTimeouts":false,"remoteAgentCurrentWorkspace":false,"enableMemoryRetrieval":false}
2025-07-15 11:24:30.216 [info] 'SidecarAnalytics' Segment analytics initialized for vscode
2025-07-15 11:24:30.243 [info] 'AugmentExtension' Retrieving model config
2025-07-15 11:24:31.608 [info] 'AugmentExtension' Retrieved model config
2025-07-15 11:24:31.608 [info] 'AugmentExtension' Returning model config
2025-07-15 11:24:31.661 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - enableNewThreadsList: false to true
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 49512
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - vscodeDirectApplyMinVersion: "" to "0.499.0"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - enableRules: false to true
  - memoriesTextEditorEnabled: false to true
  - openFileManagerV2Enabled: false to true
  - vscodeTaskListMinVersion: "" to "0.482.0"
  - vscodeSupportToolUseStartMinVersion: "" to "0.485.0"
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
  - historySummaryMaxChars: 0 to 200000
  - historySummaryLowerChars: 0 to 80000
  - historySummaryPrompt: "" to "Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.\nThis summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing with the conversation and supporting any continuing tasks.\n\nYour summary should be structured as follows:\nContext: The context to continue the conversation with. If applicable based on the current task, this should include:\n1. Previous Conversation: High level details about what was discussed throughout the entire conversation with the user. This should be written to allow someone to be able to follow the general overarching conversation flow.\n2. Current Work: Describe in detail what was being worked on prior to this request to summarize the conversation. Pay special attention to the more recent messages in the conversation.\n3. Key Technical Concepts: List all important technical concepts, technologies, coding conventions, and frameworks discussed, which might be relevant for continuing with this work.\n4. Relevant Files and Code: If applicable, enumerate specific files and code sections examined, modified, or created for the task continuation. Pay special attention to the most recent messages and changes.\n5. Problem Solving: Document problems solved thus far and any ongoing troubleshooting efforts.\n6. Pending Tasks and Next Steps: Outline all pending tasks that you have explicitly been asked to work on, as well as list the next steps you will take for all outstanding work, if applicable. Include code snippets where they add clarity. For any next steps, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no information loss in context between tasks.\n\nExample summary structure:\n1. Previous Conversation:\n[Detailed description]\n2. Current Work:\n[Detailed description]\n3. Key Technical Concepts:\n- [Concept 1]\n- [Concept 2]\n- [...]\n4. Relevant Files and Code:\n- [File Name 1]\n    - [Summary of why this file is important]\n    - [Summary of the changes made to this file, if any]\n    - [Important Code Snippet]\n- [File Name 2]\n    - [Important Code Snippet]\n- [...]\n5. Problem Solving:\n[Detailed description]\n6. Pending Tasks and Next Steps:\n- [Task 1 details & next steps]\n- [Task 2 details & next steps]\n- [...]\n\nOutput only the summary of the conversation so far, without any additional commentary or explanation.\n"
  - enableCommitIndexing: false to true
  - maxCommitsToIndex: 0 to 10000
  - retryChatStreamTimeouts: false to true
2025-07-15 11:24:31.661 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home (explicit) at 7/8/2025, 7:02:26 PM
2025-07-15 11:24:31.661 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-07-15 11:24:31.661 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-07-15 11:24:31.661 [info] 'SyncingPermissionTracker' Permission to sync folder /home granted at 7/8/2025, 7:02:26 PM; type = explicit
2025-07-15 11:24:31.661 [info] 'WorkspaceManager' Adding workspace folder home; folderRoot = /home; syncingPermission = granted
2025-07-15 11:24:31.661 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home (explicit) at 7/8/2025, 7:02:26 PM
2025-07-15 11:24:31.711 [info] 'MemoryWebviewMessageHandler' Memory webview message handler initialized
2025-07-15 11:24:31.711 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-07-15 11:24:31.711 [info] 'HotKeyHints' HotKeyHints initialized
2025-07-15 11:24:31.717 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-07-15 11:24:31.757 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-07-15 11:24:31.758 [info] 'ToolsModel' Host: localToolHost (10 tools: 178 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + git-commit-retrieval
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-15 11:24:32.543 [info] 'WorkspaceManager[home]' Start tracking
2025-07-15 11:24:32.549 [info] 'PathMap' Opened source folder /home with id 100
2025-07-15 11:24:32.549 [info] 'OpenFileManager' Opened source folder 100
2025-07-15 11:24:32.553 [info] 'MtimeCache[home]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/2cc974af6afc822c42a4e914df05c697348b80420941f6b27561b2bf688587b7/mtime-cache.json
2025-07-15 11:24:33.016 [info] 'MtimeCache[home]' read 45547 entries from /home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/2cc974af6afc822c42a4e914df05c697348b80420941f6b27561b2bf688587b7/mtime-cache.json
2025-07-15 11:24:34.640 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-15 11:24:34.640 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-15 11:24:41.302 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-07-15 11:24:41.302 [info] 'ToolsModel' Host: localToolHost (10 tools: 178 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + git-commit-retrieval
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-15 11:24:41.302 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-15 11:24:41.302 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-15 11:25:26.236 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-07-15 11:25:28.235 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-07-15 11:25:28.235 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-07-15 11:25:28.235 [info] 'ToolsModel' Saved chat mode: CHAT
2025-07-15 11:25:28.238 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-07-15 11:25:28.358 [info] 'ToolsModel' Saved chat mode: AGENT
2025-07-15 11:25:28.373 [info] 'TaskManager' Setting current root task UUID to a4aff489-84dd-4180-82df-c85ca87a08b3
2025-07-15 11:25:28.373 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-07-15 11:25:28.373 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-07-15 11:25:28.373 [info] 'ToolsModel' Host: localToolHost (10 tools: 178 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + git-commit-retrieval
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-15 11:25:29.423 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1082.634135,"timestamp":"2025-07-15T03:25:29.322Z"}]
2025-07-15 11:25:29.482 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/logs/20250715T112224/exthost4/vscode.typescript-language-features
2025-07-15 11:25:29.547 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-15 11:25:29.547 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-15 11:25:29.968 [info] 'TaskManager' Setting current root task UUID to 7883dc2f-6aec-4542-b2cf-0f5ba7ae1671
2025-07-15 11:25:29.968 [info] 'TaskManager' Setting current root task UUID to 7883dc2f-6aec-4542-b2cf-0f5ba7ae1671
2025-07-15 11:25:29.989 [error] 'GitReferenceMessenger' Failed to locally get remote url: Failed to get remote url, no remote found
2025-07-15 11:27:50.067 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:28:32.552 [info] 'ToolFileUtils' Reading file: website/fix-502.sh
2025-07-15 11:28:32.606 [info] 'ToolFileUtils' Successfully read file: website/fix-502.sh (6961 bytes)
2025-07-15 11:28:33.290 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/History/-3da7cc90
2025-07-15 11:28:34.395 [info] 'ToolFileUtils' Reading file: website/fix-502.sh
2025-07-15 11:28:34.396 [info] 'ToolFileUtils' Successfully read file: website/fix-502.sh (6988 bytes)
2025-07-15 11:28:37.627 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:28:48.312 [info] 'ToolFileUtils' Reading file: website/one-click-fix.sh
2025-07-15 11:28:48.340 [info] 'ToolFileUtils' Successfully read file: website/one-click-fix.sh (7004 bytes)
2025-07-15 11:28:49.005 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/History/483fd9c6
2025-07-15 11:28:49.868 [info] 'ToolFileUtils' Reading file: website/one-click-fix.sh
2025-07-15 11:28:49.868 [info] 'ToolFileUtils' Successfully read file: website/one-click-fix.sh (7031 bytes)
2025-07-15 11:28:53.406 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:29:54.427 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/logs/20250715T112224/exthost4/output_logging_20250715T112427
2025-07-15 11:29:58.486 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:30:01.746 [info] 'ViewTool' Tool called with path: website/fix-502.sh and view_range: [270,280]
2025-07-15 11:30:11.551 [info] 'ViewTool' Tool called with path: website/one-click-fix.sh and view_range: [283,293]
2025-07-15 11:30:29.838 [info] 'ViewTool' Tool called with path: website/ronglianweb/nginx-ronglianweb.conf and view_range: [1,20]
2025-07-15 11:30:29.844 [info] 'ViewTool' Path does not exist: website/ronglianweb/nginx-ronglianweb.conf
2025-07-15 11:30:30.212 [info] 'ToolFileUtils' File not found: website/ronglianweb/nginx-ronglianweb.conf. Similar files found:
/home/<USER>/ronglianweb-backup1/nginx-ronglianweb.conf
2025-07-15 11:30:46.194 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/logs/20250715T112224/exthost4/vscode.json-language-features
2025-07-15 11:30:48.101 [info] 'ViewTool' Tool called with path: website/ronglianweb/nginx-ronglianweb.conf and view_range: [1,20]
2025-07-15 11:30:48.109 [info] 'ViewTool' Path does not exist: website/ronglianweb/nginx-ronglianweb.conf
2025-07-15 11:30:48.404 [info] 'ToolFileUtils' File not found: website/ronglianweb/nginx-ronglianweb.conf. Similar files found:
/home/<USER>/ronglianweb-backup1/nginx-ronglianweb.conf
2025-07-15 11:30:57.267 [info] 'ViewTool' Tool called with path: website/ronglianweb and view_range: undefined
2025-07-15 11:30:57.281 [info] 'ViewTool' Listing directory: website/ronglianweb (depth: 2, showHidden: false)
2025-07-15 11:31:06.799 [info] 'ViewTool' Tool called with path: website/ronglianweb/nginx-ronglian-https.conf and view_range: [1,30]
2025-07-15 11:31:50.652 [info] 'ToolFileUtils' Reading file: website/ronglianweb/nginx-ronglian-https.conf
2025-07-15 11:31:50.672 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/nginx-ronglian-https.conf (6581 bytes)
2025-07-15 11:31:51.581 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/History/-6d356b46
2025-07-15 11:31:52.416 [info] 'ToolFileUtils' Reading file: website/ronglianweb/nginx-ronglian-https.conf
2025-07-15 11:31:52.416 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/nginx-ronglian-https.conf (6591 bytes)
2025-07-15 11:31:55.685 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:32:02.914 [info] 'ToolFileUtils' Reading file: website/ronglianweb/nginx-ronglian-https.conf
2025-07-15 11:32:02.914 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/nginx-ronglian-https.conf (6591 bytes)
2025-07-15 11:32:04.131 [info] 'ToolFileUtils' Reading file: website/ronglianweb/nginx-ronglian-https.conf
2025-07-15 11:32:04.132 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/nginx-ronglian-https.conf (6601 bytes)
2025-07-15 11:32:08.003 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:32:29.812 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1399.620934,"timestamp":"2025-07-15T03:32:29.645Z"}]
2025-07-15 11:32:38.349 [info] 'ViewTool' Tool called with path: website/ronglianweb/ssl and view_range: undefined
2025-07-15 11:32:38.356 [info] 'ViewTool' Listing directory: website/ronglianweb/ssl (depth: 2, showHidden: false)
2025-07-15 11:32:50.163 [info] 'ToolFileUtils' Reading file: website/ronglianweb/nginx-ronglian-https.conf
2025-07-15 11:32:50.164 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/nginx-ronglian-https.conf (6601 bytes)
2025-07-15 11:32:51.515 [info] 'ToolFileUtils' Reading file: website/ronglianweb/nginx-ronglian-https.conf
2025-07-15 11:32:51.515 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/nginx-ronglian-https.conf (6611 bytes)
2025-07-15 11:32:55.174 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:36:11.428 [info] 'ToolFileUtils' Reading file: website/ACCESS_ADDRESSES_UPDATED.md
2025-07-15 11:36:11.450 [info] 'ToolFileUtils' Successfully read file: website/ACCESS_ADDRESSES_UPDATED.md (2401 bytes)
2025-07-15 11:36:12.497 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/History/26c04a16
2025-07-15 11:36:12.829 [info] 'ToolFileUtils' Reading file: website/ACCESS_ADDRESSES_UPDATED.md
2025-07-15 11:36:12.829 [info] 'ToolFileUtils' Successfully read file: website/ACCESS_ADDRESSES_UPDATED.md (2978 bytes)
2025-07-15 11:36:16.463 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:37:59.524 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1277.598127,"timestamp":"2025-07-15T03:37:59.491Z"}]
2025-07-15 11:38:29.555 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1260.217598,"timestamp":"2025-07-15T03:38:29.483Z"}]
2025-07-15 11:39:40.748 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:39:46.520 [info] 'ViewTool' Tool called with path: website/ronglianweb/run.sh and view_range: undefined
2025-07-15 11:39:57.822 [info] 'ToolFileUtils' Reading file: website/ronglianweb/run.sh
2025-07-15 11:39:57.822 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/run.sh (5280 bytes)
2025-07-15 11:39:58.405 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/History/-522bc021
2025-07-15 11:39:59.225 [info] 'ToolFileUtils' Reading file: website/ronglianweb/run.sh
2025-07-15 11:39:59.225 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/run.sh (5280 bytes)
2025-07-15 11:40:02.834 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:40:14.284 [info] 'ToolFileUtils' Reading file: website/ronglianweb/run.sh
2025-07-15 11:40:14.285 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/run.sh (5280 bytes)
2025-07-15 11:40:14.324 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 256f2fe22c2df276a3f284a1c0dca2c61a574e33e69435528e49fc873328cfd8: deleted
2025-07-15 11:40:16.302 [info] 'ToolFileUtils' Reading file: website/ronglianweb/run.sh
2025-07-15 11:40:16.302 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/run.sh (5280 bytes)
2025-07-15 11:40:19.299 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:40:31.536 [info] 'ToolFileUtils' Reading file: website/ronglianweb/run.sh
2025-07-15 11:40:31.537 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/run.sh (5280 bytes)
2025-07-15 11:40:31.583 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 0060ee2bf3737df8668726372ddb15f753c67186c77a8fda41937bb2a5eb85a9: deleted
2025-07-15 11:40:32.800 [info] 'ToolFileUtils' Reading file: website/ronglianweb/run.sh
2025-07-15 11:40:32.800 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/run.sh (5280 bytes)
2025-07-15 11:40:36.641 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:40:48.299 [info] 'ToolFileUtils' Reading file: website/ronglianweb/run.sh
2025-07-15 11:40:48.299 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/run.sh (5280 bytes)
2025-07-15 11:40:48.328 [error] 'FuzzySymbolSearcher' Failed to read file tokens for a49ca7b599d14aac1491edcd7f4a8e488e9220b8ce397af2e1645d92a2d770b8: deleted
2025-07-15 11:40:49.583 [info] 'ToolFileUtils' Reading file: website/ronglianweb/run.sh
2025-07-15 11:40:49.583 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/run.sh (5280 bytes)
2025-07-15 11:40:53.311 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:41:02.732 [info] 'ToolFileUtils' Reading file: website/ronglianweb/run.sh
2025-07-15 11:41:02.732 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/run.sh (5280 bytes)
2025-07-15 11:41:02.763 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 96e6d5e1288998ee9158a919b51141d2e73a52f5f2c40385d2bd76b959f3b542: deleted
2025-07-15 11:41:04.007 [info] 'ToolFileUtils' Reading file: website/ronglianweb/run.sh
2025-07-15 11:41:04.008 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/run.sh (5280 bytes)
2025-07-15 11:41:07.745 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:42:16.508 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:51:09.210 [info] 'WorkspaceManager[home]' Tracking enabled
2025-07-15 11:51:09.210 [info] 'WorkspaceManager[home]' Path metrics:
  - directories emitted: 20800
  - files emitted: 144448
  - other paths emitted: 1230
  - total paths emitted: 166478
  - timing stats:
    - readDir: 458 ms
    - filter: 3811 ms
    - yield: 678 ms
    - total: 5507 ms
2025-07-15 11:51:09.210 [info] 'WorkspaceManager[home]' File metrics:
  - paths accepted: 96690
  - paths not accessible: 0
  - not plain files: 0
  - large files: 2189
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 37281
  - mtime cache misses: 59409
  - probe batches: 558
  - blob names probed: 246255
  - files read: 157737
  - blobs uploaded: 50934
  - timing stats:
    - ingestPath: 336 ms
    - probe: 974413 ms
    - stat: 3300 ms
    - read: 161102 ms
    - upload: 1511756 ms
2025-07-15 11:51:09.210 [info] 'WorkspaceManager[home]' Startup metrics:
  - create SourceFolder: 9 ms
  - read MtimeCache: 464 ms
  - pre-populate PathMap: 1139 ms
  - create PathFilter: 1495 ms
  - create PathNotifier: 0 ms
  - enumerate paths: 5510 ms
  - purge stale PathMap entries: 36 ms
  - enumerate: 1 ms
  - await DiskFileManager quiesced: 1587881 ms
  - enable persist: 130 ms
  - total: 1596665 ms
2025-07-15 11:51:09.210 [info] 'WorkspaceManager' Workspace startup complete in 1597564 ms
2025-07-15 11:54:30.186 [info] 'AugmentExtension' Retrieving model config
2025-07-15 11:54:31.346 [info] 'AugmentExtension' Retrieved model config
2025-07-15 11:54:31.346 [info] 'AugmentExtension' Returning model config
2025-07-15 12:09:29.274 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1016.679644,"timestamp":"2025-07-15T04:09:29.225Z"}]
2025-07-15 12:18:21.436 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 12:19:04.049 [info] 'ViewTool' Tool called with path: website/ronglianweb/views/admin/newsEdit_new.jade and view_range: [1,50]
2025-07-15 12:20:12.371 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 12:24:30.187 [info] 'AugmentExtension' Retrieving model config
2025-07-15 12:24:31.205 [info] 'AugmentExtension' Retrieved model config
2025-07-15 12:24:31.205 [info] 'AugmentExtension' Returning model config
2025-07-15 12:54:30.186 [info] 'AugmentExtension' Retrieving model config
2025-07-15 12:54:31.440 [info] 'AugmentExtension' Retrieved model config
2025-07-15 12:54:31.440 [info] 'AugmentExtension' Returning model config
2025-07-15 12:54:59.612 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1372.518474,"timestamp":"2025-07-15T04:54:59.555Z"}]
2025-07-15 12:55:29.617 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1352.178225,"timestamp":"2025-07-15T04:55:29.534Z"}]
2025-07-15 12:55:59.587 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1363.287817,"timestamp":"2025-07-15T04:55:59.545Z"}]
2025-07-15 12:56:29.877 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1596.298205,"timestamp":"2025-07-15T04:56:29.778Z"}]
2025-07-15 13:06:34.181 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 13:07:36.636 [error] 'ChatApp' Chat stream failed: Error: Cancelled
Error: Cancelled
	at Efe.cancel (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.502.1/out/extension.js:744:1081)
	at e.cancelChatStream (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.502.1/out/extension.js:744:33573)
	at qk.onUserCancel (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.502.1/out/extension.js:1885:15045)
	at /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.502.1/out/extension.js:1885:4103
	at Gh.value (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.502.1/out/extension.js:546:4163)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at PW.$onMessage (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:91544)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162827)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:07:37.212 [error] 'AugmentExtension' API request 2d29d0e7-38d5-4fb9-9aa9-6d9a81d54c57 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-15 13:07:37.212 [error] 'AugmentExtension' Dropping error report "chat_stream_failed" due to error: This operation was aborted
2025-07-15 13:07:59.828 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1530.760425,"timestamp":"2025-07-15T05:07:59.782Z"}]
