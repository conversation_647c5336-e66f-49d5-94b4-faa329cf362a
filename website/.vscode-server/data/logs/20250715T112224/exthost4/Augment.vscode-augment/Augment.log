2025-07-15 11:24:30.216 [info] 'AugmentConfigListener' settings parsed successfully
2025-07-15 11:24:30.216 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"disableFocusOnAugmentPanel":false,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-07-15 11:24:30.216 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","enableNewThreadsList":false,"enableUntruncatedContentStorage":false,"maxLinesTerminalProcessOutput":0,"vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","vscodeMinVersion":"1.96.0","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeSupportToolUseStartMinVersion":"","enableAgentAutoMode":false,"enableAgentSwarmMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5,"historySummaryMinVersion":"","historySummaryMaxChars":0,"historySummaryLowerChars":0,"historySummaryPrompt":"","enableCommitIndexing":false,"maxCommitsToIndex":0,"enableExchangeStorage":false,"conversationHistorySizeThresholdBytes":268435456,"enableToolUseStateStorage":false,"retryChatStreamTimeouts":false,"remoteAgentCurrentWorkspace":false,"enableMemoryRetrieval":false}
2025-07-15 11:24:30.216 [info] 'SidecarAnalytics' Segment analytics initialized for vscode
2025-07-15 11:24:30.243 [info] 'AugmentExtension' Retrieving model config
2025-07-15 11:24:31.608 [info] 'AugmentExtension' Retrieved model config
2025-07-15 11:24:31.608 [info] 'AugmentExtension' Returning model config
2025-07-15 11:24:31.661 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - enableNewThreadsList: false to true
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 49512
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - vscodeDirectApplyMinVersion: "" to "0.499.0"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - enableRules: false to true
  - memoriesTextEditorEnabled: false to true
  - openFileManagerV2Enabled: false to true
  - vscodeTaskListMinVersion: "" to "0.482.0"
  - vscodeSupportToolUseStartMinVersion: "" to "0.485.0"
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
  - historySummaryMaxChars: 0 to 200000
  - historySummaryLowerChars: 0 to 80000
  - historySummaryPrompt: "" to "Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.\nThis summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing with the conversation and supporting any continuing tasks.\n\nYour summary should be structured as follows:\nContext: The context to continue the conversation with. If applicable based on the current task, this should include:\n1. Previous Conversation: High level details about what was discussed throughout the entire conversation with the user. This should be written to allow someone to be able to follow the general overarching conversation flow.\n2. Current Work: Describe in detail what was being worked on prior to this request to summarize the conversation. Pay special attention to the more recent messages in the conversation.\n3. Key Technical Concepts: List all important technical concepts, technologies, coding conventions, and frameworks discussed, which might be relevant for continuing with this work.\n4. Relevant Files and Code: If applicable, enumerate specific files and code sections examined, modified, or created for the task continuation. Pay special attention to the most recent messages and changes.\n5. Problem Solving: Document problems solved thus far and any ongoing troubleshooting efforts.\n6. Pending Tasks and Next Steps: Outline all pending tasks that you have explicitly been asked to work on, as well as list the next steps you will take for all outstanding work, if applicable. Include code snippets where they add clarity. For any next steps, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no information loss in context between tasks.\n\nExample summary structure:\n1. Previous Conversation:\n[Detailed description]\n2. Current Work:\n[Detailed description]\n3. Key Technical Concepts:\n- [Concept 1]\n- [Concept 2]\n- [...]\n4. Relevant Files and Code:\n- [File Name 1]\n    - [Summary of why this file is important]\n    - [Summary of the changes made to this file, if any]\n    - [Important Code Snippet]\n- [File Name 2]\n    - [Important Code Snippet]\n- [...]\n5. Problem Solving:\n[Detailed description]\n6. Pending Tasks and Next Steps:\n- [Task 1 details & next steps]\n- [Task 2 details & next steps]\n- [...]\n\nOutput only the summary of the conversation so far, without any additional commentary or explanation.\n"
  - enableCommitIndexing: false to true
  - maxCommitsToIndex: 0 to 10000
  - retryChatStreamTimeouts: false to true
2025-07-15 11:24:31.661 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home (explicit) at 7/8/2025, 7:02:26 PM
2025-07-15 11:24:31.661 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-07-15 11:24:31.661 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-07-15 11:24:31.661 [info] 'SyncingPermissionTracker' Permission to sync folder /home granted at 7/8/2025, 7:02:26 PM; type = explicit
2025-07-15 11:24:31.661 [info] 'WorkspaceManager' Adding workspace folder home; folderRoot = /home; syncingPermission = granted
2025-07-15 11:24:31.661 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home (explicit) at 7/8/2025, 7:02:26 PM
2025-07-15 11:24:31.711 [info] 'MemoryWebviewMessageHandler' Memory webview message handler initialized
2025-07-15 11:24:31.711 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-07-15 11:24:31.711 [info] 'HotKeyHints' HotKeyHints initialized
2025-07-15 11:24:31.717 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-07-15 11:24:31.757 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-07-15 11:24:31.758 [info] 'ToolsModel' Host: localToolHost (10 tools: 178 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + git-commit-retrieval
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-15 11:24:32.543 [info] 'WorkspaceManager[home]' Start tracking
2025-07-15 11:24:32.549 [info] 'PathMap' Opened source folder /home with id 100
2025-07-15 11:24:32.549 [info] 'OpenFileManager' Opened source folder 100
2025-07-15 11:24:32.553 [info] 'MtimeCache[home]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/2cc974af6afc822c42a4e914df05c697348b80420941f6b27561b2bf688587b7/mtime-cache.json
2025-07-15 11:24:33.016 [info] 'MtimeCache[home]' read 45547 entries from /home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/2cc974af6afc822c42a4e914df05c697348b80420941f6b27561b2bf688587b7/mtime-cache.json
2025-07-15 11:24:34.640 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-15 11:24:34.640 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-15 11:24:41.302 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-07-15 11:24:41.302 [info] 'ToolsModel' Host: localToolHost (10 tools: 178 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + git-commit-retrieval
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-15 11:24:41.302 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-15 11:24:41.302 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-15 11:25:26.236 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-07-15 11:25:28.235 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-07-15 11:25:28.235 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-07-15 11:25:28.235 [info] 'ToolsModel' Saved chat mode: CHAT
2025-07-15 11:25:28.238 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-07-15 11:25:28.358 [info] 'ToolsModel' Saved chat mode: AGENT
2025-07-15 11:25:28.373 [info] 'TaskManager' Setting current root task UUID to a4aff489-84dd-4180-82df-c85ca87a08b3
2025-07-15 11:25:28.373 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-07-15 11:25:28.373 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-07-15 11:25:28.373 [info] 'ToolsModel' Host: localToolHost (10 tools: 178 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + git-commit-retrieval
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-15 11:25:29.423 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1082.634135,"timestamp":"2025-07-15T03:25:29.322Z"}]
2025-07-15 11:25:29.482 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/logs/20250715T112224/exthost4/vscode.typescript-language-features
2025-07-15 11:25:29.547 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-15 11:25:29.547 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-15 11:25:29.968 [info] 'TaskManager' Setting current root task UUID to 7883dc2f-6aec-4542-b2cf-0f5ba7ae1671
2025-07-15 11:25:29.968 [info] 'TaskManager' Setting current root task UUID to 7883dc2f-6aec-4542-b2cf-0f5ba7ae1671
2025-07-15 11:25:29.989 [error] 'GitReferenceMessenger' Failed to locally get remote url: Failed to get remote url, no remote found
2025-07-15 11:27:50.067 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:28:32.552 [info] 'ToolFileUtils' Reading file: website/fix-502.sh
2025-07-15 11:28:32.606 [info] 'ToolFileUtils' Successfully read file: website/fix-502.sh (6961 bytes)
2025-07-15 11:28:33.290 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/History/-3da7cc90
2025-07-15 11:28:34.395 [info] 'ToolFileUtils' Reading file: website/fix-502.sh
2025-07-15 11:28:34.396 [info] 'ToolFileUtils' Successfully read file: website/fix-502.sh (6988 bytes)
2025-07-15 11:28:37.627 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:28:48.312 [info] 'ToolFileUtils' Reading file: website/one-click-fix.sh
2025-07-15 11:28:48.340 [info] 'ToolFileUtils' Successfully read file: website/one-click-fix.sh (7004 bytes)
2025-07-15 11:28:49.005 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/History/483fd9c6
2025-07-15 11:28:49.868 [info] 'ToolFileUtils' Reading file: website/one-click-fix.sh
2025-07-15 11:28:49.868 [info] 'ToolFileUtils' Successfully read file: website/one-click-fix.sh (7031 bytes)
2025-07-15 11:28:53.406 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:29:54.427 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/logs/20250715T112224/exthost4/output_logging_20250715T112427
2025-07-15 11:29:58.486 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:30:01.746 [info] 'ViewTool' Tool called with path: website/fix-502.sh and view_range: [270,280]
2025-07-15 11:30:11.551 [info] 'ViewTool' Tool called with path: website/one-click-fix.sh and view_range: [283,293]
2025-07-15 11:30:29.838 [info] 'ViewTool' Tool called with path: website/ronglianweb/nginx-ronglianweb.conf and view_range: [1,20]
2025-07-15 11:30:29.844 [info] 'ViewTool' Path does not exist: website/ronglianweb/nginx-ronglianweb.conf
2025-07-15 11:30:30.212 [info] 'ToolFileUtils' File not found: website/ronglianweb/nginx-ronglianweb.conf. Similar files found:
/home/<USER>/ronglianweb-backup1/nginx-ronglianweb.conf
2025-07-15 11:30:46.194 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/logs/20250715T112224/exthost4/vscode.json-language-features
2025-07-15 11:30:48.101 [info] 'ViewTool' Tool called with path: website/ronglianweb/nginx-ronglianweb.conf and view_range: [1,20]
2025-07-15 11:30:48.109 [info] 'ViewTool' Path does not exist: website/ronglianweb/nginx-ronglianweb.conf
2025-07-15 11:30:48.404 [info] 'ToolFileUtils' File not found: website/ronglianweb/nginx-ronglianweb.conf. Similar files found:
/home/<USER>/ronglianweb-backup1/nginx-ronglianweb.conf
2025-07-15 11:30:57.267 [info] 'ViewTool' Tool called with path: website/ronglianweb and view_range: undefined
2025-07-15 11:30:57.281 [info] 'ViewTool' Listing directory: website/ronglianweb (depth: 2, showHidden: false)
2025-07-15 11:31:06.799 [info] 'ViewTool' Tool called with path: website/ronglianweb/nginx-ronglian-https.conf and view_range: [1,30]
2025-07-15 11:31:50.652 [info] 'ToolFileUtils' Reading file: website/ronglianweb/nginx-ronglian-https.conf
2025-07-15 11:31:50.672 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/nginx-ronglian-https.conf (6581 bytes)
2025-07-15 11:31:51.581 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/History/-6d356b46
2025-07-15 11:31:52.416 [info] 'ToolFileUtils' Reading file: website/ronglianweb/nginx-ronglian-https.conf
2025-07-15 11:31:52.416 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/nginx-ronglian-https.conf (6591 bytes)
2025-07-15 11:31:55.685 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:32:02.914 [info] 'ToolFileUtils' Reading file: website/ronglianweb/nginx-ronglian-https.conf
2025-07-15 11:32:02.914 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/nginx-ronglian-https.conf (6591 bytes)
2025-07-15 11:32:04.131 [info] 'ToolFileUtils' Reading file: website/ronglianweb/nginx-ronglian-https.conf
2025-07-15 11:32:04.132 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/nginx-ronglian-https.conf (6601 bytes)
2025-07-15 11:32:08.003 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:32:29.812 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1399.620934,"timestamp":"2025-07-15T03:32:29.645Z"}]
2025-07-15 11:32:38.349 [info] 'ViewTool' Tool called with path: website/ronglianweb/ssl and view_range: undefined
2025-07-15 11:32:38.356 [info] 'ViewTool' Listing directory: website/ronglianweb/ssl (depth: 2, showHidden: false)
2025-07-15 11:32:50.163 [info] 'ToolFileUtils' Reading file: website/ronglianweb/nginx-ronglian-https.conf
2025-07-15 11:32:50.164 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/nginx-ronglian-https.conf (6601 bytes)
2025-07-15 11:32:51.515 [info] 'ToolFileUtils' Reading file: website/ronglianweb/nginx-ronglian-https.conf
2025-07-15 11:32:51.515 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/nginx-ronglian-https.conf (6611 bytes)
2025-07-15 11:32:55.174 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:36:11.428 [info] 'ToolFileUtils' Reading file: website/ACCESS_ADDRESSES_UPDATED.md
2025-07-15 11:36:11.450 [info] 'ToolFileUtils' Successfully read file: website/ACCESS_ADDRESSES_UPDATED.md (2401 bytes)
2025-07-15 11:36:12.497 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/History/26c04a16
2025-07-15 11:36:12.829 [info] 'ToolFileUtils' Reading file: website/ACCESS_ADDRESSES_UPDATED.md
2025-07-15 11:36:12.829 [info] 'ToolFileUtils' Successfully read file: website/ACCESS_ADDRESSES_UPDATED.md (2978 bytes)
2025-07-15 11:36:16.463 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:37:59.524 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1277.598127,"timestamp":"2025-07-15T03:37:59.491Z"}]
2025-07-15 11:38:29.555 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1260.217598,"timestamp":"2025-07-15T03:38:29.483Z"}]
2025-07-15 11:39:40.748 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:39:46.520 [info] 'ViewTool' Tool called with path: website/ronglianweb/run.sh and view_range: undefined
2025-07-15 11:39:57.822 [info] 'ToolFileUtils' Reading file: website/ronglianweb/run.sh
2025-07-15 11:39:57.822 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/run.sh (5280 bytes)
2025-07-15 11:39:58.405 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/History/-522bc021
2025-07-15 11:39:59.225 [info] 'ToolFileUtils' Reading file: website/ronglianweb/run.sh
2025-07-15 11:39:59.225 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/run.sh (5280 bytes)
2025-07-15 11:40:02.834 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:40:14.284 [info] 'ToolFileUtils' Reading file: website/ronglianweb/run.sh
2025-07-15 11:40:14.285 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/run.sh (5280 bytes)
2025-07-15 11:40:14.324 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 256f2fe22c2df276a3f284a1c0dca2c61a574e33e69435528e49fc873328cfd8: deleted
2025-07-15 11:40:16.302 [info] 'ToolFileUtils' Reading file: website/ronglianweb/run.sh
2025-07-15 11:40:16.302 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/run.sh (5280 bytes)
2025-07-15 11:40:19.299 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:40:31.536 [info] 'ToolFileUtils' Reading file: website/ronglianweb/run.sh
2025-07-15 11:40:31.537 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/run.sh (5280 bytes)
2025-07-15 11:40:31.583 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 0060ee2bf3737df8668726372ddb15f753c67186c77a8fda41937bb2a5eb85a9: deleted
2025-07-15 11:40:32.800 [info] 'ToolFileUtils' Reading file: website/ronglianweb/run.sh
2025-07-15 11:40:32.800 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/run.sh (5280 bytes)
2025-07-15 11:40:36.641 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:40:48.299 [info] 'ToolFileUtils' Reading file: website/ronglianweb/run.sh
2025-07-15 11:40:48.299 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/run.sh (5280 bytes)
2025-07-15 11:40:48.328 [error] 'FuzzySymbolSearcher' Failed to read file tokens for a49ca7b599d14aac1491edcd7f4a8e488e9220b8ce397af2e1645d92a2d770b8: deleted
2025-07-15 11:40:49.583 [info] 'ToolFileUtils' Reading file: website/ronglianweb/run.sh
2025-07-15 11:40:49.583 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/run.sh (5280 bytes)
2025-07-15 11:40:53.311 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:41:02.732 [info] 'ToolFileUtils' Reading file: website/ronglianweb/run.sh
2025-07-15 11:41:02.732 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/run.sh (5280 bytes)
2025-07-15 11:41:02.763 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 96e6d5e1288998ee9158a919b51141d2e73a52f5f2c40385d2bd76b959f3b542: deleted
2025-07-15 11:41:04.007 [info] 'ToolFileUtils' Reading file: website/ronglianweb/run.sh
2025-07-15 11:41:04.008 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/run.sh (5280 bytes)
2025-07-15 11:41:07.745 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:42:16.508 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 11:51:09.210 [info] 'WorkspaceManager[home]' Tracking enabled
2025-07-15 11:51:09.210 [info] 'WorkspaceManager[home]' Path metrics:
  - directories emitted: 20800
  - files emitted: 144448
  - other paths emitted: 1230
  - total paths emitted: 166478
  - timing stats:
    - readDir: 458 ms
    - filter: 3811 ms
    - yield: 678 ms
    - total: 5507 ms
2025-07-15 11:51:09.210 [info] 'WorkspaceManager[home]' File metrics:
  - paths accepted: 96690
  - paths not accessible: 0
  - not plain files: 0
  - large files: 2189
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 37281
  - mtime cache misses: 59409
  - probe batches: 558
  - blob names probed: 246255
  - files read: 157737
  - blobs uploaded: 50934
  - timing stats:
    - ingestPath: 336 ms
    - probe: 974413 ms
    - stat: 3300 ms
    - read: 161102 ms
    - upload: 1511756 ms
2025-07-15 11:51:09.210 [info] 'WorkspaceManager[home]' Startup metrics:
  - create SourceFolder: 9 ms
  - read MtimeCache: 464 ms
  - pre-populate PathMap: 1139 ms
  - create PathFilter: 1495 ms
  - create PathNotifier: 0 ms
  - enumerate paths: 5510 ms
  - purge stale PathMap entries: 36 ms
  - enumerate: 1 ms
  - await DiskFileManager quiesced: 1587881 ms
  - enable persist: 130 ms
  - total: 1596665 ms
2025-07-15 11:51:09.210 [info] 'WorkspaceManager' Workspace startup complete in 1597564 ms
2025-07-15 11:54:30.186 [info] 'AugmentExtension' Retrieving model config
2025-07-15 11:54:31.346 [info] 'AugmentExtension' Retrieved model config
2025-07-15 11:54:31.346 [info] 'AugmentExtension' Returning model config
2025-07-15 12:09:29.274 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1016.679644,"timestamp":"2025-07-15T04:09:29.225Z"}]
2025-07-15 12:18:21.436 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 12:19:04.049 [info] 'ViewTool' Tool called with path: website/ronglianweb/views/admin/newsEdit_new.jade and view_range: [1,50]
2025-07-15 12:20:12.371 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 12:24:30.187 [info] 'AugmentExtension' Retrieving model config
2025-07-15 12:24:31.205 [info] 'AugmentExtension' Retrieved model config
2025-07-15 12:24:31.205 [info] 'AugmentExtension' Returning model config
2025-07-15 12:54:30.186 [info] 'AugmentExtension' Retrieving model config
2025-07-15 12:54:31.440 [info] 'AugmentExtension' Retrieved model config
2025-07-15 12:54:31.440 [info] 'AugmentExtension' Returning model config
2025-07-15 12:54:59.612 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1372.518474,"timestamp":"2025-07-15T04:54:59.555Z"}]
2025-07-15 12:55:29.617 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1352.178225,"timestamp":"2025-07-15T04:55:29.534Z"}]
2025-07-15 12:55:59.587 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1363.287817,"timestamp":"2025-07-15T04:55:59.545Z"}]
2025-07-15 12:56:29.877 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1596.298205,"timestamp":"2025-07-15T04:56:29.778Z"}]
2025-07-15 13:06:34.181 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 13:07:36.636 [error] 'ChatApp' Chat stream failed: Error: Cancelled
Error: Cancelled
	at Efe.cancel (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.502.1/out/extension.js:744:1081)
	at e.cancelChatStream (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.502.1/out/extension.js:744:33573)
	at qk.onUserCancel (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.502.1/out/extension.js:1885:15045)
	at /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.502.1/out/extension.js:1885:4103
	at Gh.value (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.502.1/out/extension.js:546:4163)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.D (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2445)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2662)
	at PW.$onMessage (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:140:91544)
	at a4.S (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164720)
	at a4.Q (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:164500)
	at a4.M (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163589)
	at a4.L (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162827)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161491)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:356:8152)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at ao.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9459)
	at H1.A (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12575)
	at Gh.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10995)
	at $.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2375)
	at $.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2593)
	at Hx.acceptChunk (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7942)
	at file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7228
	at Socket.t (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15252)
	at Socket.emit (node:events:518:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-07-15 13:07:37.212 [error] 'AugmentExtension' API request 2d29d0e7-38d5-4fb9-9aa9-6d9a81d54c57 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-15 13:07:37.212 [error] 'AugmentExtension' Dropping error report "chat_stream_failed" due to error: This operation was aborted
2025-07-15 13:07:59.828 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1530.760425,"timestamp":"2025-07-15T05:07:59.782Z"}]
2025-07-15 13:08:38.756 [info] 'ViewTool' Tool called with path: website/ronglianweb/views/admin/newsEdit_new.jade and view_range: [1,50]
2025-07-15 13:08:47.116 [info] 'ViewTool' Tool called with path: website/ronglianweb/views/admin/newsEdit_new.jade and view_range: [50,124]
2025-07-15 13:08:56.341 [info] 'ViewTool' Tool called with path: website/ronglianweb/public/plugins/admin/js/newsEdit.js and view_range: [1,50]
2025-07-15 13:10:17.445 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 13:10:27.681 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit_new.jade
2025-07-15 13:10:27.682 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit_new.jade (4597 bytes)
2025-07-15 13:10:28.286 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/History/70ac0b80
2025-07-15 13:10:29.224 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit_new.jade
2025-07-15 13:10:29.224 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit_new.jade (4934 bytes)
2025-07-15 13:10:32.696 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 13:10:46.889 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit_new.jade
2025-07-15 13:10:46.889 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit_new.jade (4934 bytes)
2025-07-15 13:10:46.922 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 2f857ef68bc91ceb33ef048b170bd5c9ec0837e170de4969160bd84b8ce81112: deleted
2025-07-15 13:10:48.242 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit_new.jade
2025-07-15 13:10:48.242 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit_new.jade (5568 bytes)
2025-07-15 13:10:51.920 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 13:11:04.035 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit_new.jade
2025-07-15 13:11:04.036 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit_new.jade (5568 bytes)
2025-07-15 13:11:04.061 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 4bb5350e121a03d179d04c289e301cda8a8b7559e3921b52d497239ede94bdf7: deleted
2025-07-15 13:11:05.325 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit_new.jade
2025-07-15 13:11:05.325 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit_new.jade (5774 bytes)
2025-07-15 13:11:09.047 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 13:12:06.293 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 13:13:04.084 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 13:13:11.967 [info] 'ToolFileUtils' Reading file: website/ronglianweb/routes/index.js
2025-07-15 13:13:11.986 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/routes/index.js (4724 bytes)
2025-07-15 13:13:19.736 [info] 'ViewTool' Tool called with path: website/ronglianweb/routes/index.js and view_range: [125,135]
2025-07-15 13:13:29.335 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1053.230086,"timestamp":"2025-07-15T05:13:29.318Z"}]
2025-07-15 13:13:31.869 [info] 'ToolFileUtils' Reading file: website/ronglianweb/routes/index.js
2025-07-15 13:13:31.869 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/routes/index.js (4724 bytes)
2025-07-15 13:13:33.329 [info] 'ToolFileUtils' Reading file: website/ronglianweb/routes/index.js
2025-07-15 13:13:33.329 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/routes/index.js (4877 bytes)
2025-07-15 13:13:36.502 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/90
2025-07-15 13:13:36.502 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/3c
2025-07-15 13:13:37.292 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/f6/bd
2025-07-15 13:13:37.292 [info] 'WorkspaceManager[home]' Directory removed: website/.cache/typescript/5.8/node_modules/types-registry
2025-07-15 13:13:37.292 [info] 'WorkspaceManager[home]' Directory created: website/.cache/typescript/5.8/node_modules/types-registry
2025-07-15 13:13:37.292 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/dd/ab
2025-07-15 13:13:37.293 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/0e
2025-07-15 13:13:37.506 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 13:14:28.216 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 13:14:50.677 [info] 'ToolFileUtils' Reading file: website/ronglianweb/health-check.sh
2025-07-15 13:14:51.018 [info] 'ToolFileUtils' File not found: website/ronglianweb/health-check.sh. Similar files found:
/home/<USER>/ronglianweb-backup1/health-check.sh
2025-07-15 13:14:51.018 [error] 'StrReplaceEditorTool' Error in tool call: File not found: website/ronglianweb/health-check.sh. Did you mean one of these?
/home/<USER>/ronglianweb-backup1/health-check.sh
2025-07-15 13:14:59.827 [info] 'ViewTool' Tool called with path: website/ronglianweb and view_range: undefined
2025-07-15 13:14:59.836 [info] 'ViewTool' Listing directory: website/ronglianweb (depth: 2, showHidden: false)
2025-07-15 13:15:29.269 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1000.229665,"timestamp":"2025-07-15T05:15:29.266Z"}]
2025-07-15 13:15:38.633 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 13:17:58.467 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 13:17:59.338 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1029.889111,"timestamp":"2025-07-15T05:17:59.297Z"}]
2025-07-15 13:19:19.567 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 13:19:27.514 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/word-editor-test.jade
2025-07-15 13:19:27.572 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/word-editor-test.jade (16328 bytes)
2025-07-15 13:19:28.192 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/History/-73d232b1
2025-07-15 13:19:29.090 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/word-editor-test.jade
2025-07-15 13:19:29.090 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/word-editor-test.jade (16335 bytes)
2025-07-15 13:19:32.880 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 13:19:42.791 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/word-editor-test.jade
2025-07-15 13:19:42.792 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/word-editor-test.jade (16335 bytes)
2025-07-15 13:19:42.834 [error] 'FuzzySymbolSearcher' Failed to read file tokens for c06112569b6d2c59a426eea0fb454cda3d54042db2b520564e367c88cadf381b: deleted
2025-07-15 13:19:44.034 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/word-editor-test.jade
2025-07-15 13:19:44.034 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/word-editor-test.jade (16341 bytes)
2025-07-15 13:19:47.808 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 13:19:56.156 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/word-editor-test.jade
2025-07-15 13:19:56.156 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/word-editor-test.jade (16341 bytes)
2025-07-15 13:19:57.409 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/word-editor-test.jade
2025-07-15 13:19:57.409 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/word-editor-test.jade (16348 bytes)
2025-07-15 13:20:01.169 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 13:20:11.040 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/word-editor-test.jade
2025-07-15 13:20:11.040 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/word-editor-test.jade (16348 bytes)
2025-07-15 13:20:11.075 [error] 'FuzzySymbolSearcher' Failed to read file tokens for c081933724eb03f05c722729714413fdccab0a35a361aa534713f4ad7dc46f8d: deleted
2025-07-15 13:20:12.305 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/word-editor-test.jade
2025-07-15 13:20:12.306 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/word-editor-test.jade (16352 bytes)
2025-07-15 13:20:16.054 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 13:20:25.357 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/word-editor-test.jade
2025-07-15 13:20:25.357 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/word-editor-test.jade (16352 bytes)
2025-07-15 13:20:25.442 [error] 'FuzzySymbolSearcher' Failed to read file tokens for fadc28173eccf23681bd42e179e2d8d3ba207dd2e91d9d0cf352969468a638c8: deleted
2025-07-15 13:20:26.625 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/word-editor-test.jade
2025-07-15 13:20:26.625 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/word-editor-test.jade (16356 bytes)
2025-07-15 13:20:30.403 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 13:20:38.347 [info] 'ViewTool' Tool called with path: website/ronglianweb/views/admin/newsEdit_new.jade and view_range: undefined
2025-07-15 13:21:09.611 [info] 'ToolFileUtils' Reading file: website/ronglianweb/libs/config.js
2025-07-15 13:21:09.666 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/libs/config.js (8524 bytes)
2025-07-15 13:21:11.387 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/History/-6a877685
2025-07-15 13:21:12.184 [info] 'ToolFileUtils' Reading file: website/ronglianweb/libs/config.js
2025-07-15 13:21:12.184 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/libs/config.js (8525 bytes)
2025-07-15 13:21:14.679 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 13:21:23.022 [info] 'ViewTool' Tool called with path: website/ronglianweb/views/layout.jade and view_range: [1,15]
2025-07-15 13:21:42.627 [info] 'ViewTool' Tool called with path: website/ronglianweb/routes/news/news.js and view_range: undefined
2025-07-15 13:21:51.980 [info] 'ToolFileUtils' Reading file: website/ronglianweb/routes/news/news.js
2025-07-15 13:21:51.981 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/routes/news/news.js (10747 bytes)
2025-07-15 13:21:52.640 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/History/51005dc6
2025-07-15 13:21:53.472 [info] 'ToolFileUtils' Reading file: website/ronglianweb/routes/news/news.js
2025-07-15 13:21:53.472 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/routes/news/news.js (10748 bytes)
2025-07-15 13:21:56.995 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 13:22:05.612 [info] 'ViewTool' Tool called with path: website/ronglianweb/public/dest/component-news/footer1.js and view_range: undefined
2025-07-15 13:22:16.144 [info] 'ToolFileUtils' Reading file: website/ronglianweb/public/dest/component-news/footer1.js
2025-07-15 13:22:16.145 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/public/dest/component-news/footer1.js (6579 bytes)
2025-07-15 13:22:16.808 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/History/-690bd0c1
2025-07-15 13:22:17.668 [info] 'ToolFileUtils' Reading file: website/ronglianweb/public/dest/component-news/footer1.js
2025-07-15 13:22:17.669 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/public/dest/component-news/footer1.js (6578 bytes)
2025-07-15 13:22:21.355 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 13:22:31.316 [info] 'ToolFileUtils' Reading file: website/ronglianweb/public/dest/component-news/footer1.js
2025-07-15 13:22:31.317 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/public/dest/component-news/footer1.js (6578 bytes)
2025-07-15 13:22:32.674 [info] 'ToolFileUtils' Reading file: website/ronglianweb/public/dest/component-news/footer1.js
2025-07-15 13:22:32.674 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/public/dest/component-news/footer1.js (6579 bytes)
2025-07-15 13:22:36.329 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 13:23:52.796 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 13:24:30.186 [info] 'AugmentExtension' Retrieving model config
2025-07-15 13:24:31.389 [info] 'AugmentExtension' Retrieved model config
2025-07-15 13:24:31.389 [info] 'AugmentExtension' Returning model config
2025-07-15 13:34:30.067 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1123.041477,"timestamp":"2025-07-15T05:34:30.066Z"}]
2025-07-15 13:34:59.716 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1389.683203,"timestamp":"2025-07-15T05:34:59.701Z"}]
2025-07-15 13:35:29.887 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1567.016757,"timestamp":"2025-07-15T05:35:29.887Z"}]
2025-07-15 13:35:59.638 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1321.690427,"timestamp":"2025-07-15T05:35:59.638Z"}]
2025-07-15 13:43:29.953 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1575.201792,"timestamp":"2025-07-15T05:43:29.868Z"}]
2025-07-15 13:47:59.772 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1396.055813,"timestamp":"2025-07-15T05:47:59.694Z"}]
2025-07-15 13:52:29.603 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1272.725045,"timestamp":"2025-07-15T05:52:29.603Z"}]
2025-07-15 13:54:30.186 [info] 'AugmentExtension' Retrieving model config
2025-07-15 13:54:31.393 [info] 'AugmentExtension' Retrieved model config
2025-07-15 13:54:31.393 [info] 'AugmentExtension' Returning model config
2025-07-15 13:57:10.628 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 13:57:54.139 [info] 'ToolFileUtils' Reading file: website/ronglianweb/public/plugins/admin/js/newsEdit-word.js
2025-07-15 13:57:54.201 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/public/plugins/admin/js/newsEdit-word.js (13420 bytes)
2025-07-15 13:57:54.862 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/History/6e997b55
2025-07-15 13:57:55.724 [info] 'ToolFileUtils' Reading file: website/ronglianweb/public/plugins/admin/js/newsEdit-word.js
2025-07-15 13:57:55.724 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/public/plugins/admin/js/newsEdit-word.js (13571 bytes)
2025-07-15 13:57:59.351 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 13:58:20.695 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit_new.jade
2025-07-15 13:58:20.791 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit_new.jade (5774 bytes)
2025-07-15 13:58:22.260 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit_new.jade
2025-07-15 13:58:22.260 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit_new.jade (6126 bytes)
2025-07-15 13:58:25.852 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 13:58:35.385 [info] 'ToolFileUtils' Reading file: website/ronglianweb/public/plugins/admin/js/newsEdit-word.js
2025-07-15 13:58:35.385 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/public/plugins/admin/js/newsEdit-word.js (13571 bytes)
2025-07-15 13:58:36.864 [info] 'ToolFileUtils' Reading file: website/ronglianweb/public/plugins/admin/js/newsEdit-word.js
2025-07-15 13:58:36.865 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/public/plugins/admin/js/newsEdit-word.js (13687 bytes)
2025-07-15 13:58:40.398 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 13:59:15.935 [info] 'ToolFileUtils' Reading file: website/ronglianweb/public/plugins/admin/js/newsEdit-word.js
2025-07-15 13:59:15.935 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/public/plugins/admin/js/newsEdit-word.js (13687 bytes)
2025-07-15 13:59:17.440 [info] 'ToolFileUtils' Reading file: website/ronglianweb/public/plugins/admin/js/newsEdit-word.js
2025-07-15 13:59:17.441 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/public/plugins/admin/js/newsEdit-word.js (18538 bytes)
2025-07-15 13:59:21.037 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 13:59:41.216 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/word-editor-test.jade
2025-07-15 13:59:41.297 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/word-editor-test.jade (16356 bytes)
2025-07-15 13:59:42.736 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/word-editor-test.jade
2025-07-15 13:59:42.736 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/word-editor-test.jade (16572 bytes)
2025-07-15 13:59:46.362 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 14:01:07.532 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 14:05:59.363 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 14:06:13.290 [info] 'ViewTool' Tool called with path: website/ronglianweb/views/admin/newsEdit_new.jade and view_range: [1,30]
2025-07-15 14:06:42.604 [info] 'ViewTool' Tool called with path: website/ronglianweb/views/admin/newsEdit.jade and view_range: [140,150]
2025-07-15 14:06:56.689 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit.jade
2025-07-15 14:06:56.690 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit.jade (4432 bytes)
2025-07-15 14:06:57.383 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/History/-18aafabf
2025-07-15 14:06:58.555 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit.jade
2025-07-15 14:06:58.555 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit.jade (4769 bytes)
2025-07-15 14:07:01.792 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 14:07:17.050 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit.jade
2025-07-15 14:07:17.050 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit.jade (4769 bytes)
2025-07-15 14:07:17.105 [error] 'FuzzySymbolSearcher' Failed to read file tokens for a67cb0249d56ddb6e7c71d4857dcd051666a5a257729f08bf15cc35371b4a054: deleted
2025-07-15 14:07:18.404 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit.jade
2025-07-15 14:07:18.404 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit.jade (5703 bytes)
2025-07-15 14:07:22.062 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 14:07:30.054 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1612.401373,"timestamp":"2025-07-15T06:07:30.019Z"}]
2025-07-15 14:07:37.508 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit.jade
2025-07-15 14:07:37.508 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit.jade (5703 bytes)
2025-07-15 14:07:37.559 [error] 'FuzzySymbolSearcher' Failed to read file tokens for ad32e10271277b6c660bb7a2d7b59c849873dd240fe5fc21f1160690311963dd: deleted
2025-07-15 14:07:38.798 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit.jade
2025-07-15 14:07:38.798 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit.jade (6273 bytes)
2025-07-15 14:07:42.524 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 14:08:37.213 [info] 'ToolFileUtils' Reading file: website/ronglianweb/public/plugins/admin/js/newsEdit.js
2025-07-15 14:08:37.265 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/public/plugins/admin/js/newsEdit.js (8420 bytes)
2025-07-15 14:08:37.869 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/History/-7b42949e
2025-07-15 14:08:38.797 [info] 'ToolFileUtils' Reading file: website/ronglianweb/public/plugins/admin/js/newsEdit.js
2025-07-15 14:08:38.798 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/public/plugins/admin/js/newsEdit.js (9554 bytes)
2025-07-15 14:08:42.282 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 14:09:09.008 [info] 'ToolFileUtils' Reading file: website/ronglianweb/public/plugins/admin/js/newsEdit.js
2025-07-15 14:09:09.009 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/public/plugins/admin/js/newsEdit.js (9554 bytes)
2025-07-15 14:09:21.971 [info] 'ViewTool' Tool called with path: website/ronglianweb/public/plugins/admin/js/newsEdit.js and view_range: [90,130]
2025-07-15 14:09:40.073 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 14:13:59.973 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1504.487547,"timestamp":"2025-07-15T06:13:59.934Z"}]
2025-07-15 14:24:30.188 [info] 'AugmentExtension' Retrieving model config
2025-07-15 14:24:31.200 [info] 'AugmentExtension' Retrieved model config
2025-07-15 14:24:31.200 [info] 'AugmentExtension' Returning model config
2025-07-15 14:27:29.539 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1050.334189,"timestamp":"2025-07-15T06:27:29.520Z"}]
2025-07-15 14:29:21.539 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 14:34:38.279 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 14:34:46.543 [info] 'ViewTool' Tool called with path: website/ronglianweb/public/plugins/admin/js/newsEdit.js and view_range: [1,50]
2025-07-15 14:34:56.503 [info] 'ViewTool' Tool called with path: website/ronglianweb/public/plugins/admin/js/newsEdit.js and view_range: undefined
2025-07-15 14:35:07.266 [info] 'ViewTool' Tool called with path: website/ronglianweb/public/plugins/admin/js/newsEdit.js and view_range: [210,250]
2025-07-15 14:35:20.138 [info] 'ToolFileUtils' Reading file: website/ronglianweb/public/plugins/admin/js/newsEdit.js
2025-07-15 14:35:20.138 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/public/plugins/admin/js/newsEdit.js (18538 bytes)
2025-07-15 14:35:21.663 [info] 'ToolFileUtils' Reading file: website/ronglianweb/public/plugins/admin/js/newsEdit.js
2025-07-15 14:35:21.663 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/public/plugins/admin/js/newsEdit.js (18751 bytes)
2025-07-15 14:35:26.169 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 14:35:37.059 [info] 'ToolFileUtils' Reading file: website/ronglianweb/public/plugins/admin/js/newsEdit.js
2025-07-15 14:35:37.059 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/public/plugins/admin/js/newsEdit.js (18751 bytes)
2025-07-15 14:35:38.315 [info] 'ToolFileUtils' Reading file: website/ronglianweb/public/plugins/admin/js/newsEdit.js
2025-07-15 14:35:38.316 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/public/plugins/admin/js/newsEdit.js (18710 bytes)
2025-07-15 14:35:42.072 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 14:36:15.639 [info] 'ViewTool' Tool called with path: website/ronglianweb/routes/admin/newsAdmin.js and view_range: [105,135]
2025-07-15 14:36:31.124 [info] 'ViewTool' Tool called with path: website/ronglianweb/app.js and view_range: undefined
2025-07-15 14:36:39.831 [info] 'ViewTool' Tool called with path: website/ronglianweb/public/plugins/admin/js/newsEdit.js and view_range: [210,250]
2025-07-15 14:37:03.330 [info] 'ToolFileUtils' Reading file: website/ronglianweb/public/plugins/admin/js/newsEdit.js
2025-07-15 14:37:03.330 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/public/plugins/admin/js/newsEdit.js (18710 bytes)
2025-07-15 14:37:04.746 [info] 'ToolFileUtils' Reading file: website/ronglianweb/public/plugins/admin/js/newsEdit.js
2025-07-15 14:37:04.746 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/public/plugins/admin/js/newsEdit.js (19603 bytes)
2025-07-15 14:37:05.221 [info] 'WorkspaceManager[home]' Directory created: website/.cache/typescript/5.8/node_modules/@types/multer
2025-07-15 14:37:05.222 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/90/f1
2025-07-15 14:37:05.222 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/cd/a6
2025-07-15 14:37:05.223 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/46/39
2025-07-15 14:37:05.424 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/0b/76
2025-07-15 14:37:05.424 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/1b
2025-07-15 14:37:05.993 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/12/5f
2025-07-15 14:37:05.994 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/8c/11
2025-07-15 14:37:05.994 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/8d/f0
2025-07-15 14:37:05.995 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/91/1d
2025-07-15 14:37:05.995 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/c0
2025-07-15 14:37:05.996 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/c0/8f
2025-07-15 14:37:05.996 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/63/47
2025-07-15 14:37:05.997 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/73/db
2025-07-15 14:37:05.997 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/83/2e
2025-07-15 14:37:05.997 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/ab/e0
2025-07-15 14:37:05.998 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/e0/d9
2025-07-15 14:37:06.072 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/85/a2
2025-07-15 14:37:06.298 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/27/f4
2025-07-15 14:37:06.298 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/6a/d3
2025-07-15 14:37:06.299 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/c7/17
2025-07-15 14:37:06.299 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/0e/ba
2025-07-15 14:37:06.448 [info] 'WorkspaceManager[home]' Directory created: website/.cache/typescript/5.8/node_modules/@types/cookie-parser
2025-07-15 14:37:06.449 [info] 'WorkspaceManager[home]' Directory created: website/.cache/typescript/5.8/node_modules/@types/express-session
2025-07-15 14:37:06.449 [info] 'WorkspaceManager[home]' Directory created: website/.cache/typescript/5.8/node_modules/@types/morgan
2025-07-15 14:37:06.449 [info] 'WorkspaceManager[home]' Directory created: website/.cache/typescript/5.8/node_modules/@types/node-schedule
2025-07-15 14:37:06.449 [info] 'WorkspaceManager[home]' Directory created: website/.cache/typescript/5.8/node_modules/@types/serve-favicon
2025-07-15 14:37:06.450 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/2e/d2
2025-07-15 14:37:06.450 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/e1/38
2025-07-15 14:37:06.665 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/93/4d
2025-07-15 14:37:06.665 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/93/e2
2025-07-15 14:37:06.666 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/b4
2025-07-15 14:37:06.666 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/b4/66
2025-07-15 14:37:06.667 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/cf/d4
2025-07-15 14:37:06.667 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/3e
2025-07-15 14:37:06.668 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/4e/51
2025-07-15 14:37:06.669 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/5a
2025-07-15 14:37:06.669 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/8f
2025-07-15 14:37:06.670 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/8f/2f
2025-07-15 14:37:06.807 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/b1/2e
2025-07-15 14:37:06.807 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/b7/7d
2025-07-15 14:37:08.476 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 14:37:21.703 [info] 'ToolFileUtils' Reading file: website/ronglianweb/public/plugins/admin/js/newsEdit.js
2025-07-15 14:37:21.703 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/public/plugins/admin/js/newsEdit.js (19603 bytes)
2025-07-15 14:37:23.025 [info] 'ToolFileUtils' Reading file: website/ronglianweb/public/plugins/admin/js/newsEdit.js
2025-07-15 14:37:23.026 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/public/plugins/admin/js/newsEdit.js (19756 bytes)
2025-07-15 14:37:26.947 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 14:37:41.454 [info] 'ViewTool' Tool called with path: website/ronglianweb/views/admin/newsEdit.jade and view_range: [180,200]
2025-07-15 14:37:55.436 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit.jade
2025-07-15 14:37:55.437 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit.jade (6273 bytes)
2025-07-15 14:37:57.406 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit.jade
2025-07-15 14:37:57.406 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit.jade (6268 bytes)
2025-07-15 14:38:00.525 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 14:38:12.760 [info] 'ViewTool' Tool called with path: website/ronglianweb/routes/admin/admin.js and view_range: undefined
2025-07-15 14:39:14.234 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/logs/20250715T112224/exthost4/vscode.html-language-features
2025-07-15 14:39:18.431 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 14:39:27.374 [info] 'ToolFileUtils' Reading file: website/ronglianweb/routes/index.js
2025-07-15 14:39:27.427 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/routes/index.js (4877 bytes)
2025-07-15 14:39:28.778 [info] 'ToolFileUtils' Reading file: website/ronglianweb/routes/index.js
2025-07-15 14:39:28.779 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/routes/index.js (5050 bytes)
2025-07-15 14:39:33.048 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 14:39:46.470 [info] 'ToolFileUtils' Reading file: website/ronglianweb/routes/index.js
2025-07-15 14:39:46.470 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/routes/index.js (5050 bytes)
2025-07-15 14:39:47.796 [info] 'ToolFileUtils' Reading file: website/ronglianweb/routes/index.js
2025-07-15 14:39:47.796 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/routes/index.js (5079 bytes)
2025-07-15 14:39:51.523 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 14:40:47.884 [error] 'AugmentExtension' API request f79e4530-1e22-43f3-8f45-a54d13a62ac5 to https://i0.api.augmentcode.com/find-missing failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-07-15 14:40:48.433 [error] 'AugmentExtension' API request 2cd33a6a-a387-42f3-a200-b92e023a73e2 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-15 14:40:48.433 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-15 14:40:48.433 [info] 'DiskFileManager[home]' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-07-15 14:40:49.386 [info] 'DiskFileManager[home]' Operation succeeded after 1 transient failures
2025-07-15 14:41:14.987 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 14:42:29.838 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1501.848513,"timestamp":"2025-07-15T06:42:29.757Z"}]
2025-07-15 14:42:59.695 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1407.507551,"timestamp":"2025-07-15T06:42:59.685Z"}]
2025-07-15 14:43:29.868 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1570.510057,"timestamp":"2025-07-15T06:43:29.793Z"}]
2025-07-15 14:43:59.565 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1324.125928,"timestamp":"2025-07-15T06:43:59.535Z"}]
2025-07-15 14:44:29.744 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1514.932968,"timestamp":"2025-07-15T06:44:29.714Z"}]
2025-07-15 14:44:59.692 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1449.245704,"timestamp":"2025-07-15T06:44:59.679Z"}]
2025-07-15 14:45:29.881 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1604.239644,"timestamp":"2025-07-15T06:45:29.843Z"}]
2025-07-15 14:54:30.188 [info] 'AugmentExtension' Retrieving model config
2025-07-15 14:54:32.158 [info] 'AugmentExtension' Retrieved model config
2025-07-15 14:54:32.158 [info] 'AugmentExtension' Returning model config
2025-07-15 15:08:29.297 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1053.673174,"timestamp":"2025-07-15T07:08:29.280Z"}]
2025-07-15 15:24:30.189 [info] 'AugmentExtension' Retrieving model config
2025-07-15 15:24:31.540 [info] 'AugmentExtension' Retrieved model config
2025-07-15 15:24:31.540 [info] 'AugmentExtension' Returning model config
2025-07-15 15:28:53.049 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 15:29:43.587 [info] 'ViewTool' Tool called with path: website/ronglianweb/views/admin/newsEdit.jade and view_range: [50,120]
2025-07-15 15:29:56.565 [info] 'ViewTool' Tool called with path: website/ronglianweb/public/plugins/admin/js/newsEdit-original.js and view_range: [1,30]
2025-07-15 15:30:33.818 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit.jade
2025-07-15 15:30:33.818 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit.jade (6268 bytes)
2025-07-15 15:30:35.386 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit.jade
2025-07-15 15:30:35.386 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit.jade (6205 bytes)
2025-07-15 15:30:39.296 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 15:30:54.735 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit.jade
2025-07-15 15:30:54.735 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit.jade (6205 bytes)
2025-07-15 15:30:54.762 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 901e9cac4e390c467052f3b26e74526937f424cf86d04b19d24f3b6eb217a8cd: deleted
2025-07-15 15:30:56.148 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit.jade
2025-07-15 15:30:56.148 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit.jade (6108 bytes)
2025-07-15 15:30:59.841 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 15:32:53.857 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 15:36:29.742 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1407.01716,"timestamp":"2025-07-15T07:36:29.645Z"}]
2025-07-15 15:42:29.623 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1361.88683,"timestamp":"2025-07-15T07:42:29.610Z"}]
2025-07-15 15:54:30.189 [info] 'AugmentExtension' Retrieving model config
2025-07-15 15:54:31.262 [info] 'AugmentExtension' Retrieved model config
2025-07-15 15:54:31.262 [info] 'AugmentExtension' Returning model config
2025-07-15 15:59:28.975 [error] 'AugmentExtension' API request 6bb797ea-4e17-4f9f-bcd0-082481ac6078 to https://i0.api.augmentcode.com/subscription-info response 502: Bad Gateway
2025-07-15 15:59:29.484 [error] 'AugmentExtension' API request 3a5f4889-158c-462a-a0f1-dce424ea5feb to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-15 15:59:29.485 [error] 'AugmentExtension' Dropping error report "subscription-info call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-15 15:59:29.485 [error] 'ChatApp' Failed to get subscription info: Error: HTTP error: 502 Bad Gateway
2025-07-15 15:59:29.606 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1261.075123,"timestamp":"2025-07-15T07:59:29.485Z"}]
2025-07-15 16:06:07.565 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 16:06:51.782 [info] 'ViewTool' Tool called with path: website/ronglianweb/public/plugins/admin/js/newsEdit.js and view_range: undefined
2025-07-15 16:07:23.159 [info] 'ViewTool' Tool called with path: website/ronglianweb/public/plugins/admin/js/newsEdit.js and view_range: undefined
2025-07-15 16:07:52.931 [info] 'ToolFileUtils' Reading file: website/ronglianweb/public/plugins/admin/js/newsEdit.js
2025-07-15 16:07:52.931 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/public/plugins/admin/js/newsEdit.js (19756 bytes)
2025-07-15 16:07:54.499 [info] 'ToolFileUtils' Reading file: website/ronglianweb/public/plugins/admin/js/newsEdit.js
2025-07-15 16:07:54.500 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/public/plugins/admin/js/newsEdit.js (20023 bytes)
2025-07-15 16:07:58.087 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 16:08:22.179 [info] 'ViewTool' Tool called with path: website/ronglianweb/views/admin/newsEdit.jade and view_range: [105,120]
2025-07-15 16:08:38.980 [info] 'ViewTool' Tool called with path: website/ronglianweb/views/admin/newsEdit.jade and view_range: [173,198]
2025-07-15 16:08:52.486 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit.jade
2025-07-15 16:08:52.486 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit.jade (6108 bytes)
2025-07-15 16:08:54.664 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit.jade
2025-07-15 16:08:54.665 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit.jade (6214 bytes)
2025-07-15 16:08:57.503 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 16:08:58.277 [error] 'FuzzySymbolSearcher' Failed to read file tokens for d70e1391abe0781c3156234cb4e52c4ae7c76acd155f1a01db97a4a8411a6495: deleted
2025-07-15 16:09:37.228 [info] 'ViewTool' Tool called with path: website/ronglianweb/public/plugins/admin/js/newsEdit.js and view_range: [68,110]
2025-07-15 16:09:52.063 [info] 'ViewTool' Tool called with path: website/ronglianweb/public/plugins/admin/js/newsEdit.js and view_range: [104,150]
2025-07-15 16:10:06.890 [info] 'ViewTool' Tool called with path: website/ronglianweb/public/plugins/admin/js/newsEdit.js and view_range: [370,400]
2025-07-15 16:11:03.143 [info] 'ToolFileUtils' Reading file: website/ronglianweb/public/plugins/admin/js/newsEdit.js
2025-07-15 16:11:03.226 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/public/plugins/admin/js/newsEdit.js (20023 bytes)
2025-07-15 16:11:04.793 [info] 'ToolFileUtils' Reading file: website/ronglianweb/public/plugins/admin/js/newsEdit.js
2025-07-15 16:11:04.794 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/public/plugins/admin/js/newsEdit.js (20574 bytes)
2025-07-15 16:11:08.316 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 16:12:28.124 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 16:12:40.163 [info] 'ToolFileUtils' Reading file: website/ronglianweb/routes/index.js
2025-07-15 16:12:40.214 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/routes/index.js (5079 bytes)
2025-07-15 16:12:42.312 [info] 'ToolFileUtils' Reading file: website/ronglianweb/routes/index.js
2025-07-15 16:12:42.312 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/routes/index.js (5261 bytes)
2025-07-15 16:12:45.227 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 16:14:29.767 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1453.712428,"timestamp":"2025-07-15T08:14:29.708Z"}]
2025-07-15 16:14:52.510 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-35e9d3d4-87dd-493e-b4a5-6cb678936290.json'
2025-07-15 16:14:59.753 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1402.306164,"timestamp":"2025-07-15T08:14:59.658Z"}]
2025-07-15 16:24:30.188 [info] 'AugmentExtension' Retrieving model config
2025-07-15 16:24:32.244 [info] 'AugmentExtension' Retrieved model config
2025-07-15 16:24:32.244 [info] 'AugmentExtension' Returning model config
