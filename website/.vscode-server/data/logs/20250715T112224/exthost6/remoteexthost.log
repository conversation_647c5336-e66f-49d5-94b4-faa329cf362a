2025-07-15 17:18:06.062 [info] Extension host with pid 16535 started
2025-07-15 17:18:06.063 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/vscode.lock': Lock acquired.
2025-07-15 17:18:06.942 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: false, activationEvent: '*', root cause: vscode.git
2025-07-15 17:18:06.945 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-07-15 17:18:07.850 [info] ExtensionService#_doActivateExtension vscode.git, startup: false, activationEvent: '*'
2025-07-15 17:18:07.851 [info] ExtensionService#_doActivateExtension vscode.github, startup: false, activationEvent: '*'
2025-07-15 17:18:07.855 [info] ExtensionService#_doActivateExtension vscode.tunnel-forwarding, startup: false, activationEvent: 'onTunnel'
2025-07-15 17:18:08.117 [info] Eager extensions activated
2025-07-15 17:18:08.118 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-07-15 17:18:08.118 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-07-15 17:18:08.119 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-07-15 17:18:53.224 [info] ExtensionService#_doActivateExtension vscode.typescript-language-features, startup: false, activationEvent: 'onLanguage:javascript'
2025-07-15 17:18:53.308 [info] ExtensionService#_doActivateExtension vscode.extension-editing, startup: false, activationEvent: 'onLanguage:markdown'
2025-07-15 17:18:53.308 [info] ExtensionService#_doActivateExtension vscode.markdown-language-features, startup: false, activationEvent: 'onLanguage:markdown'
2025-07-15 17:18:53.360 [info] ExtensionService#_doActivateExtension vscode.html-language-features, startup: false, activationEvent: 'onLanguage:html'
2025-07-15 17:30:54.727 [info] Extension host terminating: renderer disconnected for too long (2)
2025-07-15 17:30:54.728 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/vscode.lock': Marking the lockfile as scheduled to be released in 6000 ms.
2025-07-15 17:30:55.063 [info] Extension host with pid 16535 exiting with code 0
