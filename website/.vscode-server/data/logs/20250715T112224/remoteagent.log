2025-07-15 11:22:24.562 [info] 




2025-07-15 11:22:24.928 [info] Extension host agent started.
2025-07-15 11:22:25.038 [info] [<unknown>][52208208][ManagementConnection] New connection established.
2025-07-15 11:22:25.039 [info] [<unknown>][daeeaaf4][ExtensionHostConnection] New connection established.
2025-07-15 11:22:25.272 [info] [<unknown>][daeeaaf4][ExtensionHostConnection] <4656> Launched Extension Host Process.
2025-07-15 11:22:25.595 [error] #1: https://main.vscode-cdn.net/extensions/marketplace.json - error GET AggregateError [ETIMEDOUT]: 
2025-07-15 11:22:29.542 [info] Getting Manifest... augment.vscode-augment
2025-07-15 11:22:29.543 [info] Getting Manifest... ms-ceintl.vscode-language-pack-zh-hans
2025-07-15 11:22:29.736 [info] Installing extension: ms-ceintl.vscode-language-pack-zh-hans {"productVersion":{"version":"1.102.0","date":"2025-07-09T22:10:34.600Z"},"pinned":false,"operation":3,"isApplicationScoped":true,"donotVerifySignature":false,"context":{"clientTargetPlatform":"linux-x64"},"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.vscode-server/extensions/extensions.json","external":"file:///home/<USER>/.vscode-server/extensions/extensions.json","path":"/home/<USER>/.vscode-server/extensions/extensions.json","scheme":"file"}}
2025-07-15 11:22:29.743 [info] Installing extension: augment.vscode-augment {"productVersion":{"version":"1.102.0","date":"2025-07-09T22:10:34.600Z"},"pinned":false,"operation":3,"isApplicationScoped":false,"donotVerifySignature":false,"context":{"clientTargetPlatform":"linux-x64"},"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.vscode-server/extensions/extensions.json","external":"file:///home/<USER>/.vscode-server/extensions/extensions.json","path":"/home/<USER>/.vscode-server/extensions/extensions.json","scheme":"file"}}
2025-07-15 11:22:33.176 [info] Extension signature verification result for ms-ceintl.vscode-language-pack-zh-hans: Success. Internal Code: 0. Executed: true. Duration: 2403ms.
2025-07-15 11:22:33.356 [info] Extracted extension to file:///home/<USER>/.vscode-server/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.102.2025070909: ms-ceintl.vscode-language-pack-zh-hans
2025-07-15 11:22:33.366 [info] Renamed to /home/<USER>/.vscode-server/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.102.2025070909
2025-07-15 11:22:33.380 [info] Marked extension as removed ms-ceintl.vscode-language-pack-zh-hans-1.101.2025061109
2025-07-15 11:22:33.381 [info] Adding language packs from the extension ms-ceintl.vscode-language-pack-zh-hans
2025-07-15 11:22:33.406 [info] Extension installed successfully: ms-ceintl.vscode-language-pack-zh-hans file:///home/<USER>/.vscode-server/extensions/extensions.json
2025-07-15 11:22:37.015 [info] Extension signature verification result for augment.vscode-augment: Success. Internal Code: 0. Executed: true. Duration: 3068ms.
2025-07-15 11:22:39.334 [info] Extracted extension to file:///home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.502.1: augment.vscode-augment
2025-07-15 11:22:39.432 [info] Renamed to /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.502.1
2025-07-15 11:22:39.443 [info] Extension installed successfully: augment.vscode-augment file:///home/<USER>/.vscode-server/extensions/extensions.json
2025-07-15 11:22:39.445 [info] Marked extension as removed augment.vscode-augment-0.496.1
2025-07-15 11:22:47.862 [info] [<unknown>][52208208][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
2025-07-15 11:22:48.130 [info] [<unknown>][daeeaaf4][ExtensionHostConnection] <4656> Extension Host Process exited with code: 0, signal: null.
2025-07-15 11:22:48.131 [info] Cancelling previous shutdown timeout
2025-07-15 11:22:48.131 [info] Last EH closed, waiting before shutting down
2025-07-15 11:22:51.184 [info] [<unknown>][79218b0f][ManagementConnection] New connection established.
2025-07-15 11:22:51.188 [info] [<unknown>][ee34f306][ExtensionHostConnection] New connection established.
2025-07-15 11:22:51.197 [info] [<unknown>][ee34f306][ExtensionHostConnection] <4990> Launched Extension Host Process.
2025-07-15 11:24:11.441 [info] [<unknown>][79218b0f][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
2025-07-15 11:24:11.721 [info] [<unknown>][ee34f306][ExtensionHostConnection] <4990> Extension Host Process exited with code: 0, signal: null.
2025-07-15 11:24:11.721 [info] Cancelling previous shutdown timeout
2025-07-15 11:24:11.722 [info] Last EH closed, waiting before shutting down
2025-07-15 11:24:14.054 [info] [<unknown>][449970dd][ManagementConnection] New connection established.
2025-07-15 11:24:14.058 [info] [<unknown>][b7983c3e][ExtensionHostConnection] New connection established.
2025-07-15 11:24:14.070 [info] [<unknown>][b7983c3e][ExtensionHostConnection] <5342> Launched Extension Host Process.
2025-07-15 11:24:23.817 [info] [<unknown>][449970dd][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
2025-07-15 11:24:23.906 [info] [<unknown>][b7983c3e][ExtensionHostConnection] <5342> Extension Host Process exited with code: 0, signal: null.
2025-07-15 11:24:23.906 [info] Cancelling previous shutdown timeout
2025-07-15 11:24:23.906 [info] Last EH closed, waiting before shutting down
2025-07-15 11:24:26.146 [info] [<unknown>][421b34e6][ManagementConnection] New connection established.
2025-07-15 11:24:26.150 [info] [<unknown>][db4abe42][ExtensionHostConnection] New connection established.
2025-07-15 11:24:26.161 [info] [<unknown>][db4abe42][ExtensionHostConnection] <5538> Launched Extension Host Process.
2025-07-15 11:29:23.907 [info] New EH opened, aborting shutdown
2025-07-15 17:16:03.530 [info] [<unknown>][421b34e6][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
2025-07-15 17:16:03.961 [info] [<unknown>][db4abe42][ExtensionHostConnection] <5538> Extension Host Process exited with code: 0, signal: null.
2025-07-15 17:16:03.961 [info] Last EH closed, waiting before shutting down
2025-07-15 17:16:28.766 [info] [<unknown>][84459092][ManagementConnection] New connection established.
2025-07-15 17:16:28.769 [info] [<unknown>][f3879862][ExtensionHostConnection] New connection established.
2025-07-15 17:16:28.781 [info] [<unknown>][f3879862][ExtensionHostConnection] <16101> Launched Extension Host Process.
2025-07-15 17:16:29.098 [error] #13: https://main.vscode-cdn.net/extensions/marketplace.json - error GET AggregateError [ETIMEDOUT]: 
2025-07-15 17:17:18.821 [info] [<unknown>][84459092][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
2025-07-15 17:17:18.827 [error] Error: Unexpected SIGPIPE
    at process.<anonymous> (file:///home/<USER>/.vscode-server/cli/servers/Stable-cb0c47c0cfaad0757385834bd89d410c78a856c0/server/out/server-main.js:184:1060)
    at process.emit (node:events:530:35)
2025-07-15 17:17:19.235 [info] [<unknown>][f3879862][ExtensionHostConnection] <16101> Extension Host Process exited with code: 0, signal: null.
2025-07-15 17:17:19.236 [info] Cancelling previous shutdown timeout
2025-07-15 17:17:19.236 [info] Last EH closed, waiting before shutting down
2025-07-15 17:18:05.019 [info] [<unknown>][6d1832e6][ManagementConnection] New connection established.
2025-07-15 17:18:05.023 [info] [<unknown>][5fc49cf7][ExtensionHostConnection] New connection established.
2025-07-15 17:18:05.034 [info] [<unknown>][5fc49cf7][ExtensionHostConnection] <16535> Launched Extension Host Process.
2025-07-15 17:21:55.066 [info] [<unknown>][6d1832e6][ManagementConnection] The client has disconnected, will wait for reconnection 3h before disposing...
2025-07-15 17:22:19.237 [info] New EH opened, aborting shutdown
2025-07-15 17:25:54.665 [info] [<unknown>][6d1832e6][ManagementConnection] Another client has connected, will shorten the wait for reconnection 5m before disposing...
2025-07-15 17:25:54.666 [info] [<unknown>][9b2377e4][ManagementConnection] New connection established.
2025-07-15 17:25:54.671 [info] [<unknown>][045e7c5e][ExtensionHostConnection] New connection established.
2025-07-15 17:25:54.682 [info] [<unknown>][045e7c5e][ExtensionHostConnection] <17121> Launched Extension Host Process.
2025-07-15 17:25:55.094 [error] #14: https://main.vscode-cdn.net/extensions/marketplace.json - error GET AggregateError [ETIMEDOUT]: 
2025-07-15 17:30:54.828 [info] [<unknown>][6d1832e6][ManagementConnection] The reconnection short grace time of 5m has expired, so the connection will be disposed.
2025-07-15 17:30:55.169 [info] [<unknown>][5fc49cf7][ExtensionHostConnection] <16535> Extension Host Process exited with code: 0, signal: null.
