2025-07-15 17:25:57.298 [info] [main] 日志级别: Info
2025-07-15 17:25:57.298 [info] [main] 正在验证在以下位置找到的 git: "git"
2025-07-15 17:25:57.298 [info] [main] 使用来自 "git" 的 git "2.43.0"
2025-07-15 17:25:57.298 [info] [Model][doInitialScan] Initial repository scan started
2025-07-15 17:25:57.298 [info] > git rev-parse --show-toplevel [5ms]
2025-07-15 17:25:57.298 [info] fatal: 不是 git 仓库（或者任何父目录）：.git
2025-07-15 17:25:57.298 [info] > git rev-parse --show-toplevel [3ms]
2025-07-15 17:25:57.298 [info] fatal: 不是 git 仓库（或者任何父目录）：.git
2025-07-15 17:25:57.298 [info] [Model][doInitialScan] Initial repository scan completed - repositories (0), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-07-15 17:26:25.564 [info] > git rev-parse --show-toplevel [6ms]
2025-07-15 17:26:25.564 [info] fatal: 不是 git 仓库（或者任何父目录）：.git
