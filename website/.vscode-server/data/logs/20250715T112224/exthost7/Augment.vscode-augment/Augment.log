2025-07-15 17:25:58.168 [info] 'AugmentConfigListener' settings parsed successfully
2025-07-15 17:25:58.168 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"disableFocusOnAugmentPanel":false,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-07-15 17:25:58.168 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","enableNewThreadsList":false,"enableUntruncatedContentStorage":false,"maxLinesTerminalProcessOutput":0,"vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","vscodeMinVersion":"1.96.0","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeSupportToolUseStartMinVersion":"","enableAgentAutoMode":false,"enableAgentSwarmMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5,"historySummaryMinVersion":"","historySummaryMaxChars":0,"historySummaryLowerChars":0,"historySummaryPrompt":"","enableCommitIndexing":false,"maxCommitsToIndex":0,"enableExchangeStorage":false,"conversationHistorySizeThresholdBytes":268435456,"enableToolUseStateStorage":false,"retryChatStreamTimeouts":false,"remoteAgentCurrentWorkspace":false,"enableMemoryRetrieval":false}
2025-07-15 17:25:58.168 [info] 'SidecarAnalytics' Segment analytics initialized for vscode
2025-07-15 17:25:58.193 [info] 'AugmentExtension' Retrieving model config
2025-07-15 17:25:59.238 [info] 'AugmentExtension' Retrieved model config
2025-07-15 17:25:59.238 [info] 'AugmentExtension' Returning model config
2025-07-15 17:25:59.327 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - enableNewThreadsList: false to true
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 49512
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - vscodeDirectApplyMinVersion: "" to "0.499.0"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - enableRules: false to true
  - memoriesTextEditorEnabled: false to true
  - openFileManagerV2Enabled: false to true
  - vscodeTaskListMinVersion: "" to "0.482.0"
  - vscodeSupportToolUseStartMinVersion: "" to "0.485.0"
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
  - historySummaryMaxChars: 0 to 200000
  - historySummaryLowerChars: 0 to 80000
  - historySummaryPrompt: "" to "Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.\nThis summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing with the conversation and supporting any continuing tasks.\n\nYour summary should be structured as follows:\nContext: The context to continue the conversation with. If applicable based on the current task, this should include:\n1. Previous Conversation: High level details about what was discussed throughout the entire conversation with the user. This should be written to allow someone to be able to follow the general overarching conversation flow.\n2. Current Work: Describe in detail what was being worked on prior to this request to summarize the conversation. Pay special attention to the more recent messages in the conversation.\n3. Key Technical Concepts: List all important technical concepts, technologies, coding conventions, and frameworks discussed, which might be relevant for continuing with this work.\n4. Relevant Files and Code: If applicable, enumerate specific files and code sections examined, modified, or created for the task continuation. Pay special attention to the most recent messages and changes.\n5. Problem Solving: Document problems solved thus far and any ongoing troubleshooting efforts.\n6. Pending Tasks and Next Steps: Outline all pending tasks that you have explicitly been asked to work on, as well as list the next steps you will take for all outstanding work, if applicable. Include code snippets where they add clarity. For any next steps, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no information loss in context between tasks.\n\nExample summary structure:\n1. Previous Conversation:\n[Detailed description]\n2. Current Work:\n[Detailed description]\n3. Key Technical Concepts:\n- [Concept 1]\n- [Concept 2]\n- [...]\n4. Relevant Files and Code:\n- [File Name 1]\n    - [Summary of why this file is important]\n    - [Summary of the changes made to this file, if any]\n    - [Important Code Snippet]\n- [File Name 2]\n    - [Important Code Snippet]\n- [...]\n5. Problem Solving:\n[Detailed description]\n6. Pending Tasks and Next Steps:\n- [Task 1 details & next steps]\n- [Task 2 details & next steps]\n- [...]\n\nOutput only the summary of the conversation so far, without any additional commentary or explanation.\n"
  - enableCommitIndexing: false to true
  - maxCommitsToIndex: 0 to 10000
  - retryChatStreamTimeouts: false to true
2025-07-15 17:25:59.327 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home (explicit) at 7/8/2025, 7:02:26 PM
2025-07-15 17:25:59.327 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-07-15 17:25:59.327 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-07-15 17:25:59.327 [info] 'SyncingPermissionTracker' Permission to sync folder /home granted at 7/8/2025, 7:02:26 PM; type = explicit
2025-07-15 17:25:59.327 [info] 'WorkspaceManager' Adding workspace folder home; folderRoot = /home; syncingPermission = granted
2025-07-15 17:25:59.327 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home (explicit) at 7/8/2025, 7:02:26 PM
2025-07-15 17:25:59.379 [info] 'MemoryWebviewMessageHandler' Memory webview message handler initialized
2025-07-15 17:25:59.379 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-07-15 17:25:59.379 [info] 'HotKeyHints' HotKeyHints initialized
2025-07-15 17:25:59.384 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-07-15 17:25:59.425 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-07-15 17:25:59.426 [info] 'ToolsModel' Host: localToolHost (10 tools: 178 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + git-commit-retrieval
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-15 17:26:00.149 [info] 'WorkspaceManager[home]' Start tracking
2025-07-15 17:26:00.159 [info] 'PathMap' Opened source folder /home with id 100
2025-07-15 17:26:00.159 [info] 'OpenFileManager' Opened source folder 100
2025-07-15 17:26:00.160 [info] 'MtimeCache[home]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938-1/Augment.vscode-augment/2cc974af6afc822c42a4e914df05c697348b80420941f6b27561b2bf688587b7/mtime-cache.json
2025-07-15 17:26:00.161 [info] 'MtimeCache[home]' no blob name cache found at /home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938-1/Augment.vscode-augment/2cc974af6afc822c42a4e914df05c697348b80420941f6b27561b2bf688587b7/mtime-cache.json (probably new source folder); error = ENOENT: no such file or directory, open '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938-1/Augment.vscode-augment/2cc974af6afc822c42a4e914df05c697348b80420941f6b27561b2bf688587b7/mtime-cache.json'
2025-07-15 17:26:01.235 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-15 17:26:01.235 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-15 17:26:07.850 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-07-15 17:26:07.850 [info] 'ToolsModel' Host: localToolHost (10 tools: 178 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + git-commit-retrieval
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-15 17:26:07.850 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-15 17:26:07.850 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-15 17:26:08.114 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-07-15 17:26:12.936 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-07-15 17:26:12.936 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-07-15 17:26:12.937 [info] 'ToolsModel' Saved chat mode: CHAT
2025-07-15 17:26:12.939 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-07-15 17:26:13.046 [info] 'ToolsModel' Saved chat mode: AGENT
2025-07-15 17:26:13.062 [info] 'TaskManager' Setting current root task UUID to 13c13a15-3067-42db-ad2f-b595728cc18c
2025-07-15 17:26:13.062 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-07-15 17:26:13.062 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-07-15 17:26:13.062 [info] 'ToolsModel' Host: localToolHost (10 tools: 178 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + git-commit-retrieval
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-15 17:26:14.197 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-15 17:26:14.198 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-15 17:26:19.331 [info] 'TaskManager' Setting current root task UUID to 811560d9-952d-4347-a4bd-5c9db732d1f9
2025-07-15 17:26:19.332 [info] 'TaskManager' Setting current root task UUID to 811560d9-952d-4347-a4bd-5c9db732d1f9
2025-07-15 17:26:19.335 [error] 'GitReferenceMessenger' Failed to locally get remote url: Failed to get remote url, no remote found
2025-07-15 17:26:25.543 [info] 'AugmentConfigListener' settings parsed successfully
2025-07-15 17:27:17.032 [error] 'ChatModel' Failed to load asset 42adb3f9237e7960d469520fb8aa6151b261a87bd74e1bc70d1bd47b01412d55.png
2025-07-15 17:27:17.032 [error] 'ChatModel' Failed to load asset ee1b1796d845aa627745a3246a1174d9eb23a4ac2f05e1161da89ee4acb4a666.png
2025-07-15 17:27:46.586 [error] 'ChatModel' Failed to load asset 42adb3f9237e7960d469520fb8aa6151b261a87bd74e1bc70d1bd47b01412d55.png
2025-07-15 17:27:46.587 [error] 'ChatModel' Failed to load asset ee1b1796d845aa627745a3246a1174d9eb23a4ac2f05e1161da89ee4acb4a666.png
2025-07-15 17:27:48.160 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938-1/Augment.vscode-augment/augment-user-assets/tool-configs/approval/auto
2025-07-15 17:27:48.164 [error] 'ChatModel' Failed to load asset 42adb3f9237e7960d469520fb8aa6151b261a87bd74e1bc70d1bd47b01412d55.png
2025-07-15 17:27:48.164 [error] 'ChatModel' Failed to load asset ee1b1796d845aa627745a3246a1174d9eb23a4ac2f05e1161da89ee4acb4a666.png
2025-07-15 17:27:48.176 [info] 'ViewTool' Tool called with path: website/ronglianweb/views/admin/newsEdit.jade and view_range: [162,180]
2025-07-15 17:27:48.829 [error] 'ChatModel' Failed to load asset 42adb3f9237e7960d469520fb8aa6151b261a87bd74e1bc70d1bd47b01412d55.png
2025-07-15 17:27:48.829 [error] 'ChatModel' Failed to load asset ee1b1796d845aa627745a3246a1174d9eb23a4ac2f05e1161da89ee4acb4a666.png
2025-07-15 17:28:08.305 [error] 'ChatModel' Failed to load asset 42adb3f9237e7960d469520fb8aa6151b261a87bd74e1bc70d1bd47b01412d55.png
2025-07-15 17:28:08.305 [error] 'ChatModel' Failed to load asset ee1b1796d845aa627745a3246a1174d9eb23a4ac2f05e1161da89ee4acb4a666.png
2025-07-15 17:28:08.315 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit.jade
2025-07-15 17:28:08.315 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit.jade (14093 bytes)
2025-07-15 17:28:09.719 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit.jade
2025-07-15 17:28:09.720 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit.jade (14186 bytes)
2025-07-15 17:28:13.339 [error] 'ChatModel' Failed to load asset 42adb3f9237e7960d469520fb8aa6151b261a87bd74e1bc70d1bd47b01412d55.png
2025-07-15 17:28:13.339 [error] 'ChatModel' Failed to load asset ee1b1796d845aa627745a3246a1174d9eb23a4ac2f05e1161da89ee4acb4a666.png
2025-07-15 17:28:13.358 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938-1/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-fefaea2d-5510-41aa-9f73-c2b27fdd942a.json'
2025-07-15 17:28:13.614 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938-1/Augment.vscode-augment/augment-user-assets/checkpoint-documents
2025-07-15 17:28:13.614 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938-1/Augment.vscode-augment/augment-user-assets/checkpoint-documents/647c5336-e66f-49d5-94b4-faa329cf362a
2025-07-15 17:28:27.771 [error] 'ChatModel' Failed to load asset 42adb3f9237e7960d469520fb8aa6151b261a87bd74e1bc70d1bd47b01412d55.png
2025-07-15 17:28:27.771 [error] 'ChatModel' Failed to load asset ee1b1796d845aa627745a3246a1174d9eb23a4ac2f05e1161da89ee4acb4a666.png
2025-07-15 17:28:27.783 [info] 'ViewTool' Tool called with path: website/ronglianweb/public/plugins/admin/js/newsEdit.js and view_range: undefined
2025-07-15 17:28:28.732 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/logs/20250715T112224/exthost7/vscode.typescript-language-features
2025-07-15 17:28:29.005 [error] 'ChatModel' Failed to load asset 42adb3f9237e7960d469520fb8aa6151b261a87bd74e1bc70d1bd47b01412d55.png
2025-07-15 17:28:29.005 [error] 'ChatModel' Failed to load asset ee1b1796d845aa627745a3246a1174d9eb23a4ac2f05e1161da89ee4acb4a666.png
2025-07-15 17:28:42.688 [error] 'ChatModel' Failed to load asset 42adb3f9237e7960d469520fb8aa6151b261a87bd74e1bc70d1bd47b01412d55.png
2025-07-15 17:28:42.688 [error] 'ChatModel' Failed to load asset ee1b1796d845aa627745a3246a1174d9eb23a4ac2f05e1161da89ee4acb4a666.png
2025-07-15 17:28:42.699 [info] 'ViewTool' Tool called with path: website/ronglianweb/public/plugins/admin/js/newsEdit.js and view_range: [338,380]
2025-07-15 17:28:43.335 [error] 'ChatModel' Failed to load asset 42adb3f9237e7960d469520fb8aa6151b261a87bd74e1bc70d1bd47b01412d55.png
2025-07-15 17:28:43.335 [error] 'ChatModel' Failed to load asset ee1b1796d845aa627745a3246a1174d9eb23a4ac2f05e1161da89ee4acb4a666.png
2025-07-15 17:28:56.784 [error] 'ChatModel' Failed to load asset 42adb3f9237e7960d469520fb8aa6151b261a87bd74e1bc70d1bd47b01412d55.png
2025-07-15 17:28:56.784 [error] 'ChatModel' Failed to load asset ee1b1796d845aa627745a3246a1174d9eb23a4ac2f05e1161da89ee4acb4a666.png
2025-07-15 17:28:56.798 [info] 'ViewTool' Tool called with path: website/ronglianweb/public/plugins/admin/js/newsEdit.js and view_range: undefined
2025-07-15 17:28:57.314 [error] 'ChatModel' Failed to load asset 42adb3f9237e7960d469520fb8aa6151b261a87bd74e1bc70d1bd47b01412d55.png
2025-07-15 17:28:57.314 [error] 'ChatModel' Failed to load asset ee1b1796d845aa627745a3246a1174d9eb23a4ac2f05e1161da89ee4acb4a666.png
2025-07-15 17:29:42.485 [error] 'ChatModel' Failed to load asset 42adb3f9237e7960d469520fb8aa6151b261a87bd74e1bc70d1bd47b01412d55.png
2025-07-15 17:29:42.485 [error] 'ChatModel' Failed to load asset ee1b1796d845aa627745a3246a1174d9eb23a4ac2f05e1161da89ee4acb4a666.png
2025-07-15 17:29:42.496 [info] 'ToolFileUtils' Reading file: website/ronglianweb/public/plugins/admin/js/newsEdit.js
2025-07-15 17:29:42.497 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/public/plugins/admin/js/newsEdit.js (24288 bytes)
2025-07-15 17:29:44.079 [info] 'ToolFileUtils' Reading file: website/ronglianweb/public/plugins/admin/js/newsEdit.js
2025-07-15 17:29:44.079 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/public/plugins/admin/js/newsEdit.js (25200 bytes)
2025-07-15 17:29:44.731 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/3c/de
2025-07-15 17:29:47.510 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938-1/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-fefaea2d-5510-41aa-9f73-c2b27fdd942a.json'
2025-07-15 17:29:47.517 [error] 'ChatModel' Failed to load asset 42adb3f9237e7960d469520fb8aa6151b261a87bd74e1bc70d1bd47b01412d55.png
2025-07-15 17:29:47.518 [error] 'ChatModel' Failed to load asset ee1b1796d845aa627745a3246a1174d9eb23a4ac2f05e1161da89ee4acb4a666.png
2025-07-15 17:30:42.827 [error] 'ChatModel' Failed to load asset 42adb3f9237e7960d469520fb8aa6151b261a87bd74e1bc70d1bd47b01412d55.png
2025-07-15 17:30:42.827 [error] 'ChatModel' Failed to load asset ee1b1796d845aa627745a3246a1174d9eb23a4ac2f05e1161da89ee4acb4a666.png
2025-07-15 17:30:42.838 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit.jade
2025-07-15 17:30:42.838 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit.jade (14186 bytes)
2025-07-15 17:30:44.278 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit.jade
2025-07-15 17:30:44.279 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit.jade (16345 bytes)
2025-07-15 17:30:47.783 [error] 'ChatModel' Failed to load asset 42adb3f9237e7960d469520fb8aa6151b261a87bd74e1bc70d1bd47b01412d55.png
2025-07-15 17:30:47.783 [error] 'ChatModel' Failed to load asset ee1b1796d845aa627745a3246a1174d9eb23a4ac2f05e1161da89ee4acb4a666.png
2025-07-15 17:30:47.845 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938-1/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-fefaea2d-5510-41aa-9f73-c2b27fdd942a.json'
2025-07-15 17:31:13.884 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1027.082372,"timestamp":"2025-07-15T09:31:13.884Z"}]
2025-07-15 17:31:22.547 [error] 'ChatModel' Failed to load asset 42adb3f9237e7960d469520fb8aa6151b261a87bd74e1bc70d1bd47b01412d55.png
2025-07-15 17:31:22.547 [error] 'ChatModel' Failed to load asset ee1b1796d845aa627745a3246a1174d9eb23a4ac2f05e1161da89ee4acb4a666.png
2025-07-15 17:31:22.556 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit.jade
2025-07-15 17:31:22.626 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit.jade (16345 bytes)
2025-07-15 17:31:23.999 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit.jade
2025-07-15 17:31:24.000 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit.jade (16621 bytes)
2025-07-15 17:31:27.637 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938-1/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-fefaea2d-5510-41aa-9f73-c2b27fdd942a.json'
2025-07-15 17:31:27.681 [error] 'ChatModel' Failed to load asset 42adb3f9237e7960d469520fb8aa6151b261a87bd74e1bc70d1bd47b01412d55.png
2025-07-15 17:31:27.681 [error] 'ChatModel' Failed to load asset ee1b1796d845aa627745a3246a1174d9eb23a4ac2f05e1161da89ee4acb4a666.png
2025-07-15 17:31:27.851 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 112033bf427796f0404ba5995b3cb391fdcad17a54957071afc08e2dce7d9bc6: deleted
2025-07-15 17:31:43.990 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1003.507047,"timestamp":"2025-07-15T09:31:43.917Z"}]
2025-07-15 17:31:53.444 [error] 'ChatModel' Failed to load asset 42adb3f9237e7960d469520fb8aa6151b261a87bd74e1bc70d1bd47b01412d55.png
2025-07-15 17:31:53.445 [error] 'ChatModel' Failed to load asset ee1b1796d845aa627745a3246a1174d9eb23a4ac2f05e1161da89ee4acb4a666.png
2025-07-15 17:31:53.456 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit.jade
2025-07-15 17:31:53.457 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit.jade (16621 bytes)
2025-07-15 17:31:54.703 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/admin/newsEdit.jade
2025-07-15 17:31:54.703 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/admin/newsEdit.jade (16726 bytes)
2025-07-15 17:31:58.467 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938-1/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-fefaea2d-5510-41aa-9f73-c2b27fdd942a.json'
2025-07-15 17:31:59.349 [error] 'ChatModel' Failed to load asset 42adb3f9237e7960d469520fb8aa6151b261a87bd74e1bc70d1bd47b01412d55.png
2025-07-15 17:31:59.349 [error] 'ChatModel' Failed to load asset ee1b1796d845aa627745a3246a1174d9eb23a4ac2f05e1161da89ee4acb4a666.png
2025-07-15 17:32:29.454 [error] 'ChatModel' Failed to load asset 42adb3f9237e7960d469520fb8aa6151b261a87bd74e1bc70d1bd47b01412d55.png
2025-07-15 17:32:29.454 [error] 'ChatModel' Failed to load asset ee1b1796d845aa627745a3246a1174d9eb23a4ac2f05e1161da89ee4acb4a666.png
2025-07-15 17:32:35.808 [error] 'ChatModel' Failed to load asset 42adb3f9237e7960d469520fb8aa6151b261a87bd74e1bc70d1bd47b01412d55.png
2025-07-15 17:32:35.809 [error] 'ChatModel' Failed to load asset ee1b1796d845aa627745a3246a1174d9eb23a4ac2f05e1161da89ee4acb4a666.png
2025-07-15 17:32:44.114 [info] 'WorkspaceManager[home]' Tracking enabled
2025-07-15 17:32:44.114 [info] 'WorkspaceManager[home]' Path metrics:
  - directories emitted: 20892
  - files emitted: 144840
  - other paths emitted: 1230
  - total paths emitted: 166962
  - timing stats:
    - readDir: 494 ms
    - filter: 4199 ms
    - yield: 767 ms
    - total: 6083 ms
2025-07-15 17:32:44.114 [info] 'WorkspaceManager[home]' File metrics:
  - paths accepted: 96007
  - paths not accessible: 0
  - not plain files: 0
  - large files: 2192
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 0
  - mtime cache misses: 96007
  - probe batches: 136
  - blob names probed: 96094
  - files read: 143488
  - blobs uploaded: 79
  - timing stats:
    - ingestPath: 209 ms
    - probe: 310987 ms
    - stat: 2541 ms
    - read: 115859 ms
    - upload: 19414 ms
2025-07-15 17:32:44.114 [info] 'WorkspaceManager[home]' Startup metrics:
  - create SourceFolder: 12 ms
  - read MtimeCache: 1 ms
  - pre-populate PathMap: 0 ms
  - create PathFilter: 1474 ms
  - create PathNotifier: 1 ms
  - enumerate paths: 6087 ms
  - purge stale PathMap entries: 17 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 396276 ms
  - enable persist: 97 ms
  - total: 403965 ms
2025-07-15 17:32:44.115 [info] 'WorkspaceManager' Workspace startup complete in 404814 ms
2025-07-15 17:32:44.434 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938-1/Augment.vscode-augment/2cc974af6afc822c42a4e914df05c697348b80420941f6b27561b2bf688587b7
2025-07-15 17:33:22.547 [error] 'ChatModel' Failed to load asset 42adb3f9237e7960d469520fb8aa6151b261a87bd74e1bc70d1bd47b01412d55.png
2025-07-15 17:33:22.547 [error] 'ChatModel' Failed to load asset ee1b1796d845aa627745a3246a1174d9eb23a4ac2f05e1161da89ee4acb4a666.png
2025-07-15 17:33:23.457 [error] 'ChatModel' Failed to load asset 42adb3f9237e7960d469520fb8aa6151b261a87bd74e1bc70d1bd47b01412d55.png
2025-07-15 17:33:23.457 [error] 'ChatModel' Failed to load asset ee1b1796d845aa627745a3246a1174d9eb23a4ac2f05e1161da89ee4acb4a666.png
2025-07-15 17:33:23.843 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/logs/20250715T112224/exthost7/output_logging_20250715T172555
2025-07-15 17:33:27.626 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, lstat '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938-1/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-fefaea2d-5510-41aa-9f73-c2b27fdd942a.json'
2025-07-15 17:33:48.414 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1223.852675,"timestamp":"2025-07-15T09:33:48.353Z"}]
