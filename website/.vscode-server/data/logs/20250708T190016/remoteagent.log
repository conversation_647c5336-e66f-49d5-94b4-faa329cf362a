2025-07-08 19:00:16.795 [info] 




2025-07-08 19:00:16.796 [info] Extension host agent started.
2025-07-08 19:00:16.817 [info] Started initializing default profile extensions in extensions installation folder. file:///home/<USER>/.vscode-server/extensions
2025-07-08 19:00:16.843 [info] ComputeTargetPlatform: linux-x64
2025-07-08 19:00:16.846 [info] Completed initializing default profile extensions in extensions installation folder. file:///home/<USER>/.vscode-server/extensions
2025-07-08 19:00:16.873 [info] [<unknown>][861fbb2a][ManagementConnection] New connection established.
2025-07-08 19:00:16.877 [info] [<unknown>][d31b01e9][ExtensionHostConnection] New connection established.
2025-07-08 19:00:16.950 [info] Installing extensions...
2025-07-08 19:00:17.029 [info] [<unknown>][d31b01e9][ExtensionHostConnection] <174527> Launched Extension Host Process.
2025-07-08 19:00:17.222 [info] ComputeTargetPlatform: linux-x64
2025-07-08 19:00:18.289 [info] Installing extension 'ms-ceintl.vscode-language-pack-zh-hans'...
2025-07-08 19:00:19.144 [info] Getting Manifest... ms-ceintl.vscode-language-pack-zh-hans
2025-07-08 19:00:19.219 [info] Installing extension: ms-ceintl.vscode-language-pack-zh-hans {"isMachineScoped":true,"isBuiltin":false,"installGivenVersion":false,"isApplicationScoped":true,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.vscode-server/extensions/extensions.json","external":"file:///home/<USER>/.vscode-server/extensions/extensions.json","path":"/home/<USER>/.vscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.101.2","date":"2025-06-24T20:27:15.391Z"}}
2025-07-08 19:00:23.923 [info] Extension signature verification result for ms-ceintl.vscode-language-pack-zh-hans: Success. Internal Code: 0. Executed: true. Duration: 3412ms.
2025-07-08 19:00:24.120 [info] Extracted extension to file:///home/<USER>/.vscode-server/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.101.2025061109: ms-ceintl.vscode-language-pack-zh-hans
2025-07-08 19:00:24.134 [info] Renamed to /home/<USER>/.vscode-server/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.101.2025061109
2025-07-08 19:00:24.144 [info] Adding language packs from the extension ms-ceintl.vscode-language-pack-zh-hans
2025-07-08 19:00:24.168 [info] Extension installed successfully: ms-ceintl.vscode-language-pack-zh-hans file:///home/<USER>/.vscode-server/extensions/extensions.json
2025-07-08 19:00:24.169 [info] Extension 'ms-ceintl.vscode-language-pack-zh-hans' v1.101.2025061109 was successfully installed.
2025-07-08 19:01:05.824 [info] [<unknown>][861fbb2a][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
2025-07-08 19:01:05.893 [info] [<unknown>][d31b01e9][ExtensionHostConnection] <174527> Extension Host Process exited with code: 0, signal: null.
2025-07-08 19:01:05.894 [info] Cancelling previous shutdown timeout
2025-07-08 19:01:05.894 [info] Last EH closed, waiting before shutting down
2025-07-08 19:01:08.214 [info] [<unknown>][e74eec62][ManagementConnection] New connection established.
2025-07-08 19:01:08.220 [info] [<unknown>][729e1c0f][ExtensionHostConnection] New connection established.
2025-07-08 19:01:08.227 [info] [<unknown>][729e1c0f][ExtensionHostConnection] <174623> Launched Extension Host Process.
2025-07-08 19:01:55.874 [info] Getting Manifest... augment.vscode-augment
2025-07-08 19:01:56.903 [info] Installing extension: augment.vscode-augment {"installPreReleaseVersion":false,"donotVerifySignature":false,"context":{"clientTargetPlatform":"linux-x64"},"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.vscode-server/extensions/extensions.json","external":"file:///home/<USER>/.vscode-server/extensions/extensions.json","path":"/home/<USER>/.vscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.101.2","date":"2025-06-24T20:27:15.391Z"}}
2025-07-08 19:02:01.445 [info] [<unknown>][e74eec62][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
2025-07-08 19:02:01.518 [info] [<unknown>][729e1c0f][ExtensionHostConnection] <174623> Extension Host Process exited with code: 0, signal: null.
2025-07-08 19:02:01.518 [info] Cancelling previous shutdown timeout
2025-07-08 19:02:01.519 [info] Last EH closed, waiting before shutting down
2025-07-08 19:02:01.795 [info] Extension signature verification result for augment.vscode-augment: Success. Internal Code: 0. Executed: true. Duration: 2301ms.
2025-07-08 19:02:04.097 [info] Extracted extension to file:///home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.496.1: augment.vscode-augment
2025-07-08 19:02:04.181 [info] Renamed to /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.496.1
2025-07-08 19:02:04.195 [info] Extension installed successfully: augment.vscode-augment file:///home/<USER>/.vscode-server/extensions/extensions.json
2025-07-08 19:02:04.485 [info] [<unknown>][f4396656][ManagementConnection] New connection established.
2025-07-08 19:02:04.496 [info] [<unknown>][207c43e0][ExtensionHostConnection] New connection established.
2025-07-08 19:02:04.506 [info] [<unknown>][207c43e0][ExtensionHostConnection] <174821> Launched Extension Host Process.
2025-07-08 19:07:01.520 [info] New EH opened, aborting shutdown
2025-07-08 19:50:14.038 [error] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
2025-07-08 19:50:14.191 [error] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
2025-07-08 19:50:16.543 [error] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
2025-07-08 19:50:22.642 [error] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
2025-07-08 19:55:27.818 [error] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
2025-07-08 20:02:49.585 [error] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
2025-07-08 20:08:54.286 [error] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
