2025-07-08 19:02:08.308 [info] 'AugmentConfigListener' settings parsed successfully
2025-07-08 19:02:08.308 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"disableFocusOnAugmentPanel":false,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-07-08 19:02:08.308 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","enableNewThreadsList":false,"enableUntruncatedContentStorage":false,"maxLinesTerminalProcessOutput":0,"vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeSupportToolUseStartMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5,"historySummaryMinVersion":"","historySummaryMaxChars":0,"historySummaryLowerChars":0,"historySummaryPrompt":"","enableSpawnSubAgentTool":false,"enableCommitIndexing":false,"maxCommitsToIndex":0,"enableExchangeStorage":false,"conversationHistorySizeThresholdBytes":268435456,"enableToolUseStateStorage":false,"retryChatStreamTimeouts":false}
2025-07-08 19:02:08.308 [info] 'SidecarAnalytics' Segment analytics initialized for vscode
2025-07-08 19:02:08.359 [info] 'AugmentExtension' Retrieving model config
2025-07-08 19:02:10.209 [info] 'AugmentExtension' Retrieved model config
2025-07-08 19:02:10.209 [info] 'AugmentExtension' Returning model config
2025-07-08 19:02:10.259 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - enableNewThreadsList: false to true
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 49512
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - enableRules: false to true
  - memoriesTextEditorEnabled: false to true
  - openFileManagerV2Enabled: false to true
  - vscodeTaskListMinVersion: "" to "0.482.0"
  - vscodeSupportToolUseStartMinVersion: "" to "0.485.0"
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
  - historySummaryMaxChars: 0 to 200000
  - historySummaryLowerChars: 0 to 80000
  - historySummaryPrompt: "" to "Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.\nThis summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing with the conversation and supporting any continuing tasks.\n\nYour summary should be structured as follows:\nContext: The context to continue the conversation with. If applicable based on the current task, this should include:\n1. Previous Conversation: High level details about what was discussed throughout the entire conversation with the user. This should be written to allow someone to be able to follow the general overarching conversation flow.\n2. Current Work: Describe in detail what was being worked on prior to this request to summarize the conversation. Pay special attention to the more recent messages in the conversation.\n3. Key Technical Concepts: List all important technical concepts, technologies, coding conventions, and frameworks discussed, which might be relevant for continuing with this work.\n4. Relevant Files and Code: If applicable, enumerate specific files and code sections examined, modified, or created for the task continuation. Pay special attention to the most recent messages and changes.\n5. Problem Solving: Document problems solved thus far and any ongoing troubleshooting efforts.\n6. Pending Tasks and Next Steps: Outline all pending tasks that you have explicitly been asked to work on, as well as list the next steps you will take for all outstanding work, if applicable. Include code snippets where they add clarity. For any next steps, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no information loss in context between tasks.\n\nExample summary structure:\n1. Previous Conversation:\n[Detailed description]\n2. Current Work:\n[Detailed description]\n3. Key Technical Concepts:\n- [Concept 1]\n- [Concept 2]\n- [...]\n4. Relevant Files and Code:\n- [File Name 1]\n    - [Summary of why this file is important]\n    - [Summary of the changes made to this file, if any]\n    - [Important Code Snippet]\n- [File Name 2]\n    - [Important Code Snippet]\n- [...]\n5. Problem Solving:\n[Detailed description]\n6. Pending Tasks and Next Steps:\n- [Task 1 details & next steps]\n- [Task 2 details & next steps]\n- [...]\n\nOutput only the summary of the conversation so far, without any additional commentary or explanation.\n"
  - retryChatStreamTimeouts: false to true
2025-07-08 19:02:10.260 [info] 'SyncingPermissionTracker' Initial syncing permission: undefined
2025-07-08 19:02:10.260 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-07-08 19:02:10.260 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-07-08 19:02:10.260 [info] 'SyncingPermissionTracker' Permission to sync folder /home unknown: no permission information recorded
2025-07-08 19:02:10.260 [info] 'WorkspaceManager' Adding workspace folder home; folderRoot = /home; syncingPermission = unknown
2025-07-08 19:02:10.372 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-07-08 19:02:10.372 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-07-08 19:02:10.372 [info] 'HotKeyHints' HotKeyHints initialized
2025-07-08 19:02:10.378 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-07-08 19:02:10.422 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-07-08 19:02:10.423 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-08 19:02:10.645 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-07-08 19:02:10.645 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-07-08 19:02:10.645 [info] 'ToolsModel' Saved chat mode: CHAT
2025-07-08 19:02:10.646 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-07-08 19:02:11.005 [info] 'WorkspaceManager' Beginning full qualification of source folder /home
2025-07-08 19:02:11.254 [info] 'DynamicLevelKvStore' Ensuring LevelDB is initialized
2025-07-08 19:02:11.254 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-07-08 19:02:11.254 [info] 'ToolsModel' Saved chat mode: AGENT
2025-07-08 19:02:11.254 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-07-08 19:02:11.255 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-08 19:02:11.423 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-07-08 19:02:11.431 [error] 'GitReferenceMessenger' Failed to locally get remote url: Failed to get remote url, no remote found
2025-07-08 19:02:12.070 [info] 'TaskManager' Setting current root task UUID to 96eb91d7-beed-49fe-83e8-80ef2b737056
2025-07-08 19:02:12.070 [info] 'TaskManager' Setting current root task UUID to 96eb91d7-beed-49fe-83e8-80ef2b737056
2025-07-08 19:02:12.070 [info] 'TaskManager' Setting current root task UUID to b21eccd0-9f2e-4182-b5fe-b0b94700bced
2025-07-08 19:02:12.070 [info] 'TaskManager' Setting current root task UUID to b21eccd0-9f2e-4182-b5fe-b0b94700bced
2025-07-08 19:02:12.070 [info] 'TaskManager' Setting current root task UUID to 55cf731f-169c-42f6-93ca-71bae41d7cea
2025-07-08 19:02:12.070 [info] 'TaskManager' Setting current root task UUID to 55cf731f-169c-42f6-93ca-71bae41d7cea
2025-07-08 19:02:12.633 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1753.276669,"timestamp":"2025-07-08T11:02:12.397Z"}]
2025-07-08 19:02:12.836 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-08 19:02:12.836 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-08 19:02:13.656 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-08 19:02:13.657 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-08 19:02:13.765 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-07-08 19:02:13.765 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-08 19:02:13.766 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-08 19:02:13.766 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-08 19:02:16.921 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-93cf9fdf-edb4-4288-bd96-203d080bb2aa.json'
2025-07-08 19:02:21.867 [info] 'WorkspaceManager' Finished full qualification of source folder /home: trackable files: 64346, uploaded fraction: 0, is repo: false
2025-07-08 19:02:21.867 [info] 'WorkspaceManager' Requesting syncing permission because source folder does not appear to be a source repo
2025-07-08 19:02:21.882 [info] 'AwaitingSyncingPermissionApp' Registering AwaitingSyncingPermissionApp
2025-07-08 19:02:26.592 [info] 'AwaitingSyncingPermissionApp' User granted syncing permission
2025-07-08 19:02:26.592 [info] 'WorkspaceManager' Enabling syncing for all trackable source folders
2025-07-08 19:02:26.592 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home (explicit) at 7/8/2025, 7:02:26 PM
2025-07-08 19:02:26.613 [info] 'WorkspaceManager[home]' Start tracking
2025-07-08 19:02:26.618 [info] 'PathMap' Opened source folder /home with id 100
2025-07-08 19:02:26.618 [info] 'OpenFileManager' Opened source folder 100
2025-07-08 19:02:26.621 [info] 'MtimeCache[home]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/2cc974af6afc822c42a4e914df05c697348b80420941f6b27561b2bf688587b7/mtime-cache.json
2025-07-08 19:02:26.621 [info] 'MtimeCache[home]' no blob name cache found at /home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/2cc974af6afc822c42a4e914df05c697348b80420941f6b27561b2bf688587b7/mtime-cache.json (probably new source folder); error = ENOENT: no such file or directory, open '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/2cc974af6afc822c42a4e914df05c697348b80420941f6b27561b2bf688587b7/mtime-cache.json'
2025-07-08 19:02:29.397 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-07-08 19:02:29.397 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-07-08 19:02:29.476 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-07-08 19:02:29.476 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-07-08 19:02:29.476 [info] 'ToolsModel' Saved chat mode: CHAT
2025-07-08 19:02:29.476 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-07-08 19:02:29.528 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-07-08 19:02:29.633 [info] 'ToolsModel' Saved chat mode: AGENT
2025-07-08 19:02:29.633 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-07-08 19:02:29.633 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-07-08 19:02:29.633 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-08 19:02:29.637 [info] 'TaskManager' Setting current root task UUID to 38542df2-c2d4-4026-87fd-a9149176d2c0
2025-07-08 19:02:29.637 [info] 'TaskManager' Setting current root task UUID to 38542df2-c2d4-4026-87fd-a9149176d2c0
2025-07-08 19:02:29.639 [error] 'GitReferenceMessenger' Failed to locally get remote url: Failed to get remote url, no remote found
2025-07-08 19:02:29.715 [info] 'TaskManager' Setting current root task UUID to b680f0ba-9aa8-456f-8f7c-528a72393806
2025-07-08 19:02:29.715 [info] 'TaskManager' Setting current root task UUID to b680f0ba-9aa8-456f-8f7c-528a72393806
2025-07-08 19:02:29.715 [info] 'TaskManager' Setting current root task UUID to a4aff489-84dd-4180-82df-c85ca87a08b3
2025-07-08 19:02:29.715 [info] 'TaskManager' Setting current root task UUID to a4aff489-84dd-4180-82df-c85ca87a08b3
2025-07-08 19:02:31.055 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-08 19:02:31.056 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-08 19:03:01.133 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1644.355308,"timestamp":"2025-07-08T11:03:01.079Z"}]
2025-07-08 19:05:33.382 [error] 'AugmentExtension' API request 840540a6-4c98-4182-8b77-6b511bd841a9 to https://i0.api.augmentcode.com/github/list-repos response 500: Internal Server Error
2025-07-08 19:05:33.892 [error] 'AugmentExtension' API request 13ce1665-c478-4b02-905c-22ce5a75c542 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-08 19:05:33.892 [error] 'AugmentExtension' Dropping error report "github/list-repos call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-08 19:05:33.894 [error] 'GitReferenceMessenger' Failed to list user repos: HTTP error: 500 Internal Server Error
2025-07-08 19:05:33.912 [info] 'StallDetector' Recent work: [{"name":"list-github-repos-for-authenticated-user-request","durationMs":1391.747617,"timestamp":"2025-07-08T11:05:33.894Z"}]
2025-07-08 19:05:48.646 [info] 'StallDetector' Recent work: [{"name":"authenticate-github-request","durationMs":1149.363092,"timestamp":"2025-07-08T11:05:48.604Z"}]
2025-07-08 19:05:54.811 [info] 'StallDetector' Recent work: [{"name":"is-github-authenticated-request","durationMs":1145.191749,"timestamp":"2025-07-08T11:05:54.809Z"}]
2025-07-08 19:06:34.919 [info] 'ToolsModel' Saved chat mode: CHAT
2025-07-08 19:06:34.920 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-07-08 19:06:34.920 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-07-08 19:06:35.118 [info] 'TaskManager' Setting current root task UUID to dda89d88-a5bc-4097-89c2-95bd77b9a006
2025-07-08 19:06:35.118 [info] 'TaskManager' Setting current root task UUID to dda89d88-a5bc-4097-89c2-95bd77b9a006
2025-07-08 19:06:35.758 [error] 'AugmentExtension' API request 62acf08a-172f-4d45-8a90-881403ed07b6 to https://i0.api.augmentcode.com/github/list-repos response 500: Internal Server Error
2025-07-08 19:06:36.266 [error] 'AugmentExtension' API request 207ecb0e-b0f1-4678-9e72-f0193c84a363 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-08 19:06:36.266 [error] 'AugmentExtension' Dropping error report "github/list-repos call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-08 19:06:36.267 [error] 'GitReferenceMessenger' Failed to list user repos: HTTP error: 500 Internal Server Error
2025-07-08 19:06:36.418 [info] 'StallDetector' Recent work: [{"name":"list-github-repos-for-authenticated-user-request","durationMs":1295.701499,"timestamp":"2025-07-08T11:06:36.267Z"}]
2025-07-08 19:06:59.448 [error] 'AugmentExtension' API request 7fac86a2-e4ca-4a7e-b3d2-8244d178c3ae to https://i0.api.augmentcode.com/subscription-info failed: This operation was aborted
2025-07-08 19:07:00.173 [error] 'AugmentExtension' API request 0102a97b-ba06-44f9-aafb-7e6d9bd94951 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-08 19:07:00.173 [error] 'AugmentExtension' Dropping error report "subscription-info call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-08 19:07:00.173 [error] 'ChatApp' Failed to get subscription info: Error: This operation was aborted
2025-07-08 19:07:00.313 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":30761.251619,"timestamp":"2025-07-08T11:07:00.173Z"}]
2025-07-08 19:07:00.547 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1011.789202,"timestamp":"2025-07-08T11:07:00.423Z"}]
2025-07-08 19:10:30.719 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1156.153277,"timestamp":"2025-07-08T11:10:30.567Z"}]
2025-07-08 19:14:00.630 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1152.486946,"timestamp":"2025-07-08T11:14:00.562Z"}]
2025-07-08 19:14:19.363 [error] 'AugmentExtension' API request ac6fc78b-6db2-4c89-8746-a92f1000d012 to https://i0.api.augmentcode.com/checkpoint-blobs failed: This operation was aborted
2025-07-08 19:14:19.990 [error] 'AugmentExtension' API request 7a39856f-4d8c-418e-9ce7-f442c36d2316 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-08 19:14:19.990 [error] 'AugmentExtension' Dropping error report "checkpoint-blobs call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-08 19:14:19.991 [error] 'BlobsCheckpointManager' checkpoint-blobs failed with error: This operation was aborted.
2025-07-08 19:18:00.657 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1151.137548,"timestamp":"2025-07-08T11:18:00.578Z"}]
2025-07-08 19:18:30.970 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1374.922569,"timestamp":"2025-07-08T11:18:30.784Z"}]
2025-07-08 19:19:00.815 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1369.254619,"timestamp":"2025-07-08T11:19:00.780Z"}]
2025-07-08 19:21:00.741 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1326.280892,"timestamp":"2025-07-08T11:21:00.741Z"}]
2025-07-08 19:21:31.204 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1758.385028,"timestamp":"2025-07-08T11:21:31.174Z"}]
2025-07-08 19:22:01.077 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1561.882784,"timestamp":"2025-07-08T11:22:00.978Z"}]
2025-07-08 19:24:00.472 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1053.394045,"timestamp":"2025-07-08T11:24:00.472Z"}]
2025-07-08 19:29:30.495 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1063.240188,"timestamp":"2025-07-08T11:29:30.488Z"}]
2025-07-08 19:32:08.285 [info] 'AugmentExtension' Retrieving model config
2025-07-08 19:32:09.897 [info] 'AugmentExtension' Retrieved model config
2025-07-08 19:32:09.897 [info] 'AugmentExtension' Returning model config
2025-07-08 19:33:00.578 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1150.938049,"timestamp":"2025-07-08T11:33:00.578Z"}]
2025-07-08 19:33:30.796 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1305.45627,"timestamp":"2025-07-08T11:33:30.732Z"}]
2025-07-08 19:35:30.613 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1156.671247,"timestamp":"2025-07-08T11:35:30.585Z"}]
2025-07-08 19:37:30.849 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1247.41781,"timestamp":"2025-07-08T11:37:30.752Z"}]
2025-07-08 19:37:41.495 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/logs/20250708T190016/exthost3/output_logging_20250708T190205
2025-07-08 19:37:41.773 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/logs/20250708T190016/exthost3/vscode.typescript-language-features
2025-07-08 19:39:30.604 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1119.598639,"timestamp":"2025-07-08T11:39:30.550Z"}]
2025-07-08 19:42:30.608 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1171.976981,"timestamp":"2025-07-08T11:42:30.603Z"}]
2025-07-08 19:43:26.694 [info] 'ToolsModel' Saved chat mode: AGENT
2025-07-08 19:43:26.694 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-07-08 19:43:26.695 [info] 'TaskManager' Setting current root task UUID to a4aff489-84dd-4180-82df-c85ca87a08b3
2025-07-08 19:43:26.695 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-07-08 19:43:26.695 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-08 19:43:28.085 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-08 19:43:28.085 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-08 19:43:48.130 [info] 'ViewTool' Tool called with path: . and view_range: undefined
2025-07-08 19:43:48.135 [info] 'ViewTool' Listing directory: . (depth: 2, showHidden: false)
2025-07-08 19:45:28.084 [info] 'ViewTool' Tool called with path: website/ronglianweb and view_range: undefined
2025-07-08 19:45:28.208 [info] 'ViewTool' Listing directory: website/ronglianweb (depth: 2, showHidden: false)
2025-07-08 19:45:30.510 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1013.149212,"timestamp":"2025-07-08T11:45:30.445Z"}]
2025-07-08 19:45:34.616 [info] 'ViewTool' Tool called with path: website/ronglianweb/package.json and view_range: undefined
2025-07-08 19:45:40.591 [info] 'ViewTool' Tool called with path: website/ronglianweb/views/layout-vue3.jade and view_range: undefined
2025-07-08 19:45:46.162 [info] 'ViewTool' Tool called with path: website/ronglianweb/public and view_range: undefined
2025-07-08 19:45:46.166 [info] 'ViewTool' Listing directory: website/ronglianweb/public (depth: 2, showHidden: false)
2025-07-08 19:45:52.401 [info] 'ViewTool' Tool called with path: website/ronglianweb/public/dest/plugins and view_range: undefined
2025-07-08 19:45:52.405 [info] 'ViewTool' Listing directory: website/ronglianweb/public/dest/plugins (depth: 2, showHidden: false)
2025-07-08 19:45:58.726 [info] 'ViewTool' Tool called with path: website/ronglianweb/public/dest/component-news-vue3 and view_range: undefined
2025-07-08 19:45:58.730 [info] 'ViewTool' Listing directory: website/ronglianweb/public/dest/component-news-vue3 (depth: 2, showHidden: false)
2025-07-08 19:46:01.182 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1582.569734,"timestamp":"2025-07-08T11:46:01.015Z"}]
2025-07-08 19:46:04.823 [info] 'ViewTool' Tool called with path: website/ronglianweb/public/dest/plugins/vue3/main.js and view_range: undefined
2025-07-08 19:46:11.549 [info] 'ViewTool' Tool called with path: website/ronglianweb/public/dest/plugins/vue3/config.js and view_range: undefined
2025-07-08 19:46:35.023 [info] 'ViewTool' Tool called with path: website/ronglianweb/routes/index.js and view_range: undefined
2025-07-08 19:47:00.461 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1002.482378,"timestamp":"2025-07-08T11:47:00.435Z"}]
2025-07-08 19:48:00.648 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1121.105259,"timestamp":"2025-07-08T11:48:00.554Z"}]
2025-07-08 19:48:01.582 [info] 'ViewTool' Tool called with path: website/ronglianweb/public/dest/component-news-vue3/header.js and view_range: [1,50]
2025-07-08 19:48:30.875 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1352.450143,"timestamp":"2025-07-08T11:48:30.786Z"}]
2025-07-08 19:48:39.456 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/website/vue3-demo.jade
2025-07-08 19:48:39.737 [info] 'ToolFileUtils' File not found: website/ronglianweb/views/website/vue3-demo.jade. No similar files found
2025-07-08 19:48:39.737 [error] 'StrReplaceEditorTool' Error in tool call: File not found: website/ronglianweb/views/website/vue3-demo.jade
2025-07-08 19:49:00.615 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1157.951041,"timestamp":"2025-07-08T11:49:00.591Z"}]
2025-07-08 19:49:17.961 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-93cf9fdf-edb4-4288-bd96-203d080bb2aa.json'
2025-07-08 19:49:18.097 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/checkpoint-documents
2025-07-08 19:49:24.553 [info] 'ToolFileUtils' Reading file: website/ronglianweb/routes/index.js
2025-07-08 19:49:24.554 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/routes/index.js (4724 bytes)
2025-07-08 19:49:25.014 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/History/6e5c55a3
2025-07-08 19:49:25.873 [info] 'ToolFileUtils' Reading file: website/ronglianweb/routes/index.js
2025-07-08 19:49:25.874 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/routes/index.js (4823 bytes)
2025-07-08 19:49:29.602 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-93cf9fdf-edb4-4288-bd96-203d080bb2aa.json'
2025-07-08 19:49:30.395 [info] 'WorkspaceManager[home]' Directory created: website/.cache/typescript/5.8/node_modules/@types/async
2025-07-08 19:49:30.396 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/f0/1f
2025-07-08 19:49:30.396 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/ee/4c
2025-07-08 19:49:30.618 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1121.758491,"timestamp":"2025-07-08T11:49:30.555Z"}]
2025-07-08 19:49:31.278 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/f2
2025-07-08 19:49:31.279 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/f2/21
2025-07-08 19:49:31.280 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/cf/6a
2025-07-08 19:51:51.048 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-93cf9fdf-edb4-4288-bd96-203d080bb2aa.json'
2025-07-08 19:52:03.980 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/History/-98511f4
2025-07-08 19:53:00.027 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-93cf9fdf-edb4-4288-bd96-203d080bb2aa.json'
2025-07-08 19:53:02.793 [info] 'ViewTool' Tool called with path: website/ronglianweb/bin/www and view_range: undefined
2025-07-08 19:53:12.701 [info] 'ToolFileUtils' Reading file: website/ronglianweb/bin/www
2025-07-08 19:53:12.701 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/bin/www (1692 bytes)
2025-07-08 19:53:13.381 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/History/-562999d8
2025-07-08 19:53:14.202 [info] 'ToolFileUtils' Reading file: website/ronglianweb/bin/www
2025-07-08 19:53:14.203 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/bin/www (1690 bytes)
2025-07-08 19:53:15.641 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/0a/f8
2025-07-08 19:53:15.641 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/23
2025-07-08 19:53:15.641 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/23/08
2025-07-08 19:53:15.641 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/3b
2025-07-08 19:53:15.642 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/5e/84
2025-07-08 19:53:15.642 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/60/3e
2025-07-08 19:53:15.642 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/64/79
2025-07-08 19:53:15.643 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/6b/12
2025-07-08 19:53:15.643 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/af/c3
2025-07-08 19:53:15.643 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/b1/3b
2025-07-08 19:53:15.643 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/dd/31
2025-07-08 19:53:15.643 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/f2/ef
2025-07-08 19:53:15.643 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/13/ec
2025-07-08 19:53:15.644 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/2a/aa
2025-07-08 19:53:15.644 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/55/d2
2025-07-08 19:53:15.644 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/76/5a
2025-07-08 19:53:15.644 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/8b/86
2025-07-08 19:53:15.644 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/af/80
2025-07-08 19:53:15.644 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/b6/39
2025-07-08 19:53:15.645 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/c7/13
2025-07-08 19:53:15.645 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/dd/04
2025-07-08 19:53:15.645 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/ec/b0
2025-07-08 19:53:15.645 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/f2/d0
2025-07-08 19:53:15.831 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/45/a4
2025-07-08 19:53:15.831 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/7d
2025-07-08 19:53:15.832 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/7d/e6
2025-07-08 19:53:15.832 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/b8/89
2025-07-08 19:53:15.833 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/e4/b9
2025-07-08 19:53:15.911 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/19/b9
2025-07-08 19:53:15.911 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/ed/16
2025-07-08 19:53:15.912 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/content-v2/sha512/f1
2025-07-08 19:53:15.913 [info] 'WorkspaceManager[home]' Directory created: website/.npm/_cacache/index-v5/3d
2025-07-08 19:53:17.711 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-93cf9fdf-edb4-4288-bd96-203d080bb2aa.json'
2025-07-08 19:54:01.045 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1529.192101,"timestamp":"2025-07-08T11:54:01.003Z"}]
2025-07-08 19:54:28.212 [info] 'ToolFileUtils' Reading file: website/ronglianweb/bin/www
2025-07-08 19:54:28.213 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/bin/www (1690 bytes)
2025-07-08 19:54:29.452 [info] 'ToolFileUtils' Reading file: website/ronglianweb/bin/www
2025-07-08 19:54:29.452 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/bin/www (1692 bytes)
2025-07-08 19:54:30.793 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1162.316949,"timestamp":"2025-07-08T11:54:30.598Z"}]
2025-07-08 19:54:33.224 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-93cf9fdf-edb4-4288-bd96-203d080bb2aa.json'
2025-07-08 19:55:50.226 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-93cf9fdf-edb4-4288-bd96-203d080bb2aa.json'
2025-07-08 19:56:07.728 [info] 'WorkspaceManager[home]' Tracking enabled
2025-07-08 19:56:07.728 [info] 'WorkspaceManager[home]' Path metrics:
  - directories emitted: 10217
  - files emitted: 64366
  - other paths emitted: 1200
  - total paths emitted: 75783
  - timing stats:
    - readDir: 282 ms
    - filter: 1429 ms
    - yield: 243 ms
    - total: 2092 ms
2025-07-08 19:56:07.728 [info] 'WorkspaceManager[home]' File metrics:
  - paths accepted: 52271
  - paths not accessible: 0
  - not plain files: 0
  - large files: 1046
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 1
  - mtime cache misses: 52271
  - probe batches: 1073
  - blob names probed: 156892
  - files read: 115982
  - blobs uploaded: 45643
  - timing stats:
    - ingestPath: 730 ms
    - probe: 1414117 ms
    - stat: 2174 ms
    - read: 101493 ms
    - upload: 2050158 ms
2025-07-08 19:56:07.728 [info] 'WorkspaceManager[home]' Startup metrics:
  - create SourceFolder: 7 ms
  - read MtimeCache: 1 ms
  - pre-populate PathMap: 0 ms
  - create PathFilter: 668 ms
  - create PathNotifier: 1 ms
  - enumerate paths: 2096 ms
  - purge stale PathMap entries: 8 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 3218291 ms
  - enable persist: 41 ms
  - total: 3221113 ms
2025-07-08 19:56:07.728 [info] 'WorkspaceManager' Workspace startup complete in 3237493 ms
2025-07-08 19:56:08.246 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/2cc974af6afc822c42a4e914df05c697348b80420941f6b27561b2bf688587b7
2025-07-08 19:56:32.064 [info] 'ToolFileUtils' Reading file: website/ronglianweb/bin/www
2025-07-08 19:56:32.086 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/bin/www (1692 bytes)
2025-07-08 19:56:33.455 [info] 'ToolFileUtils' Reading file: website/ronglianweb/bin/www
2025-07-08 19:56:33.455 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/bin/www (1690 bytes)
2025-07-08 19:56:37.096 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-93cf9fdf-edb4-4288-bd96-203d080bb2aa.json'
2025-07-08 19:57:01.307 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1873.844234,"timestamp":"2025-07-08T11:57:01.305Z"}]
2025-07-08 19:57:02.460 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-93cf9fdf-edb4-4288-bd96-203d080bb2aa.json'
2025-07-08 19:57:17.550 [error] 'FuzzySymbolSearcher' Failed to read file tokens for c0f4a7d78c173b90a0e0d6f03acd65edadb718e42ebdb804fbeb227e4c773d2d: deleted
2025-07-08 19:58:32.863 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-93cf9fdf-edb4-4288-bd96-203d080bb2aa.json'
2025-07-08 19:59:00.983 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1414.680567,"timestamp":"2025-07-08T11:59:00.856Z"}]
2025-07-08 20:00:00.899 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1385.702517,"timestamp":"2025-07-08T12:00:00.815Z"}]
2025-07-08 20:00:06.142 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/tool-configs/approval/auto
2025-07-08 20:00:30.599 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1088.73349,"timestamp":"2025-07-08T12:00:30.518Z"}]
2025-07-08 20:00:59.124 [info] 'ToolFileUtils' Reading file: website/ronglianweb/bin/www
2025-07-08 20:00:59.144 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/bin/www (1690 bytes)
2025-07-08 20:01:00.619 [info] 'ToolFileUtils' Reading file: website/ronglianweb/bin/www
2025-07-08 20:01:00.619 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/bin/www (1692 bytes)
2025-07-08 20:01:04.153 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-93cf9fdf-edb4-4288-bd96-203d080bb2aa.json'
2025-07-08 20:01:49.210 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-93cf9fdf-edb4-4288-bd96-203d080bb2aa.json'
2025-07-08 20:02:08.285 [info] 'AugmentExtension' Retrieving model config
2025-07-08 20:02:09.415 [info] 'AugmentExtension' Retrieved model config
2025-07-08 20:02:09.415 [info] 'AugmentExtension' Returning model config
2025-07-08 20:03:17.051 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-93cf9fdf-edb4-4288-bd96-203d080bb2aa.json'
2025-07-08 20:03:40.290 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-93cf9fdf-edb4-4288-bd96-203d080bb2aa.json'
2025-07-08 20:04:04.345 [info] 'ToolFileUtils' Reading file: website/ronglianweb/PORT_CONFIGURATION.md
2025-07-08 20:04:04.364 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/PORT_CONFIGURATION.md (1690 bytes)
2025-07-08 20:04:05.100 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/History/-5b0dec7a
2025-07-08 20:04:05.952 [info] 'ToolFileUtils' Reading file: website/ronglianweb/PORT_CONFIGURATION.md
2025-07-08 20:04:05.952 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/PORT_CONFIGURATION.md (2126 bytes)
2025-07-08 20:04:09.392 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-93cf9fdf-edb4-4288-bd96-203d080bb2aa.json'
2025-07-08 20:04:51.324 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-93cf9fdf-edb4-4288-bd96-203d080bb2aa.json'
2025-07-08 20:05:45.120 [error] 'AugmentExtensionSidecar' API request 05275cee-750c-43bf-a624-a5b2e324fe03 to https://i0.api.augmentcode.com/remote-agents/list-stream failed: This operation was aborted
2025-07-08 20:05:45.120 [error] 'AugmentExtensionSidecar' AbortError: This operation was aborted
	at node:internal/deps/undici/undici:13510:13
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async OJ.globalThis.fetch (file:///home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:173:21986)
	at async dv (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.496.1/out/extension.js:310:28564)
	at async NL.callApiStream (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.496.1/out/extension.js:539:4046)
	at async NL.callApiStream (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.496.1/out/extension.js:543:58912)
	at async NL.getRemoteAgentOverviewsStream (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.496.1/out/extension.js:543:23301)
	at async e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.496.1/out/extension.js:1797:22423)
	at async /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.496.1/out/extension.js:546:5052
2025-07-08 20:05:45.749 [error] 'AugmentExtension' API request fa114dbd-a205-4ad2-9e12-30c78e1a0017 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-08 20:05:45.749 [error] 'AugmentExtension' Dropping error report "remote-agents/list-stream call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-08 20:05:45.750 [error] 'AugmentExtensionSidecar' API request 929f89e2-2d36-4bdc-bc55-7eabbbb58d10 to https://i0.api.augmentcode.com/remote-agents/list-stream failed: This operation was aborted
2025-07-08 20:05:45.750 [error] 'AugmentExtensionSidecar' AbortError: This operation was aborted
	at node:internal/deps/undici/undici:13510:13
	at runNextTicks (node:internal/process/task_queues:65:5)
	at listOnTimeout (node:internal/timers:549:9)
	at process.processTimers (node:internal/timers:523:7)
	at async OJ.globalThis.fetch (file:///home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/out/vs/workbench/api/node/extensionHostProcess.js:173:21986)
	at async dv (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.496.1/out/extension.js:310:28564)
	at async NL.callApiStream (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.496.1/out/extension.js:539:4046)
	at async NL.callApiStream (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.496.1/out/extension.js:543:58912)
	at async NL.getRemoteAgentOverviewsStream (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.496.1/out/extension.js:543:23301)
	at async e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.496.1/out/extension.js:1797:22423)
	at async /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.496.1/out/extension.js:546:5052
2025-07-08 20:05:46.261 [error] 'AugmentExtension' API request 37ae5ce1-8287-4b3f-a0dd-40b47fc0b1d5 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-08 20:05:46.261 [error] 'AugmentExtension' Dropping error report "remote-agents/list-stream call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-08 20:07:38.736 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-93cf9fdf-edb4-4288-bd96-203d080bb2aa.json'
2025-07-08 20:09:23.049 [info] 'ToolFileUtils' Reading file: website/ronglianweb/start-with-nginx.sh
2025-07-08 20:09:23.113 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/start-with-nginx.sh (2118 bytes)
2025-07-08 20:09:23.707 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/History/578a883e
2025-07-08 20:09:24.568 [info] 'ToolFileUtils' Reading file: website/ronglianweb/start-with-nginx.sh
2025-07-08 20:09:24.569 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/start-with-nginx.sh (2293 bytes)
2025-07-08 20:09:28.124 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-93cf9fdf-edb4-4288-bd96-203d080bb2aa.json'
2025-07-08 20:09:54.160 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-93cf9fdf-edb4-4288-bd96-203d080bb2aa.json'
2025-07-08 20:10:24.891 [info] 'ViewTool' Tool called with path: website/ronglianweb/routes/index.js and view_range: [118,135]
2025-07-08 20:10:32.597 [info] 'ViewTool' Tool called with path: website/ronglianweb/views/website and view_range: undefined
2025-07-08 20:10:32.602 [info] 'ViewTool' Listing directory: website/ronglianweb/views/website (depth: 2, showHidden: false)
2025-07-08 20:11:21.640 [info] 'ViewTool' Tool called with path: website/ronglianweb/app.js and view_range: [1,50]
2025-07-08 20:11:40.640 [info] 'ViewTool' Tool called with path: website/ronglianweb/app.js and view_range: [100,179]
2025-07-08 20:11:49.658 [info] 'ViewTool' Tool called with path: website/ronglianweb/app.js and view_range: [60,100]
2025-07-08 20:12:08.943 [info] 'ViewTool' Tool called with path: website/ronglianweb/routes/index.js and view_range: [130,136]
2025-07-08 20:12:17.139 [info] 'ViewTool' Tool called with path: website/ronglianweb/views/website/vue3-demo.jade and view_range: [1,20]
2025-07-08 20:12:48.412 [info] 'ToolFileUtils' Reading file: website/ronglianweb/routes/index.js
2025-07-08 20:12:48.412 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/routes/index.js (4823 bytes)
2025-07-08 20:12:49.962 [info] 'ToolFileUtils' Reading file: website/ronglianweb/routes/index.js
2025-07-08 20:12:49.962 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/routes/index.js (4858 bytes)
2025-07-08 20:12:53.422 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-93cf9fdf-edb4-4288-bd96-203d080bb2aa.json'
2025-07-08 20:13:02.011 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":2483.592317,"timestamp":"2025-07-08T12:13:01.912Z"}]
2025-07-08 20:13:58.219 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-93cf9fdf-edb4-4288-bd96-203d080bb2aa.json'
2025-07-08 20:14:05.285 [info] 'ToolFileUtils' Reading file: website/ronglianweb/routes/index.js
2025-07-08 20:14:05.286 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/routes/index.js (4858 bytes)
2025-07-08 20:14:06.540 [info] 'ToolFileUtils' Reading file: website/ronglianweb/routes/index.js
2025-07-08 20:14:06.541 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/routes/index.js (5013 bytes)
2025-07-08 20:14:10.296 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-93cf9fdf-edb4-4288-bd96-203d080bb2aa.json'
2025-07-08 20:15:07.358 [info] 'ViewTool' Tool called with path: website/ronglianweb/views/website/vue3-demo.jade and view_range: [75,85]
2025-07-08 20:15:17.284 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/website/vue3-demo.jade
2025-07-08 20:15:17.303 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/website/vue3-demo.jade (8353 bytes)
2025-07-08 20:15:17.823 [info] 'WorkspaceManager[home]' Directory created: website/.vscode-server/data/User/History/55a737f
2025-07-08 20:15:18.685 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/website/vue3-demo.jade
2025-07-08 20:15:18.685 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/website/vue3-demo.jade (8359 bytes)
2025-07-08 20:15:22.312 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-93cf9fdf-edb4-4288-bd96-203d080bb2aa.json'
2025-07-08 20:15:29.420 [info] 'ViewTool' Tool called with path: website/ronglianweb/views/website/vue3-demo.jade and view_range: undefined
2025-07-08 20:15:38.420 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/website/vue3-demo.jade
2025-07-08 20:15:38.420 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/website/vue3-demo.jade (8359 bytes)
2025-07-08 20:15:38.470 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 35c4fbf562daeed6326fbcb0a039b3a9a44634cf068c5a2c5c584f37a7fcaf58: deleted
2025-07-08 20:15:39.672 [info] 'ToolFileUtils' Reading file: website/ronglianweb/views/website/vue3-demo.jade
2025-07-08 20:15:39.672 [info] 'ToolFileUtils' Successfully read file: website/ronglianweb/views/website/vue3-demo.jade (8371 bytes)
2025-07-08 20:15:43.431 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/616ded334f0b5aa2aec0953b488e7938/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-93cf9fdf-edb4-4288-bd96-203d080bb2aa.json'
