*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[19:00:16] 




[19:00:16] Extension host agent started.
[19:00:16] Started initializing default profile extensions in extensions installation folder. file:///home/<USER>/.vscode-server/extensions
[19:00:16] ComputeTargetPlatform: linux-x64
[19:00:16] Completed initializing default profile extensions in extensions installation folder. file:///home/<USER>/.vscode-server/extensions
[19:00:16] [<unknown>][861fbb2a][ManagementConnection] New connection established.
[19:00:16] [<unknown>][d31b01e9][ExtensionHostConnection] New connection established.
[19:00:16] Installing extensions...
[19:00:17] [<unknown>][d31b01e9][ExtensionHostConnection] <174527> Launched Extension Host Process.
[19:00:17] ComputeTargetPlatform: linux-x64
[19:00:18] Installing extension 'ms-ceintl.vscode-language-pack-zh-hans'...
[19:00:19] Getting Manifest... ms-ceintl.vscode-language-pack-zh-hans
[19:00:19] Installing extension: ms-ceintl.vscode-language-pack-zh-hans {
  isMachineScoped: true,
  isBuiltin: false,
  installPreReleaseVersion: undefined,
  installGivenVersion: false,
  isApplicationScoped: true,
  profileLocation: Br {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  },
  productVersion: { version: '1.101.2', date: '2025-06-24T20:27:15.391Z' }
}
[19:00:23] Extension signature verification result for ms-ceintl.vscode-language-pack-zh-hans: Success. Internal Code: 0. Executed: true. Duration: 3412ms.
[19:00:24] Extracted extension to file:///home/<USER>/.vscode-server/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.101.2025061109: ms-ceintl.vscode-language-pack-zh-hans
[19:00:24] Renamed to /home/<USER>/.vscode-server/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.101.2025061109
[19:00:24] Adding language packs from the extension ms-ceintl.vscode-language-pack-zh-hans
[19:00:24] Extension installed successfully: ms-ceintl.vscode-language-pack-zh-hans file:///home/<USER>/.vscode-server/extensions/extensions.json
[19:00:24] Extension 'ms-ceintl.vscode-language-pack-zh-hans' v1.101.2025061109 was successfully installed.
[19:01:05] [<unknown>][861fbb2a][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[19:01:05] [<unknown>][d31b01e9][ExtensionHostConnection] <174527> Extension Host Process exited with code: 0, signal: null.
Cancelling previous shutdown timeout
[19:01:05] Cancelling previous shutdown timeout
Last EH closed, waiting before shutting down
[19:01:05] Last EH closed, waiting before shutting down
[19:01:08] [<unknown>][e74eec62][ManagementConnection] New connection established.
[19:01:08] [<unknown>][729e1c0f][ExtensionHostConnection] New connection established.
[19:01:08] [<unknown>][729e1c0f][ExtensionHostConnection] <174623> Launched Extension Host Process.
[19:01:55] Getting Manifest... augment.vscode-augment
[19:01:56] Installing extension: augment.vscode-augment {
  installPreReleaseVersion: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'linux-x64' },
  isApplicationScoped: false,
  profileLocation: Br {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  },
  productVersion: { version: '1.101.2', date: '2025-06-24T20:27:15.391Z' }
}
[19:02:01] [<unknown>][e74eec62][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[19:02:01] [<unknown>][729e1c0f][ExtensionHostConnection] <174623> Extension Host Process exited with code: 0, signal: null.
Cancelling previous shutdown timeout
[19:02:01] Cancelling previous shutdown timeout
Last EH closed, waiting before shutting down
[19:02:01] Last EH closed, waiting before shutting down
[19:02:01] Extension signature verification result for augment.vscode-augment: Success. Internal Code: 0. Executed: true. Duration: 2301ms.
[19:02:04] Extracted extension to file:///home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.496.1: augment.vscode-augment
[19:02:04] Renamed to /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.496.1
[19:02:04] Extension installed successfully: augment.vscode-augment file:///home/<USER>/.vscode-server/extensions/extensions.json
[19:02:04] [<unknown>][f4396656][ManagementConnection] New connection established.
[19:02:04] [<unknown>][207c43e0][ExtensionHostConnection] New connection established.
[19:02:04] [<unknown>][207c43e0][ExtensionHostConnection] <174821> Launched Extension Host Process.
New EH opened, aborting shutdown
[19:07:01] New EH opened, aborting shutdown
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:3000
stack trace: Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[19:50:14] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 3000
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:3000
stack trace: Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[19:50:14] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 3000
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:3000
stack trace: Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[19:50:16] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 3000
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:3000
stack trace: Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[19:50:22] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 3000
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:3000
stack trace: Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[19:55:27] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 3000
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:3000
stack trace: Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[20:02:49] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 3000
}
