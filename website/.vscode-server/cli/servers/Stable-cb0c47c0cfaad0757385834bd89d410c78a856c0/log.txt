*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[11:22:24] 




[11:22:24] Extension host agent started.
[11:22:25] [<unknown>][52208208][ManagementConnection] New connection established.
[11:22:25] [<unknown>][daeeaaf4][ExtensionHostConnection] New connection established.
[11:22:25] [<unknown>][daeeaaf4][ExtensionHostConnection] <4656> Launched Extension Host Process.
[11:22:25] #1: https://main.vscode-cdn.net/extensions/marketplace.json - error GET AggregateError [ETIMEDOUT]: 
[11:22:29] Getting Manifest... augment.vscode-augment
[11:22:29] Getting Manifest... ms-ceintl.vscode-language-pack-zh-hans
[11:22:29] Installing extension: ms-ceintl.vscode-language-pack-zh-hans {
  productVersion: { version: '1.102.0', date: '2025-07-09T22:10:34.600Z' },
  pinned: false,
  operation: 3,
  isApplicationScoped: true,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'linux-x64' },
  profileLocation: xr {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  }
}
[11:22:29] Installing extension: augment.vscode-augment {
  productVersion: { version: '1.102.0', date: '2025-07-09T22:10:34.600Z' },
  pinned: false,
  operation: 3,
  isApplicationScoped: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'linux-x64' },
  profileLocation: xr {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  }
}
[11:22:33] Extension signature verification result for ms-ceintl.vscode-language-pack-zh-hans: Success. Internal Code: 0. Executed: true. Duration: 2403ms.
[11:22:33] Extracted extension to file:///home/<USER>/.vscode-server/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.102.2025070909: ms-ceintl.vscode-language-pack-zh-hans
[11:22:33] Renamed to /home/<USER>/.vscode-server/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.102.2025070909
[11:22:33] Marked extension as removed ms-ceintl.vscode-language-pack-zh-hans-1.101.2025061109
[11:22:33] Adding language packs from the extension ms-ceintl.vscode-language-pack-zh-hans
[11:22:33] Extension installed successfully: ms-ceintl.vscode-language-pack-zh-hans file:///home/<USER>/.vscode-server/extensions/extensions.json
[11:22:37] Extension signature verification result for augment.vscode-augment: Success. Internal Code: 0. Executed: true. Duration: 3068ms.
[11:22:39] Extracted extension to file:///home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.502.1: augment.vscode-augment
[11:22:39] Renamed to /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.502.1
[11:22:39] Extension installed successfully: augment.vscode-augment file:///home/<USER>/.vscode-server/extensions/extensions.json
[11:22:39] Marked extension as removed augment.vscode-augment-0.496.1
[11:22:47] [<unknown>][52208208][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[11:22:48] [<unknown>][daeeaaf4][ExtensionHostConnection] <4656> Extension Host Process exited with code: 0, signal: null.
Cancelling previous shutdown timeout
[11:22:48] Cancelling previous shutdown timeout
Last EH closed, waiting before shutting down
[11:22:48] Last EH closed, waiting before shutting down
[11:22:51] [<unknown>][79218b0f][ManagementConnection] New connection established.
[11:22:51] [<unknown>][ee34f306][ExtensionHostConnection] New connection established.
[11:22:51] [<unknown>][ee34f306][ExtensionHostConnection] <4990> Launched Extension Host Process.
[11:24:11] [<unknown>][79218b0f][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[11:24:11] [<unknown>][ee34f306][ExtensionHostConnection] <4990> Extension Host Process exited with code: 0, signal: null.
Cancelling previous shutdown timeout
[11:24:11] Cancelling previous shutdown timeout
Last EH closed, waiting before shutting down
[11:24:11] Last EH closed, waiting before shutting down
[11:24:14] [<unknown>][449970dd][ManagementConnection] New connection established.
[11:24:14] [<unknown>][b7983c3e][ExtensionHostConnection] New connection established.
[11:24:14] [<unknown>][b7983c3e][ExtensionHostConnection] <5342> Launched Extension Host Process.
[11:24:23] [<unknown>][449970dd][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[11:24:23] [<unknown>][b7983c3e][ExtensionHostConnection] <5342> Extension Host Process exited with code: 0, signal: null.
Cancelling previous shutdown timeout
[11:24:23] Cancelling previous shutdown timeout
Last EH closed, waiting before shutting down
[11:24:23] Last EH closed, waiting before shutting down
[11:24:26] [<unknown>][421b34e6][ManagementConnection] New connection established.
[11:24:26] [<unknown>][db4abe42][ExtensionHostConnection] New connection established.
[11:24:26] [<unknown>][db4abe42][ExtensionHostConnection] <5538> Launched Extension Host Process.
New EH opened, aborting shutdown
[11:29:23] New EH opened, aborting shutdown
[17:16:03] [<unknown>][421b34e6][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[17:16:03] [<unknown>][db4abe42][ExtensionHostConnection] <5538> Extension Host Process exited with code: 0, signal: null.
Last EH closed, waiting before shutting down
[17:16:03] Last EH closed, waiting before shutting down
[17:16:28] [<unknown>][84459092][ManagementConnection] New connection established.
[17:16:28] [<unknown>][f3879862][ExtensionHostConnection] New connection established.
[17:16:28] [<unknown>][f3879862][ExtensionHostConnection] <16101> Launched Extension Host Process.
[17:16:29] #13: https://main.vscode-cdn.net/extensions/marketplace.json - error GET AggregateError [ETIMEDOUT]: 
