0 verbose cli /usr/bin/node /usr/bin/npm
1 info using npm@10.8.2
2 info using node@v20.19.3
3 silly config load:file:/usr/lib/node_modules/npm/npmrc
4 silly config load:file:/home/<USER>/.cache/typescript/5.8/.npmrc
5 silly config load:file:/home/<USER>/.npmrc
6 silly config load:file:/usr/etc/npmrc
7 verbose title npm install @types/cookie-parser@ts5.8 @types/express-session@ts5.8 @types/morgan@ts5.8 @types/node-schedule@ts5.8 @types/serve-favicon@ts5.8
8 verbose argv "install" "--ignore-scripts" "@types/cookie-parser@ts5.8" "@types/express-session@ts5.8" "@types/morgan@ts5.8" "@types/node-schedule@ts5.8" "@types/serve-favicon@ts5.8" "--save-dev" "--user-agent" "typesInstaller/5.8.3"
9 verbose logfile logs-max:10 dir:/home/<USER>/.npm/_logs/2025-07-15T06_37_05_353Z-
10 verbose logfile /home/<USER>/.npm/_logs/2025-07-15T06_37_05_353Z-debug-0.log
11 silly packumentCache heap:4345298944 maxSize:1086324736 maxEntrySize:543162368
12 silly logfile start cleaning logs, removing 1 files
13 silly logfile done cleaning log files
14 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fcookie-parser cache-miss
15 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fexpress-session cache-miss
16 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fmorgan cache-miss
17 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fnode-schedule cache-miss
18 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fserve-favicon cache-miss
19 http fetch GET 200 https://registry.npmmirror.com/@types%2fserve-favicon 133ms (cache miss)
20 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fserve-favicon set size:4448 disposed:false
21 http fetch GET 200 https://registry.npmmirror.com/@types%2fmorgan 135ms (cache miss)
22 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fmorgan set size:5873 disposed:false
23 http fetch GET 200 https://registry.npmmirror.com/@types%2fcookie-parser 139ms (cache miss)
24 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fcookie-parser set size:4768 disposed:false
25 http fetch GET 200 https://registry.npmmirror.com/@types%2fexpress-session 138ms (cache miss)
26 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fexpress-session set size:9154 disposed:false
27 http fetch GET 200 https://registry.npmmirror.com/@types%2fnode-schedule 145ms (cache miss)
28 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fnode-schedule set size:6875 disposed:false
29 silly idealTree buildDeps
30 silly fetch manifest @types/serve-favicon@2.5.7
31 silly packumentCache full:https://registry.npmmirror.com/@types%2fserve-favicon cache-miss
32 http fetch GET 200 https://registry.npmmirror.com/@types%2fserve-favicon 81ms (cache miss)
33 silly packumentCache full:https://registry.npmmirror.com/@types%2fserve-favicon set size:8630 disposed:false
34 silly fetch manifest @types/morgan@1.9.10
35 silly packumentCache full:https://registry.npmmirror.com/@types%2fmorgan cache-miss
36 http fetch GET 200 https://registry.npmmirror.com/@types%2fmorgan 70ms (cache miss)
37 silly packumentCache full:https://registry.npmmirror.com/@types%2fmorgan set size:11012 disposed:false
38 silly fetch manifest @types/cookie-parser@1.4.9
39 silly packumentCache full:https://registry.npmmirror.com/@types%2fcookie-parser cache-miss
40 http fetch GET 200 https://registry.npmmirror.com/@types%2fcookie-parser 37ms (cache miss)
41 silly packumentCache full:https://registry.npmmirror.com/@types%2fcookie-parser set size:9163 disposed:false
42 silly fetch manifest @types/express@^5.0.3
43 silly packumentCache full:https://registry.npmmirror.com/@types%2fexpress cache-miss
44 http fetch GET 200 https://registry.npmmirror.com/@types%2fexpress 32ms (cache miss)
45 silly packumentCache full:https://registry.npmmirror.com/@types%2fexpress set size:19822 disposed:false
46 silly fetch manifest @types/express-session@1.18.2
47 silly packumentCache full:https://registry.npmmirror.com/@types%2fexpress-session cache-miss
48 http fetch GET 200 https://registry.npmmirror.com/@types%2fexpress-session 73ms (cache miss)
49 silly packumentCache full:https://registry.npmmirror.com/@types%2fexpress-session set size:17099 disposed:false
50 silly fetch manifest @types/node-schedule@2.1.8
51 silly packumentCache full:https://registry.npmmirror.com/@types%2fnode-schedule cache-miss
52 http fetch GET 200 https://registry.npmmirror.com/@types%2fnode-schedule 36ms (cache miss)
53 silly packumentCache full:https://registry.npmmirror.com/@types%2fnode-schedule set size:12966 disposed:false
54 silly placeDep ROOT @types/cookie-parser@1.4.9 OK for:  want: 1.4.9
55 silly placeDep ROOT @types/express-session@1.18.2 OK for:  want: 1.18.2
56 silly placeDep ROOT @types/morgan@1.9.10 OK for:  want: 1.9.10
57 silly placeDep ROOT @types/node-schedule@2.1.8 OK for:  want: 2.1.8
58 silly placeDep ROOT @types/serve-favicon@2.5.7 OK for:  want: 2.5.7
59 silly reify moves {}
60 silly audit bulk request {
60 silly audit   '@types/async': [ '3.2.24' ],
60 silly audit   '@types/body-parser': [ '1.19.6' ],
60 silly audit   '@types/brace-expansion': [ '1.1.2' ],
60 silly audit   '@types/caseless': [ '0.12.5' ],
60 silly audit   '@types/connect': [ '3.4.38' ],
60 silly audit   '@types/cors': [ '2.8.19' ],
60 silly audit   '@types/debug': [ '4.1.12' ],
60 silly audit   '@types/estree': [ '1.0.8' ],
60 silly audit   '@types/express': [ '5.0.3' ],
60 silly audit   '@types/express-serve-static-core': [ '5.0.6' ],
60 silly audit   '@types/http-errors': [ '2.0.5' ],
60 silly audit   '@types/json-schema': [ '7.0.15' ],
60 silly audit   '@types/less': [ '3.0.8' ],
60 silly audit   '@types/lodash': [ '4.17.20' ],
60 silly audit   '@types/mime': [ '1.3.5' ],
60 silly audit   '@types/ms': [ '2.1.0' ],
60 silly audit   '@types/multer': [ '2.0.0' ],
60 silly audit   '@types/node': [ '24.0.10' ],
60 silly audit   '@types/object-assign': [ '4.0.33' ],
60 silly audit   '@types/on-finished': [ '2.3.5' ],
60 silly audit   '@types/qs': [ '6.14.0' ],
60 silly audit   '@types/range-parser': [ '1.2.7' ],
60 silly audit   '@types/react': [ '19.1.8' ],
60 silly audit   '@types/request': [ '2.48.12' ],
60 silly audit   '@types/send': [ '0.17.5' ],
60 silly audit   '@types/serve-static': [ '1.15.8' ],
60 silly audit   '@types/stylus': [ '0.48.43' ],
60 silly audit   '@types/tough-cookie': [ '4.0.5' ],
60 silly audit   '@types/type-is': [ '1.6.7' ],
60 silly audit   '@types/vary': [ '1.1.3' ],
60 silly audit   asynckit: [ '0.4.0' ],
60 silly audit   'call-bind-apply-helpers': [ '1.0.2' ],
60 silly audit   'combined-stream': [ '1.0.8' ],
60 silly audit   csstype: [ '3.1.3' ],
60 silly audit   'delayed-stream': [ '1.0.0' ],
60 silly audit   'dunder-proto': [ '1.0.1' ],
60 silly audit   'es-define-property': [ '1.0.1' ],
60 silly audit   'es-errors': [ '1.3.0' ],
60 silly audit   'es-object-atoms': [ '1.1.1' ],
60 silly audit   'es-set-tostringtag': [ '2.1.0' ],
60 silly audit   'form-data': [ '2.5.3' ],
60 silly audit   'function-bind': [ '1.1.2' ],
60 silly audit   'get-intrinsic': [ '1.3.0' ],
60 silly audit   'get-proto': [ '1.0.1' ],
60 silly audit   gopd: [ '1.2.0' ],
60 silly audit   'has-symbols': [ '1.1.0' ],
60 silly audit   'has-tostringtag': [ '1.0.2' ],
60 silly audit   hasown: [ '2.0.2' ],
60 silly audit   'math-intrinsics': [ '1.1.0' ],
60 silly audit   'mime-db': [ '1.52.0' ],
60 silly audit   'mime-types': [ '2.1.35' ],
60 silly audit   'safe-buffer': [ '5.2.1' ],
60 silly audit   'types-registry': [ '0.1.729' ],
60 silly audit   'undici-types': [ '7.8.0' ],
60 silly audit   '@types/cookie-parser': [ '1.4.9' ],
60 silly audit   '@types/express-session': [ '1.18.2' ],
60 silly audit   '@types/morgan': [ '1.9.10' ],
60 silly audit   '@types/node-schedule': [ '2.1.8' ],
60 silly audit   '@types/serve-favicon': [ '2.5.7' ]
60 silly audit }
61 silly tarball no local data for @types/node-schedule@https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-2.1.8.tgz. Extracting by manifest.
62 silly tarball no local data for @types/express-session@https://registry.npmmirror.com/@types/express-session/-/express-session-1.18.2.tgz. Extracting by manifest.
63 silly tarball no local data for @types/cookie-parser@https://registry.npmmirror.com/@types/cookie-parser/-/cookie-parser-1.4.9.tgz. Extracting by manifest.
64 silly tarball no local data for @types/morgan@https://registry.npmmirror.com/@types/morgan/-/morgan-1.9.10.tgz. Extracting by manifest.
65 silly tarball no local data for @types/serve-favicon@https://registry.npmmirror.com/@types/serve-favicon/-/serve-favicon-2.5.7.tgz. Extracting by manifest.
66 http fetch POST 404 https://registry.npmmirror.com/-/npm/v1/security/advisories/bulk 33ms
67 silly audit bulk request failed [object Object]
68 http fetch POST 404 https://registry.npmmirror.com/-/npm/v1/security/audits/quick 10ms
69 verbose audit error HttpErrorGeneral: 404 Not Found - POST https://registry.npmmirror.com/-/npm/v1/security/audits/quick - [NOT_IMPLEMENTED] /-/npm/v1/security/* not implemented yet
69 verbose audit error     at /usr/lib/node_modules/npm/node_modules/npm-registry-fetch/lib/check-response.js:95:15
69 verbose audit error     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
69 verbose audit error     at async [getReport] (/usr/lib/node_modules/npm/node_modules/@npmcli/arborist/lib/audit-report.js:336:21)
69 verbose audit error     at async AuditReport.run (/usr/lib/node_modules/npm/node_modules/@npmcli/arborist/lib/audit-report.js:106:19) {
69 verbose audit error   headers: [Object: null prototype] {
69 verbose audit error     server: [ 'Tengine' ],
69 verbose audit error     date: [ 'Tue, 15 Jul 2025 06:37:06 GMT' ],
69 verbose audit error     'content-type': [ 'application/json' ],
69 verbose audit error     'transfer-encoding': [ 'chunked' ],
69 verbose audit error     connection: [ 'keep-alive' ],
69 verbose audit error     'strict-transport-security': [ 'max-age=5184000' ],
69 verbose audit error     via: [ 'kunlun9.cn5506[,404666]' ],
69 verbose audit error     'timing-allow-origin': [ '*' ],
69 verbose audit error     eagleid: [ '6f0db51d17525614263002776e' ],
69 verbose audit error     'x-fetch-attempts': [ '1' ]
69 verbose audit error   },
69 verbose audit error   statusCode: 404,
69 verbose audit error   code: 'E404',
69 verbose audit error   method: 'POST',
69 verbose audit error   uri: 'https://registry.npmmirror.com/-/npm/v1/security/audits/quick',
69 verbose audit error   body: {
69 verbose audit error     error: '[NOT_IMPLEMENTED] /-/npm/v1/security/* not implemented yet'
69 verbose audit error   },
69 verbose audit error   pkgid: undefined
69 verbose audit error }
70 silly audit error [object Object]
71 silly audit report null
72 http fetch GET 200 https://cdn.npmmirror.com/packages/%40types/node-schedule/2.1.8/node-schedule-2.1.8.tgz 210ms (cache miss)
73 http fetch GET 200 https://cdn.npmmirror.com/packages/%40types/cookie-parser/1.4.9/cookie-parser-1.4.9.tgz 208ms (cache miss)
74 http fetch GET 200 https://cdn.npmmirror.com/packages/%40types/express-session/1.18.2/express-session-1.18.2.tgz 210ms (cache miss)
75 http fetch GET 200 https://cdn.npmmirror.com/packages/%40types/serve-favicon/2.5.7/serve-favicon-2.5.7.tgz 256ms (cache miss)
76 http fetch GET 200 https://cdn.npmmirror.com/packages/%40types/morgan/1.9.10/morgan-1.9.10.tgz 325ms (cache miss)
77 silly ADD node_modules/@types/serve-favicon
78 silly ADD node_modules/@types/node-schedule
79 silly ADD node_modules/@types/morgan
80 silly ADD node_modules/@types/express-session
81 silly ADD node_modules/@types/cookie-parser
82 verbose cwd /home/<USER>/.cache/typescript/5.8
83 verbose os Linux 6.11.0-29-generic
84 verbose node v20.19.3
85 verbose npm  v10.8.2
86 verbose exit 0
87 info ok
