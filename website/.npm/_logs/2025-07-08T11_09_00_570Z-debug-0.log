0 verbose cli /usr/local/bin/node /usr/local/bin/npm
1 info using npm@10.5.0
2 info using node@v18.20.2
3 timing npm:load:whichnode Completed in 1ms
4 timing config:load:defaults Completed in 2ms
5 timing config:load:file:/usr/local/lib/node_modules/npm/npmrc Completed in 1ms
6 timing config:load:builtin Completed in 1ms
7 timing config:load:cli Completed in 3ms
8 timing config:load:env Completed in 0ms
9 timing config:load:file:/home/<USER>/ronglianweb/.npmrc Completed in 0ms
10 timing config:load:project Completed in 1ms
11 timing config:load:file:/home/<USER>/.npmrc Completed in 1ms
12 timing config:load:user Completed in 1ms
13 timing config:load:file:/usr/local/etc/npmrc Completed in 0ms
14 timing config:load:global Completed in 0ms
15 timing config:load:setEnvs Completed in 1ms
16 timing config:load Completed in 12ms
17 timing npm:load:configload Completed in 12ms
18 timing config:load:flatten Completed in 3ms
19 timing npm:load:mkdirpcache Completed in 1ms
20 timing npm:load:mkdirplogs Completed in 0ms
21 verbose title npm start
22 verbose argv "start"
23 timing npm:load:setTitle Completed in 1ms
24 timing npm:load:display Completed in 1ms
25 verbose logfile logs-max:10 dir:/home/<USER>/.npm/_logs/2025-07-08T11_09_00_570Z-
26 verbose logfile /home/<USER>/.npm/_logs/2025-07-08T11_09_00_570Z-debug-0.log
27 timing npm:load:logFile Completed in 11ms
28 timing npm:load:timers Completed in 0ms
29 timing npm:load:configScope Completed in 0ms
30 timing npm:load Completed in 50ms
31 silly logfile start cleaning logs, removing 1 files
32 silly logfile done cleaning log files
