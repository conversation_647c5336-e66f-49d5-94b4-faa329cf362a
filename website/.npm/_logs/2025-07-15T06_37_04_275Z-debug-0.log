0 verbose cli /usr/bin/node /usr/bin/npm
1 info using npm@10.8.2
2 info using node@v20.19.3
3 silly config load:file:/usr/lib/node_modules/npm/npmrc
4 silly config load:file:/home/<USER>/.cache/typescript/5.8/.npmrc
5 silly config load:file:/home/<USER>/.npmrc
6 silly config load:file:/usr/etc/npmrc
7 verbose title npm install @types/multer@ts5.8
8 verbose argv "install" "--ignore-scripts" "@types/multer@ts5.8" "--save-dev" "--user-agent" "typesInstaller/5.8.3"
9 verbose logfile logs-max:10 dir:/home/<USER>/.npm/_logs/2025-07-15T06_37_04_275Z-
10 verbose logfile /home/<USER>/.npm/_logs/2025-07-15T06_37_04_275Z-debug-0.log
11 silly packumentCache heap:4345298944 maxSize:1086324736 maxEntrySize:543162368
12 silly logfile start cleaning logs, removing 1 files
13 silly logfile done cleaning log files
14 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fmulter cache-miss
15 http fetch GET 200 https://registry.npmmirror.com/@types%2fmulter 166ms (cache miss)
16 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fmulter set size:6756 disposed:false
17 silly idealTree buildDeps
18 silly fetch manifest @types/multer@2.0.0
19 silly packumentCache full:https://registry.npmmirror.com/@types%2fmulter cache-miss
20 http fetch GET 200 https://registry.npmmirror.com/@types%2fmulter 26ms (cache miss)
21 silly packumentCache full:https://registry.npmmirror.com/@types%2fmulter set size:14486 disposed:false
22 silly placeDep ROOT @types/multer@2.0.0 OK for:  want: 2.0.0
23 silly reify moves {}
24 silly audit bulk request {
24 silly audit   '@types/async': [ '3.2.24' ],
24 silly audit   '@types/body-parser': [ '1.19.6' ],
24 silly audit   '@types/brace-expansion': [ '1.1.2' ],
24 silly audit   '@types/caseless': [ '0.12.5' ],
24 silly audit   '@types/connect': [ '3.4.38' ],
24 silly audit   '@types/cors': [ '2.8.19' ],
24 silly audit   '@types/debug': [ '4.1.12' ],
24 silly audit   '@types/estree': [ '1.0.8' ],
24 silly audit   '@types/express': [ '5.0.3' ],
24 silly audit   '@types/express-serve-static-core': [ '5.0.6' ],
24 silly audit   '@types/http-errors': [ '2.0.5' ],
24 silly audit   '@types/json-schema': [ '7.0.15' ],
24 silly audit   '@types/less': [ '3.0.8' ],
24 silly audit   '@types/lodash': [ '4.17.20' ],
24 silly audit   '@types/mime': [ '1.3.5' ],
24 silly audit   '@types/ms': [ '2.1.0' ],
24 silly audit   '@types/node': [ '24.0.10' ],
24 silly audit   '@types/object-assign': [ '4.0.33' ],
24 silly audit   '@types/on-finished': [ '2.3.5' ],
24 silly audit   '@types/qs': [ '6.14.0' ],
24 silly audit   '@types/range-parser': [ '1.2.7' ],
24 silly audit   '@types/react': [ '19.1.8' ],
24 silly audit   '@types/request': [ '2.48.12' ],
24 silly audit   '@types/send': [ '0.17.5' ],
24 silly audit   '@types/serve-static': [ '1.15.8' ],
24 silly audit   '@types/stylus': [ '0.48.43' ],
24 silly audit   '@types/tough-cookie': [ '4.0.5' ],
24 silly audit   '@types/type-is': [ '1.6.7' ],
24 silly audit   '@types/vary': [ '1.1.3' ],
24 silly audit   asynckit: [ '0.4.0' ],
24 silly audit   'call-bind-apply-helpers': [ '1.0.2' ],
24 silly audit   'combined-stream': [ '1.0.8' ],
24 silly audit   csstype: [ '3.1.3' ],
24 silly audit   'delayed-stream': [ '1.0.0' ],
24 silly audit   'dunder-proto': [ '1.0.1' ],
24 silly audit   'es-define-property': [ '1.0.1' ],
24 silly audit   'es-errors': [ '1.3.0' ],
24 silly audit   'es-object-atoms': [ '1.1.1' ],
24 silly audit   'es-set-tostringtag': [ '2.1.0' ],
24 silly audit   'form-data': [ '2.5.3' ],
24 silly audit   'function-bind': [ '1.1.2' ],
24 silly audit   'get-intrinsic': [ '1.3.0' ],
24 silly audit   'get-proto': [ '1.0.1' ],
24 silly audit   gopd: [ '1.2.0' ],
24 silly audit   'has-symbols': [ '1.1.0' ],
24 silly audit   'has-tostringtag': [ '1.0.2' ],
24 silly audit   hasown: [ '2.0.2' ],
24 silly audit   'math-intrinsics': [ '1.1.0' ],
24 silly audit   'mime-db': [ '1.52.0' ],
24 silly audit   'mime-types': [ '2.1.35' ],
24 silly audit   'safe-buffer': [ '5.2.1' ],
24 silly audit   'types-registry': [ '0.1.729' ],
24 silly audit   'undici-types': [ '7.8.0' ],
24 silly audit   '@types/multer': [ '2.0.0' ]
24 silly audit }
25 silly tarball no local data for @types/multer@https://registry.npmmirror.com/@types/multer/-/multer-2.0.0.tgz. Extracting by manifest.
26 http fetch POST 404 https://registry.npmmirror.com/-/npm/v1/security/advisories/bulk 18ms
27 silly audit bulk request failed [object Object]
28 http fetch POST 404 https://registry.npmmirror.com/-/npm/v1/security/audits/quick 12ms
29 verbose audit error HttpErrorGeneral: 404 Not Found - POST https://registry.npmmirror.com/-/npm/v1/security/audits/quick - [NOT_IMPLEMENTED] /-/npm/v1/security/* not implemented yet
29 verbose audit error     at /usr/lib/node_modules/npm/node_modules/npm-registry-fetch/lib/check-response.js:95:15
29 verbose audit error     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
29 verbose audit error     at async [getReport] (/usr/lib/node_modules/npm/node_modules/@npmcli/arborist/lib/audit-report.js:336:21)
29 verbose audit error     at async AuditReport.run (/usr/lib/node_modules/npm/node_modules/@npmcli/arborist/lib/audit-report.js:106:19) {
29 verbose audit error   headers: [Object: null prototype] {
29 verbose audit error     server: [ 'Tengine' ],
29 verbose audit error     date: [ 'Tue, 15 Jul 2025 06:37:04 GMT' ],
29 verbose audit error     'content-type': [ 'application/json' ],
29 verbose audit error     'transfer-encoding': [ 'chunked' ],
29 verbose audit error     connection: [ 'keep-alive' ],
29 verbose audit error     'strict-transport-security': [ 'max-age=5184000' ],
29 verbose audit error     via: [ 'kunlun3.cn5506[,404666]' ],
29 verbose audit error     'timing-allow-origin': [ '*' ],
29 verbose audit error     eagleid: [ '6f0db51717525614249544354e' ],
29 verbose audit error     'x-fetch-attempts': [ '1' ]
29 verbose audit error   },
29 verbose audit error   statusCode: 404,
29 verbose audit error   code: 'E404',
29 verbose audit error   method: 'POST',
29 verbose audit error   uri: 'https://registry.npmmirror.com/-/npm/v1/security/audits/quick',
29 verbose audit error   body: {
29 verbose audit error     error: '[NOT_IMPLEMENTED] /-/npm/v1/security/* not implemented yet'
29 verbose audit error   },
29 verbose audit error   pkgid: undefined
29 verbose audit error }
30 silly audit error [object Object]
31 silly audit report null
32 http fetch GET 200 https://cdn.npmmirror.com/packages/%40types/multer/2.0.0/multer-2.0.0.tgz 238ms (cache miss)
33 silly ADD node_modules/@types/multer
34 verbose cwd /home/<USER>/.cache/typescript/5.8
35 verbose os Linux 6.11.0-29-generic
36 verbose node v20.19.3
37 verbose npm  v10.8.2
38 verbose exit 0
39 info ok
