0 verbose cli /usr/bin/node /usr/bin/npm
1 info using npm@10.8.2
2 info using node@v20.19.3
3 silly config load:file:/usr/lib/node_modules/npm/npmrc
4 silly config load:file:/home/<USER>/ronglianweb/.npmrc
5 silly config load:file:/home/<USER>/.npmrc
6 silly config load:file:/usr/etc/npmrc
7 verbose title npm start
8 verbose argv "start"
9 verbose logfile logs-max:10 dir:/home/<USER>/.npm/_logs/2025-07-15T06_40_04_701Z-
10 verbose logfile /home/<USER>/.npm/_logs/2025-07-15T06_40_04_701Z-debug-0.log
11 silly logfile start cleaning logs, removing 1 files
12 silly logfile done cleaning log files
