{"_attachments": {}, "_id": "@types/multer", "_rev": "148649-61f1973806e2bc05a1db6b49", "description": "TypeScript definitions for multer", "dist-tags": {"latest": "2.0.0", "ts2.0": "1.3.2", "ts2.1": "1.3.5", "ts2.2": "1.3.8", "ts2.3": "1.3.10", "ts2.4": "1.3.10", "ts2.5": "1.3.10", "ts2.6": "1.3.10", "ts2.7": "1.3.10", "ts2.8": "1.4.3", "ts2.9": "1.4.3", "ts3.0": "1.4.4", "ts3.1": "1.4.4", "ts3.2": "1.4.4", "ts3.3": "1.4.5", "ts3.4": "1.4.5", "ts3.5": "1.4.5", "ts3.6": "1.4.7", "ts3.7": "1.4.7", "ts3.8": "1.4.7", "ts3.9": "1.4.7", "ts4.0": "1.4.7", "ts4.1": "1.4.7", "ts4.2": "1.4.7", "ts4.3": "1.4.7", "ts4.4": "1.4.7", "ts4.5": "1.4.11", "ts4.6": "1.4.11", "ts4.7": "1.4.11", "ts4.8": "1.4.12", "ts4.9": "1.4.12", "ts5.0": "1.4.12", "ts5.1": "2.0.0", "ts5.2": "2.0.0", "ts5.3": "2.0.0", "ts5.4": "2.0.0", "ts5.5": "2.0.0", "ts5.6": "2.0.0", "ts5.7": "2.0.0", "ts5.8": "2.0.0", "ts5.9": "2.0.0"}, "license": "MIT", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "name": "@types/multer", "readme": "# Installation\r\n> `npm install --save @types/multer`\r\n\r\n# Summary\r\nThis package contains type definitions for multer (https://github.com/expressjs/multer).\r\n\r\n# Details\r\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/multer.\r\n\r\n### Additional Details\r\n * Last updated: Tu<PERSON>, 01 Jul 2025 20:02:28 GMT\r\n * Dependencies: [@types/express](https://npmjs.com/package/@types/express)\r\n\r\n# Credits\r\nThese definitions were written by [jt000](https://github.com/jt000), [vili<PERSON><PERSON><PERSON>](https://github.com/vilic), [<PERSON>](https://github.com/DavidBR-SW), [<PERSON>](https://github.com/mxl), [<PERSON>yunSeob Lee](https://github.com/hyunseob), [<PERSON>](https://github.com/Pierre<PERSON>chuente), [<PERSON><PERSON><PERSON>](https://github.com/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>), and [<PERSON>](https://github.com/b<PERSON>han<PERSON><PERSON>).\r\n", "time": {"created": "2022-01-26T18:47:20.124Z", "modified": "2025-07-01T20:02:51.918Z", "1.4.7": "2021-07-07T00:06:59.713Z", "1.4.6": "2021-06-18T10:31:55.872Z", "1.4.5": "2020-12-08T03:12:47.775Z", "1.4.4": "2020-08-14T08:23:10.020Z", "1.4.3": "2020-04-17T20:55:22.357Z", "1.4.2": "2020-02-06T18:48:53.595Z", "1.4.1": "2020-02-06T17:33:44.064Z", "1.4.0": "2020-01-27T22:09:53.583Z", "1.3.10": "2019-09-25T18:34:23.362Z", "1.3.9": "2019-08-19T01:10:44.649Z", "1.3.8": "2019-08-07T20:49:31.172Z", "1.3.7": "2018-06-13T19:23:11.910Z", "1.3.6": "2017-11-09T09:40:03.855Z", "1.3.5": "2017-10-26T19:32:31.628Z", "1.3.4": "2017-10-25T01:13:05.323Z", "1.3.3": "2017-08-23T17:54:40.212Z", "1.3.2": "2017-06-19T14:21:18.494Z", "1.3.1": "2017-06-15T20:16:47.647Z", "1.3.0": "2017-06-12T22:15:52.844Z", "0.0.34": "2017-06-02T13:56:26.886Z", "0.0.33": "2017-01-30T18:37:50.578Z", "0.0.32": "2016-09-19T17:54:49.236Z", "0.0.31": "2016-08-25T18:50:38.978Z", "0.0.30": "2016-08-22T17:58:25.069Z", "0.0.29": "2016-08-19T15:34:41.404Z", "0.0.28": "2016-08-02T16:01:08.415Z", "0.0.27": "2016-07-14T15:27:21.371Z", "0.0.26-alpha": "2016-07-08T20:56:17.227Z", "0.0.25-alpha": "2016-07-04T00:53:11.895Z", "0.0.24-alpha": "2016-07-02T03:02:06.407Z", "0.0.23-alpha": "2016-07-01T23:30:38.566Z", "0.0.22-alpha": "2016-07-01T20:09:37.524Z", "0.0.21-alpha": "2016-05-25T05:30:46.670Z", "0.0.20-alpha": "2016-05-20T20:13:25.014Z", "0.0.15-alpha": "2016-05-19T21:46:47.406Z", "0.0.14-alpha": "2016-05-17T18:23:25.222Z", "1.4.8": "2023-09-27T10:27:15.757Z", "1.4.9": "2023-10-18T09:05:40.550Z", "1.4.10": "2023-11-07T11:28:31.731Z", "1.4.11": "2023-11-21T00:37:06.924Z", "1.4.12": "2024-08-23T18:09:08.446Z", "1.4.13": "2025-06-07T02:18:08.532Z", "2.0.0": "2025-07-01T20:02:40.540Z"}, "versions": {"1.4.7": {"name": "@types/multer", "version": "1.4.7", "description": "TypeScript definitions for multer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/multer", "license": "MIT", "contributors": [{"name": "jt000", "url": "https://github.com/jt000", "githubUsername": "jt000"}, {"name": "vili<PERSON><PERSON><PERSON>", "url": "https://github.com/vilic", "githubUsername": "vilic"}, {"name": "<PERSON>-<PERSON>", "url": "https://github.com/DavidBR-SW", "githubUsername": "DavidBR-SW"}, {"name": "<PERSON>", "url": "https://github.com/mxl", "githubUsername": "mxl"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/hyunseob", "githubUsername": "hyun<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/thrymgjol", "githubUsername": "thrym<PERSON><PERSON>l"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/multer"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "32b2d0cc5d84f278945858a1be2b00bcdd046da7c6e0886d5afd0d1e002ee95a", "typeScriptVersion": "3.6", "_id": "@types/multer@1.4.7", "dist": {"shasum": "89cf03547c28c7bbcc726f029e2a76a7232cc79e", "size": 4950, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-1.4.7.tgz", "integrity": "sha512-/SNsDidUFCvqqcWDwxv2feww/yqhNeTRL5CVoL3jU4Goc4kKEL10T7Eye65ZqPNi4HRx8sAEX59pV1aEH7drNA=="}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/multer_1.4.7_1625616419507_0.5172388206384964"}, "_hasShrinkwrap": false, "publish_time": 1625616419713, "_cnpm_publish_time": 1625616419713, "_cnpmcore_publish_time": "2021-12-16T13:55:39.500Z"}, "1.4.6": {"name": "@types/multer", "version": "1.4.6", "description": "TypeScript definitions for multer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/multer", "license": "MIT", "contributors": [{"name": "jt000", "url": "https://github.com/jt000", "githubUsername": "jt000"}, {"name": "vili<PERSON><PERSON><PERSON>", "url": "https://github.com/vilic", "githubUsername": "vilic"}, {"name": "<PERSON>-<PERSON>", "url": "https://github.com/DavidBR-SW", "githubUsername": "DavidBR-SW"}, {"name": "<PERSON>", "url": "https://github.com/mxl", "githubUsername": "mxl"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/hyunseob", "githubUsername": "hyun<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/thrymgjol", "githubUsername": "thrym<PERSON><PERSON>l"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/multer"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "806207f74c254d0c52a7fbd0b2d3f4cf79b8af3c66b55eb85c93e3086e70d0d4", "typeScriptVersion": "3.6", "_id": "@types/multer@1.4.6", "dist": {"shasum": "411950b7a99ba0de6ee8f6e3713f4628980cdc73", "size": 4929, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-1.4.6.tgz", "integrity": "sha512-F4EZ+KRrzdiSm3jSFj1GVUlw3zWXus5nXYBbrQW/0MGIUv9YHw1dM0cJOxq++v2+Gl4IBdSDvQ3YCORLdyCU+Q=="}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/multer_1.4.6_1624012315731_0.04727514806483191"}, "_hasShrinkwrap": false, "publish_time": 1624012315872, "_cnpm_publish_time": 1624012315872, "_cnpmcore_publish_time": "2021-12-16T13:55:39.756Z"}, "1.4.5": {"name": "@types/multer", "version": "1.4.5", "description": "TypeScript definitions for multer", "license": "MIT", "contributors": [{"name": "jt000", "url": "https://github.com/jt000", "githubUsername": "jt000"}, {"name": "vili<PERSON><PERSON><PERSON>", "url": "https://github.com/vilic", "githubUsername": "vilic"}, {"name": "<PERSON>-<PERSON>", "url": "https://github.com/DavidBR-SW", "githubUsername": "DavidBR-SW"}, {"name": "<PERSON>", "url": "https://github.com/mxl", "githubUsername": "mxl"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/hyunseob", "githubUsername": "hyun<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/thrymgjol", "githubUsername": "thrym<PERSON><PERSON>l"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/multer"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "dc2bf6ba069755216abb545cb2b68fdc90edc0dfa5268c90b79c3942a7de202a", "typeScriptVersion": "3.3", "_id": "@types/multer@1.4.5", "dist": {"shasum": "db0557562307e9adb6661a9500c334cd7ddd0cd9", "size": 4913, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-1.4.5.tgz", "integrity": "sha512-9b/0a8JyrR0r2nQhL73JR86obWL7cogfX12augvlrvcpciCo/hkvEsgu80Z4S2g2DHGVXHr8pUIi1VhqFJ8Ufw=="}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/multer_1.4.5_1607397167638_0.8147978905170554"}, "_hasShrinkwrap": false, "publish_time": 1607397167775, "_cnpm_publish_time": 1607397167775, "_cnpmcore_publish_time": "2021-12-16T13:55:40.007Z"}, "1.4.4": {"name": "@types/multer", "version": "1.4.4", "description": "TypeScript definitions for multer", "license": "MIT", "contributors": [{"name": "jt000", "url": "https://github.com/jt000", "githubUsername": "jt000"}, {"name": "vili<PERSON><PERSON><PERSON>", "url": "https://github.com/vilic", "githubUsername": "vilic"}, {"name": "<PERSON>-<PERSON>", "url": "https://github.com/DavidBR-SW", "githubUsername": "DavidBR-SW"}, {"name": "<PERSON>", "url": "https://github.com/mxl", "githubUsername": "mxl"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/hyunseob", "githubUsername": "hyun<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/thrymgjol", "githubUsername": "thrym<PERSON><PERSON>l"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/multer"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "99f24bf8ae55f245d153d04faaf71c3117ba42c303a6429edd04e50389dfc5c2", "typeScriptVersion": "3.0", "_id": "@types/multer@1.4.4", "dist": {"shasum": "bb5d9abc410da82726ceca74008bb81813349a88", "size": 4909, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-1.4.4.tgz", "integrity": "sha512-wdfkiKBBEMTODNbuF3J+qDDSqJxt50yB9pgDiTcFew7f97Gcc7/sM4HR66ofGgpJPOALWOqKAch4gPyqEXSkeQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/multer_1.4.4_1597393389916_0.03627394726392641"}, "_hasShrinkwrap": false, "publish_time": 1597393390020, "_cnpm_publish_time": 1597393390020, "_cnpmcore_publish_time": "2021-12-16T13:55:40.210Z"}, "1.4.3": {"name": "@types/multer", "version": "1.4.3", "description": "TypeScript definitions for multer", "license": "MIT", "contributors": [{"name": "jt000", "url": "https://github.com/jt000", "githubUsername": "jt000"}, {"name": "vili<PERSON><PERSON><PERSON>", "url": "https://github.com/vilic", "githubUsername": "vilic"}, {"name": "<PERSON>-<PERSON>", "url": "https://github.com/DavidBR-SW", "githubUsername": "DavidBR-SW"}, {"name": "<PERSON>", "url": "https://github.com/mxl", "githubUsername": "mxl"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/hyunseob", "githubUsername": "hyun<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/thrymgjol", "githubUsername": "thrym<PERSON><PERSON>l"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/multer"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "28d92dc60040ea6bfb62220f475a3d1c90693c5e2827bfb1e4bef0c70a730a01", "typeScriptVersion": "2.8", "_id": "@types/multer@1.4.3", "dist": {"shasum": "bdff74b334c38a8ee1de9fbedb5d1d3dbc377422", "size": 4852, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-1.4.3.tgz", "integrity": "sha512-tWsKbF5LYtXrJ7eOfI0aLBgEv9B7fnJe1JRXTj5+Z6EMfX0yHVsRFsNGnKyN8Bs0gtDv+JR37xAqsPnALyVTqg=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/multer_1.4.3_1587156921970_0.9698466520522773"}, "_hasShrinkwrap": false, "publish_time": 1587156922357, "_cnpm_publish_time": 1587156922357, "_cnpmcore_publish_time": "2021-12-16T13:55:40.431Z"}, "1.4.2": {"name": "@types/multer", "version": "1.4.2", "description": "TypeScript definitions for multer", "license": "MIT", "contributors": [{"name": "jt000", "url": "https://github.com/jt000", "githubUsername": "jt000"}, {"name": "vili<PERSON><PERSON><PERSON>", "url": "https://github.com/vilic", "githubUsername": "vilic"}, {"name": "<PERSON>-<PERSON>", "url": "https://github.com/DavidBR-SW", "githubUsername": "DavidBR-SW"}, {"name": "<PERSON>", "url": "https://github.com/mxl", "githubUsername": "mxl"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/hyunseob", "githubUsername": "hyun<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/thrymgjol", "githubUsername": "thrym<PERSON><PERSON>l"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/multer"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "434066b0758fdcc2f3cf43e1beb3fd176230b281e3791b382e0e8588502d03bb", "typeScriptVersion": "2.8", "_id": "@types/multer@1.4.2", "dist": {"shasum": "f73e9eab7d8e0ff2842630e7968b88336cbb7e1c", "size": 4786, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-1.4.2.tgz", "integrity": "sha512-pVcPwuC0FbVcLhopJHx8Ro3WSXjvVvEpJMfy+DFAL/3DwNYAQH+hf/Vq+PqoS5kM4mng7L/4upzXhP/12yWh4w=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/multer_1.4.2_1581014933506_0.17770616584476873"}, "_hasShrinkwrap": false, "publish_time": 1581014933595, "_cnpm_publish_time": 1581014933595, "_cnpmcore_publish_time": "2021-12-16T13:55:40.639Z"}, "1.4.1": {"name": "@types/multer", "version": "1.4.1", "description": "TypeScript definitions for multer", "license": "MIT", "contributors": [{"name": "jt000", "url": "https://github.com/jt000", "githubUsername": "jt000"}, {"name": "vili<PERSON><PERSON><PERSON>", "url": "https://vilic.github.io/"}, {"name": "<PERSON>-<PERSON>", "url": "https://github.com/DavidBR-SW", "githubUsername": "DavidBR-SW"}, {"name": "<PERSON>", "url": "https://github.com/mxl", "githubUsername": "mxl"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/hyunseob", "githubUsername": "hyun<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/thrymgjol", "githubUsername": "thrym<PERSON><PERSON>l"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/multer"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "34b95067ce521b81d3ae08376d23fcc6b134f7d821405a0df7bba030438c45cf", "typeScriptVersion": "2.8", "_id": "@types/multer@1.4.1", "dist": {"shasum": "b9036b1ff7f908ec38c120732d30728210af0235", "size": 4815, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-1.4.1.tgz", "integrity": "sha512-7ftkMhORzPI2XDNecu5Hv9ISUn48dO35lxQDhboOfmDZ0uYmdcFPenteqN0JYTpr9uEt0ZKhuOKmPf55g7Nv3g=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/multer_1.4.1_1581010423967_0.8182847898450161"}, "_hasShrinkwrap": false, "publish_time": 1581010424064, "_cnpm_publish_time": 1581010424064, "_cnpmcore_publish_time": "2021-12-16T13:55:40.862Z"}, "1.4.0": {"name": "@types/multer", "version": "1.4.0", "description": "TypeScript definitions for multer", "license": "MIT", "contributors": [{"name": "jt000", "url": "https://github.com/jt000", "githubUsername": "jt000"}, {"name": "vili<PERSON><PERSON><PERSON>", "url": "https://vilic.github.io/"}, {"name": "<PERSON>-<PERSON>", "url": "https://github.com/DavidBR-SW", "githubUsername": "DavidBR-SW"}, {"name": "<PERSON>", "url": "https://github.com/mxl", "githubUsername": "mxl"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/hyunseob", "githubUsername": "hyun<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/thrymgjol", "githubUsername": "thrym<PERSON><PERSON>l"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/multer"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "4fdef18195bb50db06c46659e9e36ea0cc8a48f795296ebe25249431a9fd4fcb", "typeScriptVersion": "2.8", "_id": "@types/multer@1.4.0", "dist": {"shasum": "a4a83bee02696f61f63b65a87f9ebe8bdf12b767", "size": 4726, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-1.4.0.tgz", "integrity": "sha512-mF3lGy1HTixLELNGufKTvLWGUZKd0Amz/nZYj79nzCXWye2wTlgIo4CZ+mze7xMkezcWmQviV9uXYRMpZ0qk1w=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/multer_1.4.0_1580162993437_0.4827782214564196"}, "_hasShrinkwrap": false, "publish_time": 1580162993583, "_cnpm_publish_time": 1580162993583, "_cnpmcore_publish_time": "2021-12-16T13:55:41.075Z"}, "1.3.10": {"name": "@types/multer", "version": "1.3.10", "description": "TypeScript definitions for multer", "license": "MIT", "contributors": [{"name": "jt000", "url": "https://github.com/jt000", "githubUsername": "jt000"}, {"name": "vili<PERSON><PERSON><PERSON>", "url": "https://vilic.github.io/"}, {"name": "<PERSON>-<PERSON>", "url": "https://github.com/DavidBR-SW", "githubUsername": "DavidBR-SW"}, {"name": "<PERSON>", "url": "https://github.com/mxl", "githubUsername": "mxl"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/hyunseob", "githubUsername": "hyun<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/multer"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "39d3fe14ef10f272311215dd61734a2473e1cd5a4c82d4ee00571742dfbfacfb", "typeScriptVersion": "2.3", "_id": "@types/multer@1.3.10", "dist": {"shasum": "d7afbd916f688fceb4460320e62a8ad1ab3e3cad", "size": 3308, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-1.3.10.tgz", "integrity": "sha512-3hECfz+W0ix/LvPanp87mjO3kOyDnJYTpY9y7gdBxXnYXqEcj21pD0lW7KEUFFr8CHrDF5Mhh7o241KLEXDRoQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/multer_1.3.10_1569436463209_0.029305921345670116"}, "_hasShrinkwrap": false, "publish_time": 1569436463362, "_cnpm_publish_time": 1569436463362, "_cnpmcore_publish_time": "2021-12-16T13:55:41.312Z"}, "1.3.9": {"name": "@types/multer", "version": "1.3.9", "description": "TypeScript definitions for multer", "license": "MIT", "contributors": [{"name": "jt000", "url": "https://github.com/jt000", "githubUsername": "jt000"}, {"name": "vili<PERSON><PERSON><PERSON>", "url": "https://vilic.github.io/"}, {"name": "<PERSON>-<PERSON>", "url": "https://github.com/DavidBR-SW", "githubUsername": "DavidBR-SW"}, {"name": "<PERSON>", "url": "https://github.com/mxl", "githubUsername": "mxl"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/hyunseob", "githubUsername": "hyun<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/multer"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "d38c602a850481b3cef261133855f151900b0f434097911bfdd5ea603d6693d3", "typeScriptVersion": "2.3", "_id": "@types/multer@1.3.9", "dist": {"shasum": "da72b5d616cba2aa0eede71d9459e4234ec33a60", "size": 3312, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-1.3.9.tgz", "integrity": "sha512-aKUwxl4yH87G8b3wgmruBVdOWiG9qqmc4Epjt5Uz3mqaMZwx3dkMm4tHn5nnwBk2NT+X4t5yADJiEv/NfMFQIg=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/multer_1.3.9_1566177044551_0.16231153028453882"}, "_hasShrinkwrap": false, "publish_time": 1566177044649, "_cnpm_publish_time": 1566177044649, "_cnpmcore_publish_time": "2021-12-16T13:55:41.582Z"}, "1.3.8": {"name": "@types/multer", "version": "1.3.8", "description": "TypeScript definitions for multer", "license": "MIT", "contributors": [{"name": "jt000", "url": "https://github.com/jt000", "githubUsername": "jt000"}, {"name": "vili<PERSON><PERSON><PERSON>", "url": "https://vilic.github.io/"}, {"name": "<PERSON>-<PERSON>", "url": "https://github.com/DavidBR-SW", "githubUsername": "DavidBR-SW"}, {"name": "<PERSON>", "url": "https://github.com/mxl", "githubUsername": "mxl"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/hyunseob", "githubUsername": "hyun<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/multer"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "da5af1384e1a4902d232c8d1ff15103b13c1c651fa536cc07c7ca539f87f3686", "typeScriptVersion": "2.2", "_id": "@types/multer@1.3.8", "dist": {"shasum": "20e170a7b5c52a5c86aa555e2b586ae242d455a1", "size": 3311, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-1.3.8.tgz", "integrity": "sha512-q6y8wkcsKTCxaJBvVmL9hdtQ8p3uAex1pP0zi8CKa8uYDzrKzNn2j5/uaOiaIuMDqDZBhNg1rbiF9MyVMbwQ+w=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/multer_1.3.8_1565210971061_0.9560392609184498"}, "_hasShrinkwrap": false, "publish_time": 1565210971172, "_cnpm_publish_time": 1565210971172, "_cnpmcore_publish_time": "2021-12-16T13:55:41.781Z"}, "1.3.7": {"name": "@types/multer", "version": "1.3.7", "description": "TypeScript definitions for multer", "license": "MIT", "contributors": [{"name": "jt000", "url": "https://github.com/jt000", "githubUsername": "jt000"}, {"name": "vili<PERSON><PERSON><PERSON>", "url": "https://vilic.github.io/"}, {"name": "<PERSON>-<PERSON>", "url": "https://github.com/DavidBR-SW", "githubUsername": "DavidBR-SW"}, {"name": "<PERSON>", "url": "https://github.com/mxl", "githubUsername": "mxl"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/hyunseob", "githubUsername": "hyun<PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "a9c7854c43c33463a1917da57c717576a475ee46ba07404ef1366629d32b1a01", "typeScriptVersion": "2.2", "_id": "@types/multer@1.3.7", "dist": {"shasum": "9fe1de9f44f401ff2eaf0d4468cf16935a9c6866", "size": 3225, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-1.3.7.tgz", "integrity": "sha512-Lx4rNtGajRGtcVwJe1sKPAkAuBBWq8TOuimKJfOfK7ayY1Jc+18Lx00GjagLeIwaH2+OvFJvCv8tz+pvbt3OoA=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/multer_1.3.7_1528917791822_0.7432893970382835"}, "_hasShrinkwrap": false, "publish_time": 1528917791910, "_cnpm_publish_time": 1528917791910, "_cnpmcore_publish_time": "2021-12-16T13:55:41.991Z"}, "1.3.6": {"name": "@types/multer", "version": "1.3.6", "description": "TypeScript definitions for multer", "license": "MIT", "contributors": [{"name": "jt000", "url": "https://github.com/jt000", "githubUsername": "jt000"}, {"name": "vili<PERSON><PERSON><PERSON>", "url": "https://vilic.github.io/"}, {"name": "<PERSON>-<PERSON>", "url": "https://github.com/DavidBR-SW", "githubUsername": "DavidBR-SW"}, {"name": "<PERSON>", "url": "https://github.com/mxl", "githubUsername": "mxl"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/hyunseob", "githubUsername": "hyun<PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "c8edeb137621361799fade43a2f71e21c77c9c4c1904595ce3e14485a89537ea", "typeScriptVersion": "2.2", "_id": "@types/multer@1.3.6", "dist": {"shasum": "e00454074bf9fd86d20ea652eba9850eb76604c4", "size": 3146, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-1.3.6.tgz", "integrity": "sha512-MAFQ/UqkLtnweDfSr1je71ed0L1XIL/lL/cfpjE+eJ9gDekObXCVHYCSZXxSfTTfN8BdrP77O6YzQ1Yi4lglGg=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/multer-1.3.6.tgz_1510220403790_0.3195154007989913"}, "directories": {}, "publish_time": 1510220403855, "_hasShrinkwrap": false, "_cnpm_publish_time": 1510220403855, "_cnpmcore_publish_time": "2021-12-16T13:55:42.197Z"}, "1.3.5": {"name": "@types/multer", "version": "1.3.5", "description": "TypeScript definitions for multer", "license": "MIT", "contributors": [{"name": "jt000", "url": "https://github.com/jt000", "githubUsername": "jt000"}, {"name": "vili<PERSON><PERSON><PERSON>", "url": "https://vilic.github.io/"}, {"name": "<PERSON>-<PERSON>", "url": "https://github.com/DavidBR-SW", "githubUsername": "DavidBR-SW"}, {"name": "<PERSON>", "url": "https://github.com/mxl", "githubUsername": "mxl"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/hyunseob", "githubUsername": "hyun<PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "bd6761085fc55dbabc6c3165ebd23cd7e2afaabffff1a0ea61c67d7947d7cbf7", "typeScriptVersion": "2.1", "_id": "@types/multer@1.3.5", "dist": {"shasum": "867e92ee3f726c355a5233975a3535b6c6385a90", "size": 3139, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-1.3.5.tgz", "integrity": "sha512-D+oGz0QIsnbyNROmgvZOHnr5UzzcC6k/WN62Tr8i4ZGxxbz5sMfhQ1soLp+lDb4m5k75v4p6kNZ4EaQ82EQxdA=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/multer-1.3.5.tgz_1509046350963_0.7981942037586123"}, "directories": {}, "publish_time": 1509046351628, "_hasShrinkwrap": false, "_cnpm_publish_time": 1509046351628, "_cnpmcore_publish_time": "2021-12-16T13:55:42.435Z"}, "1.3.4": {"name": "@types/multer", "version": "1.3.4", "description": "TypeScript definitions for multer", "license": "MIT", "contributors": [{"name": "jt000", "url": "https://github.com/jt000", "githubUsername": "jt000"}, {"name": "vili<PERSON><PERSON><PERSON>", "url": "https://vilic.github.io/"}, {"name": "<PERSON>-<PERSON>", "url": "https://github.com/DavidBR-SW", "githubUsername": "DavidBR-SW"}, {"name": "<PERSON>", "url": "https://github.com/mxl", "githubUsername": "mxl"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/hyunseob", "githubUsername": "hyun<PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "f1baed624d5e45aa83717e12c4f02bf03e088b1beb1d901ccce3224f6b227a28", "typeScriptVersion": "2.3", "_id": "@types/multer@1.3.4", "dist": {"shasum": "f09c7d4136c28510f1be02c89a46d1a1a9547e82", "size": 3139, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-1.3.4.tgz", "integrity": "sha512-mwVcs5jku+IMZbaD1Dj2kT5wBSBz0gagyC60xoH/XRu85cLgvwzYqtFPLo/kfmBapZQGD0dnF9SsqncoUbgiiQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/multer-1.3.4.tgz_1508893985260_0.7431703652255237"}, "directories": {}, "publish_time": 1508893985323, "_hasShrinkwrap": false, "_cnpm_publish_time": 1508893985323, "_cnpmcore_publish_time": "2021-12-16T13:55:42.626Z"}, "1.3.3": {"name": "@types/multer", "version": "1.3.3", "description": "TypeScript definitions for multer", "license": "MIT", "contributors": [{"name": "jt000", "url": "https://github.com/jt000"}, {"name": "vili<PERSON><PERSON><PERSON>", "url": "https://vilic.github.io/"}, {"name": "<PERSON>-<PERSON>", "url": "https://github.com/DavidBR-SW"}, {"name": "<PERSON>", "url": "https://github.com/mxl"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/hyunseob"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "b72f3eea1494d4de156bd2a0c5a4e1ce0805b04e43371c01cb55d757b8a8f406", "typeScriptVersion": "2.1", "_id": "@types/multer@1.3.3", "dist": {"shasum": "57437b639dc741e59c2699139cb95dfe3ef5b21a", "size": 3115, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-1.3.3.tgz", "integrity": "sha512-a4vEHvs9MEyvHwb2/rw3Cg0MaPrp1jbEEWrhBL9xf457AYS79bOajcfkYCEICH+1NExZkqxxtZKydKIX5UTbmg=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/multer-1.3.3.tgz_1503510880096_0.9101535566151142"}, "directories": {}, "publish_time": 1503510880212, "_hasShrinkwrap": false, "_cnpm_publish_time": 1503510880212, "_cnpmcore_publish_time": "2021-12-16T13:55:42.822Z"}, "1.3.2": {"name": "@types/multer", "version": "1.3.2", "description": "TypeScript definitions for multer", "license": "MIT", "contributors": [{"name": "jt000", "url": "https://github.com/jt000"}, {"name": "vili<PERSON><PERSON><PERSON>", "url": "https://vilic.github.io/"}, {"name": "<PERSON>-<PERSON>", "url": "https://github.com/DavidBR-SW"}, {"name": "<PERSON>", "url": "https://github.com/mxl"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "14a27a19c4e695eba529e52d878575351589ddfcf5b434ed8019b7b07e7dc1fd", "typeScriptVersion": "2.0", "_id": "@types/multer@1.3.2", "dist": {"shasum": "a42e71b6966c8fd209c29b38fe3d18abfb9f246c", "size": 3068, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-1.3.2.tgz", "integrity": "sha512-ukjp51K9uKuROlvt/FRL08jWd2V6pVWIlh7bnGxqXrXSbZKCTuGdR+wOsQbhU92jUAtiDGHQlG20wTavyzIqCg=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/multer-1.3.2.tgz_1497882078422_0.003268675645813346"}, "directories": {}, "publish_time": 1497882078494, "_hasShrinkwrap": false, "_cnpm_publish_time": 1497882078494, "_cnpmcore_publish_time": "2021-12-16T13:55:43.014Z"}, "1.3.1": {"name": "@types/multer", "version": "1.3.1", "description": "TypeScript definitions for multer", "license": "MIT", "contributors": [{"name": "jt000", "url": "https://github.com/jt000"}, {"name": "vili<PERSON><PERSON><PERSON>", "url": "https://vilic.github.io/"}, {"name": "<PERSON>-<PERSON>", "url": "https://github.com/DavidBR-SW"}, {"name": "<PERSON>", "url": "https://github.com/mxl"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "17a2c79ada60bf47e819f55483935d947f1885ad50f674e1dc500cb67a3dd526", "typeScriptVersion": "2.0", "_id": "@types/multer@1.3.1", "dist": {"shasum": "ef84b005ff008870f580d9065d3c9eb8e6476bea", "size": 3075, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-1.3.1.tgz", "integrity": "sha512-dT8al2uAUAjf6E1zu6CyG8gE6j08Y9Ym1609ESQUn2cEWTTzoJwMRNlvPsD7iocQ4cRVmKsxTs9vJjjTtc8Zow=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/multer-1.3.1.tgz_1497557807563_0.12144967471249402"}, "directories": {}, "publish_time": 1497557807647, "_hasShrinkwrap": false, "_cnpm_publish_time": 1497557807647, "_cnpmcore_publish_time": "2021-12-16T13:55:43.283Z"}, "1.3.0": {"name": "@types/multer", "version": "1.3.0", "description": "TypeScript definitions for multer", "license": "MIT", "contributors": [{"name": "jt000", "url": "https://github.com/jt000"}, {"name": "vili<PERSON><PERSON><PERSON>", "url": "https://vilic.github.io/"}, {"name": "<PERSON>-<PERSON>", "url": "https://github.com/DavidBR-SW"}, {"name": "<PERSON>", "url": "https://github.com/mxl"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "51017f3555912a9f47a93c76bf7260a3081153eade677ee86f623a26514437a5", "typeScriptVersion": "2.0", "_id": "@types/multer@1.3.0", "dist": {"shasum": "4b0edcc8f5976594b6a7f1c00c7f6f790e4c6a02", "size": 3072, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-1.3.0.tgz", "integrity": "sha512-R9O2SL+hJCmTfmo4IL9A/VXgaQ4mwmISJ5NqqWe04UWpMEYoV7D0Ah9Km0laGSDcmaSsRkH7HHiPa1h/jsfwow=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/multer-1.3.0.tgz_1497305750247_0.4338476452976465"}, "directories": {}, "publish_time": 1497305752844, "_hasShrinkwrap": false, "_cnpm_publish_time": 1497305752844, "_cnpmcore_publish_time": "2021-12-16T13:55:43.527Z"}, "0.0.34": {"name": "@types/multer", "version": "0.0.34", "description": "TypeScript definitions for multer", "license": "MIT", "contributors": [{"name": "jt000", "url": "https://github.com/jt000"}, {"name": "vili<PERSON><PERSON><PERSON>", "url": "https://vilic.github.io/"}, {"name": "<PERSON>-<PERSON>", "url": "https://github.com/DavidBR-SW"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "742c956c1f3bf5eeedf92ed2b7a1eab95b79da62fda40b6a6276c87f06722950", "typeScriptVersion": "2.0", "_id": "@types/multer@0.0.34", "dist": {"shasum": "4b542b380dcf59bced8b66294654dc67a7fab383", "size": 2966, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-0.0.34.tgz", "integrity": "sha512-bZxuW8Z44E1vusAJ1iMQnA/Y16df+7QMkSKWrxGf2jCrLU9xGGuK1pwEINuDC6OAy/dY9CFLdLib7xcHywk3rw=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/multer-0.0.34.tgz_1496411786559_0.923962791915983"}, "directories": {}, "publish_time": 1496411786886, "_hasShrinkwrap": false, "_cnpm_publish_time": 1496411786886, "_cnpmcore_publish_time": "2021-12-16T13:55:43.733Z"}, "0.0.33": {"name": "@types/multer", "version": "0.0.33", "description": "TypeScript definitions for multer", "license": "MIT", "author": "jt000 <https://github.com/jt000>, vili<PERSON><PERSON><PERSON> <https://vilic.github.io/>, <PERSON> <https://github.com/DavidBR-SW>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "66bb0d7f4ae45f856645b3e208b601859802d2e2a5f9c8200e56c8918211e3ef", "typeScriptVersion": "2.0", "_id": "@types/multer@0.0.33", "dist": {"shasum": "0756df1f525ed8ef369dd1f26dd8345bd6467040", "size": 2446, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-0.0.33.tgz", "integrity": "sha512-8WfLJeBvbY61h5fg6A8xGzkz6ULRARD90c67XsrUgUJEEtZ8y4EGREQ8WL2XJ8CFLRb7qIMMd1WEILLHXjysWA=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/multer-0.0.33.tgz_1485801470334_0.9120361136738211"}, "directories": {}, "publish_time": 1485801470578, "_hasShrinkwrap": false, "_cnpm_publish_time": 1485801470578, "_cnpmcore_publish_time": "2021-12-16T13:55:43.975Z"}, "0.0.32": {"name": "@types/multer", "version": "0.0.32", "description": "TypeScript definitions for multer", "license": "MIT", "author": "jt000 <https://github.com/jt000>, vili<PERSON><PERSON><PERSON> <https://vilic.github.io/>, <PERSON> <https://github.com/DavidBR-SW>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typings": "index.d.ts", "typesPublisherContentHash": "9ce1c5bfb386ca9e83bd1b1a0411375861b935ea18931e3b8dddd9061ab1b108", "_id": "@types/multer@0.0.32", "dist": {"shasum": "f89c751227dc20b7c933c309a3e7467c499fcdec", "size": 2440, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-0.0.32.tgz", "integrity": "sha512-uYWQ/6nrYySWaCTGxQDMl4H+UCX55e1TC7qUeHHkN+U0n1Md+nE/jlpb5Hyeih9DX2vDG3ClabnKnMnkjxRXkA=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/multer-0.0.32.tgz_1474307686124_0.5514149062801152"}, "directories": {}, "publish_time": 1474307689236, "_hasShrinkwrap": false, "_cnpm_publish_time": 1474307689236, "_cnpmcore_publish_time": "2021-12-16T13:55:44.212Z"}, "0.0.31": {"name": "@types/multer", "version": "0.0.31", "description": "TypeScript definitions for multer", "license": "MIT", "author": "jt000 <https://github.com/jt000>, vili<PERSON><PERSON><PERSON> <https://vilic.github.io/>, <PERSON> <https://github.com/DavidBR-SW>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "4.0.*"}, "typings": "index.d.ts", "_id": "@types/multer@0.0.31", "dist": {"shasum": "7abba469ff4e5ec86bdf72e78ac5ca92a5a28085", "size": 2413, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-0.0.31.tgz", "integrity": "sha512-IpkqqVbEtIzeCnzLwUAEnTjbMgk9sKqjeYC3qoou+RY8AvQz8x8A2PiB/4nfWM8HwKnTWWiYwRkKrpcedKOZYQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/multer-0.0.31.tgz_1472151037066_0.9337379653006792"}, "directories": {}, "publish_time": 1472151038978, "_hasShrinkwrap": false, "_cnpm_publish_time": 1472151038978, "_cnpmcore_publish_time": "2021-12-16T13:55:44.663Z"}, "0.0.30": {"name": "@types/multer", "version": "0.0.30", "description": "TypeScript definitions for multer", "license": "MIT", "author": "jt000 <https://github.com/jt000>, vili<PERSON><PERSON><PERSON> <https://vilic.github.io/>, <PERSON> <https://github.com/DavidBR-SW>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "4.0.*"}, "typings": "index.d.ts", "_id": "@types/multer@0.0.30", "dist": {"shasum": "b212a6f61514ccfa539bc329f9dee46de77e8868", "size": 2413, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-0.0.30.tgz", "integrity": "sha512-mpno0QiHnw/elwNrRiyBaANXXto44ANI/2kze4C77qYOahz+9Zzjj4SIV6ikAtcBTPw33SNCmzW2JBqBlW9lDA=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/multer-0.0.30.tgz_1471888704850_0.27797033288516104"}, "directories": {}, "publish_time": 1471888705069, "_hasShrinkwrap": false, "_cnpm_publish_time": 1471888705069, "_cnpmcore_publish_time": "2021-12-16T13:55:44.904Z"}, "0.0.29": {"name": "@types/multer", "version": "0.0.29", "description": "TypeScript definitions for multer", "license": "MIT", "author": "jt000 <https://github.com/jt000>, vili<PERSON><PERSON><PERSON> <https://vilic.github.io/>, <PERSON> <https://github.com/DavidBR-SW>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "4.0.*"}, "typings": "index.d.ts", "_id": "@types/multer@0.0.29", "dist": {"shasum": "377cc230ecdf43508f34c9c27247afafb9988343", "size": 2346, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-0.0.29.tgz", "integrity": "sha512-irHpm4Zf6FBcjXW4PTM+Rs+6/+A4hSSm4QHPa1QPo9LefM/AhZiZxzQDqXD+JnjD7iwZJy8W3ChMFqFgGre1tw=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/multer-0.0.29.tgz_1471620880174_0.5479561432730407"}, "directories": {}, "publish_time": 1471620881404, "_hasShrinkwrap": false, "_cnpm_publish_time": 1471620881404, "_cnpmcore_publish_time": "2021-12-16T13:55:45.146Z"}, "0.0.28": {"name": "@types/multer", "version": "0.0.28", "description": "TypeScript definitions for multer", "license": "MIT", "author": "jt000 <https://github.com/jt000>, vili<PERSON><PERSON><PERSON> <https://vilic.github.io/>, <PERSON> <https://github.com/DavidBR-SW>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "4.0.*"}, "typings": "index.d.ts", "_id": "@types/multer@0.0.28", "dist": {"shasum": "b537c75d5a648aa7837ca7730d85193d1b8103ec", "size": 11776, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-0.0.28.tgz", "integrity": "sha512-3xQ11IBY01JecEgiokCaVTcL0kBWnFu6qAMkkuDSiIfcvN1/y9M0Y3S/nSfii9DjIhCGKunGGuVz01xn7kv/eQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/multer-0.0.28.tgz_1470153667422_0.01898067258298397"}, "directories": {}, "publish_time": 1470153668415, "_hasShrinkwrap": false, "_cnpm_publish_time": 1470153668415, "_cnpmcore_publish_time": "2021-12-16T13:55:45.416Z"}, "0.0.27": {"name": "@types/multer", "version": "0.0.27", "description": "TypeScript definitions for multer", "main": "", "scripts": {}, "author": {"name": "jt000", "email": "https://github.com/jt000"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "4.0.*"}, "_id": "@types/multer@0.0.27", "_shasum": "232df2335460fd2cb93944458b37f6721593a8a9", "_from": "output\\multer", "_resolved": "file:output\\multer", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "232df2335460fd2cb93944458b37f6721593a8a9", "size": 2332, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-0.0.27.tgz", "integrity": "sha512-ynGwPgxgnOp7uBMkl6DvPrLStSx0LMj3Dq5Og15/gbBEfOFjcZLzD2sxgWfizIyiJCicSW7vseTA7OoctmsC9Q=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/multer-0.0.27.tgz_1468510040897_0.20489567704498768"}, "directories": {}, "publish_time": 1468510041371, "_hasShrinkwrap": false, "_cnpm_publish_time": 1468510041371, "_cnpmcore_publish_time": "2021-12-16T13:55:45.630Z"}, "0.0.26-alpha": {"name": "@types/multer", "version": "0.0.26-alpha", "description": "TypeScript definitions for multer", "main": "", "scripts": {}, "author": {"name": "jt000", "email": "https://github.com/jt000"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "4.0.28-alpha"}, "_id": "@types/multer@0.0.26-alpha", "_shasum": "0345aaca49c39303bf27fcbeb9d939f6bb4316ce", "_from": "output\\multer", "_resolved": "file:output\\multer", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "0345aaca49c39303bf27fcbeb9d939f6bb4316ce", "size": 2339, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-0.0.26-alpha.tgz", "integrity": "sha512-rLThSLaEeABfIyo2CIEb9fuQnKrO/tT20xwV8Mmd92MxLKr+DMk2rtr6fc6YCiulSljjRY5VZRqxiGV+FtFL7w=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/multer-0.0.26-alpha.tgz_1468011376740_0.9743791485670954"}, "directories": {}, "publish_time": 1468011377227, "_hasShrinkwrap": false, "_cnpm_publish_time": 1468011377227, "_cnpmcore_publish_time": "2021-12-16T13:55:45.889Z"}, "0.0.25-alpha": {"name": "@types/multer", "version": "0.0.25-alpha", "description": "TypeScript definitions for multer", "main": "", "scripts": {}, "author": {"name": "jt000", "email": "https://github.com/jt000"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "4.0.27-alpha"}, "_id": "@types/multer@0.0.25-alpha", "_shasum": "4fe58e4caacefc7c217db29b34c0dfcd83232052", "_from": "output\\multer", "_resolved": "file:output\\multer", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "4fe58e4caacefc7c217db29b34c0dfcd83232052", "size": 2339, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-0.0.25-alpha.tgz", "integrity": "sha512-hQlxFwcf556Ah+tDuxEMh/jw89ZIuE10H+VPCYOf87L1awj3HZ5dvsL7qbpFq8Zmme/tioBJvuUHzJQAky2lYg=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/multer-0.0.25-alpha.tgz_1467593591412_0.6009115532506257"}, "directories": {}, "publish_time": 1467593591895, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467593591895, "_cnpmcore_publish_time": "2021-12-16T13:55:46.133Z"}, "0.0.24-alpha": {"name": "@types/multer", "version": "0.0.24-alpha", "description": "TypeScript definitions for multer", "main": "", "scripts": {}, "author": {"name": "jt000", "email": "https://github.com/jt000"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "4.0.26-alpha"}, "_id": "@types/multer@0.0.24-alpha", "_shasum": "480efd4b73a3a432e7c5e72e095735293aadaf29", "_from": "output\\multer", "_resolved": "file:output\\multer", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "480efd4b73a3a432e7c5e72e095735293aadaf29", "size": 2339, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-0.0.24-alpha.tgz", "integrity": "sha512-KsfKgjgU7E8ShY0bwe8MlJtoaqMb6AHB1SXdI1CG0FYLu2C/yqbykZGEUU38DxJ4YTnw7NzhuPAkvtMuXQlCMQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/multer-0.0.24-alpha.tgz_1467428525887_0.8581763757392764"}, "directories": {}, "publish_time": 1467428526407, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467428526407, "_cnpmcore_publish_time": "2021-12-16T13:55:46.323Z"}, "0.0.23-alpha": {"name": "@types/multer", "version": "0.0.23-alpha", "description": "TypeScript definitions for multer", "main": "", "scripts": {}, "author": {"name": "jt000", "email": "https://github.com/jt000"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "4.0.25-alpha"}, "_id": "@types/multer@0.0.23-alpha", "_shasum": "9c68ba4d6a181255194c7d2293b0543b2d33ff5f", "_from": "output\\multer", "_resolved": "file:output\\multer", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "9c68ba4d6a181255194c7d2293b0543b2d33ff5f", "size": 2339, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-0.0.23-alpha.tgz", "integrity": "sha512-wHGmL89aU9QVJ82aRbpldgn9GrekUZG7B4NY8/pCyOtmryEbmzoS95f5z19IVH1ecb8qg/qATWYpt5asafgggw=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/multer-0.0.23-alpha.tgz_1467415834925_0.1445520962588489"}, "directories": {}, "publish_time": 1467415838566, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467415838566, "_cnpmcore_publish_time": "2021-12-16T13:55:46.544Z"}, "0.0.22-alpha": {"name": "@types/multer", "version": "0.0.22-alpha", "description": "TypeScript definitions for multer", "main": "", "scripts": {}, "author": {"name": "jt000", "email": "https://github.com/jt000"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "4.0.*"}, "_id": "@types/multer@0.0.22-alpha", "_shasum": "47606d6ddec4a84d29545afc1eb3b6f7d0f8add1", "_from": "output\\multer", "_resolved": "file:output\\multer", "_npmVersion": "3.9.5", "_nodeVersion": "6.2.2", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "47606d6ddec4a84d29545afc1eb3b6f7d0f8add1", "size": 2330, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-0.0.22-alpha.tgz", "integrity": "sha512-9SHzBnoVpAt2HziK418w9QrzquTzkMtrqAEj1xvN97gaJww74sghE2sX/POSi9swInekh22NpRvQ5Ie41++4Ag=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/multer-0.0.22-alpha.tgz_1467403775015_0.6166939716786146"}, "directories": {}, "publish_time": 1467403777524, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467403777524, "_cnpmcore_publish_time": "2021-12-16T13:55:46.760Z"}, "0.0.21-alpha": {"name": "@types/multer", "version": "0.0.21-alpha", "description": "TypeScript definitions for multer", "main": "", "scripts": {}, "author": {"name": "jt000", "email": "https://github.com/jt000"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {}, "_id": "@types/multer@0.0.21-alpha", "_shasum": "6cfb4d8e75d28a56fec877b1e03d0a27dfd8131c", "_from": "output\\multer", "_resolved": "file:output\\multer", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "6cfb4d8e75d28a56fec877b1e03d0a27dfd8131c", "size": 2299, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-0.0.21-alpha.tgz", "integrity": "sha512-EKbezoW+cNAnm4tSZLqVcG/HQPtIvL45ZX2+CmpOb7ujWqFK2wCmiCPRBCJMF/y6wxN8tbOqviEKlUequdvqoA=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/multer-0.0.21-alpha.tgz_1464154244197_0.44338403013534844"}, "directories": {}, "publish_time": 1464154246670, "_hasShrinkwrap": false, "_cnpm_publish_time": 1464154246670, "_cnpmcore_publish_time": "2021-12-16T13:55:46.988Z"}, "0.0.20-alpha": {"name": "@types/multer", "version": "0.0.20-alpha", "description": "TypeScript definitions for multer", "main": "", "scripts": {}, "author": {"name": "jt000", "email": "https://github.com/jt000"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {}, "_id": "@types/multer@0.0.20-alpha", "_shasum": "4cc2d221df2ae38c8af3d9d8f4bd60d5623d80a5", "_from": "output\\multer", "_resolved": "file:output\\multer", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "4cc2d221df2ae38c8af3d9d8f4bd60d5623d80a5", "size": 2299, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-0.0.20-alpha.tgz", "integrity": "sha512-Rs0lhAcviaQuKxL8lRYRowHsP8/slcTrMdWna1jZ2L0qnZJJHShh6eWZxnNw0WzAKzA2/TWaKZBUAzsp34FE0g=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/multer-0.0.20-alpha.tgz_1463775202669_0.4784602641593665"}, "directories": {}, "publish_time": 1463775205014, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463775205014, "_cnpmcore_publish_time": "2021-12-16T13:55:47.213Z"}, "0.0.15-alpha": {"name": "@types/multer", "version": "0.0.15-alpha", "description": "Type definitions for multer from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "main": "", "scripts": {}, "author": {"name": "jt000", "email": "https://github.com/jt000"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {}, "_id": "@types/multer@0.0.15-alpha", "_shasum": "3cce054d419d02d9ef62beb764ba63267623db7b", "_from": "output\\multer", "_resolved": "file:output\\multer", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "3cce054d419d02d9ef62beb764ba63267623db7b", "size": 2306, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-0.0.15-alpha.tgz", "integrity": "sha512-FDBvXI583rAuqNgsGZsf1mqWqSYn9KF6f8MVkMJbl+hwwqECnglEozafMkqtydutt7Y6cCieT00AcRopbFPBPQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/multer-0.0.15-alpha.tgz_1463694406765_0.5041094387415797"}, "directories": {}, "publish_time": 1463694407406, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463694407406, "_cnpmcore_publish_time": "2021-12-16T13:55:47.413Z"}, "0.0.14-alpha": {"name": "@types/multer", "version": "0.0.14-alpha", "description": "Type definitions for multer from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "main": "", "scripts": {}, "author": {"name": "jt000", "email": "https://github.com/jt000"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {}, "_id": "@types/multer@0.0.14-alpha", "_shasum": "f7e9baf5e6bfd1ef0f47fbc93efbfa15ef247605", "_from": "output\\multer", "_resolved": "file:output\\multer", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "f7e9baf5e6bfd1ef0f47fbc93efbfa15ef247605", "size": 2290, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-0.0.14-alpha.tgz", "integrity": "sha512-Hau3chxDi7RCnDS03ZPdEO1fmSHswEUazj6lkF/2G5BcGVmJIs3lYX00zFfrD+jsgr5qPGBEP0+jJxw5GDclfQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/multer-0.0.14-alpha.tgz_1463509404651_0.9308711085468531"}, "directories": {}, "publish_time": 1463509405222, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463509405222, "_cnpmcore_publish_time": "2021-12-16T13:55:47.631Z"}, "1.4.8": {"name": "@types/multer", "version": "1.4.8", "description": "TypeScript definitions for multer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/multer", "license": "MIT", "contributors": [{"name": "jt000", "url": "https://github.com/jt000", "githubUsername": "jt000"}, {"name": "vili<PERSON><PERSON><PERSON>", "url": "https://github.com/vilic", "githubUsername": "vilic"}, {"name": "<PERSON>-<PERSON>", "url": "https://github.com/DavidBR-SW", "githubUsername": "DavidBR-SW"}, {"name": "<PERSON>", "url": "https://github.com/mxl", "githubUsername": "mxl"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/hyunseob", "githubUsername": "hyun<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/thrymgjol", "githubUsername": "thrym<PERSON><PERSON>l"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/multer"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "421ef872535fa3b91f605af9019ef9fa0035f142dc14121d08d8c30cc14aa23e", "typeScriptVersion": "4.5", "_id": "@types/multer@1.4.8", "dist": {"integrity": "sha512-VMZOW6mnmMMhA5m3fsCdXBwFwC+a+27/8gctNMuQC4f7UtWcF79KAFGoIfKZ4iqrElgWIa3j5vhMJDp0iikQ1g==", "shasum": "8d98c36f6a4e0b228a9f262cd66e881d7cd64039", "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-1.4.8.tgz", "fileCount": 5, "unpackedSize": 17370, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCuucEejQ0NTLkPK4yW0WyUw+8AHzJXvnYQs8+CInLfTAIgYGbTWVQOS0rehe/+Gwr++euGj4I9okBMTmBiXWjzZWk="}], "size": 5045}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/multer_1.4.8_1695810435628_0.6818482625538342"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-27T10:27:15.757Z", "publish_time": 1695810435757, "_source_registry_name": "default"}, "1.4.9": {"name": "@types/multer", "version": "1.4.9", "description": "TypeScript definitions for multer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/multer", "license": "MIT", "contributors": [{"name": "jt000", "githubUsername": "jt000", "url": "https://github.com/jt000"}, {"name": "vili<PERSON><PERSON><PERSON>", "githubUsername": "vilic", "url": "https://github.com/vilic"}, {"name": "<PERSON>-<PERSON>", "githubUsername": "DavidBR-SW", "url": "https://github.com/DavidBR-SW"}, {"name": "<PERSON>", "githubUsername": "mxl", "url": "https://github.com/mxl"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "hyun<PERSON><PERSON>", "url": "https://github.com/hyunseob"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON>"}, {"name": "<PERSON>", "githubUsername": "thrym<PERSON><PERSON>l", "url": "https://github.com/thrymgjol"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/multer"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "87ad430258de5f438445e731eeb40403ab6ca9c4394753316bf53b4426b937e8", "typeScriptVersion": "4.5", "_id": "@types/multer@1.4.9", "dist": {"integrity": "sha512-9NSvPJ2E8bNTc8XtJq1Cimx2Wrn2Ah48F15B2Du/hM8a8CHLhVbJMlF3ZCqhvMdht7Sa+YdP0aKP7N4fxDcrrg==", "shasum": "74e803d3a9b62f41d191ce48012c2151f62c2ed7", "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-1.4.9.tgz", "fileCount": 5, "unpackedSize": 16685, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAgL9h8tB/JerLM9mNt6kh4mC8OSKPV1Fft+NIK51ybUAiEA0mbAjbEEF/SiyVbSyEIKKKnDHmuOOnI2X5XxNGQLPYg="}], "size": 4922}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/multer_1.4.9_1697619940276_0.4033822305729535"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-18T09:05:40.550Z", "publish_time": 1697619940550, "_source_registry_name": "default"}, "1.4.10": {"name": "@types/multer", "version": "1.4.10", "description": "TypeScript definitions for multer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/multer", "license": "MIT", "contributors": [{"name": "jt000", "githubUsername": "jt000", "url": "https://github.com/jt000"}, {"name": "vili<PERSON><PERSON><PERSON>", "githubUsername": "vilic", "url": "https://github.com/vilic"}, {"name": "<PERSON>-<PERSON>", "githubUsername": "DavidBR-SW", "url": "https://github.com/DavidBR-SW"}, {"name": "<PERSON>", "githubUsername": "mxl", "url": "https://github.com/mxl"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "hyun<PERSON><PERSON>", "url": "https://github.com/hyunseob"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON>"}, {"name": "<PERSON>", "githubUsername": "thrym<PERSON><PERSON>l", "url": "https://github.com/thrymgjol"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/multer"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "70d0295b93a7aa1c084bc13170b1c46c5f79acbfba5632f03589be8f36ead11d", "typeScriptVersion": "4.5", "_id": "@types/multer@1.4.10", "dist": {"integrity": "sha512-6l9mYMhUe8wbnz/67YIjc7ZJyQNZoKq7fRXVf7nMdgWgalD0KyzJ2ywI7hoATUSXSbTu9q2HBiEwzy0tNN1v2w==", "shasum": "6bca159aaaf40ec130e99831a08e3d0ed54be611", "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-1.4.10.tgz", "fileCount": 5, "unpackedSize": 16686, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICno3eQf0nJCS/oaQyj4R88UKLsXwHTKkHg/s5TjTqvHAiEAmTPFTuFVQJTVzSXN/iVJfpZEyl2TAj61wgnhmNfLPHk="}], "size": 4916}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/multer_1.4.10_1699356511541_0.723676132109716"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-07T11:28:31.731Z", "publish_time": 1699356511731, "_source_registry_name": "default"}, "1.4.11": {"name": "@types/multer", "version": "1.4.11", "description": "TypeScript definitions for multer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/multer", "license": "MIT", "contributors": [{"name": "jt000", "githubUsername": "jt000", "url": "https://github.com/jt000"}, {"name": "vili<PERSON><PERSON><PERSON>", "githubUsername": "vilic", "url": "https://github.com/vilic"}, {"name": "<PERSON>-<PERSON>", "githubUsername": "DavidBR-SW", "url": "https://github.com/DavidBR-SW"}, {"name": "<PERSON>", "githubUsername": "mxl", "url": "https://github.com/mxl"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "hyun<PERSON><PERSON>", "url": "https://github.com/hyunseob"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON>"}, {"name": "<PERSON>", "githubUsername": "thrym<PERSON><PERSON>l", "url": "https://github.com/thrymgjol"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/multer"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "b37b2751db6482b0d3da4c4b7dd8d1bfd189749a4393f0cccb635e743fccab7a", "typeScriptVersion": "4.5", "_id": "@types/multer@1.4.11", "dist": {"integrity": "sha512-svK240gr6LVWvv3YGyhLlA+6LRRWA4mnGIU7RcNmgjBYFl6665wcXrRfxGp5tEPVHUNm5FMcmq7too9bxCwX/w==", "shasum": "c70792670513b4af1159a2b60bf48cc932af55c5", "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-1.4.11.tgz", "fileCount": 5, "unpackedSize": 16682, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID/DhAelkqHhXLLD5ez7n/ZAeZWQr2pzQkT/ATcNm3PzAiAj/UEN21H56H5e6DBN21RT05UJfvb50ZFNqi+yokBLHg=="}], "size": 4917}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/multer_1.4.11_1700527026766_0.46899628893442724"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-21T00:37:06.924Z", "publish_time": 1700527026924, "_source_registry_name": "default"}, "1.4.12": {"name": "@types/multer", "version": "1.4.12", "license": "MIT", "_id": "@types/multer@1.4.12", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/jt000", "name": "jt000", "githubUsername": "jt000"}, {"url": "https://github.com/vilic", "name": "vili<PERSON><PERSON><PERSON>", "githubUsername": "vilic"}, {"url": "https://github.com/DavidBR-SW", "name": "<PERSON>-<PERSON>", "githubUsername": "DavidBR-SW"}, {"url": "https://github.com/mxl", "name": "<PERSON>", "githubUsername": "mxl"}, {"url": "https://github.com/hyunseob", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "hyun<PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/multer", "dist": {"shasum": "da67bd0c809f3a63fe097c458c0d4af1fea50ab7", "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-1.4.12.tgz", "fileCount": 5, "integrity": "sha512-pQ2hoqvXiJt2FP9WQVLPRO+AmiIm/ZYkavPlIQnx282u4ZrVdztx0pkh3jjpQt0Kz+YI0YhSG264y08UJKoUQg==", "signatures": [{"sig": "MEUCIQCitvVUjPfotRSGgF5fmE04IknaNLHA7vLsCz+c0c70nQIgUXLOE8mi7AQHVrCWhp+raFE9+LX1Y3k5t9Ll+fU6eFU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16486, "size": 4879}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/multer"}, "description": "TypeScript definitions for multer", "directories": {}, "dependencies": {"@types/express": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.8", "_npmOperationalInternal": {"tmp": "tmp/multer_1.4.12_1724436548297_0.35485511206799747", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "fdc4b88e90facc194537f03898d16d8e2797cd1f3b8f6dcbc6c33be0e431efa1", "_cnpmcore_publish_time": "2024-08-23T18:09:08.446Z", "publish_time": 1724436548446, "_source_registry_name": "default"}, "1.4.13": {"name": "@types/multer", "version": "1.4.13", "license": "MIT", "_id": "@types/multer@1.4.13", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/jt000", "name": "jt000", "githubUsername": "jt000"}, {"url": "https://github.com/vilic", "name": "vili<PERSON><PERSON><PERSON>", "githubUsername": "vilic"}, {"url": "https://github.com/DavidBR-SW", "name": "<PERSON>-<PERSON>", "githubUsername": "DavidBR-SW"}, {"url": "https://github.com/mxl", "name": "<PERSON>", "githubUsername": "mxl"}, {"url": "https://github.com/hyunseob", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "hyun<PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/b<PERSON><PERSON><PERSON>bas", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/multer", "dist": {"shasum": "be483f909a77f13e0624cac3d001859eb12ae68b", "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-1.4.13.tgz", "fileCount": 5, "integrity": "sha512-bhhdtPw7JqCiEfC9Jimx5LqX9BDIPJEh2q/fQ4bqbBPtyEZYr3cvF22NwG0DmPZNYA0CAf2CnqDB4KIGGpJcaw==", "signatures": [{"sig": "MEQCIAdTqOCiBvucHPRRC6HvsfOzwQocTgKutHNJyNGzEAVtAiAb8RXrDCihDKfLL2XcLFRMc2ySEwy6CyIedTDuEhb4Xw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 16726, "size": 4917}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/multer"}, "description": "TypeScript definitions for multer", "directories": {}, "dependencies": {"@types/express": "*"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.1", "_npmOperationalInternal": {"tmp": "tmp/multer_1.4.13_1749262688338_0.12113184266008492", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "ea3009a3d338b242f4c077422a23fd37c202bfbb89cba2db6a6cfefc3c6a578d", "_cnpmcore_publish_time": "2025-06-07T02:18:08.532Z", "publish_time": 1749262688532, "_source_registry_name": "default"}, "2.0.0": {"name": "@types/multer", "version": "2.0.0", "license": "MIT", "_id": "@types/multer@2.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/jt000", "name": "jt000", "githubUsername": "jt000"}, {"url": "https://github.com/vilic", "name": "vili<PERSON><PERSON><PERSON>", "githubUsername": "vilic"}, {"url": "https://github.com/DavidBR-SW", "name": "<PERSON>-<PERSON>", "githubUsername": "DavidBR-SW"}, {"url": "https://github.com/mxl", "name": "<PERSON>", "githubUsername": "mxl"}, {"url": "https://github.com/hyunseob", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "hyun<PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/b<PERSON><PERSON><PERSON>bas", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/multer", "dist": {"shasum": "db5f82136b619f5ce4d923b00034eb466c13acf4", "tarball": "https://registry.npmmirror.com/@types/multer/-/multer-2.0.0.tgz", "fileCount": 5, "integrity": "sha512-C3Z9v9Evij2yST3RSBktxP9STm6OdMc5uR1xF1SGr98uv8dUlAL2hqwrZ3GVB3uyMyiegnscEK6PGtYvNrjTjw==", "signatures": [{"sig": "MEQCIAHme5JP8cMwafJbC4zpTqlMffPFGzwW67FDVz8G53VXAiBipt/DQ83/RD0K/SVNWCeSWzRwpfDJJn1FxO9QOQ5UeQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 16756, "size": 4930}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "actor": {"name": "types", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/multer"}, "description": "TypeScript definitions for multer", "directories": {}, "dependencies": {"@types/express": "*"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.1", "_npmOperationalInternal": {"tmp": "tmp/multer_2.0.0_1751400160361_0.6900812756644608", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "f6551dc7472bbda5d8b2e7793797cf9951195b30f235ae1946a50ae4e1f2a7f9", "_cnpmcore_publish_time": "2025-07-01T20:02:40.540Z", "publish_time": 1751400160540, "_source_registry_name": "default"}}, "contributors": [{"url": "https://github.com/jt000", "name": "jt000", "githubUsername": "jt000"}, {"url": "https://github.com/vilic", "name": "vili<PERSON><PERSON><PERSON>", "githubUsername": "vilic"}, {"url": "https://github.com/DavidBR-SW", "name": "<PERSON>-<PERSON>", "githubUsername": "DavidBR-SW"}, {"url": "https://github.com/mxl", "name": "<PERSON>", "githubUsername": "mxl"}, {"url": "https://github.com/hyunseob", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "hyun<PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/b<PERSON><PERSON><PERSON>bas", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/multer", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/multer"}, "_source_registry_name": "default"}