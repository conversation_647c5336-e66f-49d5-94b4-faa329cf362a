{"_attachments": {}, "_id": "@types/cookie-parser", "_rev": "291786-61f1c7562c1293059b219840", "description": "TypeScript definitions for cookie-parser", "dist-tags": {"latest": "1.4.9", "ts2.0": "1.3.30", "ts2.1": "1.3.30", "ts2.2": "1.4.1", "ts2.3": "1.4.2", "ts2.4": "1.4.2", "ts2.5": "1.4.2", "ts2.6": "1.4.2", "ts2.7": "1.4.2", "ts2.8": "1.4.2", "ts2.9": "1.4.2", "ts3.0": "1.4.2", "ts3.1": "1.4.2", "ts3.2": "1.4.2", "ts3.3": "1.4.2", "ts3.4": "1.4.2", "ts3.5": "1.4.2", "ts3.6": "1.4.2", "ts3.7": "1.4.2", "ts3.8": "1.4.2", "ts3.9": "1.4.3", "ts4.0": "1.4.3", "ts4.1": "1.4.3", "ts4.2": "1.4.3", "ts4.3": "1.4.4", "ts4.4": "1.4.4", "ts4.5": "1.4.6", "ts4.6": "1.4.7", "ts4.7": "1.4.7", "ts4.8": "1.4.7", "ts4.9": "1.4.7", "ts5.0": "1.4.8", "ts5.1": "1.4.9", "ts5.2": "1.4.9", "ts5.3": "1.4.9", "ts5.4": "1.4.9", "ts5.5": "1.4.9", "ts5.6": "1.4.9", "ts5.7": "1.4.9", "ts5.8": "1.4.9", "ts5.9": "1.4.9"}, "license": "MIT", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "name": "@types/cookie-parser", "readme": "# Installation\r\n> `npm install --save @types/cookie-parser`\r\n\r\n# Summary\r\nThis package contains type definitions for cookie-parser (https://github.com/expressjs/cookie-parser).\r\n\r\n# Details\r\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cookie-parser.\r\n## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cookie-parser/index.d.ts)\r\n````ts\r\nimport * as express from \"express\";\n\ndeclare module \"express\" {\n    // Inject additional properties on express.Request\n    interface Request {\n        /**\n         * This request's secret.\n         * Optionally set by cookie-parser if secret(s) are provided.  Can be used by other middleware.\n         * [Declaration merging](https://www.typescriptlang.org/docs/handbook/declaration-merging.html) can be used to add your own properties.\n         */\n        secret?: string | undefined;\n        /** Parsed cookies that have not been signed */\n        cookies: Record<string, any>;\n        /** Parsed cookies that have been signed */\n        signedCookies: Record<string, any>;\n    }\n}\n\ndeclare function cookieParser(\n    secret?: string | string[],\n    options?: cookieParser.CookieParseOptions,\n): express.RequestHandler;\n\ndeclare namespace cookieParser {\n    interface CookieParseOptions {\n        decode?(val: string): string;\n    }\n\n    function JSONCookie(jsonCookie: string): object | undefined;\n\n    function JSONCookies<T extends { [key: string]: string }>(jsonCookies: T): { [P in keyof T]: object | undefined };\n\n    function signedCookie(cookie: string, secret: string | string[]): string | false;\n\n    function signedCookies<T extends { [key: string]: string }>(\n        cookies: T,\n        secret: string | string[],\n    ): { [P in keyof T]?: string | false };\n}\n\nexport = cookieParser;\n\r\n````\r\n\r\n### Additional Details\r\n * Last updated: Sat, 07 Jun 2025 02:15:25 GMT\r\n * Dependencies: none\r\n * Peer dependencies: [@types/express](https://npmjs.com/package/@types/express)\r\n\r\n# Credits\r\nThese definitions were written by [Santi Albo](https://github.com/santialbo), [BendingBender](https://github.com/BendingBender), and [Sebastian Beltran](https://github.com/bjohansebas).\r\n", "time": {"created": "2022-01-26T22:12:38.882Z", "modified": "2025-06-07T02:16:47.051Z", "1.4.2": "2019-08-19T00:55:25.729Z", "1.4.1": "2017-08-21T21:50:18.297Z", "1.4.0": "2017-07-24T18:01:28.496Z", "1.3.30": "2016-09-19T16:44:26.028Z", "1.3.29": "2016-07-14T14:20:14.045Z", "1.3.28-alpha": "2016-07-07T18:58:51.272Z", "1.3.27-alpha": "2016-07-03T23:58:31.955Z", "1.3.26-alpha": "2016-07-02T02:11:13.159Z", "1.3.25-alpha": "2016-07-01T22:30:19.887Z", "1.3.24-alpha": "2016-07-01T19:05:09.828Z", "1.3.23-alpha": "2016-05-25T04:40:27.123Z", "1.3.22-alpha": "2016-05-20T19:22:31.966Z", "1.3.17-alpha": "2016-05-19T20:33:21.037Z", "1.3.16-alpha": "2016-05-17T04:40:54.674Z", "1.4.3": "2022-04-27T10:31:41.045Z", "1.4.4": "2023-09-04T16:18:26.156Z", "1.4.5": "2023-10-18T00:21:58.632Z", "1.4.6": "2023-11-07T01:03:37.505Z", "1.4.7": "2024-02-29T19:35:43.855Z", "1.4.8": "2024-11-25T21:02:36.799Z", "1.4.9": "2025-06-07T02:16:08.503Z"}, "versions": {"1.4.2": {"name": "@types/cookie-parser", "version": "1.4.2", "description": "TypeScript definitions for cookie-parser", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/santialbo", "githubUsername": "santial<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/cookie-parser"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "c7f1bd64948c00975ec74c2ebc516c33bdb987d6f802c2b45a3378a31f37bf4b", "typeScriptVersion": "2.3", "_id": "@types/cookie-parser@1.4.2", "dist": {"shasum": "e4d5c5ffda82b80672a88a4281aaceefb1bd9df5", "size": 1772, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/cookie-parser/-/cookie-parser-1.4.2.tgz", "integrity": "sha512-uwcY8m6SDQqciHsqcKDGbo10GdasYsPCYkH3hVegj9qAah6pX5HivOnOuI3WYmyQMnOATV39zv/Ybs0bC/6iVg=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cookie-parser_1.4.2_1566176125591_0.24677584384799833"}, "_hasShrinkwrap": false, "publish_time": 1566176125729, "_cnpm_publish_time": 1566176125729, "_cnpmcore_publish_time": "2021-12-16T18:28:30.386Z"}, "1.4.1": {"name": "@types/cookie-parser", "version": "1.4.1", "description": "TypeScript definitions for cookie-parser", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/santialbo"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "28c91961545323d563e4bbeeb25802a524f8648c998ac34189c8183bb7e0e2fa", "typeScriptVersion": "2.2", "_id": "@types/cookie-parser@1.4.1", "dist": {"shasum": "e88a39c41960f31549b4c52dd8620aa4a2feb4bb", "size": 1750, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/cookie-parser/-/cookie-parser-1.4.1.tgz", "integrity": "sha512-iJY6B3ZGufLiDf2OCAgiAAQuj1sMKC/wz/7XCEjZ+/MDuultfFJuSwrBKcLSmJ5iYApLzCCYBYJZs0Ws8GPmwA=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cookie-parser-1.4.1.tgz_1503352218110_0.9620355863589793"}, "directories": {}, "publish_time": 1503352218297, "_hasShrinkwrap": false, "_cnpm_publish_time": 1503352218297, "_cnpmcore_publish_time": "2021-12-16T18:28:30.606Z"}, "1.4.0": {"name": "@types/cookie-parser", "version": "1.4.0", "description": "TypeScript definitions for cookie-parser", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/santialbo/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "da47f3e0eb829dc7836c7c46027c48b08b0bedb190431a1913f167bf7a5ce28b", "typeScriptVersion": "2.2", "_id": "@types/cookie-parser@1.4.0", "dist": {"shasum": "7020ea0de59b7aa038aa0cd6d76ceb40b61e7789", "size": 1755, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/cookie-parser/-/cookie-parser-1.4.0.tgz", "integrity": "sha512-h7BMxCKEnL8XpOu2OHp2edaBKiG795ypyfKHrBGeMK0vHx8oqGBafplxtApMKrlCExjx3C+pJKjVth7cXzEpCQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cookie-parser-1.4.0.tgz_1500919288370_0.8226084706839174"}, "directories": {}, "publish_time": 1500919288496, "_hasShrinkwrap": false, "_cnpm_publish_time": 1500919288496, "_cnpmcore_publish_time": "2021-12-16T18:28:30.810Z"}, "1.3.30": {"name": "@types/cookie-parser", "version": "1.3.30", "description": "TypeScript definitions for cookie-parser v1.3.4", "license": "MIT", "author": "<PERSON><PERSON> <https://github.com/santialbo/>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typings": "index.d.ts", "typesPublisherContentHash": "6c7f2db61082396b54f32f57c12128c15e167290cddd3e0d380ecdf2fa9ebea0", "_id": "@types/cookie-parser@1.3.30", "dist": {"shasum": "70e4824086e2f214dbc674fd922d1c2744475392", "size": 1062, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/cookie-parser/-/cookie-parser-1.3.30.tgz", "integrity": "sha512-MCq87l/aM0E3JxZ5hYCDGlmipwdr+PrKNArYyPqATvkYvMxlqEHkalRfP0S151ATkVlmojyt0sHPhhIhIm+NPQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/cookie-parser-1.3.30.tgz_1474303464059_0.33324976707808673"}, "directories": {}, "publish_time": 1474303466028, "_hasShrinkwrap": false, "_cnpm_publish_time": 1474303466028, "_cnpmcore_publish_time": "2021-12-16T18:28:31.088Z"}, "1.3.29": {"name": "@types/cookie-parser", "version": "1.3.29", "description": "TypeScript definitions for cookie-parser v1.3.4", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/santialbo/"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "4.0.*"}, "_id": "@types/cookie-parser@1.3.29", "_shasum": "f14fb6b7f3141f5a23ae6593fe768816056ea564", "_from": "output\\cookie-parser", "_resolved": "file:output\\cookie-parser", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "f14fb6b7f3141f5a23ae6593fe768816056ea564", "size": 1039, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/cookie-parser/-/cookie-parser-1.3.29.tgz", "integrity": "sha512-fwvSyOaK9vQk/fvfvUDc4bZCgtCKirEXwyEt4Q5kIJ4yHKiezKWv1r2o8wvXON8SAQumbVzxQr9YtOyR6nu2tg=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/cookie-parser-1.3.29.tgz_1468506011674_0.9338062484748662"}, "directories": {}, "publish_time": 1468506014045, "_hasShrinkwrap": false, "_cnpm_publish_time": 1468506014045, "_cnpmcore_publish_time": "2021-12-16T18:28:31.308Z"}, "1.3.28-alpha": {"name": "@types/cookie-parser", "version": "1.3.28-alpha", "description": "TypeScript definitions for cookie-parser v1.3.4", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/santialbo/"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "4.0.28-alpha"}, "_id": "@types/cookie-parser@1.3.28-alpha", "_shasum": "5d15731e8ee8f8f7fdd9c0b1871bb44b8ca87562", "_from": "output\\cookie-parser", "_resolved": "file:output\\cookie-parser", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "5d15731e8ee8f8f7fdd9c0b1871bb44b8ca87562", "size": 1044, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/cookie-parser/-/cookie-parser-1.3.28-alpha.tgz", "integrity": "sha512-8FpQCxcrCqr1ww0d63x3dQCY6KVEKtaD4yhdpwgG56aUfuHSXlUcXBD+nb+O0F33VASbcBMr6y6mVKVWfPeOEg=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/cookie-parser-1.3.28-alpha.tgz_1467917929052_0.7725511856842786"}, "directories": {}, "publish_time": 1467917931272, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467917931272, "_cnpmcore_publish_time": "2021-12-16T18:28:31.526Z"}, "1.3.27-alpha": {"name": "@types/cookie-parser", "version": "1.3.27-alpha", "description": "TypeScript definitions for cookie-parser v1.3.4", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/santialbo/"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "4.0.27-alpha"}, "_id": "@types/cookie-parser@1.3.27-alpha", "_shasum": "10b0c96c3d6c4a956f53a836f51670a98a4ee2db", "_from": "output\\cookie-parser", "_resolved": "file:output\\cookie-parser", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "10b0c96c3d6c4a956f53a836f51670a98a4ee2db", "size": 1044, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/cookie-parser/-/cookie-parser-1.3.27-alpha.tgz", "integrity": "sha512-gpTetOem5PuxU+RJvbM9jevvCsM4iYB+y7Y8TKLp2TTxlPZ/uVdfzujWfQpTNXHNmG0TENAYiq+Rki7ZMP8cRQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/cookie-parser-1.3.27-alpha.tgz_1467590311415_0.970397668890655"}, "directories": {}, "publish_time": 1467590311955, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467590311955, "_cnpmcore_publish_time": "2021-12-16T18:28:31.749Z"}, "1.3.26-alpha": {"name": "@types/cookie-parser", "version": "1.3.26-alpha", "description": "TypeScript definitions for cookie-parser v1.3.4", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/santialbo/"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "4.0.25-alpha"}, "_id": "@types/cookie-parser@1.3.26-alpha", "_shasum": "3414456e6ba077c376219de90dbc0153861fea19", "_from": "output\\cookie-parser", "_resolved": "file:output\\cookie-parser", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "3414456e6ba077c376219de90dbc0153861fea19", "size": 1043, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/cookie-parser/-/cookie-parser-1.3.26-alpha.tgz", "integrity": "sha512-E9ijugxHLPBYQLQk3jCtMJWiWy0SOZGQNJ6cR66S0hbMMr7ISsODVlEVvHJUwu6YsJBcg5hL6QFHPpjdHz1Snw=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/cookie-parser-1.3.26-alpha.tgz_1467425469558_0.8209197642281651"}, "directories": {}, "publish_time": 1467425473159, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467425473159, "_cnpmcore_publish_time": "2021-12-16T18:28:31.991Z"}, "1.3.25-alpha": {"name": "@types/cookie-parser", "version": "1.3.25-alpha", "description": "TypeScript definitions for cookie-parser v1.3.4", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/santialbo/"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "4.0.24-alpha"}, "_id": "@types/cookie-parser@1.3.25-alpha", "_shasum": "93d573b13fb32c379b875bdc7178eef6a68f5731", "_from": "output\\cookie-parser", "_resolved": "file:output\\cookie-parser", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "93d573b13fb32c379b875bdc7178eef6a68f5731", "size": 1044, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/cookie-parser/-/cookie-parser-1.3.25-alpha.tgz", "integrity": "sha512-fRr5HCIN7wisOt2Wp+XBu8QnEnhugoJbqikYdnX9fqHBfqRRrg/4vfs/eQIZsC56R5eDXZYtMgGoavCv+U9XQQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/cookie-parser-1.3.25-alpha.tgz_1467412217315_0.3386126810219139"}, "directories": {}, "publish_time": 1467412219887, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467412219887, "_cnpmcore_publish_time": "2021-12-16T18:28:32.242Z"}, "1.3.24-alpha": {"name": "@types/cookie-parser", "version": "1.3.24-alpha", "description": "TypeScript definitions for cookie-parser v1.3.4", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/santialbo/"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "4.0.*"}, "_id": "@types/cookie-parser@1.3.24-alpha", "_shasum": "f3180978760de840b99822bc558511c56bf995e1", "_from": "output\\cookie-parser", "_resolved": "file:output\\cookie-parser", "_npmVersion": "3.9.5", "_nodeVersion": "6.2.2", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "f3180978760de840b99822bc558511c56bf995e1", "size": 1040, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/cookie-parser/-/cookie-parser-1.3.24-alpha.tgz", "integrity": "sha512-4zcOy1qdN6lcwyaVnOBoIuybwb3Nn6xyL7Q25TsN1OsVkI4mn/osVtEoWN86j+zMisLA4sjvIbpJdOm+MKtlrQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/cookie-parser-1.3.24-alpha.tgz_1467399907687_0.525313564343378"}, "directories": {}, "publish_time": 1467399909828, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467399909828, "_cnpmcore_publish_time": "2021-12-16T18:28:32.649Z"}, "1.3.23-alpha": {"name": "@types/cookie-parser", "version": "1.3.23-alpha", "description": "TypeScript definitions for cookie-parser v1.3.4", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/santialbo/"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "*"}, "_id": "@types/cookie-parser@1.3.23-alpha", "_shasum": "e4f3e847d0208ea2ecdfae8465f50b04ee08d016", "_from": "output\\cookie-parser", "_resolved": "file:output\\cookie-parser", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "e4f3e847d0208ea2ecdfae8465f50b04ee08d016", "size": 1011, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/cookie-parser/-/cookie-parser-1.3.23-alpha.tgz", "integrity": "sha512-aiWAehdRSiHtSzRQPpe09gOe1IIRoTPaPC/l5extyWxjUvhro2QIdP24YATf/9S+6YbGXjTdyUUVTD5PgX9QMQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/cookie-parser-1.3.23-alpha.tgz_1464151226661_0.3726709936745465"}, "directories": {}, "publish_time": 1464151227123, "_hasShrinkwrap": false, "_cnpm_publish_time": 1464151227123, "_cnpmcore_publish_time": "2021-12-16T18:28:32.850Z"}, "1.3.22-alpha": {"name": "@types/cookie-parser", "version": "1.3.22-alpha", "description": "TypeScript definitions for cookie-parser v1.3.4", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/santialbo/"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "*"}, "_id": "@types/cookie-parser@1.3.22-alpha", "_shasum": "dc553d3c1f0d3637e162c0a4852b1d9dee3d5f28", "_from": "output\\cookie-parser", "_resolved": "file:output\\cookie-parser", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "dc553d3c1f0d3637e162c0a4852b1d9dee3d5f28", "size": 1009, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/cookie-parser/-/cookie-parser-1.3.22-alpha.tgz", "integrity": "sha512-9jDjIKxVfTyRkS8DL9Goiz1r0OkILzfOUZXfqKnbIiz4lf/sZlpnrXOhzhVsOYqnhOJqrDYuMhg/365WQwd7Mg=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/cookie-parser-1.3.22-alpha.tgz_1463772149896_0.6738729258067906"}, "directories": {}, "publish_time": 1463772151966, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463772151966, "_cnpmcore_publish_time": "2021-12-16T18:28:33.061Z"}, "1.3.17-alpha": {"name": "@types/cookie-parser", "version": "1.3.17-alpha", "description": "Type definitions for cookie-parser v1.3.4 from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/santialbo/"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"equire('express'": "*"}, "_id": "@types/cookie-parser@1.3.17-alpha", "_shasum": "22b43836e7781bd901c65a6a003ed1f2ca12a840", "_from": "output\\cookie-parser", "_resolved": "file:output\\cookie-parser", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "22b43836e7781bd901c65a6a003ed1f2ca12a840", "size": 1013, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/cookie-parser/-/cookie-parser-1.3.17-alpha.tgz", "integrity": "sha512-zi0H9baiRSK3MEMEuj5pQiTdbpv98vkOYHjlRnD13tp/forNRXfvWLhfoVHHK1qe1yH/HruacMZV2XPHERdRFQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/cookie-parser-1.3.17-alpha.tgz_1463689998601_0.24909470207057893"}, "directories": {}, "publish_time": 1463690001037, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463690001037, "_cnpmcore_publish_time": "2021-12-16T18:28:33.327Z"}, "1.3.16-alpha": {"name": "@types/cookie-parser", "version": "1.3.16-alpha", "description": "Type definitions for cookie-parser v1.3.4 from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/santialbo/"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"equire('express'": "*"}, "_id": "@types/cookie-parser@1.3.16-alpha", "_shasum": "b8f785a0fa047381207dcc2026f1b3bff73f112d", "_from": "output\\cookie-parser", "_resolved": "file:output\\cookie-parser", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "b8f785a0fa047381207dcc2026f1b3bff73f112d", "size": 1007, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/cookie-parser/-/cookie-parser-1.3.16-alpha.tgz", "integrity": "sha512-R/X8MRsYIJ7SBrW9O5zNleWRtv1+NjE0UpFKFYvtYZUTBzs/Dpmq5laeWAwdHnUR29hWYMb3UFb2wLka2dj47Q=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/cookie-parser-1.3.16-alpha.tgz_1463460054099_0.22439709631726146"}, "directories": {}, "publish_time": 1463460054674, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463460054674, "_cnpmcore_publish_time": "2021-12-16T18:28:33.549Z"}, "1.4.3": {"name": "@types/cookie-parser", "version": "1.4.3", "description": "TypeScript definitions for cookie-parser", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cookie-parser", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/santialbo", "githubUsername": "santial<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/cookie-parser"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "ec228e770576f43dd247dbfd15428c81cf1fbd84c02d978f6c4f0275d6394cf0", "typeScriptVersion": "3.9", "_id": "@types/cookie-parser@1.4.3", "dist": {"integrity": "sha512-CqSKwFwefj4PzZ5n/iwad/bow2hTCh0FlNAeWLtQM3JA/NX/iYagIpWG2cf1bQKQ2c9gU2log5VUCrn7LDOs0w==", "shasum": "3a01df117c5705cf89a84c876b50c5a1fd427a21", "tarball": "https://registry.npmmirror.com/@types/cookie-parser/-/cookie-parser-1.4.3.tgz", "fileCount": 4, "unpackedSize": 5974, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH6flL+C3eddO2VVnCgplvWu1D3lce1NNh61yJ9zuTGMAiBwAgRsZpV+TKdLDGJnzQvLU81rpGXn2Bi517l1yqxxLA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaRuNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo6TQ/8DyKEPFgwNTYAEL4o+HkK71D1INQoBqFwL10LNvxwGIGsl8qG\r\nXXH4bQJ4yVSQ2DVqUH9qkec05S+IU2c25i08eU1yK0pdsSG2rB0+m0ngg0mZ\r\nsepX7C1WocEaRCZy1Aqi/MrIWWkIEsigIs+9aRCIcuWOs/dGLCOX1gV7MlTk\r\n8Gh1ylWWWSMeGNT5wwaTmHep2vEjN+FZDebJzLo12owtPEUDDeUMRKLoH8dz\r\nX9Tw0MZh9gh1l6XQQNMEeaAzq+tQMRpQ/pKJUmFMrXeb/CwbiyGa3f0/PYLV\r\nk1SCI2s7awuuihm2sfhqCq3LOZly7OpN65FS4pvHQwiY+U9pi1GqbOPXJ93e\r\nBoU4NKWnGo30l2YhiosHP5cd9FfmADJnuZpQCotyUzbI9tokd1bx97pPIt6P\r\nMa3qqKomK65Y7nQykOeFYKJHMO4HO1uJE6Z9dlMfId4nxAwa085//Bug1WSc\r\ntusYJ0y/7z7zOyV4ZniU4KVHftCzLApqa2vLSk4BiqGXP/P9zVSLBEjraJ4g\r\njiSf/rSs/JgNBSy5OYrDP8qQCNxfdikGuHZxOx9IKDyJAWbioBlk7b2Lzu7/\r\noQciQao1OcN9bLVS7FmNllbAnuMLQQe8aQVuFwLIlNxZmwvzu1jRZGB9do7I\r\nz2ahhmm8hevbL+bsPxjXxvaauTH8Lmyf+0Y=\r\n=/vPy\r\n-----END PGP SIGNATURE-----\r\n", "size": 2043}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cookie-parser_1.4.3_1651055500932_0.5260084815610633"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-04-27T11:02:15.396Z"}, "1.4.4": {"name": "@types/cookie-parser", "version": "1.4.4", "description": "TypeScript definitions for cookie-parser", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cookie-parser", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/santialbo", "githubUsername": "santial<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/cookie-parser"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "1fe3e440b2282ee003cd4d897d5b1effc9b1eefbcb49b3fae680d61535576632", "typeScriptVersion": "4.3", "_id": "@types/cookie-parser@1.4.4", "dist": {"integrity": "sha512-Var+aj5I6ZgIqsQ05N2V8q5OBrFfZXtIGWWDSrEYLIbMw758obagSwdGcLCjwh1Ga7M7+wj0SDIAaAC/WT7aaA==", "shasum": "ca49273d838da2b08858e703943a71a460aa3af8", "tarball": "https://registry.npmmirror.com/@types/cookie-parser/-/cookie-parser-1.4.4.tgz", "fileCount": 5, "unpackedSize": 6044, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICO28v2l5kygDeN6Udg2D7gObRmVhLv073NsLWknwtbZAiBvjWeN03O+7B0j4b3t7ATGwS5CZUICvKPjFy3XQ8ROig=="}], "size": 2095}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cookie-parser_1.4.4_1693844306023_0.35737331323441257"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-04T16:18:26.156Z", "publish_time": 1693844306156, "_source_registry_name": "default"}, "1.4.5": {"name": "@types/cookie-parser", "version": "1.4.5", "description": "TypeScript definitions for cookie-parser", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cookie-parser", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "santial<PERSON>", "url": "https://github.com/santialbo"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/cookie-parser"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "1607749d196346acf3fa421816152bd7fb0f75c588d4bdb37f1446ea8cbbb1ca", "typeScriptVersion": "4.5", "_id": "@types/cookie-parser@1.4.5", "dist": {"integrity": "sha512-cbpH1NldYslPt7WRHXZFm+G7DTfUg57dQSCf1qrHwT8wtGX41JHLYf3Cieiqg7waPWjorVgcSSllZov+A1PJbg==", "shasum": "b1a2091c089e44e8d027ebb890b4ead4ef910b00", "tarball": "https://registry.npmmirror.com/@types/cookie-parser/-/cookie-parser-1.4.5.tgz", "fileCount": 5, "unpackedSize": 5378, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC97CdlwyZ+2vbRAenjkH/VKIzjrbipeIAoJPHWwBJdJgIhAOQn6PQzPXWYvo3oWlDL9anjffxXFQbyeGTIwEMhrnmE"}], "size": 2025}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cookie-parser_1.4.5_1697588518432_0.4912456244770942"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-18T00:21:58.632Z", "publish_time": 1697588518632, "_source_registry_name": "default"}, "1.4.6": {"name": "@types/cookie-parser", "version": "1.4.6", "description": "TypeScript definitions for cookie-parser", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cookie-parser", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "santial<PERSON>", "url": "https://github.com/santialbo"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/cookie-parser"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "6fbc227c0569fd619a0a8cc5ae1ad66f9552f0db20842f9e21a3b818f7afb1f3", "typeScriptVersion": "4.5", "_id": "@types/cookie-parser@1.4.6", "dist": {"integrity": "sha512-KoooCrD56qlLskXPLGUiJxOMnv5l/8m7cQD2OxJ73NPMhuSz9PmvwRD6EpjDyKBVrdJDdQ4bQK7JFNHnNmax0w==", "shasum": "002643c514cccf883a65cbe044dbdc38c0b92ade", "tarball": "https://registry.npmmirror.com/@types/cookie-parser/-/cookie-parser-1.4.6.tgz", "fileCount": 5, "unpackedSize": 5378, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD9jvJEG6yO/eu31jCHdkUKJnIzsjGd/KsQlQeb1j4cnQIgfKuLaoIrCuB+ZFxkgiXa6O9LKhWzJ3BpSnjycy58S2w="}], "size": 2032}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cookie-parser_1.4.6_1699319017298_0.7980923231682591"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-07T01:03:37.505Z", "publish_time": 1699319017505, "_source_registry_name": "default"}, "1.4.7": {"name": "@types/cookie-parser", "version": "1.4.7", "description": "TypeScript definitions for cookie-parser", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cookie-parser", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "santial<PERSON>", "url": "https://github.com/santialbo"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/cookie-parser"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "cc75d9bce3ea88b43d24a319ec4efcc59b528075e55f20baa07fabb8633d592f", "typeScriptVersion": "4.6", "_id": "@types/cookie-parser@1.4.7", "dist": {"integrity": "sha512-Fvuyi354Z+uayxzIGCwYTayFKocfV7TuDYZClCdIP9ckhvAu/ixDtCB6qx2TT0FKjPLf1f3P/J1rgf6lPs64mw==", "shasum": "c874471f888c72423d78d2b3c32d1e8579cf3c8f", "tarball": "https://registry.npmmirror.com/@types/cookie-parser/-/cookie-parser-1.4.7.tgz", "fileCount": 5, "unpackedSize": 5670, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICxn7qr8lHL7Q/37Uwb3XXnizHXqIy3ssPPYE/ht9EARAiEAvACtYhhNgz8rRajBs/rjiMwyTbfZzWwbOG39oXyktQw="}], "size": 2076}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cookie-parser_1.4.7_1709235343699_0.49193074938563375"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-29T19:35:43.855Z", "publish_time": 1709235343855, "_source_registry_name": "default"}, "1.4.8": {"name": "@types/cookie-parser", "version": "1.4.8", "license": "MIT", "_id": "@types/cookie-parser@1.4.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/santialbo", "name": "<PERSON><PERSON>", "githubUsername": "santial<PERSON>"}, {"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cookie-parser", "dist": {"shasum": "d2215e7915f624fbfe4233da8f063f511679f1f3", "tarball": "https://registry.npmmirror.com/@types/cookie-parser/-/cookie-parser-1.4.8.tgz", "fileCount": 5, "integrity": "sha512-l37JqFrOJ9yQfRQkljb41l0xVphc7kg5JTjjr+pLRZ0IyZ49V4BQ8vbF4Ut2C2e+WH4al3xD3ZwYwIUfnbT4NQ==", "signatures": [{"sig": "MEYCIQCw6P1CSN/IDV1TdAVNwlGgGqB5O//2C0l0sNZlxx+iKAIhAJgOzl1VfZzAoeV7wtNE3DPHVCT8mSWlQB2ogtBYHsO6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5728, "size": 2103}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/cookie-parser"}, "description": "TypeScript definitions for cookie-parser", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "peerDependencies": {"@types/express": "*"}, "typeScriptVersion": "5.0", "_npmOperationalInternal": {"tmp": "tmp/cookie-parser_1.4.8_1732568556605_0.39842063954604656", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "407ca12aad2175b5f5fe7c379897557f353e9b7dfaf47b3eb69a86f1d6b4f251", "_cnpmcore_publish_time": "2024-11-25T21:02:36.799Z", "publish_time": 1732568556799, "_source_registry_name": "default"}, "1.4.9": {"name": "@types/cookie-parser", "version": "1.4.9", "license": "MIT", "_id": "@types/cookie-parser@1.4.9", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/santialbo", "name": "<PERSON><PERSON>", "githubUsername": "santial<PERSON>"}, {"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/b<PERSON><PERSON><PERSON>bas", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cookie-parser", "dist": {"shasum": "f0e79c766a58ee7369a52e7509b3840222f68ed2", "tarball": "https://registry.npmmirror.com/@types/cookie-parser/-/cookie-parser-1.4.9.tgz", "fileCount": 5, "integrity": "sha512-tGZiZ2Gtc4m3wIdLkZ8mkj1T6CEHb35+VApbL2T14Dew8HA7c+04dmKqsKRNC+8RJPm16JEK0tFSwdZqubfc4g==", "signatures": [{"sig": "MEUCIQDC4WhvkpNudgyZXoSd0qTSa8Ht7CpvUOfcQqfuB61XwQIgWIeO7FqSis2gp3xER8jqhDSMzT90kfREb69QygALAhw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5938, "size": 2141}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/cookie-parser"}, "description": "TypeScript definitions for cookie-parser", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "peerDependencies": {"@types/express": "*"}, "typeScriptVersion": "5.1", "_npmOperationalInternal": {"tmp": "tmp/cookie-parser_1.4.9_1749262568261_0.9435390230580318", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "2f09243789fa087340511f46b2916af401aba29fb951b7459573a6b793e0596d", "_cnpmcore_publish_time": "2025-06-07T02:16:08.503Z", "publish_time": 1749262568503, "_source_registry_name": "default"}}, "contributors": [{"url": "https://github.com/santialbo", "name": "<PERSON><PERSON>", "githubUsername": "santial<PERSON>"}, {"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/b<PERSON><PERSON><PERSON>bas", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cookie-parser", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/cookie-parser"}, "_source_registry_name": "default"}