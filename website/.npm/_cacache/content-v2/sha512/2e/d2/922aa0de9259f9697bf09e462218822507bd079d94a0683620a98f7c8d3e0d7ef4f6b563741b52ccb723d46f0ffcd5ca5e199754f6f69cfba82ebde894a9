{"_attachments": {}, "_id": "@types/express-session", "_rev": "272152-61f1b57cda124806108c69b7", "description": "TypeScript definitions for express-session", "dist-tags": {"latest": "1.18.2", "ts2.0": "1.15.5", "ts2.1": "1.15.5", "ts2.2": "1.15.13", "ts2.3": "1.15.15", "ts2.4": "1.15.15", "ts2.5": "1.15.15", "ts2.6": "1.15.15", "ts2.7": "1.15.15", "ts2.8": "1.17.0", "ts2.9": "1.17.0", "ts3.0": "1.17.0", "ts3.1": "1.17.0", "ts3.2": "1.17.3", "ts3.3": "1.17.3", "ts3.4": "1.17.3", "ts3.5": "1.17.3", "ts3.6": "1.17.4", "ts3.7": "1.17.4", "ts3.8": "1.17.4", "ts3.9": "1.17.4", "ts4.0": "1.17.5", "ts4.1": "1.17.5", "ts4.2": "1.17.6", "ts4.3": "1.17.7", "ts4.4": "1.17.7", "ts4.5": "1.17.10", "ts4.6": "1.18.0", "ts4.7": "1.18.0", "ts4.8": "1.18.0", "ts4.9": "1.18.0", "ts5.0": "1.18.1", "ts5.1": "1.18.2", "ts5.2": "1.18.2", "ts5.3": "1.18.2", "ts5.4": "1.18.2", "ts5.5": "1.18.2", "ts5.6": "1.18.2", "ts5.7": "1.18.2", "ts5.8": "1.18.2", "ts5.9": "1.18.2"}, "license": "MIT", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "name": "@types/express-session", "readme": "# Installation\r\n> `npm install --save @types/express-session`\r\n\r\n# Summary\r\nThis package contains type definitions for express-session (https://github.com/expressjs/session).\r\n\r\n# Details\r\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-session.\r\n\r\n### Additional Details\r\n * Last updated: Sat, 07 Jun 2025 02:15:25 GMT\r\n * Dependencies: [@types/express](https://npmjs.com/package/@types/express)\r\n\r\n# Credits\r\nThese definitions were written by [<PERSON><PERSON><PERSON>](https://github.com/horiuchi), [<PERSON>](https://github.com/jacobbogers), [<PERSON><PERSON>](https://github.com/builtinnya), [<PERSON>](https://github.com/ry7n), [<PERSON>](https://github.com/fiznool), [<PERSON><PERSON><PERSON>](https://github.com/peter<PERSON><PERSON><PERSON><PERSON>), [<PERSON>](https://github.com/HoldYourWaffle), and [<PERSON>](https://github.com/b<PERSON>han<PERSON>bas).\r\n", "time": {"created": "2022-01-26T20:56:28.433Z", "modified": "2025-06-07T02:17:41.981Z", "1.17.4": "2021-07-06T20:50:39.760Z", "1.17.3": "2020-11-19T06:38:16.287Z", "1.17.2": "2020-11-12T22:18:52.261Z", "1.17.1": "2020-11-09T23:53:40.982Z", "1.17.0": "2020-02-27T19:31:14.952Z", "1.15.16": "2019-11-18T22:26:54.895Z", "1.15.15": "2019-10-14T20:37:56.681Z", "1.15.14": "2019-08-19T01:03:44.414Z", "1.15.13": "2019-06-19T17:19:07.856Z", "1.15.12": "2019-02-13T18:48:58.206Z", "1.15.11": "2018-09-08T01:22:03.616Z", "1.15.10": "2018-06-06T05:17:09.027Z", "1.15.9": "2018-05-27T14:14:11.988Z", "1.15.8": "2018-02-10T21:47:42.673Z", "1.15.7": "2018-02-05T16:02:19.498Z", "1.15.6": "2017-11-08T22:43:24.480Z", "1.15.5": "2017-10-26T19:32:16.510Z", "1.15.4": "2017-10-25T00:22:14.951Z", "1.15.3": "2017-08-21T21:51:51.930Z", "1.15.2": "2017-08-01T14:06:08.797Z", "1.15.1": "2017-07-14T14:14:29.736Z", "1.15.0": "2017-05-26T00:07:36.552Z", "0.0.32": "2016-10-05T20:56:51.986Z", "0.0.31": "2016-09-19T17:32:24.215Z", "0.0.30": "2016-08-25T18:41:55.580Z", "0.0.29": "2016-08-11T13:18:18.825Z", "0.0.28": "2016-07-14T14:33:48.379Z", "0.0.27-alpha": "2016-07-08T20:03:07.999Z", "0.0.26-alpha": "2016-07-04T00:09:54.387Z", "0.0.25-alpha": "2016-07-02T02:20:12.567Z", "0.0.24-alpha": "2016-07-01T22:41:57.164Z", "0.0.23-alpha": "2016-07-01T19:20:17.051Z", "0.0.22-alpha": "2016-05-25T04:52:23.927Z", "0.0.21-alpha": "2016-05-20T19:33:56.971Z", "0.0.16-alpha": "2016-05-19T20:50:20.371Z", "0.0.15-alpha": "2016-05-17T04:54:01.814Z", "1.17.5": "2022-07-07T03:02:23.185Z", "1.17.6": "2023-02-11T21:03:11.073Z", "1.17.7": "2023-03-19T21:03:02.985Z", "1.17.8": "2023-09-23T17:30:58.948Z", "1.17.9": "2023-10-18T02:14:36.666Z", "1.17.10": "2023-11-07T03:16:13.646Z", "1.18.0": "2024-02-26T20:07:45.314Z", "1.18.1": "2024-11-26T11:02:38.148Z", "1.18.2": "2025-06-07T02:16:51.481Z"}, "versions": {"1.17.4": {"name": "@types/express-session", "version": "1.17.4", "description": "TypeScript definitions for express-session", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jacobbogers", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/builtinnya", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ry7n", "githubUsername": "ry7n"}, {"name": "<PERSON>", "url": "https://github.com/fiznool", "githubUsername": "fiznool"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/HoldYourWaffle", "githubUsername": "HoldYourWaffle"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express-session"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "c9cb0d7a618a654c1cf1ded8f8b9440df59162cf275c9fae48c3a41afa699dcf", "typeScriptVersion": "3.6", "_id": "@types/express-session@1.17.4", "dist": {"shasum": "97a30a35e853a61bdd26e727453b8ed314d6166b", "size": 7227, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.17.4.tgz", "integrity": "sha512-7cNlSI8+oOBUHTfPXMwDxF/Lchx5aJ3ho7+p9jJZYVg9dVDJFh3qdMXmJtRsysnvS+C6x46k9DRYmrmCkE+MVg=="}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-session_1.17.4_1625604639486_0.4417884022133933"}, "_hasShrinkwrap": false, "publish_time": 1625604639760, "_cnpm_publish_time": 1625604639760, "_cnpmcore_publish_time": "2021-12-16T18:55:34.421Z"}, "1.17.3": {"name": "@types/express-session", "version": "1.17.3", "description": "TypeScript definitions for express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jacobbogers", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/builtinnya", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ry7n", "githubUsername": "ry7n"}, {"name": "<PERSON>", "url": "https://github.com/fiznool", "githubUsername": "fiznool"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/HoldYourWaffle", "githubUsername": "HoldYourWaffle"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express-session"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "34c5ef067df86d5fd57ad2111ab4df46ba721eb34354d8dbba93e23728a0cb6c", "typeScriptVersion": "3.2", "_id": "@types/express-session@1.17.3", "dist": {"shasum": "4a37c5c4428b8f922ac8ac1cb4bd9190a4d2b097", "size": 7201, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.17.3.tgz", "integrity": "sha512-57DnyxiqClXOIjoCgeKCUYfKxBPOlOY/k+l1TPK+7bSwyiPTrS5FIk1Ycql7twk4wO7P5lfOVy6akDGiaMSLfw=="}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-session_1.17.3_1605767896127_0.06140178767134419"}, "_hasShrinkwrap": false, "publish_time": 1605767896287, "_cnpm_publish_time": 1605767896287, "_cnpmcore_publish_time": "2021-12-16T18:55:34.679Z"}, "1.17.2": {"name": "@types/express-session", "version": "1.17.2", "description": "TypeScript definitions for express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jacobbogers", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/builtinnya", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ry7n", "githubUsername": "ry7n"}, {"name": "<PERSON>", "url": "https://github.com/fiznool", "githubUsername": "fiznool"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/HoldYourWaffle", "githubUsername": "HoldYourWaffle"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express-session"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "08072f4ed338fbee3b9b820a0e532616f3073963ecbe97f6120636c379706194", "typeScriptVersion": "3.2", "_id": "@types/express-session@1.17.2", "dist": {"shasum": "ed6a36dd9f267c7fef86004f653bfb9b5cea3c21", "size": 7224, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.17.2.tgz", "integrity": "sha512-QRm/fUuvr/BAosL9CvK351SDQP7wpD8+h3S8ZEE/8IvHJ/ZqHrjZbjx/flYfazyPw7yNi9O5fbjFZbh0vZ1ccg=="}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-session_1.17.2_1605219532074_0.919789475291666"}, "_hasShrinkwrap": false, "publish_time": 1605219532261, "_cnpm_publish_time": 1605219532261, "_cnpmcore_publish_time": "2021-12-16T18:55:34.950Z"}, "1.17.1": {"name": "@types/express-session", "version": "1.17.1", "description": "TypeScript definitions for express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jacobbogers", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/builtinnya", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ry7n", "githubUsername": "ry7n"}, {"name": "<PERSON>", "url": "https://github.com/fiznool", "githubUsername": "fiznool"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/HoldYourWaffle", "githubUsername": "HoldYourWaffle"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express-session"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "306f047f542d710d3c001b9278671b08e84ee4f37690c637bfb28dd5d251b4f3", "typeScriptVersion": "3.2", "_id": "@types/express-session@1.17.1", "dist": {"shasum": "5d69dba9f31be7ffe1db8da3da432213688f59ba", "size": 7232, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.17.1.tgz", "integrity": "sha512-XWiWXO+IOoiAj6rUG1HggCAl+ykY4GKo/Evwil+r9N2jHWq1zbAzKqjlGn5nmh/+Xl21SyF1bHyLrpoj7cjeYw=="}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-session_1.17.1_1604966020683_0.8601174052646592"}, "_hasShrinkwrap": false, "publish_time": 1604966020982, "_cnpm_publish_time": 1604966020982, "_cnpmcore_publish_time": "2021-12-16T18:55:35.189Z"}, "1.17.0": {"name": "@types/express-session", "version": "1.17.0", "description": "TypeScript definitions for express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jacobbogers", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/builtinnya", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ry7n", "githubUsername": "ry7n"}, {"name": "<PERSON>", "url": "https://github.com/fiznool", "githubUsername": "fiznool"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express-session"}, "scripts": {}, "dependencies": {"@types/express": "*", "@types/node": "*"}, "typesPublisherContentHash": "8f467989c31ba9d0621d8b8239695ffdee7d9d8594b4ceef9438ace9d641cbd0", "typeScriptVersion": "2.8", "_id": "@types/express-session@1.17.0", "dist": {"shasum": "770daf81368f6278e3e40dd894e1e52abbdca0cd", "size": 2790, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.17.0.tgz", "integrity": "sha512-OQEHeBFE1UhChVIBhRh9qElHUvTp4BzKKHxMDkGHT7WuYk5eL93hPG7D8YAIkoBSbhNEY0RjreF15zn+U0eLjA=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-session_1.17.0_1582831874851_0.2349711682486455"}, "_hasShrinkwrap": false, "publish_time": 1582831874952, "_cnpm_publish_time": 1582831874952, "_cnpmcore_publish_time": "2021-12-16T18:55:35.395Z"}, "1.15.16": {"name": "@types/express-session", "version": "1.15.16", "description": "TypeScript definitions for express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jacobbogers", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/builtinnya", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ry7n", "githubUsername": "ry7n"}, {"name": "<PERSON>", "url": "https://github.com/fiznool", "githubUsername": "fiznool"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express-session"}, "scripts": {}, "dependencies": {"@types/express": "*", "@types/node": "*"}, "typesPublisherContentHash": "1b4d3b3b97c005221017e27b11e0dd11018f8d66241b4255b2ba03d82c17ad43", "typeScriptVersion": "2.8", "_id": "@types/express-session@1.15.16", "dist": {"shasum": "91ba1f47a2fc2088811e8d7c17702ec92d8a8b23", "size": 2544, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.15.16.tgz", "integrity": "sha512-vWQpNt9t/zc4bTX+Ow5powZb9n3NwOM0SYsAJ7PYj5vliB6FA40ye5sW5fZTw8+ekbzJf/sgvtQocf7IryJBJw=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-session_1.15.16_1574116014754_0.8685751590925845"}, "_hasShrinkwrap": false, "publish_time": 1574116014895, "_cnpm_publish_time": 1574116014895, "_cnpmcore_publish_time": "2021-12-16T18:55:35.615Z"}, "1.15.15": {"name": "@types/express-session", "version": "1.15.15", "description": "TypeScript definitions for express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jacobbogers", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/builtinnya", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ry7n", "githubUsername": "ry7n"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express-session"}, "scripts": {}, "dependencies": {"@types/express": "*", "@types/node": "*"}, "typesPublisherContentHash": "58247a88e5e469e57f2310ea5ae5f607803f5ae21e94d9523454ee02e415d0cd", "typeScriptVersion": "2.3", "_id": "@types/express-session@1.15.15", "dist": {"shasum": "8e2e2b2ffd4e2ac62465a8ff78fff30ed81a8c8f", "size": 2388, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.15.15.tgz", "integrity": "sha512-D75AXBID8QBz0faDgkZd71VY/X1dk7ow2OeQsef7D+dWDtvtWlGcTaD12NPttdNLKdXUDWvJ5hrE2vEfcUoJgQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-session_1.15.15_1571085476527_0.007831672111534127"}, "_hasShrinkwrap": false, "publish_time": 1571085476681, "_cnpm_publish_time": 1571085476681, "_cnpmcore_publish_time": "2021-12-16T18:55:35.829Z"}, "1.15.14": {"name": "@types/express-session", "version": "1.15.14", "description": "TypeScript definitions for express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jacobbogers", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/builtinnya", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ry7n", "githubUsername": "ry7n"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express-session"}, "scripts": {}, "dependencies": {"@types/express": "*", "@types/node": "*"}, "typesPublisherContentHash": "802a8a885e9c3dde423b438e6493e1fe26fdfe983fff68102ef31be4e5919ee0", "typeScriptVersion": "2.3", "_id": "@types/express-session@1.15.14", "dist": {"shasum": "76ae4fd6e2c7c02a241d469ea2426f422c841b3f", "size": 2374, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.15.14.tgz", "integrity": "sha512-7kVzFTT0Jy0zmUYDt9ik76XbcqyS9NalV4gn4eLwhk1nGQn+lS/HjPODhG3Oi/GBR2w1LQHUdkz/5KICYMACiw=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-session_1.15.14_1566176624261_0.36720026921552495"}, "_hasShrinkwrap": false, "publish_time": 1566176624414, "_cnpm_publish_time": 1566176624414, "_cnpmcore_publish_time": "2021-12-16T18:55:36.061Z"}, "1.15.13": {"name": "@types/express-session", "version": "1.15.13", "description": "TypeScript definitions for express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jacobbogers", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/builtinnya", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ry7n", "githubUsername": "ry7n"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express-session"}, "scripts": {}, "dependencies": {"@types/express": "*", "@types/node": "*"}, "typesPublisherContentHash": "44d15290b7b71cbddbc26631854419e3047d13f26bf6c43bd48ff52963ac499e", "typeScriptVersion": "2.2", "_id": "@types/express-session@1.15.13", "dist": {"shasum": "47ae56b28b187bc2f38dc9b7050c3d1086f2c717", "size": 2374, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.15.13.tgz", "integrity": "sha512-BLRzO/ZfjTTLSRakUJxB0p5I5NmBHuyHkXDyh8sezdCMYxpqXrvMljKwle81I9AeCAzdq6nfz6qafmYLQ/rU9A=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-session_1.15.13_1560964747701_0.2957082556070043"}, "_hasShrinkwrap": false, "publish_time": 1560964747856, "_cnpm_publish_time": 1560964747856, "_cnpmcore_publish_time": "2021-12-16T18:55:36.264Z"}, "1.15.12": {"name": "@types/express-session", "version": "1.15.12", "description": "TypeScript definitions for express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jacobbogers", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/builtinnya", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ry7n", "githubUsername": "ry7n"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*", "@types/node": "*"}, "typesPublisherContentHash": "0bc0af12fa809a894786708a99f70a784c755f8a7876b09eb80450528364a668", "typeScriptVersion": "2.2", "_id": "@types/express-session@1.15.12", "dist": {"shasum": "1126704826e80f8381da4fbbb35a199f550d1433", "size": 2356, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.15.12.tgz", "integrity": "sha512-DHZXzWy6Nu5Ng0syXUiVFRpZ6/1DOXoTCWa6RG3itGrub2ioBYvgtDbkT6VHHNo3iOdHRROyWANsMBJVaflblQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-session_1.15.12_1550083737999_0.8936311079032193"}, "_hasShrinkwrap": false, "publish_time": 1550083738206, "_cnpm_publish_time": 1550083738206, "_cnpmcore_publish_time": "2021-12-16T18:55:36.455Z"}, "1.15.11": {"name": "@types/express-session", "version": "1.15.11", "description": "TypeScript definitions for express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jacobbogers", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/builtinnya", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ry7n", "githubUsername": "ry7n"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*", "@types/events": "*", "@types/node": "*"}, "typesPublisherContentHash": "c6109cb33a486290132aade129004dd655c020fad6032798911181d0f68e64a1", "typeScriptVersion": "2.2", "_id": "@types/express-session@1.15.11", "dist": {"shasum": "49bca7a8fb5c00402907e8ec0d3546ee28de2c41", "size": 2373, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.15.11.tgz", "integrity": "sha512-BRgOXYBhmt71CuOR/PQvKBRgcS567xJmE7dT3r/FvTmWc1ZsR5joVH32dgH+z6F4FugniF04tMyXc4UF9M8Hfg=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-session_1.15.11_1536369723469_0.2802317511835024"}, "_hasShrinkwrap": false, "publish_time": 1536369723616, "_cnpm_publish_time": 1536369723616, "_cnpmcore_publish_time": "2021-12-16T18:55:36.645Z"}, "1.15.10": {"name": "@types/express-session", "version": "1.15.10", "description": "TypeScript definitions for express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jacobbogers", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/builtinnya", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ry7n", "githubUsername": "ry7n"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*", "@types/events": "*", "@types/node": "*"}, "typesPublisherContentHash": "7e5d826b2e188198e1515aaaeb432cad7facd60483c07437042af4221cd09829", "typeScriptVersion": "2.2", "_id": "@types/express-session@1.15.10", "dist": {"shasum": "d0ae7d3d2fee26512574306333ac272a37430c86", "size": 2376, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.15.10.tgz", "integrity": "sha512-ubOWEvckAQ99X2N2YLNU2/TPuVXGrkkWQp0BbqO8UciCTrW9Evs7ihEAZ52nlwb9rlBZcxDFFOZg6XPZQxRhKQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-session_1.15.10_1528262228889_0.24150259843880906"}, "_hasShrinkwrap": false, "publish_time": 1528262229027, "_cnpm_publish_time": 1528262229027, "_cnpmcore_publish_time": "2021-12-16T18:55:36.829Z"}, "1.15.9": {"name": "@types/express-session", "version": "1.15.9", "description": "TypeScript definitions for express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jacobbogers", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/builtinnya", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ry7n", "githubUsername": "ry7n"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*", "@types/events": "*", "@types/node": "*"}, "typesPublisherContentHash": "36898b84c717d3d23a8a606e9668260826b30e05f503d203293d7d29a298cbff", "typeScriptVersion": "2.2", "_id": "@types/express-session@1.15.9", "dist": {"shasum": "1d45d2b8d05c6b803f0237cb428653b1912e712c", "size": 2363, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.15.9.tgz", "integrity": "sha512-+6tQCRUG+ucrHjFNPL46gICIXOliHK87wvT2e2OqV4gJBw3PMVv+rh1S4MJsNOwguSMplCyMVk31tLp1o7c+gA=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-session_1.15.9_1527430451917_0.2646125191463551"}, "_hasShrinkwrap": false, "publish_time": 1527430451988, "_cnpm_publish_time": 1527430451988, "_cnpmcore_publish_time": "2021-12-16T18:55:37.043Z"}, "1.15.8": {"name": "@types/express-session", "version": "1.15.8", "description": "TypeScript definitions for express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jacobbogers", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/builtinnya", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ry7n", "githubUsername": "ry7n"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*", "@types/events": "*", "@types/node": "*"}, "typesPublisherContentHash": "446508456815b7a9b8e3633f97268baa6e2dcd03eb7d1dc36b13640a69252cf7", "typeScriptVersion": "2.2", "_id": "@types/express-session@1.15.8", "dist": {"shasum": "b187732e8fa88ad9fd5d93c021696460a0694b7c", "size": 2347, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.15.8.tgz", "integrity": "sha512-Be5N9zul4C/IH1UjRDaVJ46wkG1jsBgJlihBdWlqJWfCaiqvaVmxcyqcLey7omSFGCTIUDgdHqf0vwNjEZOSVA=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-session_1.15.8_1518299262556_0.8956664010366981"}, "_hasShrinkwrap": false, "publish_time": 1518299262673, "_cnpm_publish_time": 1518299262673, "_cnpmcore_publish_time": "2021-12-16T18:55:37.265Z"}, "1.15.7": {"name": "@types/express-session", "version": "1.15.7", "description": "TypeScript definitions for express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jacobbogers", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/builtinnya", "githubUsername": "<PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*", "@types/events": "*", "@types/node": "*"}, "typesPublisherContentHash": "9fd85f1310326eaac8dc064f567fc43d2485b15f51ee94f9a66d5f941acda44a", "typeScriptVersion": "2.2", "_id": "@types/express-session@1.15.7", "dist": {"shasum": "c97861f4a095fb35f04f2354cb4e9053395c176b", "size": 2298, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.15.7.tgz", "integrity": "sha512-tfpAi84HZ+d+RkD6UdMSaukhE44XFxJXgDjy6pd28CXSuhBgujIx04FRBs44Fku5p927QyK4K8oV0tUY4J/DKw=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-session-1.15.7.tgz_1517846539424_0.8105270524974912"}, "directories": {}, "publish_time": 1517846539498, "_hasShrinkwrap": false, "_cnpm_publish_time": 1517846539498, "_cnpmcore_publish_time": "2021-12-16T18:55:37.487Z"}, "1.15.6": {"name": "@types/express-session", "version": "1.15.6", "description": "TypeScript definitions for express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jacobbogers", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*", "@types/node": "*"}, "typesPublisherContentHash": "95b287dc2ef6d8b15eb2038e8791205987af81778f739f554e16a6c14949ddf8", "typeScriptVersion": "2.2", "_id": "@types/express-session@1.15.6", "dist": {"shasum": "eb7c913971a56ad0d83efebe4e9f7aaffa77d8ed", "size": 2248, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.15.6.tgz", "integrity": "sha512-yOWF/CQsw20YBy9phGtGXhXafcwEgc2nQXorTI8Uegi4+viguBWBw8XQU3Ctd8fiLkqX5zklJD8ow4bGJKbDKw=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-session-1.15.6.tgz_1510181004406_0.6810266191605479"}, "directories": {}, "publish_time": 1510181004480, "_hasShrinkwrap": false, "_cnpm_publish_time": 1510181004480, "_cnpmcore_publish_time": "2021-12-16T18:55:37.678Z"}, "1.15.5": {"name": "@types/express-session", "version": "1.15.5", "description": "TypeScript definitions for express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi", "githubUsername": "<PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*", "@types/node": "*"}, "typesPublisherContentHash": "a015f1fc6c487bfc787a9db99216027810267cde8cb6f08bbb1a6ce3f9e949c5", "typeScriptVersion": "2.0", "_id": "@types/express-session@1.15.5", "dist": {"shasum": "e6c181b00b786d92c97ca1a79d133602854ae9fe", "size": 2228, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.15.5.tgz", "integrity": "sha512-YaGftnHwORCPpo7NUTVExZWM+zorri5mPimUFjO1mYpEEUu3Jdg8K/xzCKwhFp+mvXYx3NpfA0VvRnqpG5l+fQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-session-1.15.5.tgz_1509046336452_0.2789072263985872"}, "directories": {}, "publish_time": 1509046336510, "_hasShrinkwrap": false, "_cnpm_publish_time": 1509046336510, "_cnpmcore_publish_time": "2021-12-16T18:55:37.897Z"}, "1.15.4": {"name": "@types/express-session", "version": "1.15.4", "description": "TypeScript definitions for express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi", "githubUsername": "<PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*", "@types/node": "*"}, "typesPublisherContentHash": "f721363fd47ddfec44fb64271fe39930c80120dfc32d58fb507fea10ab3fe9ce", "typeScriptVersion": "2.0", "_id": "@types/express-session@1.15.4", "dist": {"shasum": "6f0fc81d6364d06b14583eaadb46abd696bb16f1", "size": 2228, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.15.4.tgz", "integrity": "sha512-6hd36phc6GB5bYQyxcOmJM/ZsYjPc79bKRq6UvLceIxtJzKK1QZSSNzDcsSODepM7ohu7bic0asavMpz9FCpww=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-session-1.15.4.tgz_1508890934892_0.6423337142914534"}, "directories": {}, "publish_time": 1508890934951, "_hasShrinkwrap": false, "_cnpm_publish_time": 1508890934951, "_cnpmcore_publish_time": "2021-12-16T18:55:38.107Z"}, "1.15.3": {"name": "@types/express-session", "version": "1.15.3", "description": "TypeScript definitions for express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*", "@types/node": "*"}, "typesPublisherContentHash": "209f11b3bc0edc4c6cf8d13602417bf9e61e366c44985c897aae013e6a074475", "typeScriptVersion": "2.0", "_id": "@types/express-session@1.15.3", "dist": {"shasum": "39b6995393832284a1f368f4f1ae48acaab2aac9", "size": 2201, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.15.3.tgz", "integrity": "sha512-tjIb4AJ5FJNG5DSs03XhtgViThxXKIyv/fGdnHmFUJ+yIG1eq+KPBdsK6i6jgx49X/AO3bPJpjL6ymaJ7+Toqw=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-session-1.15.3.tgz_1503352311839_0.8736446269322187"}, "directories": {}, "publish_time": 1503352311930, "_hasShrinkwrap": false, "_cnpm_publish_time": 1503352311930, "_cnpmcore_publish_time": "2021-12-16T18:55:38.299Z"}, "1.15.2": {"name": "@types/express-session", "version": "1.15.2", "description": "TypeScript definitions for express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi/"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*", "@types/node": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "fde4d9a9fd5183d59d79eba813f99efacb078f81934c2689177f38b517fb8713", "typeScriptVersion": "2.0", "_id": "@types/express-session@1.15.2", "dist": {"shasum": "3d936febc0252bea472557cdae1aa2e377ac5e10", "size": 2220, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.15.2.tgz", "integrity": "sha512-NuyputHGAlptHatD5iqjRONQkAR0xyaLODHzLKpxsmn7ZgNMgJBAptv5ko8Gf1R97CCLBP81Cr04uwwPgbH6zA=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-session-1.15.2.tgz_1501596367875_0.746487942757085"}, "directories": {}, "publish_time": 1501596368797, "_hasShrinkwrap": false, "_cnpm_publish_time": 1501596368797, "_cnpmcore_publish_time": "2021-12-16T18:55:38.533Z"}, "1.15.1": {"name": "@types/express-session", "version": "1.15.1", "description": "TypeScript definitions for express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi/"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*", "@types/node": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "94321fd72d2901f9d0ef36efc10bf99241af19a58f2b0b462e8d618b1677c713", "typeScriptVersion": "2.0", "_id": "@types/express-session@1.15.1", "dist": {"shasum": "77a92bc30b4269879bc3719330ab75893519cf41", "size": 2217, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.15.1.tgz", "integrity": "sha512-J1xJenIdnFei3Bczrzb2FwJg/SVc5eUKXZ4cViRW4DFyQuBG5IaN96LeD00ntbxL5xKyN/MtsMZbiP5hPgs0uA=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-session-1.15.1.tgz_1500041669645_0.5525795128196478"}, "directories": {}, "publish_time": 1500041669736, "_hasShrinkwrap": false, "_cnpm_publish_time": 1500041669736, "_cnpmcore_publish_time": "2021-12-16T18:55:38.772Z"}, "1.15.0": {"name": "@types/express-session", "version": "1.15.0", "description": "TypeScript definitions for express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi/"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*", "@types/node": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "fde4d9a9fd5183d59d79eba813f99efacb078f81934c2689177f38b517fb8713", "typeScriptVersion": "2.0", "_id": "@types/express-session@1.15.0", "dist": {"shasum": "9b6822192faa80073148d32a17248f8e6c1e79e2", "size": 2220, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.15.0.tgz", "integrity": "sha512-J0tx+FcvValITZl/iNAihuih5Vw6VpikxLyNbOvcor1HwgkDRR+tRXkPxpBVupJU6PHcXbwdvw3mLpwwad+1Tg=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-session-1.15.0.tgz_1495757256425_0.8749090125784278"}, "directories": {}, "publish_time": 1495757256552, "_hasShrinkwrap": false, "_cnpm_publish_time": 1495757256552, "_cnpmcore_publish_time": "2021-12-16T18:55:38.970Z"}, "0.0.32": {"name": "@types/express-session", "version": "0.0.32", "description": "TypeScript definitions for express-session", "license": "MIT", "author": "<PERSON><PERSON><PERSON> <https://github.com/horiuchi/>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*", "@types/node": "*"}, "peerDependencies": {}, "typings": "index.d.ts", "typesPublisherContentHash": "059b6483c6f43635c2e11d8c0c82d8fa83b591d5a0433347a75bd75cc5435818", "_id": "@types/express-session@0.0.32", "dist": {"shasum": "82f9e6a028eb6125a412db95f0e61a97d1945ee0", "size": 1688, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-0.0.32.tgz", "integrity": "sha512-bdetV8FHvg5UVlhk5xDdwewSlHe+MSh5OJ+F0xwjCdLPlrXHyHUDRLnPB0U+M086nkkKNBv6yiCCvuKLfOuLTw=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/express-session-0.0.32.tgz_1475701011741_0.898646105080843"}, "directories": {}, "publish_time": 1475701011986, "_hasShrinkwrap": false, "_cnpm_publish_time": 1475701011986, "_cnpmcore_publish_time": "2021-12-16T18:55:39.197Z"}, "0.0.31": {"name": "@types/express-session", "version": "0.0.31", "description": "TypeScript definitions for express-session", "license": "MIT", "author": "<PERSON><PERSON><PERSON> <https://github.com/horiuchi/>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*", "@types/node": "*"}, "typings": "index.d.ts", "typesPublisherContentHash": "76c00cd0dbe7264b40e6373bbb3eb527dfe599e63cdbaf215a8d6201281cca97", "_id": "@types/express-session@0.0.31", "dist": {"shasum": "9c89cbeeda14685c1c4221ccaf78e8bc6011a590", "size": 1664, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-0.0.31.tgz", "integrity": "sha512-kve2Q7FEef0f8hm9Rl6HJat5nnRpejrEQI5TJGhPLxV6fREYoMeM/mJaYO3UUswJKfhCAZOMWcClcZS6vnyyIA=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/express-session-0.0.31.tgz_1474306340929_0.8660298245958984"}, "directories": {}, "publish_time": 1474306344215, "_hasShrinkwrap": false, "_cnpm_publish_time": 1474306344215, "_cnpmcore_publish_time": "2021-12-16T18:55:39.393Z"}, "0.0.30": {"name": "@types/express-session", "version": "0.0.30", "description": "TypeScript definitions for express-session", "license": "MIT", "author": "<PERSON><PERSON><PERSON> <https://github.com/horiuchi/>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "4.0.*", "@types/node": "6.0.*"}, "typings": "index.d.ts", "_id": "@types/express-session@0.0.30", "dist": {"shasum": "a58bcae6b7d95915d600bbaed7dcac63ac841c99", "size": 1662, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-0.0.30.tgz", "integrity": "sha512-l5qvEQvSpqDHrlvefVJ1Ty3LpAP78A/rlwkR7Qx48AhI7wnRJ/60he1EMofrAiGPqFdLBOm0uLo+CUcUDjEaYg=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/express-session-0.0.30.tgz_1472150515049_0.3751087870914489"}, "directories": {}, "publish_time": 1472150515580, "_hasShrinkwrap": false, "_cnpm_publish_time": 1472150515580, "_cnpmcore_publish_time": "2021-12-16T18:55:39.767Z"}, "0.0.29": {"name": "@types/express-session", "version": "0.0.29", "description": "TypeScript definitions for express-session", "license": "MIT", "author": "<PERSON><PERSON><PERSON> <https://github.com/horiuchi/>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "4.0.*", "@types/node": "6.0.*"}, "typings": "index.d.ts", "_id": "@types/express-session@0.0.29", "dist": {"shasum": "a9a9e187cec9c204401455a4979615e372afde7b", "size": 1662, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-0.0.29.tgz", "integrity": "sha512-jsDCHjkgQfxtdI9JDjcavbrV9QErStrjuZN3q8QO97bXYQmMNmBM3z+iDRjl9B0v9qNrIgeNzbtG5Vgr6CyWjQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/express-session-0.0.29.tgz_1470921498594_0.5697030934970826"}, "directories": {}, "publish_time": 1470921498825, "_hasShrinkwrap": false, "_cnpm_publish_time": 1470921498825, "_cnpmcore_publish_time": "2021-12-16T18:55:39.977Z"}, "0.0.28": {"name": "@types/express-session", "version": "0.0.28", "description": "TypeScript definitions for express-session", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "https://github.com/horiuchi/"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/node": "4.0.*"}, "_id": "@types/express-session@0.0.28", "_shasum": "785e2f18cc7fe88baa8baf84ea5238119cac3d2e", "_from": "output\\express-session", "_resolved": "file:output\\express-session", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "785e2f18cc7fe88baa8baf84ea5238119cac3d2e", "size": 1627, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-0.0.28.tgz", "integrity": "sha512-XlolGjFtLDXH3le1RCnCyPNNulD+A1JIFltNbu7hsepdB4edMFWMXu6d+eokuEU3UPwAv0uRWjJeFYLGmpZeBw=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/express-session-0.0.28.tgz_1468506824946_0.5591444987803698"}, "directories": {}, "publish_time": 1468506828379, "_hasShrinkwrap": false, "_cnpm_publish_time": 1468506828379, "_cnpmcore_publish_time": "2021-12-16T18:55:40.187Z"}, "0.0.27-alpha": {"name": "@types/express-session", "version": "0.0.27-alpha", "description": "TypeScript definitions for express-session", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "https://github.com/horiuchi/"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/node": "4.0.27-alpha"}, "_id": "@types/express-session@0.0.27-alpha", "_shasum": "653b54c462a068b2f4133bf65862b6611c485d5f", "_from": "output\\express-session", "_resolved": "file:output\\express-session", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "653b54c462a068b2f4133bf65862b6611c485d5f", "size": 1628, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-0.0.27-alpha.tgz", "integrity": "sha512-AzUlGoH1M1qwHLB1JFXKTxo/Z1uxDb/AGAWck02kSToVUAmNllMaUbIazxNCraT0yVgLlR4BFm4HnILmjLc6WQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/express-session-0.0.27-alpha.tgz_1468008185516_0.7153024091385305"}, "directories": {}, "publish_time": 1468008187999, "_hasShrinkwrap": false, "_cnpm_publish_time": 1468008187999, "_cnpmcore_publish_time": "2021-12-16T18:55:40.377Z"}, "0.0.26-alpha": {"name": "@types/express-session", "version": "0.0.26-alpha", "description": "TypeScript definitions for express-session", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "https://github.com/horiuchi/"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/node": "4.0.26-alpha"}, "_id": "@types/express-session@0.0.26-alpha", "_shasum": "d979d1247045f03037da27f32842096d8c30e4d5", "_from": "output\\express-session", "_resolved": "file:output\\express-session", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "d979d1247045f03037da27f32842096d8c30e4d5", "size": 1628, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-0.0.26-alpha.tgz", "integrity": "sha512-rY8Xat3oyJpBMUO4gitVhT3/V+T4dX3Z/jgJRMs5GDE/y4q4TpAzpMj1Mb6YOQRMG+C07NT7/K22p4kViyPhxQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/express-session-0.0.26-alpha.tgz_1467590993886_0.6541140319313854"}, "directories": {}, "publish_time": 1467590994387, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467590994387, "_cnpmcore_publish_time": "2021-12-16T18:55:40.587Z"}, "0.0.25-alpha": {"name": "@types/express-session", "version": "0.0.25-alpha", "description": "TypeScript definitions for express-session", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "https://github.com/horiuchi/"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/node": "4.0.24-alpha"}, "_id": "@types/express-session@0.0.25-alpha", "_shasum": "98385d9f0f9cbedc261e90116742041e7eed9345", "_from": "output\\express-session", "_resolved": "file:output\\express-session", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "98385d9f0f9cbedc261e90116742041e7eed9345", "size": 1634, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-0.0.25-alpha.tgz", "integrity": "sha512-pve0G2AyZftb1dMa+vkER91BiqNitSlRD8EWlgPW/mGdxbpMSmDA6jjwDLU8fGHg6iJdBcYmRAee9at6FScz3Q=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/express-session-0.0.25-alpha.tgz_1467426009274_0.9550430662930012"}, "directories": {}, "publish_time": 1467426012567, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467426012567, "_cnpmcore_publish_time": "2021-12-16T18:55:40.854Z"}, "0.0.24-alpha": {"name": "@types/express-session", "version": "0.0.24-alpha", "description": "TypeScript definitions for express-session", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "https://github.com/horiuchi/"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/node": "4.0.23-alpha"}, "_id": "@types/express-session@0.0.24-alpha", "_shasum": "a15f84de272d2cb50f665e629f32267fa4b34c9f", "_from": "output\\express-session", "_resolved": "file:output\\express-session", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "a15f84de272d2cb50f665e629f32267fa4b34c9f", "size": 1631, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-0.0.24-alpha.tgz", "integrity": "sha512-rHlMk+B9<PERSON><PERSON>irk+2tc/2gOxx9XQeFwtKIZ3WiozgsvANHIl9KheDp927IYYrrFVuuxKiLCSCjnXEFBokTpZYJDg=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/express-session-0.0.24-alpha.tgz_1467412915035_0.7921566641889513"}, "directories": {}, "publish_time": 1467412917164, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467412917164, "_cnpmcore_publish_time": "2021-12-16T18:55:41.076Z"}, "0.0.23-alpha": {"name": "@types/express-session", "version": "0.0.23-alpha", "description": "TypeScript definitions for express-session", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "https://github.com/horiuchi/"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/node": "4.0.*"}, "_id": "@types/express-session@0.0.23-alpha", "_shasum": "0786a744d9e85e0edc2370b5ded0bf9a3917a68c", "_from": "output\\express-session", "_resolved": "file:output\\express-session", "_npmVersion": "3.9.5", "_nodeVersion": "6.2.2", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "0786a744d9e85e0edc2370b5ded0bf9a3917a68c", "size": 1626, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-0.0.23-alpha.tgz", "integrity": "sha512-fOA3gjUnETkChyPAv0UVgxkVwdEUEuY+08HGANbJIGvBsEU3VhoTq0PccYwAizL5449xZ3yAoxUaGrSCbTMf+Q=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/express-session-0.0.23-alpha.tgz_1467400813644_0.9769623228348792"}, "directories": {}, "publish_time": 1467400817051, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467400817051, "_cnpmcore_publish_time": "2021-12-16T18:55:41.313Z"}, "0.0.22-alpha": {"name": "@types/express-session", "version": "0.0.22-alpha", "description": "TypeScript definitions for express-session", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "https://github.com/horiuchi/"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/node": "*"}, "_id": "@types/express-session@0.0.22-alpha", "_shasum": "40e9cd19db81799a8ae07d007cd3e090670e29f9", "_from": "output\\express-session", "_resolved": "file:output\\express-session", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "40e9cd19db81799a8ae07d007cd3e090670e29f9", "size": 1598, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-0.0.22-alpha.tgz", "integrity": "sha512-cs+uMNd78wyTaQaqpuqYpKGraWcG+0CeNSptIi4rhJssAU8f+fu0tgu2XCratgkX7QrmT0OxAM64mdMWJaH5yA=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/express-session-0.0.22-alpha.tgz_1464151941300_0.008536753244698048"}, "directories": {}, "publish_time": 1464151943927, "_hasShrinkwrap": false, "_cnpm_publish_time": 1464151943927, "_cnpmcore_publish_time": "2021-12-16T18:55:41.495Z"}, "0.0.21-alpha": {"name": "@types/express-session", "version": "0.0.21-alpha", "description": "TypeScript definitions for express-session", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "https://github.com/horiuchi/"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/node": "*"}, "_id": "@types/express-session@0.0.21-alpha", "_shasum": "8475ea2d8c34d0eee3481ce04905b845ee97ddb8", "_from": "output\\express-session", "_resolved": "file:output\\express-session", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "8475ea2d8c34d0eee3481ce04905b845ee97ddb8", "size": 1595, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-0.0.21-alpha.tgz", "integrity": "sha512-dUL681Sg+7S5jHyv0YkutlwBO/a1ls8qmwTN44dpZE2gwFWBDQiWWPglvKFFja+olj0Ry/GgcO6LH3WaBFEZtg=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/express-session-0.0.21-alpha.tgz_1463772836573_0.7859831498935819"}, "directories": {}, "publish_time": 1463772836971, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463772836971, "_cnpmcore_publish_time": "2021-12-16T18:55:41.695Z"}, "0.0.16-alpha": {"name": "@types/express-session", "version": "0.0.16-alpha", "description": "Type definitions for express-session from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "https://github.com/horiuchi/"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/node": "*"}, "_id": "@types/express-session@0.0.16-alpha", "_shasum": "600b211ab2cd848ff4e00fb07d205cb74906b4fa", "_from": "output\\express-session", "_resolved": "file:output\\express-session", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "600b211ab2cd848ff4e00fb07d205cb74906b4fa", "size": 1611, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-0.0.16-alpha.tgz", "integrity": "sha512-HTh5UqnbjoQVhhjLClbGDHdS7WQoLWaIJ1+J0rltZSL/vdzeFBWzHSe4ClLobBRvD+uEuQGHSVMAwgBGoiy0WA=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/express-session-0.0.16-alpha.tgz_1463691017936_0.7832745935302228"}, "directories": {}, "publish_time": 1463691020371, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463691020371, "_cnpmcore_publish_time": "2021-12-16T18:55:41.924Z"}, "0.0.15-alpha": {"name": "@types/express-session", "version": "0.0.15-alpha", "description": "Type definitions for express-session from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "https://github.com/horiuchi/"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {}, "_id": "@types/express-session@0.0.15-alpha", "_shasum": "36ccc2c5c44a05e8d88feef89749e37c1c964e1f", "_from": "output\\express-session", "_resolved": "file:output\\express-session", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "36ccc2c5c44a05e8d88feef89749e37c1c964e1f", "size": 1590, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-0.0.15-alpha.tgz", "integrity": "sha512-Aj8VUDw6UfjmZJecmM37sKIDIh+t1NXidEoteIcesnPM3RlgHBM5eZHdI0pLaBuFxyfsA9/A+OtDFW4/yBssCA=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/express-session-0.0.15-alpha.tgz_1463460838005_0.21304469276219606"}, "directories": {}, "publish_time": 1463460841814, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463460841814, "_cnpmcore_publish_time": "2021-12-16T18:55:42.142Z"}, "1.17.5": {"name": "@types/express-session", "version": "1.17.5", "description": "TypeScript definitions for express-session", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jacobbogers", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/builtinnya", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ry7n", "githubUsername": "ry7n"}, {"name": "<PERSON>", "url": "https://github.com/fiznool", "githubUsername": "fiznool"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/HoldYourWaffle", "githubUsername": "HoldYourWaffle"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express-session"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "9d0551cf0bdb891e1753ebd24dae4983885b37ebda7a3502c34346e1eb58cd12", "typeScriptVersion": "4.0", "_id": "@types/express-session@1.17.5", "dist": {"integrity": "sha512-l0DhkvNVfyUPEEis8fcwbd46VptfA/jmMwHfob2TfDMf3HyPLiB9mKD71LXhz5TMUobODXPD27zXSwtFQLHm+w==", "shasum": "13f48852b4aa60ff595835faeb4b4dda0ba0866e", "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.17.5.tgz", "fileCount": 5, "unpackedSize": 23600, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDJk8TILg1EM3NCVEVV+mah32kaqemfS5H3zz7PnSAKmQIgPssNKk1sl74qeegsJGZuy2kJlRstmWyqM5FfOeYR9do="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixky/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJmw/9EnJW+O+uKpbgkZvE57xg1pxryZxVBIQb0ea9iDOK3c9LZoWC\r\neRpbZ/FDY5GmXNySNz69q9SRUO4kPJmRt0AHFDs0r+qV6b4F02v1JoDASKtg\r\nMvkSHxS+grRhyxbBC16WWA/ogfTR/jH8Q1W7F334dtWScokXLU3RsuEniRxr\r\nI1LQO+8F6f/Ikz/PgzpRhDOMVoctludhcLfkqM5J/Giag68VTnP4+oXkZOP+\r\nQh02fN2QlgmbTg+6iLAtTdTgsH3OhWg71kKUjmP+XocQ94aI9aaxX5yMMxbO\r\nBDKljif+gzOhxO5w69QaarXg8Dm9YYmwuzSLkULXHfbA4w4AOCvO/WVAsLCj\r\nLgb5SRQpdoBE12fmJmhiGklvoL5zknMr6rWjIq2sdAMqudUI0kJVV4Kgchc7\r\nt+jGfkxEWB4WH+jNSNVwxlGcOMTkqQF8lacFGRYbTh9Lhpsu9s8ZEkhsW4BL\r\nNUna/r5Lcppy4gABMhdbZh60+H7Rt4UGBTmrxgjST4BgI5LGL3cFY8uYTsFp\r\nNLVUZicO6fYp6um9dpXFiXk4u09EkS1J09UZdFEnAsu8aUbnD4zHEJHjoyDE\r\n/oLs92eGwoIL76y7kInikTWC0+UxNvk3MS14PwahH2n9syT36DCMCyKNO3Uj\r\nTWtxAX7J+8fZHmjuaY3nRPeVYE+3UO00kVw=\r\n=2/bI\r\n-----END PGP SIGNATURE-----\r\n", "size": 7347}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-session_1.17.5_1657162943003_0.2816241816037295"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-07-07T03:19:13.123Z"}, "1.17.6": {"name": "@types/express-session", "version": "1.17.6", "description": "TypeScript definitions for express-session", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jacobbogers", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/builtinnya", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ry7n", "githubUsername": "ry7n"}, {"name": "<PERSON>", "url": "https://github.com/fiznool", "githubUsername": "fiznool"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/HoldYourWaffle", "githubUsername": "HoldYourWaffle"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express-session"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "84071dd38e949b0dd9779c072e2dbf03446c430205ccb0fb648c7df3c27cc495", "typeScriptVersion": "4.2", "_id": "@types/express-session@1.17.6", "dist": {"integrity": "sha512-L6sB04HVA4HEZo1hDL65JXdZdBJtzZnCiw/P7MnO4w6746tJCNtXlHtzEASyI9ccn9zyOw6IbqQuhVa03VpO4w==", "shasum": "1c8881ba0dc836ffbf1071b2f020d60fcca0f08c", "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.17.6.tgz", "fileCount": 5, "unpackedSize": 23621, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCRxd0b0kxdgwNYPreq2kHtcOaoxADkHrAyQgUP4mdsvQIhAJ1huUWqXPV2PeW4njIuGCn7LlFDU4rpsj8wPKHLUrE4"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6AKPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZhQ/9H3/ahh/mgydmt87a8EvCp9/TsqgzLhTg4y5us+ABXyj9HMj1\r\n00dVwHjZeDEDFR4GHT36iZ5hL1R0LjpT7UAjjHOiNiCXqbhj1LKhc7FKTahK\r\nPL1QMu8X8pPybXQSjAOlAFZf4NCubiU8mVskxmUOer9nNwso9OZUUF0YIl/T\r\nxn/vif98fWHhUO6AVSq1OiVGTSlPSSl61/JER4jGWu6/DHmv8N0kgidn2Db+\r\nBNzkhHDD5+b<PERSON>ewa7xJac+BRQBWlHzcl4vP5L5uC95zsqDhQxYO9EC+nQJT97\r\nGAX1r3mF0uhK5WP/slbPcABx3QZGqBiujOqi/fExt2UsV07B2hhyCBRYzTJ2\r\no5DB5D5tXks4/1cqKq/1OoyCw4iU27/jWWf1GC0K23kKEL4PPVzN3uM0hbgS\r\ncq93bKhQlXhNQ63XGGD5SgHf4Pelg4wGeQ3FxziVDiSecu7zlgTPHGr1Th0I\r\niMi5/6pu3XC7rGRoVVkTcvCsAi2A8Td5Dqeh2Yf8vDGeRfQqCFjHZcxVk7H1\r\nOkzN2Td/TAw4U5NOkcUgPn/EgC+V8061vlJnJvpTACONeoHBBrESDXAcddzN\r\nsElX2S/XrxP/7af6+VA7t88rYkunaqa0mvoBqROZrvq5Up4qLer5mc2IDKuV\r\nX+hHyf5UapmlBGMUnk48Vai2QTfj8NSR8o0=\r\n=6mFP\r\n-----END PGP SIGNATURE-----\r\n", "size": 7353}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-session_1.17.6_1676149390914_0.1791824044946042"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-11T21:03:11.073Z", "publish_time": 1676149391073}, "1.17.7": {"name": "@types/express-session", "version": "1.17.7", "description": "TypeScript definitions for express-session", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jacobbogers", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/builtinnya", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ry7n", "githubUsername": "ry7n"}, {"name": "<PERSON>", "url": "https://github.com/fiznool", "githubUsername": "fiznool"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/HoldYourWaffle", "githubUsername": "HoldYourWaffle"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express-session"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "d08830cd578b35901592704ad02629f8704fbc708e19c23f1b298ee44d7e7582", "typeScriptVersion": "4.3", "_id": "@types/express-session@1.17.7", "dist": {"integrity": "sha512-L25080PBYoRLu472HY/HNCxaXY8AaGgqGC8/p/8+BYMhG0RDOLQ1wpXOpAzr4Gi5TGozTKyJv5BVODM5UNyVMw==", "shasum": "ced215c1244cb594be10e39f2781ddcd650be9a6", "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.17.7.tgz", "fileCount": 5, "unpackedSize": 23621, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCOx1PmKJenPA/HxVl/hp4+h3oA/sANSav5nYZ4Hv6oXAIhAO3WN9e8CewHX2wjjU8kWZf22IHlFi+N/SsqTddSLPLK"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkF3iHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrdUQ//RSvkuL3krkuycieeOTJtUh35KiYWuyyUG7Tk6qP03LRVU1AU\r\nHighpbbal/tc5FfOdDTtD42uoB+s8qIo0YJQ42PkKSrt0ecq4HLeGtJd0ft4\r\nQvzryXhokV0605JnuYbZqyB54nu46qimjZ1v9toHMR0hkthZeVuV+EFQGg5e\r\n+9W2aMPPg8LtQZ4/tdLR2a6H8e0DbGA2IYDZfBWoFBXE20fBZoVpqYFPSHAt\r\nMXTj1KJc/XRKiQpHg5G6Xn5NCVouyPeQ0xEAwhKQNkDVh0bzHzVog1JZZSdw\r\nlpm5oYq9JyTaFgfTucPJ2kjCiyvb9ZYckUxsRLMpEHBOLuCaDBIpfgVLosvl\r\nepATQCdFNIbYmG1hSUDCT7d7KjDKn+A2alcLnmDRlcTGBRGEyMzbRhM0W3X5\r\ntUf/vaI1ceL4Om6YDm+n1CtpX7YkG4XMuvNAeJ/nVSN3lprQpNoPIUsc3Qja\r\nGgITyBo3HgJLqQs+Jd2hvBoHoXYEcQ2WIqHNAb2pwcGL6sijTbq/Mq5WmWqh\r\naGEpv1YR3EzRw2gonopHdFyKKj4O84Q98wmsXoREBzhrfoZ9XkPmTha1jrH5\r\n67TYfSD7bdFyTqDjb2gTLB8HKFcPDX4MPcVeanMCKlIlhPyk0lix8gs5RJkG\r\nYtSWkIi0MLFg1yF8hnSSY7t/l+soFs/OXJ0=\r\n=zQvH\r\n-----END PGP SIGNATURE-----\r\n", "size": 7357}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-session_1.17.7_1679259782777_0.878769738580873"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-19T21:03:02.985Z", "publish_time": 1679259782985}, "1.17.8": {"name": "@types/express-session", "version": "1.17.8", "description": "TypeScript definitions for express-session", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jacobbogers", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/builtinnya", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ry7n", "githubUsername": "ry7n"}, {"name": "<PERSON>", "url": "https://github.com/fiznool", "githubUsername": "fiznool"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/HoldYourWaffle", "githubUsername": "HoldYourWaffle"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express-session"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "375ef6153a76889090af3151ce4ebd6a5015c179bf2996592a0c5810d46ab12a", "typeScriptVersion": "4.5", "_id": "@types/express-session@1.17.8", "dist": {"integrity": "sha512-bFF7/3wOldMn+56XyFRGY9ZzCr3JWhNSP2ajMPgTlbZR6BQOCHdAbNA9W5dMBPgMywpIP4zkmhxP6Opm/NRYMQ==", "shasum": "e676575a661732e6f34048a6bc130a1867f9612f", "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.17.8.tgz", "fileCount": 5, "unpackedSize": 23614, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICYVWxh3fxvf0Cjmn5bV0nEAEpYnEYCfnhvmbwANACfDAiEA2ds700K7BApb/sLIpYb0bg/7HylgC3rGY/jYK0eINTk="}], "size": 7367}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-session_1.17.8_1695490258738_0.6715298642146996"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-23T17:30:58.948Z", "publish_time": 1695490258948, "_source_registry_name": "default"}, "1.17.9": {"name": "@types/express-session", "version": "1.17.9", "description": "TypeScript definitions for express-session", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/jacobbogers"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/builtinnya"}, {"name": "<PERSON>", "githubUsername": "ry7n", "url": "https://github.com/ry7n"}, {"name": "<PERSON>", "githubUsername": "fiznool", "url": "https://github.com/fiznool"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "githubUsername": "HoldYourWaffle", "url": "https://github.com/HoldYourWaffle"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express-session"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "e630a4f3e7bd295e77bf3cab4caa350935cf8f054fac85267c54aca94c03eac2", "typeScriptVersion": "4.5", "_id": "@types/express-session@1.17.9", "dist": {"integrity": "sha512-yIqficLlTPdloeEPhOVenpOUWILkdaXHUWhTOqFGx9JoSuTgeatNjb97k8VvJehbTk0kUSUAHy5r27PXMga89Q==", "shasum": "2811b8c3f2e7efb5fd239e4cb0f26f58de8c0a62", "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.17.9.tgz", "fileCount": 5, "unpackedSize": 22939, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCcNU30j5umeYO0XvC8rlVWohZpJq26r6i/H/IC7K32UgIhAPGEq8paa04bi1uG0CJ56IYSbl9Zd3R9B8jPDs2PzgLZ"}], "size": 7243}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-session_1.17.9_1697595276514_0.912120308349561"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-18T02:14:36.666Z", "publish_time": 1697595276666, "_source_registry_name": "default"}, "1.17.10": {"name": "@types/express-session", "version": "1.17.10", "description": "TypeScript definitions for express-session", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/jacobbogers"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/builtinnya"}, {"name": "<PERSON>", "githubUsername": "ry7n", "url": "https://github.com/ry7n"}, {"name": "<PERSON>", "githubUsername": "fiznool", "url": "https://github.com/fiznool"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "githubUsername": "HoldYourWaffle", "url": "https://github.com/HoldYourWaffle"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express-session"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "5afa89305f7f65e79cdf77e4adc6bb8fb70753c7b0eb97bdf49725dcc6705894", "typeScriptVersion": "4.5", "_id": "@types/express-session@1.17.10", "dist": {"integrity": "sha512-U32bC/s0ejXijw5MAzyaV4tuZopCh/K7fPoUDyNbsRXHvPSeymygYD1RFL99YOLhF5PNOkzswvOTRaVHdL1zMw==", "shasum": "3a9394f1f314a4c657af3fb1cdb52f00fc207fd2", "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.17.10.tgz", "fileCount": 5, "unpackedSize": 22940, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE1pclZvDD1eTHtizRug81eJrZF10d3L+S5Etgt99zHSAiEAgNLCqNg1TgaX911T8g35QlDVE+J/fdG+jN0bBRevJ/g="}], "size": 7246}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-session_1.17.10_1699326973444_0.39359542959236915"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-07T03:16:13.646Z", "publish_time": 1699326973646, "_source_registry_name": "default"}, "1.18.0": {"name": "@types/express-session", "version": "1.18.0", "description": "TypeScript definitions for express-session", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-session", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/horiuchi"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/jacobbogers"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/builtinnya"}, {"name": "<PERSON>", "githubUsername": "ry7n", "url": "https://github.com/ry7n"}, {"name": "<PERSON>", "githubUsername": "fiznool", "url": "https://github.com/fiznool"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "githubUsername": "HoldYourWaffle", "url": "https://github.com/HoldYourWaffle"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express-session"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "d2eafc3037eb746a0592bb0ff07e6ab9e965e6460f319eecb844a696b4e4850f", "typeScriptVersion": "4.6", "_id": "@types/express-session@1.18.0", "dist": {"integrity": "sha512-27JdDRgor6PoYlURY+Y5kCakqp5ulC0kmf7y+QwaY+hv9jEFuQOThgkjyA53RP3jmKuBsH5GR6qEfFmvb8mwOA==", "shasum": "7c6f25c3604b28d6bc08a2e3929997bbc7672fa2", "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.18.0.tgz", "fileCount": 5, "unpackedSize": 24453, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG7NcJ7p89IKDN9GxiMvphDeEjmZgWUGIA79QcR13NHtAiEAsDuDJC/WzlsN1UjuDxaci7n0xrLIxfoF9dqgEtuDd2s="}], "size": 7454}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-session_1.18.0_1708978065093_0.03533578441187757"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-26T20:07:45.314Z", "publish_time": 1708978065314, "_source_registry_name": "default"}, "1.18.1": {"name": "@types/express-session", "version": "1.18.1", "license": "MIT", "_id": "@types/express-session@1.18.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/horiuchi", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jacobbogers", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/builtinnya", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/ry7n", "name": "<PERSON>", "githubUsername": "ry7n"}, {"url": "https://github.com/fiznool", "name": "<PERSON>", "githubUsername": "fiznool"}, {"url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/HoldYourWaffle", "name": "<PERSON>", "githubUsername": "HoldYourWaffle"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-session", "dist": {"shasum": "67c629a34b60a63a4724f359aac0c0e6d1f15365", "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.18.1.tgz", "fileCount": 5, "integrity": "sha512-S6TkD/lljxDlQ2u/4A70luD8/ZxZcrU5pQwI1rVXCiaVIywoFgbA+PIUNDjPhQpPdK0dGleLtYc/y7XWBfclBg==", "signatures": [{"sig": "MEYCIQC6JA8Fqj/LQ0MFa0hLHCWEg/voPnLkeyDnaWKNE/II7gIhAOhpdSbB0X/y/Qf9DLSuILkOS49Nkge//DvyERutABZ1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24758, "size": 7559}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-session"}, "description": "TypeScript definitions for express-session", "directories": {}, "dependencies": {"@types/express": "*"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.0", "_npmOperationalInternal": {"tmp": "tmp/express-session_1.18.1_1732618957991_0.49688158134416893", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "903769a1d7f0f7d2ee2636656c9dc8235c21222b2e63e28f81da2ab3523d97ff", "_cnpmcore_publish_time": "2024-11-26T11:02:38.148Z", "publish_time": 1732618958148, "_source_registry_name": "default"}, "1.18.2": {"name": "@types/express-session", "version": "1.18.2", "license": "MIT", "_id": "@types/express-session@1.18.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/horiuchi", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jacobbogers", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/builtinnya", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/ry7n", "name": "<PERSON>", "githubUsername": "ry7n"}, {"url": "https://github.com/fiznool", "name": "<PERSON>", "githubUsername": "fiznool"}, {"url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/HoldYourWaffle", "name": "<PERSON>", "githubUsername": "HoldYourWaffle"}, {"url": "https://github.com/b<PERSON><PERSON><PERSON>bas", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-session", "dist": {"shasum": "778dc3296da9aa97d5bf8e42358a54c52a230317", "tarball": "https://registry.npmmirror.com/@types/express-session/-/express-session-1.18.2.tgz", "fileCount": 5, "integrity": "sha512-k+I0BxwVXsnEU2hV77cCobC08kIsn4y44C3gC0b46uxZVMaXA04lSPgRLR/bSL2w0t0ShJiG8o4jPzRG/nscFg==", "signatures": [{"sig": "MEYCIQCY104rI/eHfCorYxZ3/PrdYB9KVdoBpTJ5LtX8LpT+SwIhAIEwnVzYvsmFzBmio1d8QEUkzroj0d/+M59JWO8VmBxc", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 24970, "size": 7598}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-session"}, "description": "TypeScript definitions for express-session", "directories": {}, "dependencies": {"@types/express": "*"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.1", "_npmOperationalInternal": {"tmp": "tmp/express-session_1.18.2_1749262611191_0.7195491924957391", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "684a3bf08e3ce762fbe9e75a77866983245e65cc0e08830fa2880f92e9d8ae58", "_cnpmcore_publish_time": "2025-06-07T02:16:51.481Z", "publish_time": 1749262611481, "_source_registry_name": "default"}}, "contributors": [{"url": "https://github.com/horiuchi", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jacobbogers", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/builtinnya", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/ry7n", "name": "<PERSON>", "githubUsername": "ry7n"}, {"url": "https://github.com/fiznool", "name": "<PERSON>", "githubUsername": "fiznool"}, {"url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/HoldYourWaffle", "name": "<PERSON>", "githubUsername": "HoldYourWaffle"}, {"url": "https://github.com/b<PERSON><PERSON><PERSON>bas", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-session", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-session"}, "_source_registry_name": "default"}