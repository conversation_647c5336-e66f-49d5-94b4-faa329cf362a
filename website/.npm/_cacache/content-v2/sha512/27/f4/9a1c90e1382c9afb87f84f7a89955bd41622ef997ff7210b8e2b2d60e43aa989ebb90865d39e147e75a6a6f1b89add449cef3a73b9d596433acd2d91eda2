{"_attachments": {}, "_id": "@types/express", "_rev": "4366-61f15010b77ea98a7491eefe", "description": "TypeScript definitions for express", "dist-tags": {"latest": "5.0.3", "ts2.0": "4.0.35", "ts2.1": "4.0.35", "ts2.2": "4.17.0", "ts2.3": "4.17.2", "ts2.4": "4.17.2", "ts2.5": "4.17.2", "ts2.6": "4.17.2", "ts2.7": "4.17.2", "ts2.8": "4.17.6", "ts2.9": "4.17.6", "ts3.0": "4.17.7", "ts3.1": "4.17.8", "ts3.2": "4.17.9", "ts3.3": "4.17.10", "ts3.4": "4.17.11", "ts3.5": "4.17.12", "ts3.6": "4.17.13", "ts3.7": "4.17.13", "ts3.8": "4.17.13", "ts3.9": "4.17.13", "ts4.0": "4.17.13", "ts4.1": "4.17.14", "ts4.2": "4.17.17", "ts4.3": "4.17.17", "ts4.4": "4.17.17", "ts4.5": "4.17.21", "ts4.6": "4.17.21", "ts4.7": "4.17.21", "ts4.8": "5.0.0", "ts4.9": "5.0.0", "ts5.0": "5.0.1", "ts5.1": "5.0.3", "ts5.2": "5.0.3", "ts5.3": "5.0.3", "ts5.4": "5.0.3", "ts5.5": "5.0.3", "ts5.6": "5.0.3", "ts5.7": "5.0.3", "ts5.8": "5.0.3", "ts5.9": "5.0.3"}, "license": "MIT", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "name": "@types/express", "readme": "# Installation\r\n> `npm install --save @types/express`\r\n\r\n# Summary\r\nThis package contains type definitions for express (http://expressjs.com).\r\n\r\n# Details\r\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express.\r\n\r\n### Additional Details\r\n * Last updated: Sat, 07 Jun 2025 02:15:25 GMT\r\n * Dependencies: [@types/body-parser](https://npmjs.com/package/@types/body-parser), [@types/express-serve-static-core](https://npmjs.com/package/@types/express-serve-static-core), [@types/serve-static](https://npmjs.com/package/@types/serve-static)\r\n\r\n# Credits\r\nThese definitions were written by [<PERSON>](https://github.com/b<PERSON><PERSON><PERSON>), [Puneet Arora](https://github.com/puneetar), [<PERSON>](https://github.com/dfrankland), and [<PERSON>](https://github.com/bjohansebas).\r\n", "time": {"created": "2022-01-26T13:43:44.743Z", "modified": "2025-06-07T02:19:57.019Z", "4.17.13": "2021-07-06T20:49:45.161Z", "4.17.12": "2021-05-25T17:01:38.171Z", "4.17.11": "2021-01-12T21:43:21.668Z", "4.17.10": "2021-01-11T22:15:20.561Z", "4.17.9": "2020-11-11T01:02:17.094Z", "4.17.8": "2020-09-01T13:46:24.822Z", "4.17.7": "2020-07-06T22:41:43.587Z", "4.17.6": "2020-04-09T20:56:18.411Z", "4.17.5": "2020-04-08T16:39:38.706Z", "4.17.4": "2020-03-31T23:15:51.660Z", "4.17.3": "2020-03-03T18:55:19.030Z", "4.17.2": "2019-11-01T17:24:55.969Z", "4.17.1": "2019-08-19T00:57:03.297Z", "4.17.0": "2019-06-04T20:46:08.489Z", "4.16.1": "2019-01-22T17:51:27.213Z", "4.16.0": "2018-06-05T00:03:27.771Z", "4.11.1": "2018-02-01T22:26:36.771Z", "4.11.0": "2017-12-20T14:51:52.241Z", "4.0.39": "2017-10-26T19:32:09.597Z", "4.0.38": "2017-10-25T00:22:06.413Z", "4.0.37": "2017-08-21T21:51:40.144Z", "4.0.36": "2017-06-15T20:13:37.806Z", "4.0.35": "2017-01-18T06:06:53.985Z", "4.0.34": "2016-11-15T14:56:13.496Z", "4.0.33": "2016-09-19T17:30:22.258Z", "4.0.32": "2016-08-25T18:41:36.080Z", "4.0.31": "2016-08-19T15:24:53.704Z", "4.0.30": "2016-08-02T15:52:09.301Z", "4.0.29": "2016-07-14T14:32:38.620Z", "4.0.28-alpha": "2016-07-06T21:31:51.543Z", "4.0.27-alpha": "2016-07-03T23:21:08.123Z", "4.0.26-alpha": "2016-07-02T02:19:17.535Z", "4.0.25-alpha": "2016-07-01T22:39:25.963Z", "4.0.24-alpha": "2016-07-01T19:22:05.018Z", "4.0.23-alpha": "2016-05-25T04:51:16.283Z", "4.0.22-alpha": "2016-05-20T19:33:03.103Z", "4.0.17-alpha": "2016-05-19T20:48:45.330Z", "4.0.16-alpha": "2016-05-17T04:52:53.278Z", "4.17.14": "2022-09-13T18:26:29.199Z", "4.17.15": "2022-12-13T23:33:27.097Z", "4.17.16": "2023-01-23T21:32:42.951Z", "4.17.17": "2023-02-03T21:32:52.154Z", "4.17.18": "2023-09-23T17:26:30.610Z", "4.17.19": "2023-10-10T18:13:20.034Z", "4.17.20": "2023-10-18T02:07:39.878Z", "4.17.21": "2023-11-07T03:10:10.569Z", "5.0.0": "2024-09-25T19:20:29.866Z", "5.0.1": "2025-03-19T19:32:13.062Z", "5.0.2": "2025-05-16T23:34:15.854Z", "4.17.22": "2025-05-16T23:34:24.033Z", "5.0.3": "2025-06-07T02:19:15.168Z", "4.17.23": "2025-06-07T02:19:33.740Z"}, "versions": {"4.17.13": {"name": "@types/express", "version": "4.17.13", "description": "TypeScript definitions for Express", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "China Medical University Hospital", "url": "https://github.com/CMUH", "githubUsername": "CMUH"}, {"name": "<PERSON>et A<PERSON>ra", "url": "https://github.com/puneetar", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dfrankland", "githubUsername": "dfrankland"}], "main": "", "types": "index.d.ts", "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.18", "@types/qs": "*", "@types/serve-static": "*"}, "typesPublisherContentHash": "df0c9de39b435f4152916282f0ae9e98f0548d6b50f6bb6aedddc52e4e3f25a7", "typeScriptVersion": "3.6", "_id": "@types/express@4.17.13", "dist": {"shasum": "a76e2995728999bab51a33fabce1d705a3709034", "size": 2870, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.17.13.tgz", "integrity": "sha512-6bSZTPaTIACxn48l50SR+axgrqm6qXFIxrdAKaG6PaJk3+zuUr35hBlgT7vOmJcum+OEaIBLtHV/qloEAFITeA=="}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express_4.17.13_1625604585057_0.16287282726138108"}, "_hasShrinkwrap": false, "publish_time": 1625604585161, "_cnpm_publish_time": 1625604585161, "_cnpmcore_publish_time": "2021-12-13T12:23:40.116Z"}, "4.17.12": {"name": "@types/express", "version": "4.17.12", "description": "TypeScript definitions for Express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "China Medical University Hospital", "url": "https://github.com/CMUH", "githubUsername": "CMUH"}, {"name": "<PERSON>et A<PERSON>ra", "url": "https://github.com/puneetar", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dfrankland", "githubUsername": "dfrankland"}], "main": "", "types": "index.d.ts", "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.18", "@types/qs": "*", "@types/serve-static": "*"}, "typesPublisherContentHash": "d049caa40a56607a828a6217665f711f33b7a3a9055f4722521e06beb76b0c20", "typeScriptVersion": "3.5", "_id": "@types/express@4.17.12", "dist": {"shasum": "4bc1bf3cd0cfe6d3f6f2853648b40db7d54de350", "size": 2854, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.17.12.tgz", "integrity": "sha512-pTYas6FrP15B1Oa0bkN5tQMNqOcVXa9j4FTFtO8DWI9kppKib+6NJtfTOOLcwxuuYvcX2+dVG6et1SxW/Kc17Q=="}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express_4.17.12_1621962097586_0.6830464842194817"}, "_hasShrinkwrap": false, "publish_time": 1621962098171, "_cnpm_publish_time": 1621962098171, "_cnpmcore_publish_time": "2021-12-13T12:23:40.392Z"}, "4.17.11": {"name": "@types/express", "version": "4.17.11", "description": "TypeScript definitions for Express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "China Medical University Hospital", "url": "https://github.com/CMUH", "githubUsername": "CMUH"}, {"name": "<PERSON>et A<PERSON>ra", "url": "https://github.com/puneetar", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dfrankland", "githubUsername": "dfrankland"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.18", "@types/qs": "*", "@types/serve-static": "*"}, "typesPublisherContentHash": "51b7ca65fbad2f43fbcff8d74c47db7b8f36b31f71458c1fb328511d0075ac5a", "typeScriptVersion": "3.4", "_id": "@types/express@4.17.11", "dist": {"shasum": "debe3caa6f8e5fcda96b47bd54e2f40c4ee59545", "size": 2706, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.17.11.tgz", "integrity": "sha512-no+R6rW60JEc59977wIxreQVsIEOAYwgCqldrA/vkpCnbD7MqTefO97lmoBe4WE0F156bC4uLSP1XHDOySnChg=="}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express_4.17.11_1610487801527_0.8561602524891956"}, "_hasShrinkwrap": false, "publish_time": 1610487801668, "_cnpm_publish_time": 1610487801668, "_cnpmcore_publish_time": "2021-12-13T12:23:40.678Z"}, "4.17.10": {"name": "@types/express", "version": "4.17.10", "description": "TypeScript definitions for Express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "China Medical University Hospital", "url": "https://github.com/CMUH", "githubUsername": "CMUH"}, {"name": "<PERSON>et A<PERSON>ra", "url": "https://github.com/puneetar", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dfrankland", "githubUsername": "dfrankland"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "*", "@types/qs": "*", "@types/serve-static": "*"}, "typesPublisherContentHash": "cab83c5ce0e118f64236605dc6370cb115347f6e0cea8c47dffc89d0bd5526f1", "typeScriptVersion": "3.3", "_id": "@types/express@4.17.10", "dist": {"shasum": "850fe07d6d887412d522a4295fb84a685e69f683", "size": 2698, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.17.10.tgz", "integrity": "sha512-GRwKdE+iV6mA8glCvQ7W5iaoIhd6u1HDsNTF76UPRi7T89SLjOfeCLShVmQSgpXzcpf3zgcz2SbMiCcjnYRRxQ=="}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express_4.17.10_1610403320292_0.7378392088201524"}, "_hasShrinkwrap": false, "publish_time": 1610403320561, "_cnpm_publish_time": 1610403320561, "_cnpmcore_publish_time": "2021-12-13T12:23:41.024Z"}, "4.17.9": {"name": "@types/express", "version": "4.17.9", "description": "TypeScript definitions for Express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "China Medical University Hospital", "url": "https://github.com/CMUH", "githubUsername": "CMUH"}, {"name": "<PERSON>et A<PERSON>ra", "url": "https://github.com/puneetar", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dfrankland", "githubUsername": "dfrankland"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "*", "@types/qs": "*", "@types/serve-static": "*"}, "typesPublisherContentHash": "7ab86361b26b09c4776f6242516a0597c3d2fe95d50942d8b6934633c75e5ac0", "typeScriptVersion": "3.2", "_id": "@types/express@4.17.9", "dist": {"shasum": "f5f2df6add703ff28428add52bdec8a1091b0a78", "size": 2669, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.17.9.tgz", "integrity": "sha512-SDzEIZInC4sivGIFY4Sz1GG6J9UObPwCInYJjko2jzOf/Imx/dlpume6Xxwj1ORL82tBbmN4cPDIDkLbWHk9hw=="}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express_4.17.9_1605056536811_0.9632944274222708"}, "_hasShrinkwrap": false, "publish_time": 1605056537094, "_cnpm_publish_time": 1605056537094, "_cnpmcore_publish_time": "2021-12-13T12:23:41.406Z"}, "4.17.8": {"name": "@types/express", "version": "4.17.8", "description": "TypeScript definitions for Express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "China Medical University Hospital", "url": "https://github.com/CMUH", "githubUsername": "CMUH"}, {"name": "<PERSON>et A<PERSON>ra", "url": "https://github.com/puneetar", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dfrankland", "githubUsername": "dfrankland"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "*", "@types/qs": "*", "@types/serve-static": "*"}, "typesPublisherContentHash": "96005d6f6692d0f0137502480774a884f89e10f2b3d6fc74e5f9e69501336a5f", "typeScriptVersion": "3.1", "_id": "@types/express@4.17.8", "dist": {"shasum": "3df4293293317e61c60137d273a2e96cd8d5f27a", "size": 2656, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.17.8.tgz", "integrity": "sha512-wLhcKh3PMlyA2cNAB9sjM1BntnhPMiM0JOBwPBqttjHev2428MLEB4AYVN+d8s2iyCVZac+o41Pflm/ZH5vLXQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express_4.17.8_1598967984701_0.7384959242760181"}, "_hasShrinkwrap": false, "publish_time": 1598967984822, "_cnpm_publish_time": 1598967984822, "_cnpmcore_publish_time": "2021-12-13T12:23:41.794Z"}, "4.17.7": {"name": "@types/express", "version": "4.17.7", "description": "TypeScript definitions for Express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "China Medical University Hospital", "url": "https://github.com/CMUH", "githubUsername": "CMUH"}, {"name": "<PERSON>et A<PERSON>ra", "url": "https://github.com/puneetar", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dfrankland", "githubUsername": "dfrankland"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "*", "@types/qs": "*", "@types/serve-static": "*"}, "typesPublisherContentHash": "2d5d7b5ea5d674c61265136248d6b905af2b400bbe3d1bde75eecaeda711c681", "typeScriptVersion": "3.0", "_id": "@types/express@4.17.7", "dist": {"shasum": "42045be6475636d9801369cd4418ef65cdb0dd59", "size": 2660, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.17.7.tgz", "integrity": "sha512-dCOT5lcmV/uC2J9k0rPafATeeyz+99xTt54ReX11/LObZgfzJqZNcW27zGhYyX+9iSEGXGt5qLPwRSvBZcLvtQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express_4.17.7_1594075303452_0.3261856573044659"}, "_hasShrinkwrap": false, "publish_time": 1594075303587, "_cnpm_publish_time": 1594075303587, "_cnpmcore_publish_time": "2021-12-13T12:23:42.134Z"}, "4.17.6": {"name": "@types/express", "version": "4.17.6", "description": "TypeScript definitions for Express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "China Medical University Hospital", "url": "https://github.com/CMUH", "githubUsername": "CMUH"}, {"name": "<PERSON>et A<PERSON>ra", "url": "https://github.com/puneetar", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dfrankland", "githubUsername": "dfrankland"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "*", "@types/qs": "*", "@types/serve-static": "*"}, "typesPublisherContentHash": "f81de6cba6637f88e36510f46978b3f1e8429a4d83afb0e1b090385e39bbad2e", "typeScriptVersion": "2.8", "_id": "@types/express@4.17.6", "dist": {"shasum": "6bce49e49570507b86ea1b07b806f04697fac45e", "size": 2668, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.17.6.tgz", "integrity": "sha512-n/mr9tZI83kd4azlPG5y997C/M4DNABK9yErhFM6hKdym4kkmd9j0vtsJyjFIwfRBxtrxZtAfGZCNRIBMFLK5w=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express_4.17.6_1586465778267_0.8777667869692583"}, "_hasShrinkwrap": false, "publish_time": 1586465778411, "_cnpm_publish_time": 1586465778411, "_cnpmcore_publish_time": "2021-12-13T12:23:42.475Z"}, "4.17.5": {"name": "@types/express", "version": "4.17.5", "description": "TypeScript definitions for Express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "China Medical University Hospital", "url": "https://github.com/CMUH", "githubUsername": "CMUH"}, {"name": "<PERSON>et A<PERSON>ra", "url": "https://github.com/puneetar", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dfrankland", "githubUsername": "dfrankland"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "*", "@types/qs": "*", "@types/serve-static": "*"}, "typesPublisherContentHash": "cddb7e0f74b1c428978e5c11a6f9cb553c274aaecc94e64fd0c791a920c0f4b2", "typeScriptVersion": "2.8", "_id": "@types/express@4.17.5", "dist": {"shasum": "f7457497c72ca7bf98b13bb6f1ef2898ed550bd9", "size": 2663, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.17.5.tgz", "integrity": "sha512-u4Si7vYAjy5/UyRFa8EoqLHh6r82xOZPbWRQHlSf6alob0rlyza7EkU0RbR8kOZqgWp6R5+aRcHMYYby7w12Bg=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express_4.17.5_1586363978585_0.6803676053246901"}, "_hasShrinkwrap": false, "publish_time": 1586363978706, "_cnpm_publish_time": 1586363978706, "_cnpmcore_publish_time": "2021-12-13T12:23:42.793Z"}, "4.17.4": {"name": "@types/express", "version": "4.17.4", "description": "TypeScript definitions for Express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "China Medical University Hospital", "url": "https://github.com/CMUH", "githubUsername": "CMUH"}, {"name": "<PERSON>et A<PERSON>ra", "url": "https://github.com/puneetar", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dfrankland", "githubUsername": "dfrankland"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "*", "@types/qs": "*", "@types/serve-static": "*"}, "typesPublisherContentHash": "ebe0948d1568a4e4a90eacb8cc722f4a7d90cf05bc984c01d9a08e414dc852cd", "typeScriptVersion": "2.8", "_id": "@types/express@4.17.4", "dist": {"shasum": "e78bf09f3f530889575f4da8a94cd45384520aac", "size": 2638, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.17.4.tgz", "integrity": "sha512-DO1L53rGqIDUEvOjJKmbMEQ5Z+BM2cIEPy/eV3En+s166Gz+FeuzRerxcab757u/U4v4XF4RYrZPmqKa+aY/2w=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express_4.17.4_1585696551545_0.6791876980816665"}, "_hasShrinkwrap": false, "publish_time": 1585696551660, "_cnpm_publish_time": 1585696551660, "_cnpmcore_publish_time": "2021-12-13T12:23:43.156Z"}, "4.17.3": {"name": "@types/express", "version": "4.17.3", "description": "TypeScript definitions for Express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "China Medical University Hospital", "url": "https://github.com/CMUH", "githubUsername": "CMUH"}, {"name": "<PERSON>et A<PERSON>ra", "url": "https://github.com/puneetar", "githubUsername": "<PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "*", "@types/serve-static": "*"}, "typesPublisherContentHash": "3c5eb756cb320608cab3b3623c0be6270e9d470f33151e8a17ae593e98b2e9fc", "typeScriptVersion": "2.8", "_id": "@types/express@4.17.3", "dist": {"shasum": "38e4458ce2067873b09a73908df488870c303bd9", "size": 2541, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.17.3.tgz", "integrity": "sha512-I8cGRJj3pyOLs/HndoP+25vOqhqWkAZsWMEmq1qXy/b/M3ppufecUwaK2/TVDVxcV61/iSdhykUjQQ2DLSrTdg=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express_4.17.3_1583261718921_0.7880166132175628"}, "_hasShrinkwrap": false, "publish_time": 1583261719030, "_cnpm_publish_time": 1583261719030, "_cnpmcore_publish_time": "2021-12-13T12:23:43.523Z"}, "4.17.2": {"name": "@types/express", "version": "4.17.2", "description": "TypeScript definitions for Express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "China Medical University Hospital", "url": "https://github.com/CMUH", "githubUsername": "CMUH"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "*", "@types/serve-static": "*"}, "typesPublisherContentHash": "8b71f3f86019a2933648e51284d5f964f1c64717b31814b1311316de4e68790e", "typeScriptVersion": "2.3", "_id": "@types/express@4.17.2", "dist": {"shasum": "a0fb7a23d8855bac31bc01d5a58cadd9b2173e6c", "size": 2445, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.17.2.tgz", "integrity": "sha512-5mHFNyavtLoJmnusB8OKJ5bshSzw+qkMIBAobLrIM48HJvunFva9mOa6aBwh64lBFyNwBbs0xiEFuj4eU/NjCA=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express_4.17.2_1572629095855_0.47162890299013727"}, "_hasShrinkwrap": false, "publish_time": 1572629095969, "_cnpm_publish_time": 1572629095969, "_cnpmcore_publish_time": "2021-12-13T12:23:43.952Z"}, "4.17.1": {"name": "@types/express", "version": "4.17.1", "description": "TypeScript definitions for Express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "*", "@types/serve-static": "*"}, "typesPublisherContentHash": "3326fa16931c66beb8881132d339f23366535f6eeafe3db1f4351d12d84cc64c", "typeScriptVersion": "2.3", "_id": "@types/express@4.17.1", "dist": {"shasum": "4cf7849ae3b47125a567dfee18bfca4254b88c5c", "size": 2359, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.17.1.tgz", "integrity": "sha512-VfH/XCP0QbQk5B5puLqTLEeFgR8lfCJHZJKkInZ9mkYd+u8byX0kztXEQxEk4wZXJs8HI+7km2ALXjn4YKcX9w=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express_4.17.1_1566176223185_0.21494387851900054"}, "_hasShrinkwrap": false, "publish_time": 1566176223297, "_cnpm_publish_time": 1566176223297, "_cnpmcore_publish_time": "2021-12-13T12:23:44.423Z"}, "4.17.0": {"name": "@types/express", "version": "4.17.0", "description": "TypeScript definitions for Express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "*", "@types/serve-static": "*"}, "typesPublisherContentHash": "39d6f8d89e5d9f653b4e7b4d19c2042beb580d2c3e4908f242c4b9f48257ad7b", "typeScriptVersion": "2.2", "_id": "@types/express@4.17.0", "dist": {"shasum": "49eaedb209582a86f12ed9b725160f12d04ef287", "size": 2362, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.17.0.tgz", "integrity": "sha512-CjaMu57cjgjuZbh9DpkloeGxV45CnMGlVd+XpG7Gm9QgVrd7KFq+X4HY0vM+2v0bczS48Wg7bvnMY5TN+Xmcfw=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express_4.17.0_1559681168104_0.8583758900213656"}, "_hasShrinkwrap": false, "publish_time": 1559681168489, "_cnpm_publish_time": 1559681168489, "_cnpmcore_publish_time": "2021-12-13T12:23:44.852Z"}, "4.16.1": {"name": "@types/express", "version": "4.16.1", "description": "TypeScript definitions for Express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "*", "@types/serve-static": "*"}, "typesPublisherContentHash": "dd6d4d84c129accd75bc39e8829c791da5dea05f65a0063887b77b72e2f54042", "typeScriptVersion": "2.2", "_id": "@types/express@4.16.1", "dist": {"shasum": "d756bd1a85c34d87eaf44c888bad27ba8a4b7cf0", "size": 2322, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.16.1.tgz", "integrity": "sha512-V0c<PERSON><PERSON>ow23WeyblmACoxbHBu2JKlE5TiIme6Lem14FnPW9gsttyHtk6wq7njcdIWH1njAaFgR8gW09lgY98gQg=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express_4.16.1_1548179486948_0.8355080677906364"}, "_hasShrinkwrap": false, "publish_time": 1548179487213, "_cnpm_publish_time": 1548179487213, "_cnpmcore_publish_time": "2021-12-13T12:23:45.264Z"}, "4.16.0": {"name": "@types/express", "version": "4.16.0", "description": "TypeScript definitions for Express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "typesPublisherContentHash": "ad0e06d4038090c42b1301290bf770b34ddab9a112d674ea06b9d70c51262116", "typeScriptVersion": "2.2", "_id": "@types/express@4.16.0", "dist": {"shasum": "6d8bc42ccaa6f35cf29a2b7c3333cb47b5a32a19", "size": 2277, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.16.0.tgz", "integrity": "sha512-TtPEYumsmSTtTetAPXlJVf3kEqb6wZK0bZojpJQrnD/djV4q1oB6QQ8aKvKqwNPACoe02GNiy5zDzcYivR5Z2w=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express_4.16.0_1528157007702_0.03606732189482997"}, "_hasShrinkwrap": false, "publish_time": 1528157007771, "_cnpm_publish_time": 1528157007771, "_cnpmcore_publish_time": "2021-12-13T12:23:45.784Z"}, "4.11.1": {"name": "@types/express", "version": "4.11.1", "description": "TypeScript definitions for Express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "typesPublisherContentHash": "bea29462c13a5204859d5ac3bb917bae720aff3de620dd36b71440b71556efa5", "typeScriptVersion": "2.2", "_id": "@types/express@4.11.1", "dist": {"shasum": "f99663b3ab32d04cb11db612ef5dd7933f75465b", "size": 2287, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.11.1.tgz", "integrity": "sha512-ttWle8cnPA5rAelauSWeWJimtY2RsUf2aspYZs7xPHiWgOlPn6nnUfBMtrkcnjFJuIHJF4gNOdVvpLK2Zmvh6g=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-4.11.1.tgz_1517523996588_0.7968565141782165"}, "directories": {}, "publish_time": 1517523996771, "_hasShrinkwrap": false, "_cnpm_publish_time": 1517523996771, "_cnpmcore_publish_time": "2021-12-13T12:23:46.219Z"}, "4.11.0": {"name": "@types/express", "version": "4.11.0", "description": "TypeScript definitions for Express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "typesPublisherContentHash": "6e3a4d9ef67dc6ae8499529a28f059f784fd4fdb3f155902a4b2f552880a144e", "typeScriptVersion": "2.2", "_id": "@types/express@4.11.0", "dist": {"shasum": "234d65280af917cb290634b7a8d6bcac24aecbad", "size": 2282, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.11.0.tgz", "integrity": "sha512-N1Wdp3v4KmdO3W/CM7KXrDwM4xcVZjlHF2dAOs7sNrTUX8PY3G4n9NkaHlfjGFEfgFeHmRRjywoBd4VkujDs9w=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-4.11.0.tgz_1513781512181_0.3758411444723606"}, "directories": {}, "publish_time": 1513781512241, "_hasShrinkwrap": false, "_cnpm_publish_time": 1513781512241, "_cnpmcore_publish_time": "2021-12-13T12:23:46.705Z"}, "4.0.39": {"name": "@types/express", "version": "4.0.39", "description": "TypeScript definitions for Express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "typesPublisherContentHash": "51446cee04134591547aa8f623ea4a6dabeccd7870b7684f00dccb7d4f289bc6", "typeScriptVersion": "2.2", "_id": "@types/express@4.0.39", "dist": {"shasum": "1441f21d52b33be8d4fa8a865c15a6a91cd0fa09", "size": 2288, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.0.39.tgz", "integrity": "sha512-dBUam7jEjyuEofigUXCtublUHknRZvcRgITlGsTbFgPvnTwtQUt2NgLakbsf+PsGo/Nupqr3IXCYsOpBpofyrA=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-4.0.39.tgz_1509046329544_0.5551460727583617"}, "directories": {}, "publish_time": 1509046329597, "_hasShrinkwrap": false, "_cnpm_publish_time": 1509046329597, "_cnpmcore_publish_time": "2021-12-13T12:23:47.229Z"}, "4.0.38": {"name": "@types/express", "version": "4.0.38", "description": "TypeScript definitions for Express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "typesPublisherContentHash": "32dc234bc1b9e25982326a18a6f8d09e9fd40e678721ea07f2d82d89d8ec3f45", "typeScriptVersion": "2.3", "_id": "@types/express@4.0.38", "dist": {"shasum": "500e0524cb57ea8e3119efd5ca7ab8fca29ad9a5", "size": 2318, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.0.38.tgz", "integrity": "sha512-1HfDZFzzmcusHNTl/hRhrLIoVGZt3yqJVIZtMaQm/K14naptdjU9fEeXl1gCDzE2E9BqnoL6B5oidi7da3etbQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-4.0.38.tgz_1508890926288_0.7479865993373096"}, "directories": {}, "publish_time": 1508890926413, "_hasShrinkwrap": false, "_cnpm_publish_time": 1508890926413, "_cnpmcore_publish_time": "2021-12-13T12:23:47.716Z"}, "4.0.37": {"name": "@types/express", "version": "4.0.37", "description": "TypeScript definitions for Express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "typesPublisherContentHash": "ee3f3950a5c96d4bc07687b8843650db92fe1f16e5e011958979b9b96518e44f", "typeScriptVersion": "2.2", "_id": "@types/express@4.0.37", "dist": {"shasum": "625ac3765169676e01897ca47011c26375784971", "size": 2164, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.0.37.tgz", "integrity": "sha512-tIULTLzQpFFs5/PKnFIAFOsXQxss76glppbVKR3/jddPK26SBsD5HF5grn5G2jOGtpRWSBvYmDYoduVv+3wOXg=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-4.0.37.tgz_1503352300083_0.37321534869261086"}, "directories": {}, "publish_time": 1503352300144, "_hasShrinkwrap": false, "_cnpm_publish_time": 1503352300144, "_cnpmcore_publish_time": "2021-12-13T12:23:48.233Z"}, "4.0.36": {"name": "@types/express", "version": "4.0.36", "description": "TypeScript definitions for Express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON><PERSON>/"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "effdeb917f7cb7d5aa60f870d758d6a1b228c3014a095584f3fd722612116008", "typeScriptVersion": "2.2", "_id": "@types/express@4.0.36", "dist": {"shasum": "14eb47de7ecb10319f0a2fb1cf971aa8680758c2", "size": 2172, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.0.36.tgz", "integrity": "sha512-bT9q2eqH/E72AGBQKT50dh6AXzheTqigGZ1GwDiwmx7vfHff0bZOrvUWjvGpNWPNkRmX1vDF6wonG6rlpBHb1A=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express-4.0.36.tgz_1497557617706_0.2121476698666811"}, "directories": {}, "publish_time": 1497557617806, "_hasShrinkwrap": false, "_cnpm_publish_time": 1497557617806, "_cnpmcore_publish_time": "2021-12-13T12:23:48.802Z"}, "4.0.35": {"name": "@types/express", "version": "4.0.35", "description": "TypeScript definitions for Express", "license": "MIT", "author": "<PERSON> <https://github.com/boris<PERSON><PERSON>/>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "c3c508a3f9eda6225a5545f1006a32e40f1f377672b30c5e4548d9763dbef964", "typeScriptVersion": "2.0", "_id": "@types/express@4.0.35", "dist": {"shasum": "6267c7b60a51fac473467b3c4a02cd1e441805fe", "size": 1679, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.0.35.tgz", "integrity": "sha512-B1BCEmQLo4Z5VM6SZhmHkYA6z8z8M2v+Wq3mySX7rZVhb0dySOzqbyaCV6CZI1NZkb0PO7Zr0TkVQCfXqOZ9ww=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/express-4.0.35.tgz_1484719613769_0.4645449265372008"}, "directories": {}, "publish_time": 1484719613985, "_hasShrinkwrap": false, "_cnpm_publish_time": 1484719613985, "_cnpmcore_publish_time": "2021-12-13T12:23:49.382Z"}, "4.0.34": {"name": "@types/express", "version": "4.0.34", "description": "TypeScript definitions for Express 4.x", "license": "MIT", "author": "<PERSON> <https://github.com/boris<PERSON><PERSON>/>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express-serve-static-core": "*", "@types/serve-static": "*"}, "peerDependencies": {}, "typings": "index.d.ts", "typesPublisherContentHash": "cb2883774f9ff8bf15e334e96c3f98fd01820ea5da7fe8c99b6a1131b8816784", "_id": "@types/express@4.0.34", "dist": {"shasum": "cdc0afd69d70d2295b81b3aa47f26f672afcde1c", "size": 1672, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.0.34.tgz", "integrity": "sha512-EME+zSrClFeh1WxiHBnjsNE8J1jxkUhgr+K0Dmj6bMR+UGz6cxhjXjTMDWUaI+E0p3sHB/DPARXeKM0hR1gwCg=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/express-4.0.34.tgz_1479221773267_0.6036220847163349"}, "directories": {}, "publish_time": 1479221773496, "_hasShrinkwrap": false, "_cnpm_publish_time": 1479221773496, "_cnpmcore_publish_time": "2021-12-13T12:23:49.998Z"}, "4.0.33": {"name": "@types/express", "version": "4.0.33", "description": "TypeScript definitions for Express 4.x", "license": "MIT", "author": "<PERSON> <https://github.com/boris<PERSON><PERSON>/>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express-serve-static-core": "*", "@types/serve-static": "*"}, "typings": "index.d.ts", "typesPublisherContentHash": "f90dd99bbc97c7740fc818b879061f6a5c5c49275069d6380ccfa0a65ccf077a", "_id": "@types/express@4.0.33", "dist": {"shasum": "9212b6c67e02e09ee9f80740ded04306050739ab", "size": 1476, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.0.33.tgz", "integrity": "sha512-FvkfyDA7nImXjJ9AObcBeXK/eyWhSHC+r9xJtqKBK/G4UqfXkELSDmxOOo1YT4INRvEqL/RgqziLsnFGn1F4JA=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/express-4.0.33.tgz_1474306218987_0.4083134310785681"}, "directories": {}, "publish_time": 1474306222258, "_hasShrinkwrap": false, "_cnpm_publish_time": 1474306222258, "_cnpmcore_publish_time": "2021-12-13T12:23:50.603Z"}, "4.0.32": {"name": "@types/express", "version": "4.0.32", "description": "TypeScript definitions for Express 4.x", "license": "MIT", "author": "<PERSON> <https://github.com/boris<PERSON><PERSON>/>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express-serve-static-core": "4.0.*", "@types/serve-static": "1.7.*"}, "typings": "index.d.ts", "_id": "@types/express@4.0.32", "dist": {"shasum": "56c1772d9f14219f2afadd2ae9bbe005744aa020", "size": 1474, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.0.32.tgz", "integrity": "sha512-VGWBtVEWpnjJzZrteai1zssODBkv/arv5rKcKiUCzb7bz14n8Q1R18xOwPEjFCOZWiwE/UOQ0vX4gu5hD/SNlg=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/express-4.0.32.tgz_1472150494079_0.7518135118298233"}, "directories": {}, "publish_time": 1472150496080, "_hasShrinkwrap": false, "_cnpm_publish_time": 1472150496080, "_cnpmcore_publish_time": "2021-12-13T12:23:51.163Z"}, "4.0.31": {"name": "@types/express", "version": "4.0.31", "description": "TypeScript definitions for Express 4.x", "license": "MIT", "author": "<PERSON> <https://github.com/boris<PERSON><PERSON>/>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express-serve-static-core": "4.0.*", "@types/serve-static": "1.7.*"}, "typings": "index.d.ts", "_id": "@types/express@4.0.31", "dist": {"shasum": "f194ad070b32a7ab7115085203b6fcc97b51ae29", "size": 1474, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.0.31.tgz", "integrity": "sha512-yOwdVd3j6Wy2jf//NIbM8YHw4feYTSJphcIU3Ooybo+TpV7XX3we1FfLvJuHNJtoIkhJj3jpCYgB/orvHauhcA=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/express-4.0.31.tgz_1471620292492_0.2945292096119374"}, "directories": {}, "publish_time": 1471620293704, "_hasShrinkwrap": false, "_cnpm_publish_time": 1471620293704, "_cnpmcore_publish_time": "2021-12-13T12:23:51.803Z"}, "4.0.30": {"name": "@types/express", "version": "4.0.30", "description": "TypeScript definitions for Express 4.x", "license": "MIT", "author": "<PERSON> <https://github.com/boris<PERSON><PERSON>/>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express-serve-static-core": "4.0.*", "@types/serve-static": "1.7.*"}, "typings": "index.d.ts", "_id": "@types/express@4.0.30", "dist": {"shasum": "09014ceae4e738a55ffad6884d7bcb8bf14c6713", "size": 8192, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.0.30.tgz", "integrity": "sha512-YcmWccxwviuH99ETe9W8NFtqZvefdzuXzAzCTuop5auYvGkqc5auaDOL9QKT9Vn6WMZfVKStuAH33QR/vvh93w=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/express-4.0.30.tgz_1470153128322_0.21815698291175067"}, "directories": {}, "publish_time": 1470153129301, "_hasShrinkwrap": false, "_cnpm_publish_time": 1470153129301, "_cnpmcore_publish_time": "2021-12-13T12:23:52.451Z"}, "4.0.29": {"name": "@types/express", "version": "4.0.29", "description": "TypeScript definitions for Express 4.x", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/serve-static": "1.7.*", "@types/express-serve-static-core": "4.0.*"}, "_id": "@types/express@4.0.29", "_shasum": "1b72ccf48e871e015be7184fe39c3bcd1a3a56d6", "_from": "output\\express", "_resolved": "file:output\\express", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "1b72ccf48e871e015be7184fe39c3bcd1a3a56d6", "size": 1479, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.0.29.tgz", "integrity": "sha512-XfrKW4C4huxUqcckQ2gP5+Ef6D3viUTlKO+1bW6tULmFHi9TDj/Tu+H5Fnj9KRYQhPPQ3JMtzOFH3ICGHdtB6g=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/express-4.0.29.tgz_1468506756504_0.2337088098283857"}, "directories": {}, "publish_time": 1468506758620, "_hasShrinkwrap": false, "_cnpm_publish_time": 1468506758620, "_cnpmcore_publish_time": "2021-12-13T12:23:53.030Z"}, "4.0.28-alpha": {"name": "@types/express", "version": "4.0.28-alpha", "description": "TypeScript definitions for Express 4.x", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/serve-static": "1.7.25-alpha", "@types/express-serve-static-core": "4.0.26-alpha"}, "_id": "@types/express@4.0.28-alpha", "_shasum": "158e746f042f417e7cb2325592ba118f8be6369c", "_from": "output\\express", "_resolved": "file:output\\express", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "158e746f042f417e7cb2325592ba118f8be6369c", "size": 1495, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.0.28-alpha.tgz", "integrity": "sha512-+I824G068p+2+NrDE+l4PE/gOV8ES0Z7aOl44ygG78VP/DFUqINBhgqzN8suSudIhKBRTY2VybtK1ta6W30OYQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/express-4.0.28-alpha.tgz_1467840710458_0.7282508085481822"}, "directories": {}, "publish_time": 1467840711543, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467840711543, "_cnpmcore_publish_time": "2021-12-13T12:23:53.648Z"}, "4.0.27-alpha": {"name": "@types/express", "version": "4.0.27-alpha", "description": "TypeScript definitions for Express 4.x", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express-serve-static-core": "4.0.26-alpha"}, "_id": "@types/express@4.0.27-alpha", "_shasum": "675233fb8856e8480d93e88ba34e4f8f1ae4cd1f", "_from": "output\\express", "_resolved": "file:output\\express", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "675233fb8856e8480d93e88ba34e4f8f1ae4cd1f", "size": 1457, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.0.27-alpha.tgz", "integrity": "sha512-0ZyhrEDShkccuU8uxZ22AGM7QvkJbVM4Uv7/U6qLkfUdjRCU5upzY+dZhTsuMLlJUFEeV+QJyAiLTBwjCE5uPQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/express-4.0.27-alpha.tgz_1467588064810_0.41114497371017933"}, "directories": {}, "publish_time": 1467588068123, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467588068123, "_cnpmcore_publish_time": "2021-12-13T12:23:54.357Z"}, "4.0.26-alpha": {"name": "@types/express", "version": "4.0.26-alpha", "description": "TypeScript definitions for Express 4.x", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express-serve-static-core": "4.0.24-alpha"}, "_id": "@types/express@4.0.26-alpha", "_shasum": "81ef5392a0bfe5a5918af2b2a8dd59bf411ed911", "_from": "output\\express", "_resolved": "file:output\\express", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "81ef5392a0bfe5a5918af2b2a8dd59bf411ed911", "size": 1456, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.0.26-alpha.tgz", "integrity": "sha512-b8oenXypKJ1p907l1ShozM4wz889WR0RTHyLiO/0Ho2piqgttWbJK34uZjQg2t0BU2J4h7zaBpyxAoul5LqSsA=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/express-4.0.26-alpha.tgz_1467425956813_0.3038533120416105"}, "directories": {}, "publish_time": 1467425957535, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467425957535, "_cnpmcore_publish_time": "2021-12-13T12:23:55.085Z"}, "4.0.25-alpha": {"name": "@types/express", "version": "4.0.25-alpha", "description": "TypeScript definitions for Express 4.x", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express-serve-static-core": "4.0.23-alpha"}, "_id": "@types/express@4.0.25-alpha", "_shasum": "2d7c3f53721727692f84ce6bb8da5a7930ece460", "_from": "output\\express", "_resolved": "file:output\\express", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "2d7c3f53721727692f84ce6bb8da5a7930ece460", "size": 1456, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.0.25-alpha.tgz", "integrity": "sha512-eyZ7dF5I3FNgKhI1AuO9bXiXYTAAzDmAgqpB/xPqEEH7O5CpjCfMRAACRk1IEKr7vHjyzP6noVlTggVTvQ3fpg=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/express-4.0.25-alpha.tgz_1467412765326_0.001893854234367609"}, "directories": {}, "publish_time": 1467412765963, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467412765963, "_cnpmcore_publish_time": "2021-12-13T12:23:55.658Z"}, "4.0.24-alpha": {"name": "@types/express", "version": "4.0.24-alpha", "description": "TypeScript definitions for Express 4.x", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express-serve-static-core": "4.0.*"}, "_id": "@types/express@4.0.24-alpha", "_shasum": "eca7932141d1013cefe2ee714724a98de11a7164", "_from": "output\\express", "_resolved": "file:output\\express", "_npmVersion": "3.9.5", "_nodeVersion": "6.2.2", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "eca7932141d1013cefe2ee714724a98de11a7164", "size": 1452, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.0.24-alpha.tgz", "integrity": "sha512-qgps/sXVp28ljrhxppCKKfU6nxX2AiOC1c7iNq4weuUxeF83chz2mqICOcUxU4cAz1EMmGtj8/V6+JsoTx/zFQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/express-4.0.24-alpha.tgz_1467400924420_0.17103134817443788"}, "directories": {}, "publish_time": 1467400925018, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467400925018, "_cnpmcore_publish_time": "2021-12-13T12:23:56.331Z"}, "4.0.23-alpha": {"name": "@types/express", "version": "4.0.23-alpha", "description": "TypeScript definitions for Express 4.x", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express-serve-static-core": "*"}, "_id": "@types/express@4.0.23-alpha", "_shasum": "c1300873e38536ac37b1f0075a5bbbd4c5973f92", "_from": "output\\express", "_resolved": "file:output\\express", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "c1300873e38536ac37b1f0075a5bbbd4c5973f92", "size": 1430, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.0.23-alpha.tgz", "integrity": "sha512-PMuIez3UN0wbjd2Ttil43F7X1SJFbW6JIzE51mwTW7/Lbrh8dkPjkDlDrqAKWVJPqyn1PB7jPw6OugPSqnRf2Q=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/express-4.0.23-alpha.tgz_1464151875838_0.9312994808424264"}, "directories": {}, "publish_time": 1464151876283, "_hasShrinkwrap": false, "_cnpm_publish_time": 1464151876283, "_cnpmcore_publish_time": "2021-12-13T12:23:57.100Z"}, "4.0.22-alpha": {"name": "@types/express", "version": "4.0.22-alpha", "description": "TypeScript definitions for Express 4.x", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express-serve-static-core": "*"}, "_id": "@types/express@4.0.22-alpha", "_shasum": "119db67e8d9bbc079f533c41f4af91c6af9ebdbd", "_from": "output\\express", "_resolved": "file:output\\express", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "119db67e8d9bbc079f533c41f4af91c6af9ebdbd", "size": 1426, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.0.22-alpha.tgz", "integrity": "sha512-pT9H6i+Se+mn+v/UubGtr4BLQ8TKD/MIkbHiSdlVZMCY1xG39DQ+AwpA6gA/K6nGpBngW1T0kvngfcom+GRA6A=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/express-4.0.22-alpha.tgz_1463772782709_0.07826171116903424"}, "directories": {}, "publish_time": 1463772783103, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463772783103, "_cnpmcore_publish_time": "2021-12-13T12:23:57.852Z"}, "4.0.17-alpha": {"name": "@types/express", "version": "4.0.17-alpha", "description": "Type definitions for Express 4.x from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"../serve-static": "*", "express-serve-static-core": "*", "@types/express-serve-static-core": "*"}, "_id": "@types/express@4.0.17-alpha", "_shasum": "4ad1f6b1c3aa7093d91a45cca8ba08171bafd545", "_from": "output\\express", "_resolved": "file:output\\express", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "4ad1f6b1c3aa7093d91a45cca8ba08171bafd545", "size": 1443, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.0.17-alpha.tgz", "integrity": "sha512-SLzBFBg/n51W8DWJTxvhd8Z1TTHf5uuoIklJ9rfo1UnpVW8mL6DYtd920OgoH82u3cfX+L+XGD/godCkV4a65g=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/express-4.0.17-alpha.tgz_1463690924707_0.6485122456215322"}, "directories": {}, "publish_time": 1463690925330, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463690925330, "_cnpmcore_publish_time": "2021-12-13T12:23:58.677Z"}, "4.0.16-alpha": {"name": "@types/express", "version": "4.0.16-alpha", "description": "Type definitions for Express 4.x from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"../serve-static": "*", "express-serve-static-core": "*"}, "_id": "@types/express@4.0.16-alpha", "_shasum": "577a09ecc7f91c076820ea9e683ef56bc04c99cc", "_from": "output\\express", "_resolved": "file:output\\express", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "577a09ecc7f91c076820ea9e683ef56bc04c99cc", "size": 1430, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.0.16-alpha.tgz", "integrity": "sha512-dWA4rzmMAnWavHg/E5yQnpHKlhlrBiOjxqsuCUTgX8xnftAZCJCo44tnB30jv7cWkNv0+moXuqRLcRzSs0vzhg=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/express-4.0.16-alpha.tgz_1463460772751_0.03159188851714134"}, "directories": {}, "publish_time": 1463460773278, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463460773278, "_cnpmcore_publish_time": "2021-12-13T12:23:59.477Z"}, "4.17.14": {"name": "@types/express", "version": "4.17.14", "description": "TypeScript definitions for Express", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "China Medical University Hospital", "url": "https://github.com/CMUH", "githubUsername": "CMUH"}, {"name": "<PERSON>et A<PERSON>ra", "url": "https://github.com/puneetar", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dfrankland", "githubUsername": "dfrankland"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.18", "@types/qs": "*", "@types/serve-static": "*"}, "typesPublisherContentHash": "c1bc3eb1de87678353401acb958785c75095c71fec66004c015b6aba2aeee230", "typeScriptVersion": "4.1", "_id": "@types/express@4.17.14", "dist": {"integrity": "sha512-TEbt+vaPFQ+xpxFLFssxUDXj5cWCxZJjIcB7Yg0k0GMHGtgtQgpvx/MUQUeAkNbA9AAGrwkAsoeItdTgS7FMyg==", "shasum": "143ea0557249bc1b3b54f15db4c81c3d4eb3569c", "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.17.14.tgz", "fileCount": 5, "unpackedSize": 8243, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICaFoW85pq2wmoXN/i+Kc6Q5XNxZvSf8gda6E5xIU/gpAiEAtX1VIMasS67JsujJNT1S9KxlREr4ZP7K/fKBgRsNp3o="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIMtVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJ/RAApGLyPezxzMZqju+88kArMyJ8F4/t9KYfUwWwLL9eI6F89woL\r\n74C09CF/waQZztNJcUi1HUM3qWeMO5YeVgOsMWtXKYVgwPBgym6Cum88P9/W\r\nxHDsHugTSE5oOaQErgBusiqgctpRWuw6B+b8h2vPxKsm09l810Yr7UhJDy57\r\nDaKFjf8Ob4Zjqt7WbHPCbzIF/RRkCKavOM4ViRBF91q04OcjwZjUvLLa5DWj\r\nR7hh1l7is82OvgOibYpHUsuznwne3LsxfbPbAXx4PZstv+nNbWuLu5Dh2c/c\r\nz+4X5/SAKMbTWfBTWJWtECtdajqDpu3q5Ck/Pmrn6trEEiJK78iJHtUrP5dT\r\n3Iz7fMrEoXXN/x+nJOIBId2db+KWRsYG1JTzMTqppCSiJsEecR67unHBdSQy\r\nQnit5HSEceBlDGzt0VudgogLGJpafwfXo6lCzomyhgI8V29nmpAEKKfuKkHa\r\n2l7FefqIuOjfhJePyD0T/7JKRh/S/iS5oXb5d1A143Z+KYRKuRAJrg2QMFNo\r\nOlLdsa+2Zumd9jD8tKVNLTAiNnI4WeS8rh4NXdfxDVwoUinmE/yWb48ZHHZX\r\ny5V///2WEYeALXmHqoRnCX2OtuApUUBMNKSo8P10qpelD6e/VEGWIH1tlfEK\r\nNgMIPQI+fYKBLCvPvzENc0jKOPeAFLDDBVQ=\r\n=fx4c\r\n-----END PGP SIGNATURE-----\r\n", "size": 2760}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express_4.17.14_1663093589056_0.7295734765833677"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-09-13T18:27:59.984Z"}, "4.17.15": {"name": "@types/express", "version": "4.17.15", "description": "TypeScript definitions for Express", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "China Medical University Hospital", "url": "https://github.com/CMUH", "githubUsername": "CMUH"}, {"name": "<PERSON>et A<PERSON>ra", "url": "https://github.com/puneetar", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dfrankland", "githubUsername": "dfrankland"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.31", "@types/qs": "*", "@types/serve-static": "*"}, "typesPublisherContentHash": "a3b86a7c7eb6f9cd2c24e3ecba223a93a983155d3c2e3bdd1a9d006150af8455", "typeScriptVersion": "4.2", "_id": "@types/express@4.17.15", "dist": {"integrity": "sha512-Yv0k4bXGOH+8a+7bELd2PqHQsuiANB+A8a4gnQrkRWzrkKlb6KHaVvyXhqs04sVW/OWlbPyYxRgYlIXLfrufMQ==", "shasum": "9290e983ec8b054b65a5abccb610411953d417ff", "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.17.15.tgz", "fileCount": 5, "unpackedSize": 8243, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC4mAJUOYilmupQedDUV60cXX+yTl9JhQlPU6TLky6ojQIgZ/vbkToYL8kCqi15o/e4X9grt8mY5u/JZGEsxP/apSE="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmQvHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrCxQ//XVd9GwjblGVhUzf+Y+yornYQg3S8ImbU5PFO6pdOyYrjqOd2\r\nkspskNz9buDupJtNW7ZoQyPx8uoICy52B6qGuuxwgIzn81q7xQzYrLDZ6mnu\r\nsAU/x9qvk+lTYXNEYRGfaxdWASHEgVS5SkYgI+Idp0AntgexrcJcAs7TlJCF\r\n5ESol/9eDNDQS1yKEIh6Skf+647WVGe+IrmDZE6yZSS+rsUxtQ5LWAPj36GS\r\nHj54zTqH4/h6nE5Kjjcp2eDwYy/eTtzXOqTwYBLZU8dyb2AFYPv6cP5sKa9g\r\nzDtPpSUKwpDynrVeCfFkIp2i2UME4vPogrku9PVdTzPDv39jkRfB2iJMPkEx\r\nLB85Iiibcigy4IlTVUg7sBCXR2Fd3QEwGI9gi6lCb7ij48m86Fxg75ZxHUMp\r\nW06sb3cPKTTkF3aVH9kmjuvtriuRsdA6hNDOtWgk5dKPAQ3FbzTA1RGO7lDz\r\nrc/Y/agoFakBM81IFjnSF/lFViFRuNzwbH15EprB+FHiW/Af93hGJ7gokCNs\r\n5m/cXoYLUaZLmWoH+NJpzcc0v5KrcULi1CFoy+tsAjsJbobWGWdjk6O4ucGV\r\nlt9VKOiscAO5S0zEEIx8kqazEgDEVm8kL9VX4dw2Ny/mx9C1bUlLj2GKHYCT\r\nhZfBMNTChcFRkUriNUNND1Ly9MpxUqJxYUA=\r\n=/X4H\r\n-----END PGP SIGNATURE-----\r\n", "size": 2762}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express_4.17.15_1670974406883_0.12898919972866207"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-13T23:34:47.151Z"}, "4.17.16": {"name": "@types/express", "version": "4.17.16", "description": "TypeScript definitions for Express", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "China Medical University Hospital", "url": "https://github.com/CMUH", "githubUsername": "CMUH"}, {"name": "<PERSON>et A<PERSON>ra", "url": "https://github.com/puneetar", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dfrankland", "githubUsername": "dfrankland"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.31", "@types/qs": "*", "@types/serve-static": "*"}, "typesPublisherContentHash": "d11eb6e6fa9b8300c36502ac43c9937dc56b8cb2799a3cac32548afc0e404298", "typeScriptVersion": "4.2", "_id": "@types/express@4.17.16", "dist": {"integrity": "sha512-LkKpqRZ7zqXJuvoELakaFYuETHjZkSol8EV6cNnyishutDBCCdv6+dsKPbKkCcIk57qRphOLY5sEgClw1bO3gA==", "shasum": "986caf0b4b850611254505355daa24e1b8323de8", "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.17.16.tgz", "fileCount": 5, "unpackedSize": 8294, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDxobnAzj8+xgGylY3GMT2dFhvJOC+NWa58LZZTeNZ4DgIhAOleo6r08gIdoPAuz+iXnPC69g6GvA0xJC+dkhOG/DMu"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzvz6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpgdBAAiS9h4BL8dHmolJwBIIrAzN//RoSPWxAS2VQpHMz2SjvMK+j3\r\nBah+T8B34DtKqvY2/hg6CKI9wJXch5erOBYfBjAP3V/zMbqMe7IHCpgA42Ec\r\nS6ufdXCcYGtULRdfwiEyVTlVW/dnJOZ5FarjxxkwfvXxwDm6x2MMFec3X9kn\r\nrFSgBIjclARg7LNchFZMPiMJIOSAwuvvsSxjunAqGC+Rf3FEN8hBeMGFdg1M\r\nypTBTyNplc5T/RCe2bRJDjQLSZfXt0LsO3ZorOsHks56y0UQDq22WUmkX/NC\r\nNMbrFsGeW5CCZxLKO4aqfOckjVUpLleD7PHxl7sDIxh4rZ+98Pl/JhjIOCg8\r\nXP8lgi68KiVul0BznHLDcjTAEpRv2e32a+nPei+VVsa1u+QxRUSHjeh7nym9\r\nRcxwnisHtLHHTbGZd4KP3dZeoH5WuAOLavJ7PuGIBO2yFNTEV4CQOlxglpAh\r\nyhjWLdkmKEAlGpej79s8qQmooA6y3ulflu9JVRo9zdHwn5FVsddxpN9Cy1lX\r\nu/ZvYjIUuvJpchNztoGVTWUPSQ0QkG169rdKEzoF9yj0zV5oTKz4gH85ao9D\r\nNMJw16zioZRU/92dqxHW6yQxVNaV/XluuUl9u6+qaW2aZkKcLVu2fOSpDnpU\r\nmXh/4wVZiu1gyMM+7cMTHFMZa6EKVbYYQxQ=\r\n=ubhb\r\n-----END PGP SIGNATURE-----\r\n", "size": 2764}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express_4.17.16_1674509562772_0.23783647751108594"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-23T21:32:42.951Z", "publish_time": 1674509562951}, "4.17.17": {"name": "@types/express", "version": "4.17.17", "description": "TypeScript definitions for Express", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "China Medical University Hospital", "url": "https://github.com/CMUH", "githubUsername": "CMUH"}, {"name": "<PERSON>et A<PERSON>ra", "url": "https://github.com/puneetar", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dfrankland", "githubUsername": "dfrankland"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.33", "@types/qs": "*", "@types/serve-static": "*"}, "typesPublisherContentHash": "a4c3d98898199c22408b7c0662e85011cacce7899cd22a66275337ec40edac14", "typeScriptVersion": "4.2", "_id": "@types/express@4.17.17", "dist": {"integrity": "sha512-Q4FmmuLGBG58btUnfS1c1r/NQdlp3DMfGDGig8WhfpA2YRUtEkxAjkZb0yvplJGYdF1fsQ81iMDcH24sSCNC/Q==", "shasum": "01d5437f6ef9cfa8668e616e13c2f2ac9a491ae4", "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.17.17.tgz", "fileCount": 5, "unpackedSize": 8294, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA11fbhLm1dizt7XkFQzd2SW6phjE/oxpY5U3cQ32FZGAiA+Rh6Jja6XIHGQxUOgPs+k3gHbtbQm0w9W/cL5Ybp94Q=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3X2EACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqXUg//Qme0xUmjLAiUB8/Xea2wdsZeoeUC382/EUOUUmIKLxIw4LRD\r\nW2C8AlDsn8lLErNpvneWDsG35KzyamrGhBu74G/TqM/JtxYB3el9vTh3uuF8\r\nW62Wegso532dP5g0dTwjNbA31KFm8FQ6CwWsbBGe5BZV0CZ6QU3ILLuVtfaG\r\nDa2nMYxCOqwLOmk3P+/LoOk4/D9Ucv4fQpapNiNb020KL3zcnj7rwQy13WjV\r\nbmK2BzltQyldqMWWTkFOuCdFJ0deT8wqL4IL8qXhESZeEBGUu6vmLwrZXpqn\r\nMdI64hjjDLxi5HSwjuhDIwTHY/0qSWe1g1xrSx3hGc4uj4Fx7qG2/TadWK9f\r\n75NjfYF85e+mWR5fKcegM3vmZkdwjbmzgpIK1Olnf0KuyJPr29YwMrLVp6g7\r\nd4H9+9BUQP0NqOLgPoQiCckXtfUOaVaJ0OsXsM7CS4IlMr6g3qZM/5hZz1Xg\r\nc5M29zChSARn7+Q/v0+OCf8LD6/mzxehT9lsq3Pzx/hqDjQUctrJWM/0aXDp\r\n+9I2jjw+voygA11+gGlAj96qRIX1SfPFOdsa1+kHqbKzI0uw3lnzYFr+q7eH\r\nWCMmRqgWbJiTfE5oUZ9fwHhortfxQ7spIfXNxiS2RE066SGTj27Vbt/Vl0zQ\r\nv8TPFMCTwRjOjArow/H6tdziNO41yAD25Q4=\r\n=3Gtj\r\n-----END PGP SIGNATURE-----\r\n", "size": 2765}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express_4.17.17_1675459971964_0.4443981834023867"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-03T21:32:52.154Z", "publish_time": 1675459972154}, "4.17.18": {"name": "@types/express", "version": "4.17.18", "description": "TypeScript definitions for Express", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "China Medical University Hospital", "url": "https://github.com/CMUH", "githubUsername": "CMUH"}, {"name": "<PERSON>et A<PERSON>ra", "url": "https://github.com/puneetar", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dfrankland", "githubUsername": "dfrankland"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.33", "@types/qs": "*", "@types/serve-static": "*"}, "typesPublisherContentHash": "ded87c8062ca0c1458a29d8e8d3372e0ad8b5291a4e9c71a39298c22ba4113df", "typeScriptVersion": "4.5", "_id": "@types/express@4.17.18", "dist": {"integrity": "sha512-Sxv8BSLLgsBYmcnGdGjjEjqET2U+AKAdCRODmMiq02FgjwuV75Ut85DRpvFjyw/Mk0vgUOliGRU0UUmuuZHByQ==", "shasum": "efabf5c4495c1880df1bdffee604b143b29c4a95", "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.17.18.tgz", "fileCount": 5, "unpackedSize": 8298, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDLO+Y/2iaa/Fmb3znHS98X50oHcOenpRc908UFHd7e2gIgVEBmb3QEW4v45nFj2XvBl856wNXzcHhB1yFWyeUFKH0="}], "size": 2780}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express_4.17.18_1695489990430_0.30414175405847765"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-23T17:26:30.610Z", "publish_time": 1695489990610, "_source_registry_name": "default"}, "4.17.19": {"name": "@types/express", "version": "4.17.19", "description": "TypeScript definitions for Express", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "China Medical University Hospital", "url": "https://github.com/CMUH", "githubUsername": "CMUH"}, {"name": "<PERSON>et A<PERSON>ra", "url": "https://github.com/puneetar", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dfrankland", "githubUsername": "dfrankland"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.33", "@types/qs": "*", "@types/serve-static": "*"}, "typesPublisherContentHash": "c5300aeff579e82d7905c9a505efcfaacad1da8a23a07e89191a708cee5db18e", "typeScriptVersion": "4.5", "_id": "@types/express@4.17.19", "dist": {"integrity": "sha512-UtOfBtzN9OvpZPPbnnYunfjM7XCI4jyk1NvnFhTVz5krYAnW4o5DCoIekvms+8ApqhB4+9wSge1kBijdfTSmfg==", "shasum": "6ff9b4851fda132c5d3dcd2f89fdb6a7a0031ced", "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.17.19.tgz", "fileCount": 5, "unpackedSize": 8298, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDDGEGwmf31y3uuTNkN5YDgXyl3e7AoGm8f8tryX7WLrgIhAK1PNFDJXdU+Fsoh1mdr2F0v3PvmNFh7a3U7Mr5eMhWf"}], "size": 2782}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express_4.17.19_1696961599850_0.7662507941780505"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-10T18:13:20.034Z", "publish_time": 1696961600034, "_source_registry_name": "default"}, "4.17.20": {"name": "@types/express", "version": "4.17.20", "description": "TypeScript definitions for express", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov"}, {"name": "China Medical University Hospital", "githubUsername": "CMUH", "url": "https://github.com/CMUH"}, {"name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/puneetar"}, {"name": "<PERSON>", "githubUsername": "dfrankland", "url": "https://github.com/dfrankland"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.33", "@types/qs": "*", "@types/serve-static": "*"}, "typesPublisherContentHash": "564df074e8079566345f143877a68744d44358d987c7c16e87eefda6409e5750", "typeScriptVersion": "4.5", "_id": "@types/express@4.17.20", "dist": {"integrity": "sha512-rOaqlkgEvOW495xErXMsmyX3WKBInbhG5eqojXYi3cGUaLoRDlXa5d52fkfWZT963AZ3v2eZ4MbKE6WpDAGVsw==", "shasum": "e7c9b40276d29e38a4e3564d7a3d65911e2aa433", "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.17.20.tgz", "fileCount": 5, "unpackedSize": 7863, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGQ+bVHDfmLfU9j7YtAWPb6XfM9Qah3eM9Htv5vAhtNHAiAu170XodT8ctNKpNXGiYN7vVWCTIDoAIwuz86sXGsGXg=="}], "size": 2699}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express_4.17.20_1697594859582_0.9537531376905848"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-18T02:07:39.878Z", "publish_time": 1697594859878, "_source_registry_name": "default"}, "4.17.21": {"name": "@types/express", "version": "4.17.21", "description": "TypeScript definitions for express", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov"}, {"name": "China Medical University Hospital", "githubUsername": "CMUH", "url": "https://github.com/CMUH"}, {"name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/puneetar"}, {"name": "<PERSON>", "githubUsername": "dfrankland", "url": "https://github.com/dfrankland"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.33", "@types/qs": "*", "@types/serve-static": "*"}, "typesPublisherContentHash": "fa18ce9be07653182e2674f9a13cf8347ffb270031a7a8d22ba0e785bbc16ce4", "typeScriptVersion": "4.5", "_id": "@types/express@4.17.21", "dist": {"integrity": "sha512-ejlPM315qwLpaQlQDTjPdsUFSc6ZsP4AN6AlWnogPjQ7CVi7PYF3YVz+CY3jE2pwYf7E/7HlDAN0rV2GxTG0HQ==", "shasum": "c26d4a151e60efe0084b23dc3369ebc631ed192d", "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.17.21.tgz", "fileCount": 5, "unpackedSize": 7863, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD8zJWmmvEQdadTcvlxPtLQO2UjlOJen+Qke6udwwvFcgIgchEEcoRHauBppor2g91fRZ2FfIrI1cDS6XyVQdOjsdQ="}], "size": 2699}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/express_4.17.21_1699326610331_0.06397851099336571"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-07T03:10:10.569Z", "publish_time": 1699326610569, "_source_registry_name": "default"}, "5.0.0": {"name": "@types/express", "version": "5.0.0", "license": "MIT", "_id": "@types/express@5.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/CMUH", "name": "China Medical University Hospital", "githubUsername": "CMUH"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "dist": {"shasum": "13a7d1f75295e90d19ed6e74cab3678488eaa96c", "tarball": "https://registry.npmmirror.com/@types/express/-/express-5.0.0.tgz", "fileCount": 5, "integrity": "sha512-DvZriSMehGHL1ZNLzi6MidnsDhUZM/x2pRdDIKdwbUNqqwHxMlRdkxtn6/EPKyqKpHqTl/4nRZsRNLpZxZRpPQ==", "signatures": [{"sig": "MEQCICV0QvN+HhKerOZFwlGQJh88vaNlP9gJAx7HRu8SsrAQAiBDEUORZ+zhjnd7hwwWgBuKEQN6L7b48JR9q850UwaQ2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7859, "size": 2704}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "^5.0.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.8", "_npmOperationalInternal": {"tmp": "tmp/express_5.0.0_1727292029659_0.981321677373928", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "906f793a5f72703639a36afa9b7c5b41256100f5efc93138ed2551c101aea99f", "_cnpmcore_publish_time": "2024-09-25T19:20:29.866Z", "publish_time": 1727292029866, "_source_registry_name": "default"}, "5.0.1": {"name": "@types/express", "version": "5.0.1", "license": "MIT", "_id": "@types/express@5.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/CMUH", "name": "China Medical University Hospital", "githubUsername": "CMUH"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "dist": {"shasum": "138d741c6e5db8cc273bec5285cd6e9d0779fc9f", "tarball": "https://registry.npmmirror.com/@types/express/-/express-5.0.1.tgz", "fileCount": 5, "integrity": "sha512-UZUw8vjpWFXuDnjFTh7/5c2TWDlQqeXHi6hcN7F2XSVT5P+WmUnnbFS3KA6Jnc6IsEqI2qCVu2bK0R0J4A8ZQQ==", "signatures": [{"sig": "MEUCIAeCZwXaM9TRnAyle59x1VS7XsmWJ6a9OrfBQvNMagWvAiEAt/H3g/j7Od6DLsmk9Lfyiw3tfflV+jqzjAIsSswEq+o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7584, "size": 2651}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for express", "directories": {}, "dependencies": {"@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "^5.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.0", "_npmOperationalInternal": {"tmp": "tmp/express_5.0.1_1742412732850_0.6368596021591244", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "fc4e2b97399a28edf0ea8af60a18a56b71622eab908009e7e3a18677f98a80d8", "_cnpmcore_publish_time": "2025-03-19T19:32:13.062Z", "publish_time": 1742412733062, "_source_registry_name": "default"}, "5.0.2": {"name": "@types/express", "version": "5.0.2", "license": "MIT", "_id": "@types/express@5.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "dist": {"shasum": "7be9e337a5745d6b43ef5b0c352dad94a7f0c256", "tarball": "https://registry.npmmirror.com/@types/express/-/express-5.0.2.tgz", "fileCount": 5, "integrity": "sha512-BtjL3ZwbCQriyb0DGw+Rt12qAXPiBTPs815lsUvtt1Grk0vLRMZNMUZ741d5rjk+UQOxfDiBZ3dxpX00vSkK3g==", "signatures": [{"sig": "MEUCIQD9kdTGxzFVq82Z3pmIKJ30xRu9wDS7D6sfSqzgIlXVdwIgNPYrR8zy7oQPIvaf2gcZNDhZcaONp/TeHAsMzQlNWFo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7361, "size": 2610}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for express", "directories": {}, "dependencies": {"@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "^5.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.1", "_npmOperationalInternal": {"tmp": "tmp/express_5.0.2_1747438455675_0.47231925586696577", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "008dc0ec8e811b6dd78e7cf776bcc8ae50f3d97097463bc36e3b416fd22cc7e9", "_cnpmcore_publish_time": "2025-05-16T23:34:15.854Z", "publish_time": 1747438455854, "_source_registry_name": "default"}, "4.17.22": {"name": "@types/express", "version": "4.17.22", "license": "MIT", "_id": "@types/express@4.17.22", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "dist": {"shasum": "14cfcf120f7eb56ebb8ca77b7fa9a14d21de7c96", "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.17.22.tgz", "fileCount": 5, "integrity": "sha512-eZUmSnhRX9YRSkplpz0N+k6NljUUn5l3EWZIKZvYzhvMphEuNiyyy1viH/ejgt66JWgALwC/gtSUAeQKtSwW/w==", "signatures": [{"sig": "MEQCIERgs6b9CLCqTp0Z/VZXulPBsMiAsW7v62qISYesVQqOAiA4rOY/tO79g+0Gz8fjtFoUaqd3+tF2BwK2q6BslEsnkA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7671, "size": 2674}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "^4.17.33"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.1", "_npmOperationalInternal": {"tmp": "tmp/express_4.17.22_1747438463860_0.4383349896613731", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "0419587ffe8374d5cf260318c152eae67d48eb163a0fe4677e05b55886ea22d6", "_cnpmcore_publish_time": "2025-05-16T23:34:24.033Z", "publish_time": 1747438464033, "_source_registry_name": "default"}, "5.0.3": {"name": "@types/express", "version": "5.0.3", "license": "MIT", "_id": "@types/express@5.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}, {"url": "https://github.com/b<PERSON><PERSON><PERSON>bas", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "dist": {"shasum": "6c4bc6acddc2e2a587142e1d8be0bce20757e956", "tarball": "https://registry.npmmirror.com/@types/express/-/express-5.0.3.tgz", "fileCount": 5, "integrity": "sha512-wGA0NX93b19/dZC1J18tKWVIYWyyF2ZjT9vin/NRu0qzzvfVzWjs04iq2rQ3H65vCTQYlRqs3YHfY7zjdV+9Kw==", "signatures": [{"sig": "MEYCIQDhFIe3x4G+hr+QCBteVScHgtiMZKModd79Ewam4N1JFAIhAPbK6SlR9eMg3bcluUiEmdrx48FJOB3BrcYpfeiHqkng", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7573, "size": 2646}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for express", "directories": {}, "dependencies": {"@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "^5.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.1", "_npmOperationalInternal": {"tmp": "tmp/express_5.0.3_1749262754995_0.982238418877152", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "50a15d13b8596ea6054e9e404451e427c71c41ab505b7dc01a72b122e2ee16f6", "_cnpmcore_publish_time": "2025-06-07T02:19:15.168Z", "publish_time": 1749262755168, "_source_registry_name": "default"}, "4.17.23": {"name": "@types/express", "version": "4.17.23", "license": "MIT", "_id": "@types/express@4.17.23", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}, {"url": "https://github.com/b<PERSON><PERSON><PERSON>bas", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "dist": {"shasum": "35af3193c640bfd4d7fe77191cd0ed411a433bef", "tarball": "https://registry.npmmirror.com/@types/express/-/express-4.17.23.tgz", "fileCount": 5, "integrity": "sha512-Crp6WY9aTYP3qPi2wGDo9iUe/rceX01UMhnF1jmwDcKCFM6cx7YhGP/Mpr3y9AASpfHixIG0E6azCcL5OcDHsQ==", "signatures": [{"sig": "MEUCIETg5jztb7RY4LcxJpCT+GOzE9mqLlp54ykYIYXsdezZAiEAxmFf5TFMlio00xjHRvkiSB5l0kvk+hhtrKIWA/dFoX8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7883, "size": 2712}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "^4.17.33"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.1", "_npmOperationalInternal": {"tmp": "tmp/express_4.17.23_1749262773550_0.6722893092872222", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "c7c276342e7f5808be5916c5dec3e614fa8b6e60b3323e369f793c389ee6f860", "_cnpmcore_publish_time": "2025-06-07T02:19:33.740Z", "publish_time": 1749262773740, "_source_registry_name": "default"}}, "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}, {"url": "https://github.com/b<PERSON><PERSON><PERSON>bas", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "_source_registry_name": "default"}