{"name": "@types/basic-auth", "dist-tags": {"ts2.0": "1.1.2", "ts2.1": "1.1.2", "ts2.2": "1.1.2", "ts2.3": "1.1.2", "ts2.4": "1.1.2", "ts2.5": "1.1.2", "ts2.6": "1.1.2", "ts2.7": "1.1.2", "ts2.8": "1.1.3", "ts2.9": "1.1.3", "ts3.0": "1.1.3", "ts3.1": "1.1.3", "ts3.2": "1.1.3", "ts3.3": "1.1.3", "ts3.4": "1.1.3", "ts3.5": "1.1.3", "ts3.6": "1.1.3", "ts3.7": "1.1.3", "ts3.8": "1.1.3", "ts3.9": "1.1.3", "ts4.0": "1.1.3", "ts4.1": "1.1.3", "ts4.2": "1.1.3", "ts4.3": "1.1.3", "ts4.4": "1.1.3", "ts4.5": "1.1.6", "ts5.8": "1.1.8", "ts5.7": "1.1.8", "latest": "1.1.8", "ts4.6": "1.1.8", "ts4.7": "1.1.8", "ts4.8": "1.1.8", "ts4.9": "1.1.8", "ts5.0": "1.1.8", "ts5.1": "1.1.8", "ts5.2": "1.1.8", "ts5.3": "1.1.8", "ts5.4": "1.1.8", "ts5.5": "1.1.8", "ts5.6": "1.1.8", "ts5.9": "1.1.8"}, "versions": {"0.0.16-alpha": {"name": "@types/basic-auth", "version": "0.0.16-alpha", "dependencies": {"express": "*"}, "dist": {"shasum": "77c8b90b0a79b438c568dad23475808282e2908f", "tarball": "https://registry.npmjs.org/@types/basic-auth/-/basic-auth-0.0.16-alpha.tgz", "integrity": "sha512-1kBXTk0icLXdub2wuFwrV/OtadRk2is5+OxJXk2jRyv16OSUgzNZYlvpdjgefiqy79pI4FXZv/F2JB/32WXtDQ==", "signatures": [{"sig": "MEUCIQD3/Pe0ZOSiavGnqsRDXBnTrKzUY9/sDzVoBymrDQRNLgIgI7bcVGF/U2Hth/lRxlmN/xR1v9nwJbs6T/EM68kSivs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.17-alpha": {"name": "@types/basic-auth", "version": "0.0.17-alpha", "dependencies": {"express": "*"}, "dist": {"shasum": "6246aa339b51a44d0782727cbdd351d8bacb4b4e", "tarball": "https://registry.npmjs.org/@types/basic-auth/-/basic-auth-0.0.17-alpha.tgz", "integrity": "sha512-gIOHRckHCnQK+2e0b3DLFKliJPxXvDOgYHL4QKx34F8+JOWLBiC6GR0VHpgwZMSuGACdVkJg8ncaWU6nwBbOvg==", "signatures": [{"sig": "MEQCIE6TyfzQ6hNTMPQ/w3pocD8abqUZP8li0g2KCzv1sFJiAiBo4cXXMw67Y9VliuWMWEzkM54xaLgcMVTMpvbF9uA6JA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.22-alpha": {"name": "@types/basic-auth", "version": "0.0.22-alpha", "dependencies": {"@types/express": "*"}, "dist": {"shasum": "9dd9c6b80fa79e921b85c4d3ce91eb0257198e51", "tarball": "https://registry.npmjs.org/@types/basic-auth/-/basic-auth-0.0.22-alpha.tgz", "integrity": "sha512-KkPA1H2XffMY0GC9/me1rh910cbbceq9Kw3ll6jSzBkpQZj0tpqSb9XFjo4kFGZGRKIghK5CwpfdjpznI0GTjA==", "signatures": [{"sig": "MEUCIQCul+AEog3pmd+kmGvQV7mqP21waSGIOxl6gbfdW0oBBwIgI2jREoUKO4tV1C/G5t+ix8dzlFM0TsYElY1Z7ORaUfA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.23-alpha": {"name": "@types/basic-auth", "version": "0.0.23-alpha", "dependencies": {"@types/express": "*"}, "dist": {"shasum": "88406ca0073f75c2f5c20aa15219556d88f52dbc", "tarball": "https://registry.npmjs.org/@types/basic-auth/-/basic-auth-0.0.23-alpha.tgz", "integrity": "sha512-qKJbdsAidvawh+2JtR85+5s74IiiZ73keR32zAznptOncG2cwgytkevT31VwkwLiIGHw0E4Q0TjVr2CEOooeGA==", "signatures": [{"sig": "MEQCIEuUf0ZLNAjXyu3K2ars4l7bssxggOHWnk+YXPHtMQ0bAiAsPtCDFG3BOH/PwtWkccqvQCdZQ5C+z+ffwZXjKzHENg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.24-alpha": {"name": "@types/basic-auth", "version": "0.0.24-alpha", "dependencies": {"@types/express": "4.0.*"}, "dist": {"shasum": "b70530221568d76ef701090fa41206c61691b2a1", "tarball": "https://registry.npmjs.org/@types/basic-auth/-/basic-auth-0.0.24-alpha.tgz", "integrity": "sha512-fsCxWPjjcFXVtaUhIHr8aC1IGdt6DIFuOO3VlTyrTUM4z1Ew0vHMAXOwY3i3hGDXZEljmhjhPYmCcUuXadPp8g==", "signatures": [{"sig": "MEYCIQDmFDtBFpkAJ+oYkAtqG8TZ91WzI2V0bUI0GMjDTt1aogIhAJyaSubUlI+jejMlDZkvkRdMuXlN91EoKEP5prFgfU9n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.25-alpha": {"name": "@types/basic-auth", "version": "0.0.25-alpha", "dependencies": {"@types/express": "4.0.24-alpha"}, "dist": {"shasum": "1311d17e44a4f690349b810f99067e8908f7d3bb", "tarball": "https://registry.npmjs.org/@types/basic-auth/-/basic-auth-0.0.25-alpha.tgz", "integrity": "sha512-TfJ5nQd/qisZSISdYDSoL133lfRfyB5lAf+STNkxDF7J4lWVHb2+hVZtHEPP2QE4tK8i1E+aYNL5Gr4vtAlyZA==", "signatures": [{"sig": "MEYCIQCqbbuhPO7uoevOxIlZGjJNKNW5+zRNHx3JCTXPjtvUywIhAKrcSKDLT/5w9cqCxaJucAyqv95oIyXYCJNO2XKiHijy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.26-alpha": {"name": "@types/basic-auth", "version": "0.0.26-alpha", "dependencies": {"@types/express": "4.0.25-alpha"}, "dist": {"shasum": "49f87aba7a6a8e6bc0738d00d0a125f71fa6777b", "tarball": "https://registry.npmjs.org/@types/basic-auth/-/basic-auth-0.0.26-alpha.tgz", "integrity": "sha512-Eh2sn8paQZC34PncnCD5adsdosqfsyT27JvOt4ImfZUxQCI110szZe2iw4f5ym0GuO+zyDyGRUhCR9UKH10Jng==", "signatures": [{"sig": "MEUCIQCECjk2095RiGsmvG8wa/M43Q1TmERrW0AlpPOKVDK0owIgSrf0xqF/TkvUaLZcw+rsOZKKrGgV7LVDUbJY4sy0GVI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.27-alpha": {"name": "@types/basic-auth", "version": "0.0.27-alpha", "dependencies": {"@types/express": "4.0.27-alpha"}, "dist": {"shasum": "6ee8b9e4d4989b10434615bc433024980a91cf7f", "tarball": "https://registry.npmjs.org/@types/basic-auth/-/basic-auth-0.0.27-alpha.tgz", "integrity": "sha512-uCvJtwga0wptLbyKOcXlsV8C14gSUbb+jajA6Tm4kWM2VC4grEnizKEkNBaBQ1paphYY+DxQcUAuca3IydUpIg==", "signatures": [{"sig": "MEUCIAvbUSVYH0W5Xw9SSDqLHTmto3ZvFksInHKhNO+X4RgtAiEA07Rxyx9w3UX3kgYgivibw3KrkoGtAMA2Ism0DG7+12w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.28-alpha": {"name": "@types/basic-auth", "version": "0.0.28-alpha", "dependencies": {"@types/express": "4.0.28-alpha"}, "dist": {"shasum": "7ef83f853b8fa2342d9f298f43d2a6637ad52fdc", "tarball": "https://registry.npmjs.org/@types/basic-auth/-/basic-auth-0.0.28-alpha.tgz", "integrity": "sha512-iX9N+oZUpgCGDA+OAlnKrTq3kLidEEvu8HoATJIbogAF1/zI2e4ahMBUIDaMm+NkA4pCheLwIc8dCCTlNbPgpg==", "signatures": [{"sig": "MEUCIGeokIhIYWy2f4qfSnL6on91dHjaE+ITYZIsrLtUnI63AiEAmS2DoWZjQmrXZrpe/zimEQAq7yUC9rpyKbSzqpHKuLw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.29": {"name": "@types/basic-auth", "version": "0.0.29", "dependencies": {"@types/express": "4.0.*"}, "dist": {"shasum": "b2adaad9422e26f0bf5e7aa3633c98d4eb5aa242", "tarball": "https://registry.npmjs.org/@types/basic-auth/-/basic-auth-0.0.29.tgz", "integrity": "sha512-b4/tJzIl8CJgMFH8hVEaOO3KlwmYH2MstOnzQdkG3W+lYBjRxQBinGOoYO26yxwYkPFK0EZ+s1F/8yWDPWDYsg==", "signatures": [{"sig": "MEQCIGKLYAUjG2hQDJgJMZZTSqyp1KWYDFygBHA8qKyhyN64AiBsdLPQxhIZ+gR0LQ6Tba44uUw7xGy6V69C/CfVvyESXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.30": {"name": "@types/basic-auth", "version": "0.0.30", "dependencies": {"@types/express": "*"}, "dist": {"shasum": "b6abbc942b710dfef06f23f1b4769ccc8ea4e267", "tarball": "https://registry.npmjs.org/@types/basic-auth/-/basic-auth-0.0.30.tgz", "integrity": "sha512-0RenybVjOpUjtV4sXBhY24D8kcrmCutFSmAfZM5vkCjFhV8ICigy33jZ75uzbTtC/oktdIjz2VGm044VIUgEkQ==", "signatures": [{"sig": "MEQCIFcGXJCiNlsz3QWJzIkMOTbz3jE5qraVGzS6ycfolksBAiAo6Q+RKITG21QkZCGRgtzw6Az6UzILwklgthnB/EMtGA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.31": {"name": "@types/basic-auth", "version": "0.0.31", "dependencies": {"@types/node": "*"}, "dist": {"shasum": "99834e2d0302623eb53420853c570b1c059ca891", "tarball": "https://registry.npmjs.org/@types/basic-auth/-/basic-auth-0.0.31.tgz", "integrity": "sha512-8LNQfdWLvJa2Kpf4u4a/HQ1dTfZ+3JQ808Infk3QGCUSzwec1DtY5N5RtaeJHCDxZGgJUJqRF8x33falgfyU4w==", "signatures": [{"sig": "MEYCIQDvaWEo43BED3f61DoZko90lguikUfohS3hyvC+1ZfccwIhAOL8d15Ft9K3W6BCn+kcZTOEw+EMgqE+5eifgJXk1+ce", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.0": {"name": "@types/basic-auth", "version": "1.1.0", "dependencies": {"@types/node": "*"}, "dist": {"shasum": "a6d0abd761fd5e92f334bea0b02e48842a6934ca", "tarball": "https://registry.npmjs.org/@types/basic-auth/-/basic-auth-1.1.0.tgz", "integrity": "sha512-c80y6kbW09J3UK/pp4CjLtrtK0Rqb0IxOpqS7opsbrsmcd6Mf4e92ZPkj5FXzlUMY2C+nJumu/GCf08+VkelMA==", "signatures": [{"sig": "MEUCIQC8am/NRTcgEohl5ju1lQMjfHtP5PuQgxlTsQEGN/TPkwIgLZd874T4eYSWoZmHgcl81spiiDUEiG6BTqGcIAK/7x4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.1": {"name": "@types/basic-auth", "version": "1.1.1", "dependencies": {"@types/node": "*"}, "dist": {"shasum": "246d8934ab85d73eb75cd492c62b21d9a6310713", "tarball": "https://registry.npmjs.org/@types/basic-auth/-/basic-auth-1.1.1.tgz", "integrity": "sha512-/13tYshwNSfLZuwvJVyEnz52ornC2A7IaT+IEZgkDEr0IMgDJHzun6QjYTKwHqLVE+rrN/Nk32d3pBeLx+1x+w==", "signatures": [{"sig": "MEYCIQD9KYF2fSt9uJf+KEOaQbhuEZDhBIFtievfHCvUfR32dgIhAJkXxhjTcjQCiDhRyCOcgnnm10p2CmIp3u02OGfeI+KK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.2": {"name": "@types/basic-auth", "version": "1.1.2", "dependencies": {"@types/node": "*"}, "dist": {"shasum": "810fbded879f14327fc1d3413bdb92cfe9e24f73", "tarball": "https://registry.npmjs.org/@types/basic-auth/-/basic-auth-1.1.2.tgz", "integrity": "sha512-NzkkcC+gkkILWaBi3+/z/3do6Ybk6TWeTqV5zCVXmG2KaBoT5YqlJvfqP44HCyDA+Cu58pp7uKAxy/G58se/TA==", "signatures": [{"sig": "MEQCIE5AsOiBpoevK+p6rQ8NlvZE8kHbVO7bKC7J+MzULGo6AiBwcvF0CT52cJogPrh3hjjrbv1gMaJMjLQKJXVixCYC2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.3": {"name": "@types/basic-auth", "version": "1.1.3", "dependencies": {"@types/node": "*"}, "dist": {"shasum": "a787ede8310804174fbbf3d6c623ab1ccedb02cd", "tarball": "https://registry.npmjs.org/@types/basic-auth/-/basic-auth-1.1.3.tgz", "fileCount": 4, "integrity": "sha512-W3rv6J0IGlxqgE2eQ2pTb0gBjaGtejQpJ6uaCjz3UQ65+TFTPC5/lAE+POfx1YLdjtxvejJzsIAfd3MxWiVmfg==", "signatures": [{"sig": "MEUCIB2CpVWJDLIsHLavAMSk5lKntIEF3DtZ114EXQDIOrK1AiEAvBHdFX/YWxHww1zC2TZQxNhu2ZqCnptCY7SUbVzTzr8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeOFdZCRA9TVsSAnZWagAAU28QAJg1oryaQRnR0R40yfRX\n4cDwelPch1dIgDKl1orOaEoacwGWT6fEpVoVYOBAmP7QiblC83wC6HoJaBnL\nXKwb0FnlZKt0rIaBcXtPMc3CUZsTMYINP4BU2ADRif56n9hg5QU+QZPfMbJq\npR1a4nlkX/lemMnqtCAg65USmo0zayxuuucEeUefEclibe2v0y3Sf67eEcw1\nOlRnVIWTmUgIYaILSeKofMoiX+tlMNBfERUCbQGECUg4TwEpratK9b6jAPtg\ncb25o01qFmlaJ+6bChLk7Hjd4oo5Q39KYQ8mRKkXV9MSPpeUtfsGQwNXFEXl\n1iYnuFlBV/2sxx3UCxT07dORhdKPRI01lS2Ima0UOGDfqCTypSExlW0Eky79\nTNgh10//YH6nlPJe3Ke6B+/NNmPzSnUtNiuLN4MWTAupYRX2HntLCL/57cyQ\ncxqOj9kssVlwYWNbF7cp7ABlzrG0NtvLYjVEsv/sUN4YXq5hCgBVpwdvvRFD\neY3VbrkIvkVBSfTj1q71qkWyely4yjI/LddklevhcXNPoBv81S0egFBG+VmY\n7cZwOg7tXQVL/ehjJo9/la04d5e9hXhLePCbmdQfcXywO7vwCNowEfyQEH8T\nDT5w0Zc0+dxUMmek1kKKvzdq6cAl3AWcpdYsqc0b+qgxFL4WImLIDhpaWGmx\nYlWN\r\n=Apsk\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.4": {"name": "@types/basic-auth", "version": "1.1.4", "dependencies": {"@types/node": "*"}, "dist": {"shasum": "bd16d1382b3690c387d0117ad5c61f26e42c0ab8", "tarball": "https://registry.npmjs.org/@types/basic-auth/-/basic-auth-1.1.4.tgz", "fileCount": 5, "integrity": "sha512-UFJ4rxjOIQ1Y9R3Vb2RvnspPMdlsZ6Lcv7BQO1TmgtRdVrfGfuwN57lSpRuOJ9/Bwf61nKY+pnK0RPG3LtVnlw==", "signatures": [{"sig": "MEYCIQDFqlgq1nYK+lFU7129mAnH7bV7XJG3TEadTVv5qbvrWQIhAL+qSLyPz1CiVcTyd6NSeloPbc2F064yAcZlf7CKH6GC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4698}}, "1.1.5": {"name": "@types/basic-auth", "version": "1.1.5", "dependencies": {"@types/node": "*"}, "dist": {"shasum": "2da02c35276f587536b63e1614bcdd8b1265f3e8", "tarball": "https://registry.npmjs.org/@types/basic-auth/-/basic-auth-1.1.5.tgz", "fileCount": 5, "integrity": "sha512-eYnGxbb3KZuTU4ZnrF8cMclnPpnrywEjGVjLbmO9TDBsCQ3wTQyGX0lcYEWpHOHw195kWpJ/YSMvvHUajrSIZw==", "signatures": [{"sig": "MEUCIF85OxGs41sGXvJ2nqNdmwgdQ1eAd7Coyeiy03iKqR+SAiEAwMs78pwuXA3SFqbtlfT70Z7aIfqGEu2CeTiCFl4NqQ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4064}}, "1.1.6": {"name": "@types/basic-auth", "version": "1.1.6", "dependencies": {"@types/node": "*"}, "dist": {"shasum": "ab3ae51a4f0576a570bf0b38b4d6ec7692ab12a3", "tarball": "https://registry.npmjs.org/@types/basic-auth/-/basic-auth-1.1.6.tgz", "fileCount": 5, "integrity": "sha512-RGqL8Eh/pHAt9REIlIbw8w1YbYs3s7syfPScYgq3qDiY7oaMUxGVA/YobE6hq4vL1kLRdE4g6CIvEnYdY64dPg==", "signatures": [{"sig": "MEYCIQDYcLGX0R51Jpq6sAibSkJz1VM3oe3h2pf7d32WPlNr5wIhAPgthWXnOy0XSvUpyRB8KO/C8xDSUI2mXp711WBc058c", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4064}}, "1.1.7": {"name": "@types/basic-auth", "version": "1.1.7", "dependencies": {"@types/node": "*"}, "dist": {"shasum": "5eeb98dca9163858a7dc84bc3d881d0efd3dfb55", "tarball": "https://registry.npmjs.org/@types/basic-auth/-/basic-auth-1.1.7.tgz", "fileCount": 5, "integrity": "sha512-bFR3Ld3Fty5ayg45sqr3RI4e/GTXyp2W8jzMmw3WOC8RuQ19TrpsZE4y3jcw9iGSZj5f9mH6e+2biPeFUDovww==", "signatures": [{"sig": "MEYCIQCbGb9jsDD0CP9ybMIZUBazXQIpDhRxOJjbxDiDWXAIIwIhAJWlEf85ewUJHMQeTLqBtm3HQCZie5msZDAhXN6hMwur", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4014}}, "1.1.8": {"name": "@types/basic-auth", "version": "1.1.8", "dependencies": {"@types/node": "*"}, "dist": {"shasum": "ea235203c89e233faae66b99e03665747576e9e9", "tarball": "https://registry.npmjs.org/@types/basic-auth/-/basic-auth-1.1.8.tgz", "fileCount": 5, "integrity": "sha512-dKcUeixGuZn8pBjcUrf1N7x5K6lWuKuwHHitM2IZ4vwZUDWEhhNtwCWiba8jTA9zn0GQQ+fTFkWpKx8pOU/enw==", "signatures": [{"sig": "MEUCIHc5vte+r6RiPnvlAllzHUwhotOpsPTltxHUsINUeEIVAiEAowV+DfgT8h7KE8jHvFrbhLvoX7ZA10iYP6PGypomEdk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4038}}}, "modified": "2025-02-23T06:26:27.578Z"}