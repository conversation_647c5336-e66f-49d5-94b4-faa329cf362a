{"_attachments": {}, "_id": "@types/serve-favicon", "_rev": "271902-61f1b55561011c8ed863e61a", "description": "TypeScript definitions for serve-favicon", "dist-tags": {"latest": "2.5.7", "ts2.0": "2.2.29", "ts2.1": "2.2.29", "ts2.2": "2.2.30", "ts2.3": "2.2.31", "ts2.4": "2.2.31", "ts2.5": "2.2.31", "ts2.6": "2.2.31", "ts2.7": "2.2.31", "ts2.8": "2.5.0", "ts2.9": "2.5.0", "ts3.0": "2.5.0", "ts3.1": "2.5.0", "ts3.2": "2.5.1", "ts3.3": "2.5.2", "ts3.4": "2.5.2", "ts3.5": "2.5.2", "ts3.6": "2.5.3", "ts3.7": "2.5.3", "ts3.8": "2.5.3", "ts3.9": "2.5.3", "ts4.0": "2.5.3", "ts4.1": "2.5.3", "ts4.2": "2.5.3", "ts4.3": "2.5.4", "ts4.4": "2.5.4", "ts4.5": "2.5.7", "ts4.6": "2.5.7", "ts4.7": "2.5.7", "ts4.8": "2.5.7", "ts4.9": "2.5.7", "ts5.0": "2.5.7", "ts5.1": "2.5.7", "ts5.2": "2.5.7", "ts5.3": "2.5.7", "ts5.4": "2.5.7", "ts5.5": "2.5.7", "ts5.6": "2.5.7", "ts5.7": "2.5.7", "ts5.8": "2.5.7", "ts5.9": "2.5.7"}, "license": "MIT", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "name": "@types/serve-favicon", "readme": "# Installation\r\n> `npm install --save @types/serve-favicon`\r\n\r\n# Summary\r\nThis package contains type definitions for serve-favicon (https://github.com/expressjs/serve-favicon).\r\n\r\n# Details\r\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/serve-favicon.\r\n## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/serve-favicon/index.d.ts)\r\n````ts\r\n/* =================== USAGE ===================\n\n    import serveFavicon = require('serve-favicon');\n    app.use(serveFavicon(__dirname + '/public/favicon.ico'));\n\n =============================================== */\n\nimport express = require(\"express\");\n\n/**\n * Node.js middleware for serving a favicon.\n */\ndeclare function serveFavicon(\n    path: string | Buffer,\n    options?: serveFavicon.Options,\n): express.RequestHandler;\n\ndeclare namespace serveFavicon {\n    interface Options {\n        /**\n         * The cache-control max-age directive in ms, defaulting to 1 year.\n         * This can also be a string accepted by the `ms` module.\n         */\n        maxAge?: number | string | undefined;\n    }\n}\n\nexport = serveFavicon;\n\r\n````\r\n\r\n### Additional Details\r\n * Last updated: Tue, 07 Nov 2023 15:11:36 GMT\r\n * Dependencies: [@types/express](https://npmjs.com/package/@types/express)\r\n\r\n# Credits\r\nThese definitions were written by [Uros Smolnik](https://github.com/urossmolnik), and [Piotr Błażejewicz](https://github.com/peterblazejewicz).\r\n", "time": {"created": "2022-01-26T20:55:49.977Z", "modified": "2025-02-23T07:49:39.378Z", "2.5.3": "2021-07-06T16:59:03.445Z", "2.5.2": "2020-12-02T02:24:00.702Z", "2.5.1": "2020-10-20T19:16:00.302Z", "2.5.0": "2019-11-22T17:24:04.734Z", "2.2.31": "2019-08-19T01:14:44.366Z", "2.2.30": "2017-11-09T15:18:04.127Z", "2.2.29": "2017-08-21T22:04:11.467Z", "2.2.28": "2016-09-19T18:11:19.366Z", "2.2.27": "2016-07-14T16:01:57.027Z", "2.2.26-alpha": "2016-07-08T21:30:28.523Z", "2.2.25-alpha": "2016-07-04T01:16:56.899Z", "2.2.24-alpha": "2016-07-02T03:26:14.391Z", "2.2.23-alpha": "2016-07-02T00:00:29.603Z", "2.2.22-alpha": "2016-07-01T20:32:47.839Z", "2.2.21-alpha": "2016-05-25T05:55:30.770Z", "2.2.20-alpha": "2016-05-20T20:40:00.850Z", "2.2.15-alpha": "2016-05-19T22:21:37.602Z", "2.2.14-alpha": "2016-05-17T18:54:45.790Z", "2.5.4": "2023-04-19T06:33:10.219Z", "2.5.5": "2023-09-25T14:23:16.666Z", "2.5.6": "2023-10-18T14:42:06.748Z", "2.5.7": "2023-11-07T16:20:35.951Z"}, "versions": {"2.5.3": {"name": "@types/serve-favicon", "version": "2.5.3", "description": "TypeScript definitions for serve-favicon", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/serve-favicon", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/urossmolnik", "githubUsername": "urossmolnik"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/serve-favicon"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "8e9fd4c2ea85528ff25c1e30a43e4e1ddbe8094999921e152a2df32bf2cefce3", "typeScriptVersion": "3.6", "_id": "@types/serve-favicon@2.5.3", "dist": {"shasum": "6380c875059711090631abea8f3edc5906cccd32", "size": 1938, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/serve-favicon/-/serve-favicon-2.5.3.tgz", "integrity": "sha512-HirXLRJjLXzwiSnjhE1vMu55X7+qaY+noXsKqi/7eK1uByl3L6TwkcALZuJnQXqOalMdmBz3b662yXvaR+89Vw=="}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/serve-favicon_2.5.3_1625590743291_0.114377645257153"}, "_hasShrinkwrap": false, "publish_time": 1625590743445, "_cnpm_publish_time": 1625590743445, "_cnpmcore_publish_time": "2021-12-17T03:41:11.301Z"}, "2.5.2": {"name": "@types/serve-favicon", "version": "2.5.2", "description": "TypeScript definitions for serve-favicon", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/urossmolnik", "githubUsername": "urossmolnik"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/serve-favicon"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "b9e657540fb27e4b19e23db27c0007eb25a76731f8043f21fa78bf0eaa8904c4", "typeScriptVersion": "3.3", "_id": "@types/serve-favicon@2.5.2", "dist": {"shasum": "c0f73f9e4f61fe61ad209ec7b8daacf3159f0fa8", "size": 1871, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/serve-favicon/-/serve-favicon-2.5.2.tgz", "integrity": "sha512-tGpJ3ZdAxnmuHX/BCRnZcPNQe2J8PbmwWFB2Fi6BvepDYuakFKraMG7orLkv387HS2C4MJm2UCINaxv4p7kP9A=="}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/serve-favicon_2.5.2_1606875840547_0.47575328568072406"}, "_hasShrinkwrap": false, "publish_time": 1606875840702, "_cnpm_publish_time": 1606875840702, "_cnpmcore_publish_time": "2021-12-17T03:41:11.522Z"}, "2.5.1": {"name": "@types/serve-favicon", "version": "2.5.1", "description": "TypeScript definitions for serve-favicon", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/urossmolnik", "githubUsername": "urossmolnik"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/serve-favicon"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "bb651b47ef2bf37a01dca062a54f8fd0920966f38d93d9937d4cb22895619bf4", "typeScriptVersion": "3.2", "_id": "@types/serve-favicon@2.5.1", "dist": {"shasum": "d8e4279e7b70bede84ed9fa82667fd537cf9b2c9", "size": 1846, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/serve-favicon/-/serve-favicon-2.5.1.tgz", "integrity": "sha512-yCAHOVf2fvDBear9cqkjQz+/OJky5pj4QJNFj+C+kHZtum+4HI9a8B4mQmZhHE+dmhTQbC0+fYeH0j+Tgs1baA=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/serve-favicon_2.5.1_1603221360182_0.7596671514006075"}, "_hasShrinkwrap": false, "publish_time": 1603221360302, "_cnpm_publish_time": 1603221360302, "_cnpmcore_publish_time": "2021-12-17T03:41:11.728Z"}, "2.5.0": {"name": "@types/serve-favicon", "version": "2.5.0", "description": "TypeScript definitions for serve-favicon", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/urossmolnik", "githubUsername": "urossmolnik"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/serve-favicon"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "bc823b46f0c96ce1cf4ea02b49453d6425f347b659b2a81c3240c0c20f19a2b5", "typeScriptVersion": "2.8", "_id": "@types/serve-favicon@2.5.0", "dist": {"shasum": "21164e61290d577d75e22de1b3119fad70bf52b6", "size": 1794, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/serve-favicon/-/serve-favicon-2.5.0.tgz", "integrity": "sha512-APK6i1tJp8XBYCZyU4HqtNZBiwipIBQvpQVLYZezTm4TaKKl0KrsGokQK9k3Ll2CaEGNuehppKhXp/Ki9oWT/w=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/serve-favicon_2.5.0_1574443444578_0.8123695679474208"}, "_hasShrinkwrap": false, "publish_time": 1574443444734, "_cnpm_publish_time": 1574443444734, "_cnpmcore_publish_time": "2021-12-17T03:41:12.104Z"}, "2.2.31": {"name": "@types/serve-favicon", "version": "2.2.31", "description": "TypeScript definitions for serve-favicon", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/urossmolnik", "githubUsername": "urossmolnik"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/serve-favicon"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "640a16fe65ab9a3196c0632a49c67a86eca9518c45ed4bd574731b9d2b5fb8bf", "typeScriptVersion": "2.3", "_id": "@types/serve-favicon@2.2.31", "dist": {"shasum": "1e62e3f57036f5ff15017765b012b3d8b4d94aa8", "size": 1764, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/serve-favicon/-/serve-favicon-2.2.31.tgz", "integrity": "sha512-BfGO//1n649yo2ooH0wDemWLwElgGT1tq6y1l4orwtYXjCNhHJf3LWAe6VBpxq8RGaqiM7AXYAHWIOP52y0gtA=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/serve-favicon_2.2.31_1566177284214_0.2107460721457335"}, "_hasShrinkwrap": false, "publish_time": 1566177284366, "_cnpm_publish_time": 1566177284366, "_cnpmcore_publish_time": "2021-12-17T03:41:12.308Z"}, "2.2.30": {"name": "@types/serve-favicon", "version": "2.2.30", "description": "TypeScript definitions for serve-favicon", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/urossmolnik", "githubUsername": "urossmolnik"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "ffc798e54944dce302ab5b61e13fc0f2ec9e615b442ecffed44b653ee9c826ec", "typeScriptVersion": "2.2", "_id": "@types/serve-favicon@2.2.30", "dist": {"shasum": "5bea4ead966a9ad5e0f409141edba0cebf20da73", "size": 1756, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/serve-favicon/-/serve-favicon-2.2.30.tgz", "integrity": "sha512-6bU8cLUb1qYv0YecMRWKxS4L0+mjPMcQq2ZXtsUe0ij4oALCUIvCvy7rsGqWbOKcNwauh6jiuF4V/vCsObmXkw=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/serve-favicon-2.2.30.tgz_1510240683962_0.5015627106186002"}, "directories": {}, "publish_time": 1510240684127, "_hasShrinkwrap": false, "_cnpm_publish_time": 1510240684127, "_cnpmcore_publish_time": "2021-12-17T03:41:12.616Z"}, "2.2.29": {"name": "@types/serve-favicon", "version": "2.2.29", "description": "TypeScript definitions for serve-favicon", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/urossmolnik"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "01e663c0804a9a12e5080066dabce4be84d0c4249f4686f213422e5a53e52637", "typeScriptVersion": "2.0", "_id": "@types/serve-favicon@2.2.29", "dist": {"shasum": "879296aa0a65fe0d9832abbaee5a24bbb862e4c8", "size": 1738, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/serve-favicon/-/serve-favicon-2.2.29.tgz", "integrity": "sha512-sJfI8Mj5AdfkdvKYCxJqB0HzwVIF6WnQH3WRsibqqi7KX+mFhZ2v7zkZtgY/JwWKaHgzOnuAGGxoKunsPVukxg=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/serve-favicon-2.2.29.tgz_1503353051286_0.5966243017464876"}, "directories": {}, "publish_time": 1503353051467, "_hasShrinkwrap": false, "_cnpm_publish_time": 1503353051467, "_cnpmcore_publish_time": "2021-12-17T03:41:12.840Z"}, "2.2.28": {"name": "@types/serve-favicon", "version": "2.2.28", "description": "TypeScript definitions for serve-favicon 2.2.0", "license": "MIT", "author": "<PERSON><PERSON> <https://github.com/urossmolnik/>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typings": "index.d.ts", "typesPublisherContentHash": "cc688855565423f3eb9c9a496957fdbcdd31411025e3e3fba95151f2cb79c40d", "_id": "@types/serve-favicon@2.2.28", "dist": {"shasum": "f5aae24eafeeb202dfb7983e27c152a8810eaab5", "size": 1273, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/serve-favicon/-/serve-favicon-2.2.28.tgz", "integrity": "sha512-qmX9wuHb/qKh50ZYoUkicy1gciBO/00YPeKjkP/YShYSIYWQgnbrQ5MeQZ7Wlr57Llb4M1yhTz+DBEyjwLCCrQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/serve-favicon-2.2.28.tgz_1474308679143_0.968324180925265"}, "directories": {}, "publish_time": 1474308679366, "_hasShrinkwrap": false, "_cnpm_publish_time": 1474308679366, "_cnpmcore_publish_time": "2021-12-17T03:41:13.028Z"}, "2.2.27": {"name": "@types/serve-favicon", "version": "2.2.27", "description": "TypeScript definitions for serve-favicon 2.2.0", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/urossmolnik/"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "4.0.*"}, "_id": "@types/serve-favicon@2.2.27", "_shasum": "7062f9bc6d9fc687b2865636aebdcfe77e9dd1f6", "_from": "output\\serve-favicon", "_resolved": "file:output\\serve-favicon", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "7062f9bc6d9fc687b2865636aebdcfe77e9dd1f6", "size": 1255, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/serve-favicon/-/serve-favicon-2.2.27.tgz", "integrity": "sha512-EXgKPz1ju0DK3tWS10ciNrpmAKP3ffXMLvAhKZ8zXV+8/iRZnsvB3r+I7Mnapfo3q6D683XrbKhu2s56pUR/oQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/serve-favicon-2.2.27.tgz_1468512114700_0.6874835065100342"}, "directories": {}, "publish_time": 1468512117027, "_hasShrinkwrap": false, "_cnpm_publish_time": 1468512117027, "_cnpmcore_publish_time": "2021-12-17T03:41:13.281Z"}, "2.2.26-alpha": {"name": "@types/serve-favicon", "version": "2.2.26-alpha", "description": "TypeScript definitions for serve-favicon 2.2.0", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/urossmolnik/"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "4.0.28-alpha"}, "_id": "@types/serve-favicon@2.2.26-alpha", "_shasum": "5ca2caae26848238c0cd51dfc6bff838d307ad9a", "_from": "output\\serve-favicon", "_resolved": "file:output\\serve-favicon", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "5ca2caae26848238c0cd51dfc6bff838d307ad9a", "size": 1261, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/serve-favicon/-/serve-favicon-2.2.26-alpha.tgz", "integrity": "sha512-9EfxF6IPHMEFt+DdtpYwo1wzFil8ajpcIybiUFfakV8x+mLniUh7nXh3AUF7jA74V2yIQVPpg1Rhmw2ZpD3byA=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/serve-favicon-2.2.26-alpha.tgz_1468013426913_0.7260439000092447"}, "directories": {}, "publish_time": 1468013428523, "_hasShrinkwrap": false, "_cnpm_publish_time": 1468013428523, "_cnpmcore_publish_time": "2021-12-17T03:41:13.516Z"}, "2.2.25-alpha": {"name": "@types/serve-favicon", "version": "2.2.25-alpha", "description": "TypeScript definitions for serve-favicon 2.2.0", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/urossmolnik/"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "4.0.27-alpha"}, "_id": "@types/serve-favicon@2.2.25-alpha", "_shasum": "4eab53942cb96589880fceadc796cab290b4757c", "_from": "output\\serve-favicon", "_resolved": "file:output\\serve-favicon", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "4eab53942cb96589880fceadc796cab290b4757c", "size": 1260, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/serve-favicon/-/serve-favicon-2.2.25-alpha.tgz", "integrity": "sha512-a/NWzbMx1r5Qf3c+3OCFT076xrDcC2BzMx4YSb1nqa5NX3lv8yKdG8P9+9QPJDWbE8sJFWERW8x9ASXXZPkeQg=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/serve-favicon-2.2.25-alpha.tgz_1467595013215_0.710199624998495"}, "directories": {}, "publish_time": 1467595016899, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467595016899, "_cnpmcore_publish_time": "2021-12-17T03:41:13.855Z"}, "2.2.24-alpha": {"name": "@types/serve-favicon", "version": "2.2.24-alpha", "description": "TypeScript definitions for serve-favicon 2.2.0", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/urossmolnik/"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "4.0.26-alpha"}, "_id": "@types/serve-favicon@2.2.24-alpha", "_shasum": "4f43530f6431fa9fff11809dc13d3b715c324604", "_from": "output\\serve-favicon", "_resolved": "file:output\\serve-favicon", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "4f43530f6431fa9fff11809dc13d3b715c324604", "size": 1260, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/serve-favicon/-/serve-favicon-2.2.24-alpha.tgz", "integrity": "sha512-VC/TFd1h4K/b0icRiMVShqh69XVLOPsWr39c7zxoGrirx84R/ZkgTYeOX84sAn+Cmq06E2wQIQSwCO65STNc5g=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/serve-favicon-2.2.24-alpha.tgz_1467429973791_0.02093917573802173"}, "directories": {}, "publish_time": 1467429974391, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467429974391, "_cnpmcore_publish_time": "2021-12-17T03:41:14.063Z"}, "2.2.23-alpha": {"name": "@types/serve-favicon", "version": "2.2.23-alpha", "description": "TypeScript definitions for serve-favicon 2.2.0", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/urossmolnik/"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "4.0.25-alpha"}, "_id": "@types/serve-favicon@2.2.23-alpha", "_shasum": "cc90102e16d4261bb28decc4ab6e083165b20501", "_from": "output\\serve-favicon", "_resolved": "file:output\\serve-favicon", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "cc90102e16d4261bb28decc4ab6e083165b20501", "size": 1259, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/serve-favicon/-/serve-favicon-2.2.23-alpha.tgz", "integrity": "sha512-EASYqbTH2uShirkvyT80zz8eTav5OzgU5iMlJ7h1CnUMo0Y26ntxUYK3isUQKeRehlB5pZT+FL8f3h7i2WDwoA=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/serve-favicon-2.2.23-alpha.tgz_1467417629100_0.7050648531876504"}, "directories": {}, "publish_time": 1467417629603, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467417629603, "_cnpmcore_publish_time": "2021-12-17T03:41:14.264Z"}, "2.2.22-alpha": {"name": "@types/serve-favicon", "version": "2.2.22-alpha", "description": "TypeScript definitions for serve-favicon 2.2.0", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/urossmolnik/"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "4.0.*"}, "_id": "@types/serve-favicon@2.2.22-alpha", "_shasum": "1747cf031d0df9462c88050198a1d9ce240c66a4", "_from": "output\\serve-favicon", "_resolved": "file:output\\serve-favicon", "_npmVersion": "3.9.5", "_nodeVersion": "6.2.2", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "1747cf031d0df9462c88050198a1d9ce240c66a4", "size": 1247, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/serve-favicon/-/serve-favicon-2.2.22-alpha.tgz", "integrity": "sha512-abQwZsDg5q3lLJanmRmaTPBvA5/G9GrHrSqiYWzOrPhQdk+eDInXHiubP1/J4MpQGanreyYipzULWnF1fq5/5A=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/serve-favicon-2.2.22-alpha.tgz_1467405167268_0.11879893788136542"}, "directories": {}, "publish_time": 1467405167839, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467405167839, "_cnpmcore_publish_time": "2021-12-17T03:41:14.492Z"}, "2.2.21-alpha": {"name": "@types/serve-favicon", "version": "2.2.21-alpha", "description": "TypeScript definitions for serve-favicon 2.2.0", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/urossmolnik/"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "*"}, "_id": "@types/serve-favicon@2.2.21-alpha", "_shasum": "bed2c28ffd3b5d0b3fea024e1e1007936949d52f", "_from": "output\\serve-favicon", "_resolved": "file:output\\serve-favicon", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "bed2c28ffd3b5d0b3fea024e1e1007936949d52f", "size": 1226, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/serve-favicon/-/serve-favicon-2.2.21-alpha.tgz", "integrity": "sha512-u0N9taYlB9wuOucHJnYGdUdj7wSwIerT2K5WwhvsEdEFy0obK5iC2Ys2WwIejpEstKws2wwlb3qsYxe9OdWgxQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/serve-favicon-2.2.21-alpha.tgz_1464155730285_0.36180558800697327"}, "directories": {}, "publish_time": 1464155730770, "_hasShrinkwrap": false, "_cnpm_publish_time": 1464155730770, "_cnpmcore_publish_time": "2021-12-17T03:41:14.704Z"}, "2.2.20-alpha": {"name": "@types/serve-favicon", "version": "2.2.20-alpha", "description": "TypeScript definitions for serve-favicon 2.2.0", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/urossmolnik/"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "*"}, "_id": "@types/serve-favicon@2.2.20-alpha", "_shasum": "0b952573da4e3ff848c90f25f86c562392d8f21d", "_from": "output\\serve-favicon", "_resolved": "file:output\\serve-favicon", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "0b952573da4e3ff848c90f25f86c562392d8f21d", "size": 1225, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/serve-favicon/-/serve-favicon-2.2.20-alpha.tgz", "integrity": "sha512-nlEG6aWyL56pS6DzpYVMLxUlpqAxVlYM/s6EbRhzMFJ5SU2QMr+OjomFAZst48MwxI/eR7v09IjmL4rlH6o1MA=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/serve-favicon-2.2.20-alpha.tgz_1463776800451_0.2520958869718015"}, "directories": {}, "publish_time": 1463776800850, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463776800850, "_cnpmcore_publish_time": "2021-12-17T03:41:14.900Z"}, "2.2.15-alpha": {"name": "@types/serve-favicon", "version": "2.2.15-alpha", "description": "Type definitions for serve-favicon 2.2.0 from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/urossmolnik/"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"equire('express'": "*"}, "_id": "@types/serve-favicon@2.2.15-alpha", "_shasum": "d91fe5f5a9eb447f6e1b218c5da2be5c646f86e4", "_from": "output\\serve-favicon", "_resolved": "file:output\\serve-favicon", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "d91fe5f5a9eb447f6e1b218c5da2be5c646f86e4", "size": 1234, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/serve-favicon/-/serve-favicon-2.2.15-alpha.tgz", "integrity": "sha512-UA9fKEQDiizhlGdj3069rwcUXeKuBKq5qx1kB0THP/ZrwcBmOZrcx6ps4k8v+436/Gc/ysbWsF8WweWQcl4MgA=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/serve-favicon-2.2.15-alpha.tgz_1463696496869_0.30539053841494024"}, "directories": {}, "publish_time": 1463696497602, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463696497602, "_cnpmcore_publish_time": "2021-12-17T03:41:15.102Z"}, "2.2.14-alpha": {"name": "@types/serve-favicon", "version": "2.2.14-alpha", "description": "Type definitions for serve-favicon 2.2.0 from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "main": "", "scripts": {}, "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/urossmolnik/"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"equire('express'": "*"}, "_id": "@types/serve-favicon@2.2.14-alpha", "_shasum": "9ce4c487b50314821bff69a6a828766a5bbdb17e", "_from": "output\\serve-favicon", "_resolved": "file:output\\serve-favicon", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "9ce4c487b50314821bff69a6a828766a5bbdb17e", "size": 1226, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/serve-favicon/-/serve-favicon-2.2.14-alpha.tgz", "integrity": "sha512-IkIbiNKTIan+ixQVIcI5eQ1Zr5Ams95aO+aEria2rAz8GSglzHBn2fKqsX5KIaD2R8AzqH8F6vzp9xuOCBPa3g=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/serve-favicon-2.2.14-alpha.tgz_1463511285249_0.03856476116925478"}, "directories": {}, "publish_time": 1463511285790, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463511285790, "_cnpmcore_publish_time": "2021-12-17T03:41:15.336Z"}, "2.5.4": {"name": "@types/serve-favicon", "version": "2.5.4", "description": "TypeScript definitions for serve-favicon", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/serve-favicon", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/urossmolnik", "githubUsername": "urossmolnik"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/serve-favicon"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "9c7fe273f92250097d66833d2e70a3e1a72a38f3a5d26098089fad9e610f407d", "typeScriptVersion": "4.3", "_id": "@types/serve-favicon@2.5.4", "dist": {"integrity": "sha512-ly+yd6J/1myO40DKhZGx835/e+DXuLzA2J6dsRyBOzNnQoCsnGcuqkUkMmJD6Q8K9CSZOf+CyxL707WHa1PZGA==", "shasum": "8df8a13633df56e2579f8597f5741a370ed5c1df", "tarball": "https://registry.npmmirror.com/@types/serve-favicon/-/serve-favicon-2.5.4.tgz", "fileCount": 5, "unpackedSize": 5003, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC0x+1lFjwmBV+i0TLwDw+OOx/S4zXx/FqcSMMQ4FegiwIgb/9ILs4KLN7QzMNaNm4DJBXj6g4R18MYtYnv2PeiEFc="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkP4smACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoCaRAAl6COKMgWMapR6nsH2sI/M5Z7FnuFGNUrTEg/oUgubV1QkV65\r\nE8z0R9EY1f7qxr3jyZu4PqNEZGk+SDxNNqqlt9hHKH+VZ9PfA6bMdCNm91lK\r\nVM49Z1VtKq/wJg0y1oryr5w7t7VglDC1TvDMLhn4VDT0L8Vfb3oYVeoYWPHC\r\nON9Bq/Vh/QQMv7A0Zy5yPYD8XsFlb5rfDhUjjlL5pUJRCiWLSruW60poBNbK\r\nYMbVfSH0Su82I0YMvDXf8XfZHJ+1VaE5jUjUfQVfzbfFoGyUcaALV6FmF0AE\r\naSZUWc2X+u/HGCvgm+qfJNL5Lf9x7SrfoFLAmgaIUr409DfDkc825aZ92wbi\r\nxWILkaykDscvAQvgI/HoSafzVo4mvPvVefrR8KRk4P1emNVKV2n4IX7uwmTR\r\nqR8Ol+35SbNg9OKu5feVMrUE+KD/F2eRNI/TO4ItCRpPazY0q1FFeR85XMMD\r\n/Z7Y/k3CI99dtpxVCvdut5eDzGkjxoqwIsutKJV+4ipNOp8m6q0hK0kUydNX\r\nlmPB84BKA0axf7TjjUZ0hOEaLE+3uwluf8hJIsML76CTqNNWjcPBu1STsppA\r\nMYW4bBEK8Fpi+nsbIHgZjXONG5WT0qf0uKqrbmYj5GwtV3LTmojkBBWyJqsj\r\nZQ34fMSlOiD/tgqp/LVD5QEFXl/rYKEw9Io=\r\n=s5tH\r\n-----END PGP SIGNATURE-----\r\n", "size": 1978}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/serve-favicon_2.5.4_1681885990005_0.9607808691177084"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-19T06:33:10.219Z", "publish_time": 1681885990219}, "2.5.5": {"name": "@types/serve-favicon", "version": "2.5.5", "description": "TypeScript definitions for serve-favicon", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/serve-favicon", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/urossmolnik", "githubUsername": "urossmolnik"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/serve-favicon"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "858455054cc1c3dd70cda7ddae3db41933731d54ece04a9143086db81c20da68", "typeScriptVersion": "4.5", "_id": "@types/serve-favicon@2.5.5", "dist": {"integrity": "sha512-E/P1MhsGcalASnOVUPr9QQ4BIXyqQoGtLscG4fcMcEpZ7Z7tl6S4uSJnBJzWj7bj6rRZLIFOv0dR1YcepLNFFA==", "shasum": "88dbbc222ab3b295931491749921250927ec9d22", "tarball": "https://registry.npmmirror.com/@types/serve-favicon/-/serve-favicon-2.5.5.tgz", "fileCount": 5, "unpackedSize": 5005, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCG9hy3fGwbtO9R50WXxqzrFwrSkqi6INb1nyLcZciRJgIgKR4Y65UmxwpKp+5l0iivi0/Deajp/y2a1KQXT4euFGo="}], "size": 2002}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/serve-favicon_2.5.5_1695651796420_0.08136509147383819"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-25T14:23:16.666Z", "publish_time": 1695651796666, "_source_registry_name": "default"}, "2.5.6": {"name": "@types/serve-favicon", "version": "2.5.6", "description": "TypeScript definitions for serve-favicon", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/serve-favicon", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "urossmolnik", "url": "https://github.com/urossmolnik"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/serve-favicon"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "933bac5ae6ce67ee9e55e49c4884476587a62ec8de350bf5c5b034c0cdaf44c1", "typeScriptVersion": "4.5", "_id": "@types/serve-favicon@2.5.6", "dist": {"integrity": "sha512-Qm/Fct+DtjepE85kOAvPtI/OkB8gPZkBuVhKSv3Xpmy3J7zboVdUspGZOZJVVDa/U7ypaCt2cF3Xm5A9gqUvmg==", "shasum": "141cae3780993375e5ea2ec6dac74b039e2f18fc", "tarball": "https://registry.npmmirror.com/@types/serve-favicon/-/serve-favicon-2.5.6.tgz", "fileCount": 5, "unpackedSize": 4367, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDYdYaoo08G+8DiXmxegUXB3ggEXtLXS2z8fdOpsNeYdAiEA5dPFHzdZOxVmQ5akt173mjStGBRdQI3aQS4P3a4YyMU="}], "size": 1919}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/serve-favicon_2.5.6_1697640126470_0.06432975976182687"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-18T14:42:06.748Z", "publish_time": 1697640126748, "_source_registry_name": "default"}, "2.5.7": {"name": "@types/serve-favicon", "version": "2.5.7", "description": "TypeScript definitions for serve-favicon", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/serve-favicon", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "urossmolnik", "url": "https://github.com/urossmolnik"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/serve-favicon"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "8917b8c8ab9637907baa96b326eab550ef22db6c9da56a44034ae47ad002e510", "typeScriptVersion": "4.5", "_id": "@types/serve-favicon@2.5.7", "dist": {"integrity": "sha512-z9TNUQXdQ+W/OJMP1e3KOYUZ99qJS4+ZfFOIrPGImcayqKoyifbJSEFkVq1MCKBbqjMZpjPj3B5ilrQAR2+TOw==", "shasum": "a556fe9016984341a43af5b8345e274f3887f5ac", "tarball": "https://registry.npmmirror.com/@types/serve-favicon/-/serve-favicon-2.5.7.tgz", "fileCount": 5, "unpackedSize": 4367, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC14VogfZMa19rb+Is2nGmLS7u+j6b2Q7GNd503OluRrAiBaVCHcNClnZ3JNJDe8fXJUqksg2kGlj2rHVsg7A6Ld7g=="}], "size": 1920}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/serve-favicon_2.5.7_1699374035679_0.108046537156961"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-07T16:20:35.951Z", "publish_time": 1699374035951, "_source_registry_name": "default"}}, "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "urossmolnik", "url": "https://github.com/urossmolnik"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/serve-favicon", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/serve-favicon"}, "_source_registry_name": "default"}