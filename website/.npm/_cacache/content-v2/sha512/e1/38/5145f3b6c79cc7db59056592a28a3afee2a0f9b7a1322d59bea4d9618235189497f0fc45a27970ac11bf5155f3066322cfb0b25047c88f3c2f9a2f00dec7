{"_attachments": {}, "_id": "@types/node-schedule", "_rev": "297537-61f1ccb02c1293059b22f91a", "description": "TypeScript definitions for node-schedule", "dist-tags": {"latest": "2.1.8", "ts2.0": "1.2.4", "ts2.1": "1.2.4", "ts2.2": "1.2.4", "ts2.3": "1.2.4", "ts2.4": "1.2.4", "ts2.5": "1.2.4", "ts2.6": "1.2.4", "ts2.7": "1.2.4", "ts2.8": "1.3.0", "ts2.9": "1.3.0", "ts3.0": "1.3.0", "ts3.1": "1.3.0", "ts3.2": "1.3.1", "ts3.3": "1.3.1", "ts3.4": "1.3.1", "ts3.5": "1.3.1", "ts3.6": "1.3.2", "ts3.7": "1.3.2", "ts3.8": "1.3.2", "ts3.9": "2.1.0", "ts4.0": "2.1.0", "ts4.1": "2.1.0", "ts4.2": "2.1.0", "ts4.3": "2.1.0", "ts4.4": "2.1.0", "ts4.5": "2.1.4", "ts4.6": "2.1.6", "ts4.7": "2.1.7", "ts4.8": "2.1.7", "ts4.9": "2.1.7", "ts5.0": "2.1.7", "ts5.1": "2.1.8", "ts5.2": "2.1.8", "ts5.3": "2.1.8", "ts5.4": "2.1.8", "ts5.5": "2.1.8", "ts5.6": "2.1.8", "ts5.7": "2.1.8", "ts5.8": "2.1.8", "ts5.9": "2.1.8"}, "license": "MIT", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "name": "@types/node-schedule", "readme": "# Installation\r\n> `npm install --save @types/node-schedule`\r\n\r\n# Summary\r\nThis package contains type definitions for node-schedule (https://github.com/node-schedule/node-schedule).\r\n\r\n# Details\r\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node-schedule.\r\n\r\n### Additional Details\r\n * Last updated: Fri, 11 Jul 2025 18:02:44 GMT\r\n * Dependencies: [@types/node](https://npmjs.com/package/@types/node)\r\n\r\n# Credits\r\nThese definitions were written by [<PERSON>](https://github.com/cyrilschumacher), [<PERSON><PERSON><PERSON>](https://github.com/flowpl), [<PERSON><PERSON>](https://github.com/spike008t), and [<PERSON><PERSON><PERSON>](https://github.com/seohyun0120).\r\n", "time": {"created": "2022-01-26T22:35:28.982Z", "modified": "2025-07-11T18:03:07.153Z", "1.3.2": "2021-07-07T00:11:51.618Z", "1.3.1": "2020-10-13T17:11:25.117Z", "1.3.0": "2019-12-20T07:09:26.431Z", "1.2.4": "2019-08-19T20:08:17.287Z", "1.2.3": "2019-02-13T21:41:00.339Z", "1.2.2": "2018-01-05T15:45:01.417Z", "1.2.1": "2017-10-25T01:13:27.643Z", "1.2.0": "2017-04-18T18:29:09.911Z", "0.0.37": "2017-04-17T17:56:54.362Z", "0.0.36": "2016-11-08T13:39:34.663Z", "0.0.35": "2016-10-26T19:27:16.387Z", "0.0.34": "2016-10-05T22:54:53.480Z", "0.0.33": "2016-10-05T21:00:03.154Z", "0.0.32": "2016-09-19T17:56:15.485Z", "0.0.31": "2016-08-25T18:51:28.777Z", "0.0.30": "2016-08-19T15:35:25.597Z", "0.0.29": "2016-08-02T16:01:52.303Z", "0.0.28": "2016-07-14T15:33:00.165Z", "0.0.27-alpha": "2016-07-08T21:01:06.402Z", "0.0.26-alpha": "2016-07-04T00:56:18.143Z", "0.0.25-alpha": "2016-07-02T03:08:15.543Z", "0.0.24-alpha": "2016-07-01T23:37:18.647Z", "0.0.23-alpha": "2016-07-01T20:17:36.626Z", "0.0.22-alpha": "2016-05-25T05:34:33.015Z", "0.0.21-alpha": "2016-05-20T20:17:51.702Z", "0.0.16-alpha": "2016-05-19T21:51:29.398Z", "0.0.15-alpha": "2016-05-17T18:28:12.734Z", "2.1.0": "2022-05-03T14:31:44.687Z", "2.1.1": "2023-09-23T21:00:25.788Z", "2.1.2": "2023-10-18T09:47:22.125Z", "2.1.3": "2023-11-07T11:56:35.269Z", "2.1.4": "2023-11-22T00:41:41.430Z", "2.1.5": "2023-12-04T21:06:51.613Z", "2.1.6": "2024-02-04T13:07:08.575Z", "2.1.7": "2024-04-04T19:07:09.875Z", "2.1.8": "2025-07-11T18:02:54.341Z"}, "versions": {"1.3.2": {"name": "@types/node-schedule", "version": "1.3.2", "description": "TypeScript definitions for node-schedule", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node-schedule", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/cyrilschumacher", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/flowpl", "githubUsername": "flowpl"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/spike008t", "githubUsername": "spike008t"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/seohyun0120", "githubUsername": "seohyun0120"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/node-schedule"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "3d16e4f4ab924f8a85c3bdab4952e843ea6ec015a0a06cdf91d0dcc97470574b", "typeScriptVersion": "3.6", "_id": "@types/node-schedule@1.3.2", "dist": {"shasum": "cc7e32c6795cbadc8de03d0e1f86311727375423", "size": 3194, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-1.3.2.tgz", "integrity": "sha512-Y0CqdAr+lCpArT8CJJjJq4U2v8Bb5e7ru2nV/NhDdaptCMCRdOL3Y7tAhen39HluQMaIKWvPbDuiFBUQpg7Srw=="}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-schedule_1.3.2_1625616711433_0.06261800880764046"}, "_hasShrinkwrap": false, "publish_time": 1625616711618, "_cnpm_publish_time": 1625616711618, "_cnpmcore_publish_time": "2021-12-17T05:23:37.754Z"}, "1.3.1": {"name": "@types/node-schedule", "version": "1.3.1", "description": "TypeScript definitions for node-schedule", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/cyrilschumacher", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/flowpl", "githubUsername": "flowpl"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/spike008t", "githubUsername": "spike008t"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/seohyun0120", "githubUsername": "seohyun0120"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/node-schedule"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "26977364260898fbce41e9576595ef23e98b0e9371a2d1229a4cfaa3cc75113a", "typeScriptVersion": "3.2", "_id": "@types/node-schedule@1.3.1", "dist": {"shasum": "6785ea71b12b0b8899c3fce0650b2ef5a7ea9d1e", "size": 3152, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-1.3.1.tgz", "integrity": "sha512-xAY/ZATrThUkMElSDfOk+5uXprCrV6c6GQ5gTw3U04qPS6NofE1dhOUW+yrOF2UyrUiAax/Zc4WtagrbPAN3Tw=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-schedule_1.3.1_1602609084950_0.52902059524197"}, "_hasShrinkwrap": false, "publish_time": 1602609085117, "_cnpm_publish_time": 1602609085117, "_cnpmcore_publish_time": "2021-12-17T05:23:37.954Z"}, "1.3.0": {"name": "@types/node-schedule", "version": "1.3.0", "description": "TypeScript definitions for node-schedule", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/cyrilschumacher", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/flowpl", "githubUsername": "flowpl"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/spike008t", "githubUsername": "spike008t"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/node-schedule"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "a0f15a1f080639698b85476d0fe6cbf87ab471592418dbfbbd21a6598056534d", "typeScriptVersion": "2.8", "_id": "@types/node-schedule@1.3.0", "dist": {"shasum": "100f69078e74d736d59433fc4634ff49d0a9142d", "size": 3113, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-1.3.0.tgz", "integrity": "sha512-gjKmC9wFxn8laKYKwVP2iZvxeiA1DWUb6CXAYXtoYBcDbiyMqxLn2sQq+8aaNc7Xr0p93Hf0O0VizdaEPUO0vA=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-schedule_1.3.0_1576825766334_0.296235467025193"}, "_hasShrinkwrap": false, "publish_time": 1576825766431, "_cnpm_publish_time": 1576825766431, "_cnpmcore_publish_time": "2021-12-17T05:23:38.137Z"}, "1.2.4": {"name": "@types/node-schedule", "version": "1.2.4", "description": "TypeScript definitions for node-schedule", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/cyrilschumacher", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/flowpl", "githubUsername": "flowpl"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/node-schedule"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "654a23a71e09bddc08da65d52f82cdc9f2221e5e804fa57d5da5dc2342d940c4", "typeScriptVersion": "2.0", "_id": "@types/node-schedule@1.2.4", "dist": {"shasum": "2dce38dd7d87e77cd293113ac72f795fb9f6dfe0", "size": 2979, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-1.2.4.tgz", "integrity": "sha512-s8ie8rUwAtX0Si75SiKH14akE/Ofw/Hx4Exbulv4wOQJEDerI7zeOaDODr/a1UEaHN19VkmZCt9Q0rFEKrVE5g=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-schedule_1.2.4_1566245297141_0.6741355465940391"}, "_hasShrinkwrap": false, "publish_time": 1566245297287, "_cnpm_publish_time": 1566245297287, "_cnpmcore_publish_time": "2021-12-17T05:23:38.553Z"}, "1.2.3": {"name": "@types/node-schedule", "version": "1.2.3", "description": "TypeScript definitions for node-schedule", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/cyrilschumacher", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/flowpl", "githubUsername": "flowpl"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "d79cb654e122dbb79bf6624945adfcc10c282a1a50241383cb9d655a9035739f", "typeScriptVersion": "2.0", "_id": "@types/node-schedule@1.2.3", "dist": {"shasum": "614ba775c4c9b200e5f939a4eeef6727eb0ed6b7", "size": 2968, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-1.2.3.tgz", "integrity": "sha512-pmuGe7xlx4hnUnOeGk2G46xxGKesF8reddLPiQu0tEIOQePMjDezjaOXFLfRPFBDWm03Aj8FoqPTuFxQt9LDaw=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-schedule_1.2.3_1550094060174_0.5553808348152163"}, "_hasShrinkwrap": false, "publish_time": 1550094060339, "_cnpm_publish_time": 1550094060339, "_cnpmcore_publish_time": "2021-12-17T05:23:38.782Z"}, "1.2.2": {"name": "@types/node-schedule", "version": "1.2.2", "description": "TypeScript definitions for node-schedule", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/cyrilschumacher", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/flowpl", "githubUsername": "flowpl"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/events": "*", "@types/node": "*"}, "typesPublisherContentHash": "b8eae1be4bc31607e1115a6edcfd018c43e7971a391e3c9ea102f83ff15ca803", "typeScriptVersion": "2.0", "_id": "@types/node-schedule@1.2.2", "dist": {"shasum": "bb5beae99b7327b4f2ddc1834f1fdae4378891b0", "size": 2993, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-1.2.2.tgz", "integrity": "sha512-tPZexm6xqXT550VpbiktBFDoSJLBGSaBEM1cThP5NKvH7w6W6dMAkHl81ffxmYmMoskvEkLUoGvtSmNNpBhNiw=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-schedule-1.2.2.tgz_1515167101314_0.2669140277430415"}, "directories": {}, "publish_time": 1515167101417, "_hasShrinkwrap": false, "_cnpm_publish_time": 1515167101417, "_cnpmcore_publish_time": "2021-12-17T05:23:39.004Z"}, "1.2.1": {"name": "@types/node-schedule", "version": "1.2.1", "description": "TypeScript definitions for node-schedule", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/cyrilschumacher", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/flowpl", "githubUsername": "flowpl"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "d3716c8a7f407dbe0630fa527d17935b5973afa0bc3506cdfbe6894c81afbb91", "typeScriptVersion": "2.0", "_id": "@types/node-schedule@1.2.1", "dist": {"shasum": "d7cbfb71ea1cbc91832bb80d499a6be65a6cc73b", "size": 2985, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-1.2.1.tgz", "integrity": "sha512-3eDJD7nCPpsO/S7k3og3AuCkWEF/GppKge0aM7Lp2ABn0Oyx/Na3eL/AfU1L4x6ya5LKGlfKARV5qVZT2hPW+Q=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-schedule-1.2.1.tgz_1508894007575_0.2569251225795597"}, "directories": {}, "publish_time": 1508894007643, "_hasShrinkwrap": false, "_cnpm_publish_time": 1508894007643, "_cnpmcore_publish_time": "2021-12-17T05:23:39.237Z"}, "1.2.0": {"name": "@types/node-schedule", "version": "1.2.0", "description": "TypeScript definitions for node-schedule", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/cyrilschumacher"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/flowpl"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "5b86851ed61d4c043eb23cb4289158311b266044be60b772befe8bb37b03126a", "typeScriptVersion": "2.0", "_id": "@types/node-schedule@1.2.0", "dist": {"shasum": "fcd16bb17d18eda118d5e3c4597d2f0e9cde897e", "size": 3381, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-1.2.0.tgz", "integrity": "sha512-9K1qjnnFcMPnP7nqyfmUTARc7Dx8cVm0Tu7vKkF6XHeKsjWLm0HZLU3KixkZQspCO9xOiWRl7eRoMNwyzUG5JA=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/node-schedule-1.2.0.tgz_1492540147952_0.8802271320018917"}, "directories": {}, "publish_time": 1492540149911, "_hasShrinkwrap": false, "_cnpm_publish_time": 1492540149911, "_cnpmcore_publish_time": "2021-12-17T05:23:39.456Z"}, "0.0.37": {"name": "@types/node-schedule", "version": "0.0.37", "description": "TypeScript definitions for node-schedule", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/cyrilschumacher"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/flowpl"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "7ab324351ab8d5b050900d332e6fbddf101e232270d1024df0c95fe2e3f0bdda", "typeScriptVersion": "2.0", "_id": "@types/node-schedule@0.0.37", "dist": {"shasum": "35811f1f9b9987f68f6e038693c713c372431779", "size": 3375, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-0.0.37.tgz", "integrity": "sha512-poImGhqGIqaw+Tdywva3ORIlDIA/rcGvyhjzvv10mCyRWqcLy5fW0ovvUqMVaNbldZzXYAOfARSjsja59qrb8w=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-schedule-0.0.37.tgz_1492451814117_0.5267371700610965"}, "directories": {}, "publish_time": 1492451814362, "_hasShrinkwrap": false, "_cnpm_publish_time": 1492451814362, "_cnpmcore_publish_time": "2021-12-17T05:23:39.671Z"}, "0.0.36": {"name": "@types/node-schedule", "version": "0.0.36", "description": "TypeScript definitions for node-schedule", "license": "MIT", "author": "<PERSON> <https://github.com/cyrilschumacher>, <PERSON><PERSON><PERSON> <https://github.com/flowpl>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typings": "index.d.ts", "typesPublisherContentHash": "c7633e9aa712ec4facb6755310b0ee4daa6a1da3a0a5676ae3d66f5fa259255b", "_id": "@types/node-schedule@0.0.36", "dist": {"shasum": "b6daa1c8a4b635a225097ccb3806336f68c937df", "size": 2802, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-0.0.36.tgz", "integrity": "sha512-HmWOPlTZA5TslvCXBGxzaU61jGPoMA7VPOSeYWnnXOgzhGAW7muzHnX2Kkq92skLQLUR9ePPUHFyxwkXdzHKUA=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/node-schedule-0.0.36.tgz_1478612372586_0.8107968172989786"}, "directories": {}, "publish_time": 1478612374663, "_hasShrinkwrap": false, "_cnpm_publish_time": 1478612374663, "_cnpmcore_publish_time": "2021-12-17T05:23:39.930Z"}, "0.0.35": {"name": "@types/node-schedule", "version": "0.0.35", "description": "TypeScript definitions for node-schedule", "license": "MIT", "author": "<PERSON> <https://github.com/cyrilschumacher>, <PERSON><PERSON><PERSON> <https://github.com/flowpl>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typings": "index.d.ts", "typesPublisherContentHash": "0212572a2a6cb01747d03f2580a3ebdeb9441822be3e21fac98e6494e4ec33ac", "_id": "@types/node-schedule@0.0.35", "dist": {"shasum": "f7ad18f56ca3192571481aab6f8e5b1f491ff431", "size": 2710, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-0.0.35.tgz", "integrity": "sha512-/u14T+RhNSmrCEJSmwaYkmCORPW0cHMZ4WzlhC8LYma38yn2dpdShlja+xHVeawIKCfHm+s36WOFsrnrb6a6Og=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/node-schedule-0.0.35.tgz_1477510032655_0.11905971402302384"}, "directories": {}, "publish_time": 1477510036387, "_hasShrinkwrap": false, "_cnpm_publish_time": 1477510036387, "_cnpmcore_publish_time": "2021-12-17T05:23:40.170Z"}, "0.0.34": {"name": "@types/node-schedule", "version": "0.0.34", "description": "TypeScript definitions for node-schedule", "license": "MIT", "author": "<PERSON> <https://github.com/cyrilschumacher>, <PERSON><PERSON><PERSON> <https://github.com/flowpl>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typings": "index.d.ts", "typesPublisherContentHash": "3d6681d55b617e40fbeaf9661c4dfc98469e958263a66fe42fdb63c2ce71ebf5", "_id": "@types/node-schedule@0.0.34", "dist": {"shasum": "28f7d4d45bbbe674f20688b776fac12952cba0f3", "size": 2681, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-0.0.34.tgz", "integrity": "sha512-APDx7CtIRczF2Qe1HE12Djx2/L2n+f+oQli5N6owEuAXdP4Ue+UDG2vWBcxdpdZY/iMY4/ey7Par2CgfUGMslA=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-schedule-0.0.34.tgz_1475708093238_0.18674258887767792"}, "directories": {}, "publish_time": 1475708093480, "_hasShrinkwrap": false, "_cnpm_publish_time": 1475708093480, "_cnpmcore_publish_time": "2021-12-17T05:23:40.402Z"}, "0.0.33": {"name": "@types/node-schedule", "version": "0.0.33", "description": "TypeScript definitions for node-schedule", "license": "MIT", "author": "<PERSON> <https://github.com/cyrilschumacher>, <PERSON><PERSON><PERSON> <https://github.com/flowpl>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typings": "index.d.ts", "typesPublisherContentHash": "bc09521c959be97dea2b3d891c1a8d38840f20838690089ee788bed1c9eba175", "_id": "@types/node-schedule@0.0.33", "dist": {"shasum": "087035f0410f9c95448e62612457bfec0e3330dd", "size": 2674, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-0.0.33.tgz", "integrity": "sha512-6+gJpBVuet8LbgHU+2Z2NklC2FiBAOosS9GXyDwoopq9Oqoc0hiuP/raOxs3cIA3T614QilhCtfHux3UM4O8oQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-schedule-0.0.33.tgz_1475701202902_0.6911888860631734"}, "directories": {}, "publish_time": 1475701203154, "_hasShrinkwrap": false, "_cnpm_publish_time": 1475701203154, "_cnpmcore_publish_time": "2021-12-17T05:23:40.596Z"}, "0.0.32": {"name": "@types/node-schedule", "version": "0.0.32", "description": "TypeScript definitions for node-schedule", "license": "MIT", "author": "<PERSON> <https://github.com/cyrilschumacher>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typings": "index.d.ts", "typesPublisherContentHash": "ee3c0f23e2d2fed413e094b9185d1056109786d7f9336604dc42b0e83ed74e49", "_id": "@types/node-schedule@0.0.32", "dist": {"shasum": "3222375371d885613c36047234d6b230202c0593", "size": 2643, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-0.0.32.tgz", "integrity": "sha512-SNuV8SXLjUh5n7u2c8Cg+D1/d1fNsAKXnDaRfJo25CYOESuo5VNC5LIctTRRut+MoRM/GuG/jrz2xohozQqEKg=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/node-schedule-0.0.32.tgz_1474307772382_0.1608609000686556"}, "directories": {}, "publish_time": 1474307775485, "_hasShrinkwrap": false, "_cnpm_publish_time": 1474307775485, "_cnpmcore_publish_time": "2021-12-17T05:23:40.804Z"}, "0.0.31": {"name": "@types/node-schedule", "version": "0.0.31", "description": "TypeScript definitions for node-schedule", "license": "MIT", "author": "<PERSON> <https://github.com/cyrilschumacher>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "6.0.*"}, "typings": "index.d.ts", "_id": "@types/node-schedule@0.0.31", "dist": {"shasum": "18ca5abd73a23ea9d52b44ddeb7d81f858b5db55", "size": 2640, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-0.0.31.tgz", "integrity": "sha512-JRwk7yNcKDPk3Zn8Hi3QdnPUCpcbA4AEuBDHJgB+qRiVCfXIt2mCogMKyh/iePAES9nwpXFqG5vgQ1T27ULLEw=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/node-schedule-0.0.31.tgz_1472151088213_0.8880193415097892"}, "directories": {}, "publish_time": 1472151088777, "_hasShrinkwrap": false, "_cnpm_publish_time": 1472151088777, "_cnpmcore_publish_time": "2021-12-17T05:23:41.025Z"}, "0.0.30": {"name": "@types/node-schedule", "version": "0.0.30", "description": "TypeScript definitions for node-schedule", "license": "MIT", "author": "<PERSON> <https://github.com/cyrilschumacher>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "6.0.*"}, "typings": "index.d.ts", "_id": "@types/node-schedule@0.0.30", "dist": {"shasum": "271bece3ebafceb7a9e9c81ab9314e8414d8458e", "size": 2639, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-0.0.30.tgz", "integrity": "sha512-6a1FekJ/PzoJcz2zyY04GDXH/jOir/E0Sp/ILoPhw/tMTCa4r8+pYEpG0Xxl+hZzb8YAIIO62JyM+YyenWujNw=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-schedule-0.0.30.tgz_1471620923882_0.37696978636085987"}, "directories": {}, "publish_time": 1471620925597, "_hasShrinkwrap": false, "_cnpm_publish_time": 1471620925597, "_cnpmcore_publish_time": "2021-12-17T05:23:41.240Z"}, "0.0.29": {"name": "@types/node-schedule", "version": "0.0.29", "description": "TypeScript definitions for node-schedule", "license": "MIT", "author": "<PERSON> <https://github.com/cyrilschumacher>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "6.0.*"}, "typings": "index.d.ts", "_id": "@types/node-schedule@0.0.29", "dist": {"shasum": "dc3eb6a7ef580a9e86d433c0c73ffbcc9ecf137e", "size": 12800, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-0.0.29.tgz", "integrity": "sha512-Cg1nLkJZdrf9WefA+gvJSc5lZO6l7kgrhmPiTamD6CxgUDGfwE6RZWVOg0wMv/RPPtMc85SdW7XCjE+RtiT+oA=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/node-schedule-0.0.29.tgz_1470153711351_0.7331404546275735"}, "directories": {}, "publish_time": 1470153712303, "_hasShrinkwrap": false, "_cnpm_publish_time": 1470153712303, "_cnpmcore_publish_time": "2021-12-17T05:23:41.440Z"}, "0.0.28": {"name": "@types/node-schedule", "version": "0.0.28", "description": "TypeScript definitions for node-schedule", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/cyrilschumacher"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {}, "_id": "@types/node-schedule@0.0.28", "_shasum": "1de1ea78390cbc5f6d84ef5ac603edcd2222d692", "_from": "output\\node-schedule", "_resolved": "file:output\\node-schedule", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "1de1ea78390cbc5f6d84ef5ac603edcd2222d692", "size": 1757, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-0.0.28.tgz", "integrity": "sha512-rUUA6OYCDppttFZqlPT34XaRw26Xje9u7MpTP5jfXXoyXivUQV+UGibi0xokhqK7xZjHE5Wa7RKQPsAbnBzcHQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-schedule-0.0.28.tgz_1468510377709_0.8803210908081383"}, "directories": {}, "publish_time": 1468510380165, "_hasShrinkwrap": false, "_cnpm_publish_time": 1468510380165, "_cnpmcore_publish_time": "2021-12-17T05:23:41.649Z"}, "0.0.27-alpha": {"name": "@types/node-schedule", "version": "0.0.27-alpha", "description": "TypeScript definitions for node-schedule", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/cyrilschumacher"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/node-schedule": "0.0.27-alpha"}, "_id": "@types/node-schedule@0.0.27-alpha", "_shasum": "3e12fe3391a8b2a901cdbeb3475f4457f7ee675b", "_from": "output\\node-schedule", "_resolved": "file:output\\node-schedule", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "3e12fe3391a8b2a901cdbeb3475f4457f7ee675b", "size": 1797, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-0.0.27-alpha.tgz", "integrity": "sha512-tKVnyfaKPaiSs3K2Mnd1x+c3sC5qBmaNl5uRtEwMVB13olCEczbHaeW3pk58ldJz+D3r/iff64OYJGj37Ztzjg=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-schedule-0.0.27-alpha.tgz_1468011664038_0.19014041172340512"}, "directories": {}, "publish_time": 1468011666402, "_hasShrinkwrap": false, "_cnpm_publish_time": 1468011666402, "_cnpmcore_publish_time": "2021-12-17T05:23:41.857Z"}, "0.0.26-alpha": {"name": "@types/node-schedule", "version": "0.0.26-alpha", "description": "TypeScript definitions for node-schedule", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/cyrilschumacher"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/node-schedule": "0.0.26-alpha"}, "_id": "@types/node-schedule@0.0.26-alpha", "_shasum": "937c78eb10384bbee63f3afa6d40c0ed1c79e854", "_from": "output\\node-schedule", "_resolved": "file:output\\node-schedule", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "937c78eb10384bbee63f3afa6d40c0ed1c79e854", "size": 1799, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-0.0.26-alpha.tgz", "integrity": "sha512-MBwOzSWwq64P80NkL8ySegmvwyxZqtmbJ63+7MGGjpEuURp8DCRi4nJoQIjTzeRPxXf1S6VuolKbhA6Kd4Wvqw=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-schedule-0.0.26-alpha.tgz_1467593777593_0.23643510113470256"}, "directories": {}, "publish_time": 1467593778143, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467593778143, "_cnpmcore_publish_time": "2021-12-17T05:23:42.077Z"}, "0.0.25-alpha": {"name": "@types/node-schedule", "version": "0.0.25-alpha", "description": "TypeScript definitions for node-schedule", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/cyrilschumacher"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/node-schedule": "0.0.24-alpha"}, "_id": "@types/node-schedule@0.0.25-alpha", "_shasum": "3c9d7b61c565c53baa2f141fbb6b22d07377675f", "_from": "output\\node-schedule", "_resolved": "file:output\\node-schedule", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "3c9d7b61c565c53baa2f141fbb6b22d07377675f", "size": 1800, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-0.0.25-alpha.tgz", "integrity": "sha512-rbmI5a1CuOtDAguT1nfxzVfAb7g9AojteuMbOvpAoR48b6ILou3IsnB6Hv8sna2DuE8S+stJ0iI2cp6N0Tim7A=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-schedule-0.0.25-alpha.tgz_1467428894960_0.6019801869988441"}, "directories": {}, "publish_time": 1467428895543, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467428895543, "_cnpmcore_publish_time": "2021-12-17T05:23:42.281Z"}, "0.0.24-alpha": {"name": "@types/node-schedule", "version": "0.0.24-alpha", "description": "TypeScript definitions for node-schedule", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/cyrilschumacher"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/node-schedule": "0.0.23-alpha"}, "_id": "@types/node-schedule@0.0.24-alpha", "_shasum": "df897077f68d81f23d11b4d6681e6210d63eae74", "_from": "output\\node-schedule", "_resolved": "file:output\\node-schedule", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "df897077f68d81f23d11b4d6681e6210d63eae74", "size": 1799, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-0.0.24-alpha.tgz", "integrity": "sha512-Y4argA1h6q9zjEkPcaJD9JWtpH7H0mOVcgeSmgWG1IoUWCO5P6GPOmpo1Zk8+LNYD4CFQlPR8PcdvhqJce0BRw=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/node-schedule-0.0.24-alpha.tgz_1467416234918_0.8670135387219489"}, "directories": {}, "publish_time": 1467416238647, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467416238647, "_cnpmcore_publish_time": "2021-12-17T05:23:42.532Z"}, "0.0.23-alpha": {"name": "@types/node-schedule", "version": "0.0.23-alpha", "description": "TypeScript definitions for node-schedule", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/cyrilschumacher"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/node-schedule": "0.0.*"}, "_id": "@types/node-schedule@0.0.23-alpha", "_shasum": "92a65b7a74278a246d2d802fc3c649459beb930c", "_from": "output\\node-schedule", "_resolved": "file:output\\node-schedule", "_npmVersion": "3.9.5", "_nodeVersion": "6.2.2", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "92a65b7a74278a246d2d802fc3c649459beb930c", "size": 1795, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-0.0.23-alpha.tgz", "integrity": "sha512-77RnckA2H54BhivSlDL9CWYdaOAeAewelbSWbfUGKpzWhgRKdQyAywjRofH2/BikeS80e80USnLw0OhaKzjNjQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-schedule-0.0.23-alpha.tgz_1467404255990_0.4144398095086217"}, "directories": {}, "publish_time": 1467404256626, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467404256626, "_cnpmcore_publish_time": "2021-12-17T05:23:42.786Z"}, "0.0.22-alpha": {"name": "@types/node-schedule", "version": "0.0.22-alpha", "description": "TypeScript definitions for node-schedule", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/cyrilschumacher"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/node-schedule": "*"}, "_id": "@types/node-schedule@0.0.22-alpha", "_shasum": "29d3f8e849d3f82dc3c221ef488a34f26e5e5a84", "_from": "output\\node-schedule", "_resolved": "file:output\\node-schedule", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "29d3f8e849d3f82dc3c221ef488a34f26e5e5a84", "size": 1771, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-0.0.22-alpha.tgz", "integrity": "sha512-ZjgeWJ8/SYU65bDOaz/GoAMyPns9doy8aXTMwE4SyGMsQrNxwIXKchG4Mb+OHUhgb+vl3dxHgxvjpH3FPah+4w=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/node-schedule-0.0.22-alpha.tgz_1464154470332_0.3433354655280709"}, "directories": {}, "publish_time": 1464154473015, "_hasShrinkwrap": false, "_cnpm_publish_time": 1464154473015, "_cnpmcore_publish_time": "2021-12-17T05:23:43.027Z"}, "0.0.21-alpha": {"name": "@types/node-schedule", "version": "0.0.21-alpha", "description": "TypeScript definitions for node-schedule", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/cyrilschumacher"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/node-schedule": "*"}, "_id": "@types/node-schedule@0.0.21-alpha", "_shasum": "1b367932a823ecd425037006ced054481c440474", "_from": "output\\node-schedule", "_resolved": "file:output\\node-schedule", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "1b367932a823ecd425037006ced054481c440474", "size": 1771, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-0.0.21-alpha.tgz", "integrity": "sha512-jJNYLbOQGxXKeWeBepgtrzQWLcI6SHwIqGft+1K7dAp4abGniPH9SMTwFQEeHIWEAOkjnrnqtOPsradXhwqV9w=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-schedule-0.0.21-alpha.tgz_1463775471296_0.4916138560511172"}, "directories": {}, "publish_time": 1463775471702, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463775471702, "_cnpmcore_publish_time": "2021-12-17T05:23:43.601Z"}, "0.0.16-alpha": {"name": "@types/node-schedule", "version": "0.0.16-alpha", "description": "Type definitions for node-schedule from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/cyrilschumacher"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"equire('node-schedule'": "*"}, "_id": "@types/node-schedule@0.0.16-alpha", "_shasum": "6595f038170e29492c33e17e56370ecea7777697", "_from": "output\\node-schedule", "_resolved": "file:output\\node-schedule", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "6595f038170e29492c33e17e56370ecea7777697", "size": 1795, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-0.0.16-alpha.tgz", "integrity": "sha512-nW2ysyKAzIkbPny0ZnEZvXoOLExenv8Bp2JhSksQbOU8mOUFv8osdLvgAdc3bGY0IIzS3fraItVbCDi76aIWzw=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-schedule-0.0.16-alpha.tgz_1463694688654_0.31638644612394273"}, "directories": {}, "publish_time": 1463694689398, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463694689398, "_cnpmcore_publish_time": "2021-12-17T05:23:43.815Z"}, "0.0.15-alpha": {"name": "@types/node-schedule", "version": "0.0.15-alpha", "description": "Type definitions for node-schedule from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/cyrilschumacher"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"equire('node-schedule'": "*"}, "_id": "@types/node-schedule@0.0.15-alpha", "_shasum": "1d1a0c228ddff97827e9c9175fbc6d7ad4be6c0d", "_from": "output\\node-schedule", "_resolved": "file:output\\node-schedule", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "1d1a0c228ddff97827e9c9175fbc6d7ad4be6c0d", "size": 1787, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-0.0.15-alpha.tgz", "integrity": "sha512-Q+uUkrQdd4OssWONzSOXn1yEVMWXxJ3JskRrClxaQA2/lN6ZwpY/J/d1h/iPjDRAb/xnRyIzcLqPIB59UkfI0g=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/node-schedule-0.0.15-alpha.tgz_1463509688602_0.6051663386169821"}, "directories": {}, "publish_time": 1463509692734, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463509692734, "_cnpmcore_publish_time": "2021-12-17T05:23:44.036Z"}, "2.1.0": {"name": "@types/node-schedule", "version": "2.1.0", "description": "TypeScript definitions for node-schedule", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node-schedule", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/cyrilschumacher", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/flowpl", "githubUsername": "flowpl"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/spike008t", "githubUsername": "spike008t"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/seohyun0120", "githubUsername": "seohyun0120"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/node-schedule"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "a037d023dac7fbc49cb5c5541df1fbce151eb7c258a980706e76d501adeee361", "typeScriptVersion": "3.9", "_id": "@types/node-schedule@2.1.0", "dist": {"integrity": "sha512-NiTwl8YN3v/1YCKrDFSmCTkVxFDylueEqsOFdgF+vPsm+AlyJKGAo5yzX1FiOxPsZiN6/r8gJitYx2EaSuBmmg==", "shasum": "60375640c0509bab963573def9d1f417f438c290", "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-2.1.0.tgz", "fileCount": 4, "unpackedSize": 9652, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIECWrp0Y8ltZKP/qKGdViSEhzrBMlC9S2yAnhpWPdVvhAiEAxazNufDH104EaCDNNqEnD0qM6A7GSoe91nksZuklung="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicTzQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmonBg/9HlXW1rJLPGx6VGyt70CMDRZmR4ZYohrLNkdd1fNmZ3WCaMYf\r\nGnj3dkKnRliQ+Rx18McNwGhXY18txMpDCKR2Ms7FS5Yh8inkugnQF7GLRgEl\r\nGODKQHZT8mzVYt9+MmTkuXW5fDIecLJETLsVh0BCg+Zi0jtWP0GC1zfQENwj\r\nZZ90SE+75W7urFm7+GzKr7cWlx/s/BYpyZEKp6idUVHLljyimpYTk4ri9ZVJ\r\nqnRs/q1fI1KuVk4WmsqmqE1e2/qUR4dmvAst55Xj7Et0yB3argwtm2jL+sz6\r\nrEUJV32ierSLvRT0CBJn/3XKFPyBT9w1mPW3jGFYyvQPn+n18J4RNY0wP/pw\r\nvbqS9Ln5aaefcBSkgqUpJMTgVnK+ucqU8TZZ5w2ubNGWohM6t+aP7+ZV4tHB\r\ngXUdstNnbyX7/1IQKDVE7jpawRpPZhuscQMezzMEfaqbCdQ3ozXnpPnalQxi\r\nnQjkJF6vcZxUlApZoanahrCIfzZetmU/HysQ0aXgAY8HjSzNrBJ9pcqHi1UW\r\n+q4qX2tL1wFfk+FhjtDvH2XzMlOk6Z6cGn0df7/e4hsl6uDc7aTAZnQ/nHLi\r\nXBO/qN1WXJqPEaJTBnlv/tsJIKmnhK87B6wKNvvq0Pt/khLtF/SYUGxHc4MG\r\nf3U/Rao+e3NUzjEz/JheiuQhuXhmnSd4ROk=\r\n=XMYh\r\n-----END PGP SIGNATURE-----\r\n", "size": 3265}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-schedule_2.1.0_1651588304333_0.686791882819692"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-05-03T14:31:49.354Z"}, "2.1.1": {"name": "@types/node-schedule", "version": "2.1.1", "description": "TypeScript definitions for node-schedule", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node-schedule", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/cyrilschumacher", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/flowpl", "githubUsername": "flowpl"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/spike008t", "githubUsername": "spike008t"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/seohyun0120", "githubUsername": "seohyun0120"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/node-schedule"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "9bdca41b699d5286c025293b180b6773cbb920591984a95793d06199d99ff580", "typeScriptVersion": "4.5", "_id": "@types/node-schedule@2.1.1", "dist": {"integrity": "sha512-FaqkbBizA+DinA0XWtAhdbEXykUkkqzBWT4BSnhn71z9C+vvcDgNcHvTP59nBhMg3o39E/ZY8zB/AQ6/HGuRag==", "shasum": "64449eb95ea7da93db3d4f570172325a3faf5ab0", "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-2.1.1.tgz", "fileCount": 5, "unpackedSize": 9704, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDEWJvr0vxqV+mwRtLQGAsJq1jxZ3K2X/CHFDaeib3SQQIhAPyv9dk4Pi/4x+m+7kqix2uSMrdH34vepkeJf40D585v"}], "size": 3324}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-schedule_2.1.1_1695502825609_0.82192729918994"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-23T21:00:25.788Z", "publish_time": 1695502825788, "_source_registry_name": "default"}, "2.1.2": {"name": "@types/node-schedule", "version": "2.1.2", "description": "TypeScript definitions for node-schedule", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node-schedule", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/cyrilschumacher"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "flowpl", "url": "https://github.com/flowpl"}, {"name": "<PERSON><PERSON>", "githubUsername": "spike008t", "url": "https://github.com/spike008t"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "seohyun0120", "url": "https://github.com/seohyun0120"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/node-schedule"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "f277e6ed15fdd0b8c162349f8ce831b3c25bb6b14ec7e64f2c66b27b48ed39d2", "typeScriptVersion": "4.5", "_id": "@types/node-schedule@2.1.2", "dist": {"integrity": "sha512-pNf6vCw14EYbqo0Y1eLGhkyv9RhgvphrxpPk4bd1CqwsWbHCrLSVYpO+9NmKOCUSYwxG6eRaWDR3Y6C+4gtzow==", "shasum": "405f54672d3811dd9099ecf07ee8621bc14852fc", "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-2.1.2.tgz", "fileCount": 5, "unpackedSize": 9240, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD2HIfmNUxjN4s0UKU1OV9Wuc2KujKJHnR9eaU04CXOSwIhAOyataApzy2S97MqG3NQ0WZE63EPJYc9Z8KFgrBAk/mp"}], "size": 3231}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-schedule_2.1.2_1697622441912_0.9063729811330858"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-18T09:47:22.125Z", "publish_time": 1697622442125, "_source_registry_name": "default"}, "2.1.3": {"name": "@types/node-schedule", "version": "2.1.3", "description": "TypeScript definitions for node-schedule", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node-schedule", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/cyrilschumacher"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "flowpl", "url": "https://github.com/flowpl"}, {"name": "<PERSON><PERSON>", "githubUsername": "spike008t", "url": "https://github.com/spike008t"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "seohyun0120", "url": "https://github.com/seohyun0120"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/node-schedule"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "4daf79be90f134582b13482acb48d3f8b8f367a328883f764cd0020bf1310f17", "typeScriptVersion": "4.5", "_id": "@types/node-schedule@2.1.3", "dist": {"integrity": "sha512-sTWHBCD+17XjHRuxkLwoC0VdxAx/TVNny+1DUxv8RTPJNZGuokEKiSiGag+v9XdrnJ/c36ObWi4HA3JiokvOQw==", "shasum": "4a05f98861db2a81fb4837f4b621ce3093816f0a", "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-2.1.3.tgz", "fileCount": 5, "unpackedSize": 9240, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDpbQ7EiXUaWoDt+fKHtdi87uidmskEYTkQor7eZM49dQIgOs4ega9OD1r/737g8Nqqaq6ufk3kbzt8tM4NxzWeylE="}], "size": 3233}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-schedule_2.1.3_1699358195132_0.5480282583130056"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-07T11:56:35.269Z", "publish_time": 1699358195269, "_source_registry_name": "default"}, "2.1.4": {"name": "@types/node-schedule", "version": "2.1.4", "description": "TypeScript definitions for node-schedule", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node-schedule", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/cyrilschumacher"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "flowpl", "url": "https://github.com/flowpl"}, {"name": "<PERSON><PERSON>", "githubUsername": "spike008t", "url": "https://github.com/spike008t"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "seohyun0120", "url": "https://github.com/seohyun0120"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/node-schedule"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "130512ffb92e20dd835a251f23daf6c431025eb14b261af8a2fcb006ea5bc465", "typeScriptVersion": "4.5", "_id": "@types/node-schedule@2.1.4", "dist": {"integrity": "sha512-H32fetKLCtKatSkQ6VT9wuiHyrLftuUZZp8g3iU8biYxjQIB/MlV0K4UX/mMOIdGnADCnbe5uzIa5mO6LqVD1g==", "shasum": "4cfa5f3c656da34d8f55bb7c28dae2cdcbe515b5", "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-2.1.4.tgz", "fileCount": 5, "unpackedSize": 9308, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA+SRtlHocsrdQjqBvlz5A07/THxmsliAR4x5d7sowwpAiEA9pIhDMmQ+D7Zvcm8TWuQyTZ1rjQXLdthMyWrRzb6EzA="}], "size": 3263}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-schedule_2.1.4_1700613701287_0.9731366613607568"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-22T00:41:41.430Z", "publish_time": 1700613701430, "_source_registry_name": "default"}, "2.1.5": {"name": "@types/node-schedule", "version": "2.1.5", "description": "TypeScript definitions for node-schedule", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node-schedule", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/cyrilschumacher"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "flowpl", "url": "https://github.com/flowpl"}, {"name": "<PERSON><PERSON>", "githubUsername": "spike008t", "url": "https://github.com/spike008t"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "seohyun0120", "url": "https://github.com/seohyun0120"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/node-schedule"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "f8cc9bf41ae491fdc7a10d87316d4d599dbccd0be20ef63b58c7a35dde799664", "typeScriptVersion": "4.6", "_id": "@types/node-schedule@2.1.5", "dist": {"integrity": "sha512-bN0MiplDBUxNMmlEi4iykjLYD7+Ze3DEevzliCn8WYuDwYSPj/5XFh8wZw+YXPLpLxiNWlIONYiQ67g/vowSMA==", "shasum": "1d458577f3ad94b7a7d342241983f6cef27a772e", "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-2.1.5.tgz", "fileCount": 5, "unpackedSize": 9332, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDUGWaPdzm6mgdNzSUr1oFHYMP391/zcGSQ8AJfEs6JoAIgHRE26uINbGA48Uew+jGsfMqU7brtMcOdRJ8XI6upq2s="}], "size": 3281}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-schedule_2.1.5_1701724011358_0.7580377440805628"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-12-04T21:06:51.613Z", "publish_time": 1701724011613, "_source_registry_name": "default"}, "2.1.6": {"name": "@types/node-schedule", "version": "2.1.6", "description": "TypeScript definitions for node-schedule", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node-schedule", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/cyrilschumacher"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "flowpl", "url": "https://github.com/flowpl"}, {"name": "<PERSON><PERSON>", "githubUsername": "spike008t", "url": "https://github.com/spike008t"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "seohyun0120", "url": "https://github.com/seohyun0120"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/node-schedule"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "b0750eef81e6de0933ff5e17cc018921bde57c5a968de0dcb1d7b44f07f93ef8", "typeScriptVersion": "4.6", "_id": "@types/node-schedule@2.1.6", "dist": {"integrity": "sha512-6AlZSUiNTdaVmH5jXYxX9YgmF1zfOlbjUqw0EllTBmZCnN1R5RR/m/u3No1OiWR05bnQ4jM4/+w4FcGvkAtnKQ==", "shasum": "9bf790a929735080521ef893cba7e4dce9ea14e9", "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-2.1.6.tgz", "fileCount": 5, "unpackedSize": 9324, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDRmpgo6gkP2E2pJYTHTBtanTvElt+RAdExva1kDfULZAIhAJQWhrPup+QspomBdM1raffDXeAjgJEhEOaaLEfxUdBP"}], "size": 3278}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-schedule_2.1.6_1707052028386_0.7263969506086652"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-04T13:07:08.575Z", "publish_time": 1707052028575, "_source_registry_name": "default"}, "2.1.7": {"name": "@types/node-schedule", "version": "2.1.7", "description": "TypeScript definitions for node-schedule", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node-schedule", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/cyrilschumacher"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "flowpl", "url": "https://github.com/flowpl"}, {"name": "<PERSON><PERSON>", "githubUsername": "spike008t", "url": "https://github.com/spike008t"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "seohyun0120", "url": "https://github.com/seohyun0120"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/node-schedule"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "23806f64c3452d6a648d937e3943885f726325c88a28738ba900b3805c456ea0", "typeScriptVersion": "4.7", "_id": "@types/node-schedule@2.1.7", "dist": {"integrity": "sha512-G7Z3R9H7r3TowoH6D2pkzUHPhcJrDF4Jz1JOQ80AX0K2DWTHoN9VC94XzFAPNMdbW9TBzMZ3LjpFi7RYdbxtXA==", "shasum": "79a1e61adc7bbf8d8eaabcef307e07d76cb40d82", "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-2.1.7.tgz", "fileCount": 5, "unpackedSize": 9304, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCkGls4+A3P8s/tdEllu2j/DLxqmWoy4OosGii+W2ze4wIhAJLErrUJXwIFY4lMBhNOiiABnSG+r6ToMT4Yn/sxJA5w"}], "size": 3316}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-schedule_2.1.7_1712257629708_0.9259494290460171"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-04T19:07:09.875Z", "publish_time": 1712257629875, "_source_registry_name": "default"}, "2.1.8": {"name": "@types/node-schedule", "version": "2.1.8", "license": "MIT", "_id": "@types/node-schedule@2.1.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/cyrilschumacher", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/flowpl", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "flowpl"}, {"url": "https://github.com/spike008t", "name": "<PERSON><PERSON>", "githubUsername": "spike008t"}, {"url": "https://github.com/seohyun0120", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "seohyun0120"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node-schedule", "dist": {"shasum": "138e73c9301335d044f33015d1342a602d849ae4", "tarball": "https://registry.npmmirror.com/@types/node-schedule/-/node-schedule-2.1.8.tgz", "fileCount": 5, "integrity": "sha512-k00g6Yj/oUg/CDC+MeLHUzu0+OFxWbIqrFfDiLi6OPKxTujvpv29mHGM8GtKr7B+9Vv92FcK/8mRqi1DK5f3hA==", "signatures": [{"sig": "MEUCICvxJxEjHScxmcP1vz2zsUST9rm1wlOrhObft2lHpQTAAiEA5wim6Xb9kTKNzWYyc9fodC5N9I50UaoZR0nTkXM3PUI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9339, "size": 3330}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/node-schedule"}, "description": "TypeScript definitions for node-schedule", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.1", "_npmOperationalInternal": {"tmp": "tmp/node-schedule_2.1.8_1752256974129_0.8506181511425093", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "82335221db46f6c0ec7f9050ca871836b1ed55be6abedee9e283591ccb0f2365", "_cnpmcore_publish_time": "2025-07-11T18:02:54.341Z", "publish_time": 1752256974341, "_source_registry_name": "default"}}, "contributors": [{"url": "https://github.com/cyrilschumacher", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/flowpl", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "flowpl"}, {"url": "https://github.com/spike008t", "name": "<PERSON><PERSON>", "githubUsername": "spike008t"}, {"url": "https://github.com/seohyun0120", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "seohyun0120"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node-schedule", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/node-schedule"}, "_source_registry_name": "default"}