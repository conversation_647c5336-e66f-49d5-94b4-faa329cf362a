{"_attachments": {}, "_id": "@types/morgan", "_rev": "182961-61f19eb661011c8ed85fb809", "description": "TypeScript definitions for morgan", "dist-tags": {"latest": "1.9.10", "ts2.0": "1.7.32", "ts2.1": "1.7.32", "ts2.2": "1.7.36", "ts2.3": "1.7.37", "ts2.4": "1.7.37", "ts2.5": "1.7.37", "ts2.6": "1.7.37", "ts2.7": "1.7.37", "ts2.8": "1.9.0", "ts2.9": "1.9.0", "ts3.0": "1.9.1", "ts3.1": "1.9.1", "ts3.2": "1.9.2", "ts3.3": "1.9.2", "ts3.4": "1.9.2", "ts3.5": "1.9.2", "ts3.6": "1.9.3", "ts3.7": "1.9.3", "ts3.8": "1.9.3", "ts3.9": "1.9.3", "ts4.0": "1.9.3", "ts4.1": "1.9.3", "ts4.2": "1.9.4", "ts4.3": "1.9.5", "ts4.4": "1.9.5", "ts4.5": "1.9.9", "ts4.6": "1.9.9", "ts4.7": "1.9.9", "ts4.8": "1.9.9", "ts4.9": "1.9.9", "ts5.0": "1.9.9", "ts5.1": "1.9.10", "ts5.2": "1.9.10", "ts5.3": "1.9.10", "ts5.4": "1.9.10", "ts5.5": "1.9.10", "ts5.6": "1.9.10", "ts5.7": "1.9.10", "ts5.8": "1.9.10", "ts5.9": "1.9.10"}, "license": "MIT", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "name": "@types/morgan", "readme": "# Installation\r\n> `npm install --save @types/morgan`\r\n\r\n# Summary\r\nThis package contains type definitions for morgan (https://github.com/expressjs/morgan).\r\n\r\n# Details\r\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/morgan.\r\n\r\n### Additional Details\r\n * Last updated: Sat, 07 Jun 2025 02:15:25 GMT\r\n * Dependencies: [@types/node](https://npmjs.com/package/@types/node)\r\n\r\n# Credits\r\nThese definitions were written by [<PERSON>](https://github.com/staticfunction), [<PERSON>](https://github.com/pscanf), [<PERSON><PERSON><PERSON>](https://github.com/peter<PERSON><PERSON><PERSON><PERSON>), and [<PERSON>](https://github.com/bjohansebas).\r\n", "time": {"created": "2022-01-26T19:19:18.992Z", "modified": "2025-06-07T02:18:38.176Z", "1.9.3": "2021-07-07T00:06:06.699Z", "1.9.2": "2020-10-28T19:04:37.611Z", "1.9.1": "2020-06-12T13:34:41.438Z", "1.9.0": "2020-02-28T18:48:25.637Z", "1.7.37": "2019-08-19T01:10:23.504Z", "1.7.36": "2019-07-18T23:39:32.023Z", "1.7.35": "2017-10-26T19:32:29.266Z", "1.7.34": "2017-10-25T01:12:59.996Z", "1.7.33": "2017-09-18T14:15:28.355Z", "1.7.32": "2016-09-19T17:55:20.424Z", "1.7.31": "2016-08-25T18:50:23.371Z", "1.7.30": "2016-08-19T15:34:25.232Z", "1.7.29": "2016-08-02T16:00:56.126Z", "1.2.28": "2016-07-14T15:26:25.534Z", "1.2.27-alpha": "2016-07-07T18:58:11.991Z", "1.2.26-alpha": "2016-07-04T00:49:10.447Z", "1.2.25-alpha": "2016-07-02T03:02:53.003Z", "1.2.24-alpha": "2016-07-01T23:27:52.703Z", "1.2.23-alpha": "2016-07-01T20:05:10.071Z", "1.2.22-alpha": "2016-05-25T05:30:10.466Z", "1.2.21-alpha": "2016-05-20T20:12:44.487Z", "1.2.16-alpha": "2016-05-19T21:45:47.765Z", "1.2.15-alpha": "2016-05-17T18:22:38.471Z", "1.9.4": "2022-12-30T23:19:13.429Z", "1.9.5": "2023-08-22T18:18:31.360Z", "1.9.6": "2023-09-27T10:24:04.979Z", "1.9.7": "2023-10-18T08:58:53.151Z", "1.9.8": "2023-11-06T19:42:25.944Z", "1.9.9": "2023-11-07T11:23:14.705Z", "1.9.10": "2025-06-07T02:17:59.602Z"}, "versions": {"1.9.3": {"name": "@types/morgan", "version": "1.9.3", "description": "TypeScript definitions for morgan", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/morgan", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/staticfunction", "githubUsername": "staticfunction"}, {"name": "<PERSON>", "url": "https://github.com/pscanf", "githubUsername": "pscanf"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/morgan"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "e7e39719324915f007f2ec64a188e1a3bc597f70c067059ab2bae33fccdd282e", "typeScriptVersion": "3.6", "_id": "@types/morgan@1.9.3", "dist": {"shasum": "ae04180dff02c437312bc0cfb1e2960086b2f540", "size": 3316, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.9.3.tgz", "integrity": "sha512-BiLcfVqGBZCyNCnCH3F4o2GmDLrpy0HeBVnNlyZG4fo88ZiE9SoiBe3C+2ezuwbjlEyT+PDZ17//TAlRxAn75Q=="}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/morgan_1.9.3_1625616366601_0.9826063873995119"}, "_hasShrinkwrap": false, "publish_time": 1625616366699, "_cnpm_publish_time": 1625616366699, "_cnpmcore_publish_time": "2021-12-16T18:19:40.765Z"}, "1.9.2": {"name": "@types/morgan", "version": "1.9.2", "description": "TypeScript definitions for morgan", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/staticfunction", "githubUsername": "staticfunction"}, {"name": "<PERSON>", "url": "https://github.com/pscanf", "githubUsername": "pscanf"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/morgan"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "66d21f984458ac0aa1a5c2fbbb47fcb8df95131b5fc6af512516cbbf960acacd", "typeScriptVersion": "3.2", "_id": "@types/morgan@1.9.2", "dist": {"shasum": "450f958a4d3fb0694a3ba012b09c8106f9a2885e", "size": 3290, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.9.2.tgz", "integrity": "sha512-edtGMEdit146JwwIeyQeHHg9yID4WSolQPxpEorHmN3KuytuCHyn2ELNr5Uxy8SerniFbbkmgKMrGM933am5BQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/morgan_1.9.2_1603911877461_0.07786775040508376"}, "_hasShrinkwrap": false, "publish_time": 1603911877611, "_cnpm_publish_time": 1603911877611, "_cnpmcore_publish_time": "2021-12-16T18:19:40.961Z"}, "1.9.1": {"name": "@types/morgan", "version": "1.9.1", "description": "TypeScript definitions for morgan", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/staticfunction", "githubUsername": "staticfunction"}, {"name": "<PERSON>", "url": "https://github.com/pscanf", "githubUsername": "pscanf"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/morgan"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "7bbccfb32f368b22e0e1d149152f74e6505353158937314d27172b70cd3aab42", "typeScriptVersion": "3.0", "_id": "@types/morgan@1.9.1", "dist": {"shasum": "6457872df95647c1dbc6b3741e8146b71ece74bf", "size": 3278, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.9.1.tgz", "integrity": "sha512-2j5IKrgJpEP6xw/uiVb2Xfga0W0sSVD9JP9t7EZLvpBENdB0OKgcnoKS8IsjNeNnZ/86robdZ61Orl0QCFGOXg=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/morgan_1.9.1_1591968881317_0.12117971945960981"}, "_hasShrinkwrap": false, "publish_time": 1591968881438, "_cnpm_publish_time": 1591968881438, "_cnpmcore_publish_time": "2021-12-16T18:19:41.177Z"}, "1.9.0": {"name": "@types/morgan", "version": "1.9.0", "description": "TypeScript definitions for morgan", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/staticfunction", "githubUsername": "staticfunction"}, {"name": "<PERSON>", "url": "https://github.com/pscanf", "githubUsername": "pscanf"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/morgan"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "d025dcf7289b19f34dcfa237b441b8de6291e486c8ee96e98737e913351e0c68", "typeScriptVersion": "2.8", "_id": "@types/morgan@1.9.0", "dist": {"shasum": "342119ae57fe67d36b91537143fc5aef16c2479f", "size": 3091, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.9.0.tgz", "integrity": "sha512-warrzirh5dlTMaETytBTKR886pRXwr+SMZD87ZE13gLMR8Pzz69SiYFkvoDaii78qGP1iyBIUYz5GiXyryO//A=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/morgan_1.9.0_1582915705505_0.1807390839996641"}, "_hasShrinkwrap": false, "publish_time": 1582915705637, "_cnpm_publish_time": 1582915705637, "_cnpmcore_publish_time": "2021-12-16T18:19:41.426Z"}, "1.7.37": {"name": "@types/morgan", "version": "1.7.37", "description": "TypeScript definitions for morgan", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/staticfunction", "githubUsername": "staticfunction"}, {"name": "<PERSON>", "url": "https://github.com/pscanf", "githubUsername": "pscanf"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/morgan"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "2af2b57f50c4b7dca8ac7e13ab49e48d5d9d052c6cb210a3ba51563a54ef47c7", "typeScriptVersion": "2.3", "_id": "@types/morgan@1.7.37", "dist": {"shasum": "ebdd0b0f0276073f85283bf4f03c7c54284874df", "size": 2974, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.7.37.tgz", "integrity": "sha512-tIdEA10BcHcOumMmUiiYdw8lhiVVq62r0ghih5Xpp4WETkfsMiTUZL4w9jCI502BBOrKhFrAOGml9IeELvVaBA=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/morgan_1.7.37_1566177023414_0.36316686994906533"}, "_hasShrinkwrap": false, "publish_time": 1566177023504, "_cnpm_publish_time": 1566177023504, "_cnpmcore_publish_time": "2021-12-16T18:19:41.636Z"}, "1.7.36": {"name": "@types/morgan", "version": "1.7.36", "description": "TypeScript definitions for morgan", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/staticfunction", "githubUsername": "staticfunction"}, {"name": "<PERSON>", "url": "https://github.com/pscanf", "githubUsername": "pscanf"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/morgan"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "07d2841659cc9f2d422ad7b4915494fb9cab4c946ae39234e63bf5fcbd6cf58a", "typeScriptVersion": "2.2", "_id": "@types/morgan@1.7.36", "dist": {"shasum": "75b2bbf85cb33233a812ceff997efa08b2af13a1", "size": 2976, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.7.36.tgz", "integrity": "sha512-Hc2UfTpnqS3gfGZFPk6aaQf/nwxFHboC/o1O25W29UsENPLv8qd/GJUBqzrBuczgaIS3/vZxZRHTfFF28uFNeQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/morgan_1.7.36_1563493171894_0.744958405403493"}, "_hasShrinkwrap": false, "publish_time": 1563493172023, "_cnpm_publish_time": 1563493172023, "_cnpmcore_publish_time": "2021-12-16T18:19:41.860Z"}, "1.7.35": {"name": "@types/morgan", "version": "1.7.35", "description": "TypeScript definitions for morgan", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/staticfunction", "githubUsername": "staticfunction"}, {"name": "<PERSON>", "url": "https://github.com/pscanf", "githubUsername": "pscanf"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "4327cf701e7606e0ae951aa65ab46453a0e0a912f5150400ab8e1dfe3d64f2cf", "typeScriptVersion": "2.2", "_id": "@types/morgan@1.7.35", "dist": {"shasum": "6358f502931cc2583d7a94248c41518baa688494", "size": 2960, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.7.35.tgz", "integrity": "sha512-E9qFi0seOkdlQnCTPv54brNfGWeFdRaEhI5tSue4pdx/V+xfxvMETsxXhOEcj1cYL+0n/jcTEmj/jD2gjzCwMg=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/morgan-1.7.35.tgz_1509046349073_0.8811825204174966"}, "directories": {}, "publish_time": 1509046349266, "_hasShrinkwrap": false, "_cnpm_publish_time": 1509046349266, "_cnpmcore_publish_time": "2021-12-16T18:19:42.081Z"}, "1.7.34": {"name": "@types/morgan", "version": "1.7.34", "description": "TypeScript definitions for morgan", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/staticfunction", "githubUsername": "staticfunction"}, {"name": "<PERSON>", "url": "https://github.com/pscanf", "githubUsername": "pscanf"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "b83244d52fb3b81b43a1fdff7e98522dc92606e77208a1df719a7cfff254ce39", "typeScriptVersion": "2.3", "_id": "@types/morgan@1.7.34", "dist": {"shasum": "4513d7b08fd18dc5c86db826d48cd39d57a74d5b", "size": 2961, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.7.34.tgz", "integrity": "sha512-CIwxrnVFH4y2WDfo54SNCd5Zy9zofmkoqgTy1xgvolTx65b/bP4IF9rJVd/CmNc43UdtAJnHOJg3W0fLMGW5+g=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/morgan-1.7.34.tgz_1508893979919_0.6767417406663299"}, "directories": {}, "publish_time": 1508893979996, "_hasShrinkwrap": false, "_cnpm_publish_time": 1508893979996, "_cnpmcore_publish_time": "2021-12-16T18:19:42.288Z"}, "1.7.33": {"name": "@types/morgan", "version": "1.7.33", "description": "TypeScript definitions for morgan", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/staticfunction", "githubUsername": "staticfunction"}, {"name": "<PERSON>", "url": "https://github.com/pscanf", "githubUsername": "pscanf"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "487159b68d7c0f74d2c3e2c5f36f95b2e743b59edb5d3f8729a42aa004ce90a0", "typeScriptVersion": "2.2", "_id": "@types/morgan@1.7.33", "dist": {"shasum": "149a1f07335d3e51d01753493e6d7173f94f4766", "size": 2961, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.7.33.tgz", "integrity": "sha512-HBsWVjFJWDbH79Aug/Pyxsc5KWyZs2vkxn7qbo+9a7w2jVur6egGsyJeacDW5Pb5cO+fUl+X5kZaDct8asYp1w=="}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/morgan-1.7.33.tgz_1505744127405_0.714722502278164"}, "directories": {}, "publish_time": 1505744128355, "_hasShrinkwrap": false, "_cnpm_publish_time": 1505744128355, "_cnpmcore_publish_time": "2021-12-16T18:19:42.498Z"}, "1.7.32": {"name": "@types/morgan", "version": "1.7.32", "description": "TypeScript definitions for morgan 1.7.0", "license": "MIT", "author": "<PERSON> <https://github.com/staticfunction>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typings": "index.d.ts", "typesPublisherContentHash": "7e57583ba08632752b9aea35fb25196852229cb99f182324a4aa3e62bf1c759f", "_id": "@types/morgan@1.7.32", "dist": {"shasum": "fab1ece4dae172e1a377d563d33e3634fa04927d", "size": 2364, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.7.32.tgz", "integrity": "sha512-g7b2XSRd8Rq13ahqIKmVmVVVLW2z4aBIj62AH+tezlGccQ8dcPxPbwedghfZ4bNLXT6VkTL2EC0iRjvbh5lsqQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/morgan-1.7.32.tgz_1474307717306_0.046656999504193664"}, "directories": {}, "publish_time": 1474307720424, "_hasShrinkwrap": false, "_cnpm_publish_time": 1474307720424, "_cnpmcore_publish_time": "2021-12-16T18:19:42.713Z"}, "1.7.31": {"name": "@types/morgan", "version": "1.7.31", "description": "TypeScript definitions for morgan 1.7.0", "license": "MIT", "author": "<PERSON> <https://github.com/staticfunction>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "4.0.*"}, "typings": "index.d.ts", "_id": "@types/morgan@1.7.31", "dist": {"shasum": "b0108fc3d57dde395309cecc1458451799487749", "size": 2353, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.7.31.tgz", "integrity": "sha512-lAcz11t7gCULvUIii9+scIFqQTC/B07V//W1C3Vx8rl6weivdfuGiR5v4ZnERzCNxAaC4WlRm2hV9lo1HEu9LQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/morgan-1.7.31.tgz_1472151022780_0.3704446644987911"}, "directories": {}, "publish_time": 1472151023371, "_hasShrinkwrap": false, "_cnpm_publish_time": 1472151023371, "_cnpmcore_publish_time": "2021-12-16T18:19:42.952Z"}, "1.7.30": {"name": "@types/morgan", "version": "1.7.30", "description": "TypeScript definitions for morgan 1.7.0", "license": "MIT", "author": "<PERSON> <https://github.com/staticfunction>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "4.0.*"}, "typings": "index.d.ts", "_id": "@types/morgan@1.7.30", "dist": {"shasum": "96ead4001b772291168ad079b7a0dd99407510fc", "size": 2351, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.7.30.tgz", "integrity": "sha512-y8O+704T7p92zkidGibstXgLtHYvfkwvVHPrHDkjy938MXMi53ygHIyKxZNl+qobt/2qpnZwQuqPEBuP7LTTMw=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/morgan-1.7.30.tgz_1471620863454_0.8997291401028633"}, "directories": {}, "publish_time": 1471620865232, "_hasShrinkwrap": false, "_cnpm_publish_time": 1471620865232, "_cnpmcore_publish_time": "2021-12-16T18:19:43.191Z"}, "1.7.29": {"name": "@types/morgan", "version": "1.7.29", "description": "TypeScript definitions for morgan 1.7.0", "license": "MIT", "author": "<PERSON> <https://github.com/staticfunction>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/express": "4.0.*"}, "typings": "index.d.ts", "_id": "@types/morgan@1.7.29", "dist": {"shasum": "b6aa32c9a91c2af705897d06a52c34abfc568d23", "size": 13824, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.7.29.tgz", "integrity": "sha512-dALbK6pJCpAqhG3AprKu3IC7GtXEasArdNToakwmYuEhYV31t6PrJWtVlMPj1q53l9sQeZ9yI8kVuUX13ZVkxg=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/morgan-1.7.29.tgz_1470153654399_0.5722485696896911"}, "directories": {}, "publish_time": 1470153656126, "_hasShrinkwrap": false, "_cnpm_publish_time": 1470153656126, "_cnpmcore_publish_time": "2021-12-16T18:19:43.354Z"}, "1.2.28": {"name": "@types/morgan", "version": "1.2.28", "description": "TypeScript definitions for morgan 1.2.2", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/staticfunction"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "4.0.*"}, "_id": "@types/morgan@1.2.28", "_shasum": "4c3c9f10d9b529a5f6616da4bc647abd9eef4b92", "_from": "output\\morgan", "_resolved": "file:output\\morgan", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "4c3c9f10d9b529a5f6616da4bc647abd9eef4b92", "size": 1928, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.2.28.tgz", "integrity": "sha512-wjB<PERSON>CUGObFGkLVRG1GScZQDYIO0px5guDOekVfNIW+x0+xwyCJ7Z2E5A3AQbYodhvcmJvoB0YfrN2rwJTDlvWw=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/morgan-1.2.28.tgz_1468509983351_0.7705913588870317"}, "directories": {}, "publish_time": 1468509985534, "_hasShrinkwrap": false, "_cnpm_publish_time": 1468509985534, "_cnpmcore_publish_time": "2021-12-16T18:19:43.546Z"}, "1.2.27-alpha": {"name": "@types/morgan", "version": "1.2.27-alpha", "description": "TypeScript definitions for morgan 1.2.2", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/staticfunction"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "4.0.28-alpha"}, "_id": "@types/morgan@1.2.27-alpha", "_shasum": "ea77a5445d9e2485595fd2b6bb711b0332692827", "_from": "output\\morgan", "_resolved": "file:output\\morgan", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "ea77a5445d9e2485595fd2b6bb711b0332692827", "size": 1933, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.2.27-alpha.tgz", "integrity": "sha512-NXiJIwAFnNs9FjC2SWaquACxku9+dLJ0WokyBhKfXHHxZ8eVvLCSukjUge+uOLgMIUEZZC3qC/EOZjrKOTAstw=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/morgan-1.2.27-alpha.tgz_1467917891554_0.6333202011883259"}, "directories": {}, "publish_time": 1467917891991, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467917891991, "_cnpmcore_publish_time": "2021-12-16T18:19:43.786Z"}, "1.2.26-alpha": {"name": "@types/morgan", "version": "1.2.26-alpha", "description": "TypeScript definitions for morgan 1.2.2", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/staticfunction"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "4.0.27-alpha"}, "_id": "@types/morgan@1.2.26-alpha", "_shasum": "26b057d68f8f48f82485078cd279051f8df089d1", "_from": "output\\morgan", "_resolved": "file:output\\morgan", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "26b057d68f8f48f82485078cd279051f8df089d1", "size": 1930, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.2.26-alpha.tgz", "integrity": "sha512-RykkOHnsanPmO5uRdAgxwRtV6r784xu98YIU/J+RDEULvqvO5mLK7y6feJmb2fwEEnYKRk39wWhykqsPiEcQvA=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/morgan-1.2.26-alpha.tgz_1467593349869_0.07953040604479611"}, "directories": {}, "publish_time": 1467593350447, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467593350447, "_cnpmcore_publish_time": "2021-12-16T18:19:44.032Z"}, "1.2.25-alpha": {"name": "@types/morgan", "version": "1.2.25-alpha", "description": "TypeScript definitions for morgan 1.2.2", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/staticfunction"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "4.0.26-alpha"}, "_id": "@types/morgan@1.2.25-alpha", "_shasum": "161ffc8873067536a2a5a599993e9ba87c2ed548", "_from": "output\\morgan", "_resolved": "file:output\\morgan", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "161ffc8873067536a2a5a599993e9ba87c2ed548", "size": 1936, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.2.25-alpha.tgz", "integrity": "sha512-dgbcWlGXNIUauTCEwPxA2D45v9w+vZ2aamHkEzOwUwkeZ3QWEy8C2zabYbrFf8f2cWDwXFJw4F7VTjsL9x4Rhg=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/morgan-1.2.25-alpha.tgz_1467428569631_0.095291493460536"}, "directories": {}, "publish_time": 1467428573003, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467428573003, "_cnpmcore_publish_time": "2021-12-16T18:19:44.293Z"}, "1.2.24-alpha": {"name": "@types/morgan", "version": "1.2.24-alpha", "description": "TypeScript definitions for morgan 1.2.2", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/staticfunction"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "4.0.25-alpha"}, "_id": "@types/morgan@1.2.24-alpha", "_shasum": "51a193c5c0d0654283eb2a5b1aa06428245e6713", "_from": "output\\morgan", "_resolved": "file:output\\morgan", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "51a193c5c0d0654283eb2a5b1aa06428245e6713", "size": 1933, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.2.24-alpha.tgz", "integrity": "sha512-go9m1De8xgRaCuXVVQN98i12Q6Kya6nD+evaDvDoaTXmzBsPc/I5IA0Jnw80eQTesTYLAMFVtJe4osfDfq/5lQ=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/morgan-1.2.24-alpha.tgz_1467415672196_0.22267330414615571"}, "directories": {}, "publish_time": 1467415672703, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467415672703, "_cnpmcore_publish_time": "2021-12-16T18:19:44.477Z"}, "1.2.23-alpha": {"name": "@types/morgan", "version": "1.2.23-alpha", "description": "TypeScript definitions for morgan 1.2.2", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/staticfunction"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "4.0.*"}, "_id": "@types/morgan@1.2.23-alpha", "_shasum": "501932cfbf996f5f5d7d06e1b815fb96ae1ebefa", "_from": "output\\morgan", "_resolved": "file:output\\morgan", "_npmVersion": "3.9.5", "_nodeVersion": "6.2.2", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "501932cfbf996f5f5d7d06e1b815fb96ae1ebefa", "size": 1923, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.2.23-alpha.tgz", "integrity": "sha512-f2ty5aeSWTNOunwgE5H2hRLPwLvO/SzeR7KBvYT0eU9Pmtbi4/WPpUmxvymu8JsVWElFS+n/PfvYtYIv5WPPkg=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/morgan-1.2.23-alpha.tgz_1467403506221_0.5893252429086715"}, "directories": {}, "publish_time": 1467403510071, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467403510071, "_cnpmcore_publish_time": "2021-12-16T18:19:44.706Z"}, "1.2.22-alpha": {"name": "@types/morgan", "version": "1.2.22-alpha", "description": "TypeScript definitions for morgan 1.2.2", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/staticfunction"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "*"}, "_id": "@types/morgan@1.2.22-alpha", "_shasum": "a0f728e24913a11d5a4916dd306ec05ee2a02187", "_from": "output\\morgan", "_resolved": "file:output\\morgan", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "a0f728e24913a11d5a4916dd306ec05ee2a02187", "size": 1905, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.2.22-alpha.tgz", "integrity": "sha512-0CXUdzu37IUDawmsLo0oPvHCIAV922poQ+AA87qvucNXyeL4F3J5m+11+7ha8SPhjHo4Hi2XXYss1ycymS2xag=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/morgan-1.2.22-alpha.tgz_1464154210023_0.5388690675608814"}, "directories": {}, "publish_time": 1464154210466, "_hasShrinkwrap": false, "_cnpm_publish_time": 1464154210466, "_cnpmcore_publish_time": "2021-12-16T18:19:45.016Z"}, "1.2.21-alpha": {"name": "@types/morgan", "version": "1.2.21-alpha", "description": "TypeScript definitions for morgan 1.2.2", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/staticfunction"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/express": "*"}, "_id": "@types/morgan@1.2.21-alpha", "_shasum": "def07fb9e8acd391d1e2917f1d8dbc7bb7ad2817", "_from": "output\\morgan", "_resolved": "file:output\\morgan", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "def07fb9e8acd391d1e2917f1d8dbc7bb7ad2817", "size": 1904, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.2.21-alpha.tgz", "integrity": "sha512-8wjDLWspC9koNFQAyUoPJQmCLbKQFpIRdcvOu9qQRkTW2vEc8uF7qZs7T+65inrnPxn9EcNfJJcFx796IKzt0Q=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/morgan-1.2.21-alpha.tgz_1463775162426_0.8410159193444997"}, "directories": {}, "publish_time": 1463775164487, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463775164487, "_cnpmcore_publish_time": "2021-12-16T18:19:45.260Z"}, "1.2.16-alpha": {"name": "@types/morgan", "version": "1.2.16-alpha", "description": "Type definitions for morgan 1.2.2 from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/staticfunction"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"equire('express'": "*"}, "_id": "@types/morgan@1.2.16-alpha", "_shasum": "f58d12e8cc8d3ed0f863a1e3d17be7289e456189", "_from": "output\\morgan", "_resolved": "file:output\\morgan", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "f58d12e8cc8d3ed0f863a1e3d17be7289e456189", "size": 1910, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.2.16-alpha.tgz", "integrity": "sha512-OfUdFf172odMvy9NvXsuATMInQ0Qh+Hr4xVSVws0SOcxussoz7zj+AYKZ3VM1jqJbXGRTCdqMMCkjSV8jr0Y3A=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/morgan-1.2.16-alpha.tgz_1463694345220_0.6801850150804967"}, "directories": {}, "publish_time": 1463694347765, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463694347765, "_cnpmcore_publish_time": "2021-12-16T18:19:45.448Z"}, "1.2.15-alpha": {"name": "@types/morgan", "version": "1.2.15-alpha", "description": "Type definitions for morgan 1.2.2 from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/staticfunction"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"equire('express'": "*"}, "_id": "@types/morgan@1.2.15-alpha", "_shasum": "73d932bff1f7227009016358c75107891363efd1", "_from": "output\\morgan", "_resolved": "file:output\\morgan", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "73d932bff1f7227009016358c75107891363efd1", "size": 1901, "noattachment": false, "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.2.15-alpha.tgz", "integrity": "sha512-uJLrgmkPYMKcxEpL5dSF7eASjpa5l1eenZw/Ugrdar50XGLHe/S/UmsjjQ1O/CxFgHZzWEHw7+zWGouqIKGuRg=="}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/morgan-1.2.15-alpha.tgz_1463509357865_0.7523995572701097"}, "directories": {}, "publish_time": 1463509358471, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463509358471, "_cnpmcore_publish_time": "2021-12-16T18:19:45.641Z"}, "1.9.4": {"name": "@types/morgan", "version": "1.9.4", "description": "TypeScript definitions for morgan", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/morgan", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/staticfunction", "githubUsername": "staticfunction"}, {"name": "<PERSON>", "url": "https://github.com/pscanf", "githubUsername": "pscanf"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/morgan"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "79465016da4e8a6626ad2f824fa2502da2be6a2d5e05462f0260197b6ea457eb", "typeScriptVersion": "4.2", "_id": "@types/morgan@1.9.4", "dist": {"integrity": "sha512-cXoc4k+6+YAllH3ZHmx4hf7La1dzUk6keTR4bF4b4Sc0mZxU/zK4wO7l+ZzezXm/jkYj/qC+uYGZrarZdIVvyQ==", "shasum": "99965ad2bdc7c5cee28d8ce95cfa7300b19ea562", "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.9.4.tgz", "fileCount": 5, "unpackedSize": 13795, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEyO0vmhPdRzNqBBNM0Lay5RXfrZUzICXrZ9gBTNWG+zAiBEA5b8lJhm9UNh1DBV0iGEgo1shavG1K7rxPCv4uxlxw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjr3HxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqIPhAAivgdW3xHpaRuqzDA0wt8hse9yriOc3e7RSD2PG0M22QlNCgy\r\n5tdJmXuUgKZ0U+hu3wTAh95hNR5lZliQvyo/2k1SUQFLFihf0zQQFTeYBOkA\r\n0pme4MI+bozX9IWctlmpae4rLHMrrDcqUaF7v94UTQvrug4wY3z369+QlQKr\r\nye5SvcPsK60afZ+xKHy6V9V57QmLhiLOHNUAkvKVY5Gvaoo/L4m69CgN0m2t\r\nvkRtzlyIBf6qnLGY5N2z/72IVMNObuDvFYGCNy0IqQSW4t6tYNLe/+Kr61Tm\r\n1/FpdBp68GyCmyqukEF8vVsLsOavAltZruSonj/y6uTOXvVEo44g8diMcx6z\r\n3mlwBY4lIfXjJW79mF6zUm4LFGcl0zuw9GXpYI0Q9lIqC8rCRUL1WV/rbVJQ\r\n5j5IE5cpl4GvTzncjKvkL231Lt1IPCcZrvewpwKG2HzpA+A/8CkcdlvkHb3l\r\nFwUAqXZN/6g8Y4gKmhJ1TOZGcVnsktxZCNhsK9iqzwndgyI+K/5R2dXwRImg\r\ngD156G1s8oLmmmuY8PW20iJG4uwd+r3Hdz0BqiBSCy+v/yZjurAJIYU/si+s\r\nzBKxbQHTOh2vKig23kdUU9f+N8ocCq5fPcudKPlXhxt/Mdq//s4+YAqRO5o5\r\nMPlX+0M4xQSVXwKb5YPLGMN0a1SaxtaNH7s=\r\n=M4kx\r\n-----END PGP SIGNATURE-----\r\n", "size": 3353}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/morgan_1.9.4_1672442353218_0.0841108808778106"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-30T23:19:17.349Z"}, "1.9.5": {"name": "@types/morgan", "version": "1.9.5", "description": "TypeScript definitions for morgan", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/morgan", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/staticfunction", "githubUsername": "staticfunction"}, {"name": "<PERSON>", "url": "https://github.com/pscanf", "githubUsername": "pscanf"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/morgan"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "807416d481c4584df944c5a403c05dbda7877c94ff5e461127fc8a9e784e2399", "typeScriptVersion": "4.3", "_id": "@types/morgan@1.9.5", "dist": {"integrity": "sha512-5TgfIWm0lcTGnbCZExwc19dCOMOMmAiiBZQj8Ko3NRxsVDgRxf+AEGRQTqNVA5Yh2xfdWp4clbAEMbYP+jkOqg==", "shasum": "dda7388af1e67863f9fa4496d6d63f6f53334306", "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.9.5.tgz", "fileCount": 5, "unpackedSize": 13829, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD0SDqcWaLoUxBexO9JuWz0tqWVZF7Kn8mRaRTWqQkcgwIgVP4sM0L4wODKvll4jX0Xkph0uB3rS7hHZcb/ji0xnuQ="}], "size": 3367}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/morgan_1.9.5_1692728311143_0.6616345235342749"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-22T18:18:31.360Z", "publish_time": 1692728311360, "_source_registry_name": "default"}, "1.9.6": {"name": "@types/morgan", "version": "1.9.6", "description": "TypeScript definitions for morgan", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/morgan", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/staticfunction", "githubUsername": "staticfunction"}, {"name": "<PERSON>", "url": "https://github.com/pscanf", "githubUsername": "pscanf"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/morgan"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "60315abadd5b525e9d39c8f511a5c38b578fb77f6492892ec8d0fd00f1250671", "typeScriptVersion": "4.5", "_id": "@types/morgan@1.9.6", "dist": {"integrity": "sha512-xfKogz5WcKww2DAiVT9zxMgrqQt+Shq8tDVeLT+otoj6dJnkRkyJxMF51mHtUc3JCPKGk5x1EBU0buuGpfftlQ==", "shasum": "108aee8914ec2ebfc3710ba6ef67e120b6a20dfc", "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.9.6.tgz", "fileCount": 5, "unpackedSize": 14084, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD+Ve8spH3/QeZqv7+uWFFxfL1eB43MHlI0EJ7HRE5k+wIhAN+OL9UvdRv+cejZqjBG/LIUMAyN1/pi/z9XcsOwk8l/"}], "size": 3408}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/morgan_1.9.6_1695810244799_0.6644049028284718"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-27T10:24:04.979Z", "publish_time": 1695810244979, "_source_registry_name": "default"}, "1.9.7": {"name": "@types/morgan", "version": "1.9.7", "description": "TypeScript definitions for morgan", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/morgan", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "staticfunction", "url": "https://github.com/staticfunction"}, {"name": "<PERSON>", "githubUsername": "pscanf", "url": "https://github.com/pscanf"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/morgan"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "61d63181c79b679062751da72540e88a357f883a7ea47f5c3fd53707a4282c00", "typeScriptVersion": "4.5", "_id": "@types/morgan@1.9.7", "dist": {"integrity": "sha512-4sJFBUBrIZkP5EvMm1L6VCXp3SQe8dnXqlVpe1jsmTjS1JQVmSjnpMNs8DosQd6omBi/K7BSKJ6z/Mc3ki0K9g==", "shasum": "ba1e980841be06cd164eedfba7e3e1e2f4d0c911", "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.9.7.tgz", "fileCount": 5, "unpackedSize": 13692, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIACwy1Jj7qjEIEjM/nl0gxJFifzTZK83uikBscaJhYI6AiAsjz14MZwc7KAL12V77yAscQYnTkhWMZS7EzW+ZBQOCg=="}], "size": 3332}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/morgan_1.9.7_1697619532979_0.09003118738019844"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-18T08:58:53.151Z", "publish_time": 1697619533151, "_source_registry_name": "default"}, "1.9.8": {"name": "@types/morgan", "version": "1.9.8", "description": "TypeScript definitions for morgan", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/morgan", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "staticfunction", "url": "https://github.com/staticfunction"}, {"name": "<PERSON>", "githubUsername": "pscanf", "url": "https://github.com/pscanf"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/morgan"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "926ca381b644eb392603d2bb1e362f9f694638f1431a31db66878d1a7cc19813", "typeScriptVersion": "4.5", "_id": "@types/morgan@1.9.8", "dist": {"integrity": "sha512-IS65B0+LgfdI61N6pn634vGc3ePn7ttt1iCiEKVk22DpGHMVpMZ7rbUxbjyOSqXI7zYi/nUGrZRD+PpvGlamnA==", "shasum": "9e967ede43256722b18debed8a706ef577243b2c", "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.9.8.tgz", "fileCount": 5, "unpackedSize": 13852, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEvooxXgirDanf5hfr1MWUviebSr7bWYRVu4U/n6Qz94AiBQaCB1LD/ZDAuuJR5kiWA3BUewEQPD7aECB47nGz2hGw=="}], "size": 3332}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/morgan_1.9.8_1699299745769_0.14934496960026888"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-06T19:42:25.944Z", "publish_time": 1699299745944, "_source_registry_name": "default"}, "1.9.9": {"name": "@types/morgan", "version": "1.9.9", "description": "TypeScript definitions for morgan", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/morgan", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "staticfunction", "url": "https://github.com/staticfunction"}, {"name": "<PERSON>", "githubUsername": "pscanf", "url": "https://github.com/pscanf"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/morgan"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "747ad423ca0627a1b0edf2df57f410014fcdd9d97eba8cbf192abb0cf7b22a5c", "typeScriptVersion": "4.5", "_id": "@types/morgan@1.9.9", "dist": {"integrity": "sha512-iRYSDKVaC6FkGSpEVVIvrRGw0DfJMiQzIn3qr2G5B3C//AWkulhXgaBd7tS9/J79GWSYMTHGs7PfI5b3Y8m+RQ==", "shasum": "d60dec3979e16c203a000159daa07d3fb7270d7f", "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.9.9.tgz", "fileCount": 5, "unpackedSize": 13852, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICGlR1wAynrwGXesfZNdrHmhERrc+MmuRnuxUbDAJrOcAiBiqHuIHbY8IGpMRsa4DrEc0iv/DnQWpE2/+hpXUfZ+pQ=="}], "size": 3336}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/morgan_1.9.9_1699356194499_0.7356549277867546"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-07T11:23:14.705Z", "publish_time": 1699356194705, "_source_registry_name": "default"}, "1.9.10": {"name": "@types/morgan", "version": "1.9.10", "license": "MIT", "_id": "@types/morgan@1.9.10", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/staticfunction", "name": "<PERSON>", "githubUsername": "staticfunction"}, {"url": "https://github.com/pscanf", "name": "<PERSON>", "githubUsername": "pscanf"}, {"url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/b<PERSON><PERSON><PERSON>bas", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/morgan", "dist": {"shasum": "725c15d95a5e6150237524cd713bc2d68f9edf1a", "tarball": "https://registry.npmmirror.com/@types/morgan/-/morgan-1.9.10.tgz", "fileCount": 5, "integrity": "sha512-sS4A1zheMvsADRVfT0lYbJ4S9lmsey8Zo2F7cnbYjWHP67Q0AwMYuuzLlkIM2N8gAbb9cubhIVFwcIN2XyYCkA==", "signatures": [{"sig": "MEUCIQDuQNsEx7voZzbosVMwoUmMUj6yOC7wn4x8Gpx8u0bdvgIgcyT+Rp7eeXPbfkQX6oI3tjH9BXIlFqv5SRP3VlI6rgo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14093, "size": 3377}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/morgan"}, "description": "TypeScript definitions for morgan", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.1", "_npmOperationalInternal": {"tmp": "tmp/morgan_1.9.10_1749262679441_0.41000443629370475", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "14873954e80ecab6fc13b0143fc6c38093e9c8f0355d7dd413a9217bc8bd776a", "_cnpmcore_publish_time": "2025-06-07T02:17:59.602Z", "publish_time": 1749262679602, "_source_registry_name": "default"}}, "contributors": [{"url": "https://github.com/staticfunction", "name": "<PERSON>", "githubUsername": "staticfunction"}, {"url": "https://github.com/pscanf", "name": "<PERSON>", "githubUsername": "pscanf"}, {"url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/b<PERSON><PERSON><PERSON>bas", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/morgan", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/morgan"}, "_source_registry_name": "default"}