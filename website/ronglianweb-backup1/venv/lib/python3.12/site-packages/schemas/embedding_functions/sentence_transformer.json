{"$schema": "http://json-schema.org/draft-07/schema#", "title": "SentenceTransformer Embedding Function Schema", "description": "Schema for the SentenceTransformer embedding function configuration", "version": "1.0.0", "type": "object", "properties": {"model_name": {"type": "string", "description": "Identifier of the SentenceTransformer model"}, "device": {"type": "string", "description": "Device used for computation"}, "normalize_embeddings": {"type": "boolean", "description": "Whether to normalize returned vectors"}, "kwargs": {"type": "object", "description": "Additional arguments to pass to the SentenceTransformer model", "additionalProperties": {"type": ["string", "integer", "number", "boolean", "array", "object"]}}}, "required": ["model_name", "device", "normalize_embeddings"], "additionalProperties": false}