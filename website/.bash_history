ping www.baidu.com
ping *********
mkdir -p ~/.nvm
sudo chown -R $USER:$USER ~/.nvm
rm -rf ~/.nvm
curl -o- https://gitee.com/mirrors/nvm/raw/master/install.sh | bash
rm -rf ~/.nvm
curl -o- https://gitee.com/mirrors/nvm/raw/master/install.sh | bash
sudo curl -o- https://gitee.com/mirrors/nvm/raw/master/install.sh | bash
curl -o- https://cdn.jsdelivr.net/gh/nvm-sh/nvm@v0.39.7/install.sh | bash
curl -o- https://hub.fgit.cf/nvm-sh/nvm/raw/v0.39.7/install.sh | bash
export NVM_DIR="$HOME/.nvm"
source ~/.nvm/nvm.sh
export NVM_DIR="$HOME/.nvm"
source ~/.nvm/nvm.sh
echo 'source ~/.nvm/nvm.sh' >> ~/.bashrc
ls -al ~/.nvm
# 检查bashrc配置
grep -i nvm ~/.bashrc
# 重新加载配置
source ~/.bashrc
# 验证nvm版本
nvm --version
# 查看node可用版本
nvm ls-remote --lts
mkdir -p ~/.nvm
sudo chown -R $USER:$USER ~/.nvm
curl -o install.sh https://cdn.jsdelivr.net/gh/nvm-sh/nvm@v0.39.7/install.sh
bash install.sh
tree -L 3 ~/.nvm
# 查看安装日志
cat /tmp/nvm-install.log
ls -ld ~
sudo chown -R $USER:$USER $HOME
ls -ld ~
sudo chown -R $USER:$USER $HOME
npm run dev
rm -rf node_modules package-lock.json
npm install --save-dev vite @vitejs/plugin-vue
sudo npm install --save-dev vite @vitejs/plugin-vue
npm install -g vite
sudo npm install -g vite
ls node_modules/.bin/vite
npm run dev
npm config set registry https://registry.npmmirror.com/
npm cache clean --force
npm install vite @vitejs/plugin-vue --save-dev
sudo rm -rf node_modules
sudo chown -R $USER:$USER .
npm install
ls -al node_modules/.vite
sudo rm -rf node_modules
sudo chown -R $USER:$USER .
npm install
ls -al node_modules/.vite
npm install vite @vitejs/plugin-vue --save-dev
npm install tinymce@6.8.3 @tinymce/tinymce-vue@6.1.0 @types/node@20.11.0 --save-dev
npm config set strict-ssl false
npm install vite @vitejs/plugin-vue --save-dev
sudo apt-get install --reinstall ca-certificates
npm config set registry https://registry.npmmirror.com/
npm config set electron_mirror https://cdn.npmmirror.com/binaries/electron/
npm config set sass_binary_site https://cdn.npmmirror.com/binaries/node-sass
npm config set @electron:mirror="https://cdn.npmmirror.com/binaries/electron/"
npm config set @sass:binary_site="https://cdn.npmmirror.com/binaries/node-sass"
npm config set electron_mirror="https://cdn.npmmirror.com/binaries/electron/"
npm config set sass_binary_site="https://cdn.npmmirror.com/binaries/node-sass"
npm config get electron_mirror
npm config get sass_binary_site
rm -rf node_modules package-lock.json
npm cache clean --force
npm install --registry=https://registry.npmmirror.com
npm config set electron_mirror="https://cdn.npmmirror.com/binaries/electron/"
npm config set sass_binary_site="https://cdn.npmmirror.com/binaries/node-sass"
npm -v
# 要求版本≥6.0.0
npm config set electron_mirror="https://cdn.npmmirror.com/binaries/electron/"
npm config set sass_binary_site="https://cdn.npmmirror.com/binaries/node-sass"
echo 'electron_mirror="https://cdn.npmmirror.com/binaries/electron/"' >> ~/.npmrc
echo 'sass_binary_site="https://cdn.npmmirror.com/binaries/node-sass"' >> ~/.npmrc
npm config set electron_mirror="https://cdn.npmmirror.com/binaries/electron/"
npm config set sass_binary_site="https://cdn.npmmirror.com/binaries/node-sass"
npm config set electron_mirror="https://cdn.npmmirror.com/binaries/electron/"
npm config set sass_binary_site="https://cdn.npmmirror.com/binaries/node-sass"
cd /home/<USER>/rlxwglpt/
find . -path ./project_development.md -prune -o -exec rm -rf {} +
npm create vite@latest . -- --template vue-ts
npm config set registry https://registry.npmmirror.com
npm config set electron_mirror https://cdn.npmmirror.com/binaries/electron/
npm config set sass_binary_site https://cdn.npmmirror.com/binaries/node-sass
npm install vue@3.4.21 pinia@2.1.7 element-plus@2.5.6 vue-router@4.2.5 axios@1.6.8 echarts@5.4.3 --save
npm install @vitejs/plugin-vue@5.0.4 @types/node@20.11.0 --save-dev
npm install tinymce@6.8.3 @tinymce/tinymce-vue@6.1.0 --save
mkdir -p src/{api,components,router,store,views/news,utils}
# 创建关键文件
touch src/router/index.ts src/store/index.ts src/api/news.ts
touch src/views/news/NewsList.vue src/views/news/NewsEditor.vue
npm config delete electron_mirror
npm config delete sass_binary_site
npm config set @electron:mirror https://cdn.npmmirror.com/binaries/electron/
npm config set sass_binary_site https://cdn.npmmirror.com/binaries/node-sass/
cp .npmrc .npmrc.bak 2>/dev/null
cat > .npmrc << 'EOF'
registry=https://registry.npmmirror.com/
electron_mirror=https://cdn.npmmirror.com/binaries/electron/
sass_binary_site=https://cdn.npmmirror.com/binaries/node-sass/
EOF

# 2. 清理缓存和旧依赖
npm cache clean --force
rm -rf node_modules package-lock.json
# 3. 重新安装依赖（使用配置好的镜像源）
npm install
# 4. 验证安装结果
ls node_modules | grep electron
ls node_modules | grep sass
node -v  # 应显示v18.x.x
# 确认Vite版本
npm list vite
npm list electron sass
npm install electron@28.2.0 node-sass@9.0.0 --save-dev
# 2. 验证安装结果
npm list electron node-sass
# 3. 若仍失败，强制清理并重新安装
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
npm install electron@28.2.0 node-sass@9.0.0 --save-dev
cd /home/<USER>/rlxwglpt/
# 安全删除除目标文件外的所有内容
find . -maxdepth 1 -mindepth 1 -path ./project_development.md -prune -o -exec rm -rf {} +
ls -la
# 应只显示project_development.md文件
npm create vite@latest . -- --template vue-ts --registry=https://registry.npmmirror.com
npm install vue@3.4.21 pinia@2.1.7 element-plus@2.5.6 vue-router@4.2.5 axios@1.6.8 tinymce@6.8.3 --registry=https://registry.npmmirror.com
cat > .npmrc << 'EOF'
registry=https://registry.npmmirror.com
@electron:mirror=https://cdn.npmmirror.com/binaries/electron/
sass_binary_site=https://cdn.npmmirror.com/binaries/node-sass/
EOF

npm run dev
npm run build
node -v
nvm install --lts
nvm use --lts
# 或者使用apt（Ubuntu/Debian）
sudo apt update
rm -rf node_modules package-lock.json
# 重新安装依赖
npm install --registry=https://registry.npmmirror.com
npm run dev
node -v
rm -rf node_modules package-lock.json
npm cache clean --force
# 使用国内镜像重新安装
npm install --registry=https://registry.npmmirror.com
node -v  # 应显示v18.x.x
# 确认Vite版本
npm list vite
npm run dev
node -v
# 预期输出：v18.x.x 或更高版本
# 检查npm版本
npm -v
# 预期输出：8.x.x 或更高版本
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt-get install -y nodejs
# 验证安装
node -v
npm cache clean --force
# 删除node_modules和package-lock.json
rm -rf node_modules package-lock.json
# 使用国内镜像重新安装依赖
npm install --registry=https://registry.npmmirror.com
npm list vite
# 如果Vite版本过高，降级到5.0.x稳定版
npm install vite@5.0.12 --registry=https://registry.npmmirror.com
nvm ls
# 明确指定使用Node.js 18
nvm use 18
# 再次尝试启动
npm run dev
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
# 安装Node.js 18
apt-get install -y nodejs
node -v
# 预期输出：v18.x.x
# 检查npm版本
npm -v
# 预期输出：8.x.x 或更高版本
npm run dev
node -v
# Expected output: v18.x.x or higher
# If still showing older version, check Node.js path
which node
# This should show a path containing 'node-v18'
sudo apt-get purge nodejs npm -y
cd /home/<USER>/rlxwglpt
# Remove node_modules and cache
rm -rf node_modules package-lock.json ~/.npm/_cacache
# Install with version constraints
npm install --registry=https://registry.npmmirror.com   vue@3.4.21   pinia@2.1.7   element-plus@2.5.6   vue-router@4.2.5   axios@1.6.8   tinymce@6.8.3   vite@5.0.12
node -v
# Check npm version
npm -v
# Check Vite version
npx vite --version
npm run dev
node -v && npm -v && npx vite --version && echo $PATH
node -v  # Should show v18.x.x
npm -v   # Should show 8.x.x or higher
npm list vite @vitejs/plugin-vue
npm cache clean --force
rm -rf node_modules package-lock.json
# Set npmmirror registry and reinstall
npm config set registry https://registry.npmmirror.com/
npm install
# Verify Vite version
npm list vite
npm list -g vite
# If found, remove global Vite
npm uninstall -g vite
npm install vite@5.0.12 @vitejs/plugin-vue@5.0.4
npm run dev
npm install --save-dev @types/axios
npm install --save-dev @element-plus/icons-vue
# 确保Vue类型正确
npm install --save-dev @vuedx/typescript-plugin-vue
npm install @types/axios @element-plus/icons-vue
npm install --save-dev @vuedx/typescript-plugin-vue
npm run dev
npm install --save-dev @types/node @types/estree @vue/tsconfig
npm install --save-dev @types/web-bluetooth
npm install --save-dev @types/estree @types/web-bluetooth @vue/tsconfig
npm install --save-dev @vue/tsconfig
npm update @types/estree @types/web-bluetooth
npm run dev
npm install @element-plus/icons-vue
npm run dev
npm update @element-plus/icons-vue
q
ls
pwd
exit
ls
pwd
exit
ls
df -m
mv ronglianweb/ ronglianweb-backup1
tar xvf web2.tar 
npm update @element-plus/icons-vue
npm run dev
touch .env
mv src/api/Untitled-3 .env
npm run dev
npm run dev
npm install --save-dev @types/cors
mkdir backend
cd backend
# 初始化项目
npm init -y
# 安装核心依赖
npm install express cors dotenv mongoose axios
npm install --save-dev typescript ts-node @types/express @types/node nodemon
# 初始化TypeScript配置
npx tsc --init
npm run dev
cd backend
npm install --save-dev @types/cors
# 2. 修改路由文件导出方式（如上所示）
# 3. 重新启动后端服务
npm run dev
cd backend && npm install --save-dev @types/cors
cd backend && npm run dev
cd /home/<USER>/rlxwglpt/backend && npm run dev
cd ronglianweb
./run.sh stop
sudo lsof -i 80
sudo lsof -i :80
cd /
ls -l /etc/nginx/
ls -l /etc/nginx/sites-enabled/
netstat -tuln | grep :8080
ls -l /home/<USER>/ronglianweb/
cd /home/<USER>/ronglianweb
nohup node ./bin/www > app.log 2>&1 &
cd /
netstat -tuln | grep :8080
netstat -tulnp | grep :3000
./run.sh start
sudo reboot
ls
./run.sh start
./run.sh stop
lsof -i :80
./run.sh start
./run.sh status
sudo apt-get update && sudo apt-get install -y nginx
CONFIG_CONTENT="server {\n    listen 80;\n    server_name localhost;\n\n    location / {\n        proxy_pass http://localhost:3000;\n        proxy_http_version 1.1;\n        proxy_set_header Upgrade \$http_upgrade;\n        proxy_set_header Connection 'upgrade';\n        proxy_set_header Host \$host;\n        proxy_set_header X-Real-IP \$remote_addr;\n        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto \$scheme;\n        proxy_cache_bypass \$http_upgrade;\n    }\n}" && echo "$CONFIG_CONTENT" | sudo tee /etc/nginx/sites-available/ronglianweb > /dev/null
sudo ln -s /etc/nginx/sites-available/ronglianweb /etc/nginx/sites-enabled/ && sudo systemctl reload nginx
sudo systemctl reload nginx
sudo systemctl start nginx
systemctl status nginx.service
CONFIG_CONTENT="server {\n    listen 80;\n    server_name localhost;\n\n    location / {\n        proxy_pass http://localhost:3000;\n        proxy_http_version 1.1;\n        proxy_set_header Upgrade \$http_upgrade;\n        proxy_set_header Connection 'upgrade';\n        proxy_set_header Host \$host;\n        proxy_set_header X-Real-IP \$remote_addr;\n        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto \$scheme;\n        proxy_cache_bypass \$http_upgrade;\n    }\n}" && echo -e "$CONFIG_CONTENT" | sudo tee /etc/nginx/sites-available/ronglianweb > /dev/null
sudo systemctl start nginx
systemctl status nginx.service
sudo lsof -i :80
sudo pkill nginx && sudo systemctl start nginx
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash - && sudo apt-get install -y nodejs
sudo apt-get install -y mongodb redis-server
sudo apt-get install gnupg curl && curl -fsSL https://pgp.mongodb.com/server-6.0.asc | sudo gpg -o /usr/share/keyrings/mongodb-server-6.0.gpg --dearmor
echo "deb [ arch=amd64,arm64 signed-by=/usr/share/keyrings/mongodb-server-6.0.gpg ] https://repo.mongodb.org/apt/ubuntu jammy/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list
sudo apt-get update && sudo apt-get install -y mongodb-org
sudo apt-get install -y redis-server && sudo systemctl start mongod && sudo systemctl enable mongod && sudo systemctl start redis-server && sudo systemctl enable redis-server
npm install
sudo npm install pm2 -g
pm2 start npm --name "ronglianweb" -- start
pm2 startup
sudo env PATH=$PATH:/usr/local/bin /usr/local/lib/node_modules/pm2/bin/pm2 startup systemd -u website --hp /home/<USER>
pm2 save
chmod +x run.sh
./run.sh status
sudo visudo
echo 'website ALL=(ALL) NOPASSWD: ALL' | sudo tee /etc/sudoers.d/website-nopasswd-all
./run.sh status
git status
git init
git add .
git config --global user.email "<EMAIL>" && git config --global user.name "Your Name" && git commit -m "Initial commit: Deploy application with Nginx and PM2"
pwd
cd ronglianweb
ls
./run.sh status
./run.sh stop
poweroff
sudo poweroff
cd ronglianweb
./run.sh start
./run.sh status
cd ronglianweb
./run.sh status
./run.sh start
cd.
cd..
..
.
pwd
cd ronglianweb
ls
./run.sh stop
poweroff
sudo poweroff
ip addr show
ping *********
ip addr show
sudo apt update
sudo apt upgrade
pwd
ls
cd ronglianweb
./run.sh status
cd ..
ls
ls *.tar
exit
pwd
ls
mv ronglianweb rlwebtest
chmod +x redeploy-dl380.sh && ./redeploy-dl380.sh
./redeploy-dl380.sh
exit
node --version
cd /home/<USER>/ronglianweb
pwd
ls -la
npm --version
# 检查是否有Node.js进程在运行
ps aux | grep node
# 检查端口3000是否被占用
netstat -tlnp | grep :3000
# 检查所有相关端口
netstat -tlnp | grep -E ":(80|443|3000)"
npm install --production
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs
node --version
npm --versison
npm --version
node --version
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
apt-get install nodejs -y
sudo apt-get install nodejs -y
node --version
cd /home/<USER>/ronglianweb
pwd
ls -la package.json app.js bin/www
node --version
npm --version
ls -d node_modules
ls *sh
./run.sh start
rm -rf node_modules package-lock.json 
npm install
npm install express-session
npm install express jade body-parser cookie-parser morgan serve-favicon
ls -la node_modules/express-session
npm list --depth=0
./run.sh status
./run.sh stop
ls .bashrc 
vi .bashrc 
cd .nvm/
ls
pwd
ls
cd ..
rm -rf .nvm
vi .bashrc 
node --version
sshpass
sudo apt install sshpass
sshpass
man sshpass
poweroff
sudo poweroff
sudo apt install sshpass
rm -rf .nvm
man sshpass
cd ronglianweb
./run.sh status
./run.sh start
./run.sh status
./run.sh start
itatus
./run.sh statys
cd ronglianweb
./run.sh statys
stop
./run.sh stop
./run.sh status
./run.sh statys
./run.sh status
./run.sh start
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
id
cd ~
ls
pwd
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
curl -I -k https://*********/news/index
curl -I -k https://*********/admin/login
curl -I http://*********:3000
grep -n "localhost" run.sh
./run.sh status
./run.sh
cd ronglianweb
./run.sh status
cd ronglianweb
./run.sh status
..
pwd
cd ronglianweb
./run.sh status
./run.sh start
echo "测试更新后的访问地址..." && echo "1. 测试HTTP服务:" && curl -I http://********* && echo "2. 测试Vue 3演示:" && curl -I http://*********/vue3-demo
cd website/ronglianweb && pwd
sudo cat /etc/nginx/sites-enabled/ronglianweb
sudo ls -la /etc/nginx/sites-enabled/
sudo cat /etc/nginx/sites-enabled/ronglian-main
sudo cp nginx-ronglian-https.conf /etc/nginx/sites-available/ronglian-main
sudo nginx -t
sudo cp nginx-ronglian-https.conf /etc/nginx/sites-available/ronglian-main
sudo nginx -t
sudo systemctl reload nginx
echo "测试HTTPS访问:" && curl -I -k https://*********
echo "测试新闻页面:" && curl -I -k https://*********/news/index
more /etc/hosts
sudo vi /etc/hosts
ping dl380
./health-check-word.sh
find . -name "*.js" -o -name "*.jade" -o -name "*.json" | xargs grep -l "荣之联" 2>/dev/null | head -10
./run.sh restart
cp public/plugins/admin/js/newsEdit.js public/plugins/admin/js/newsEdit-original.js
cp public/plugins/admin/js/newsEdit-word.js public/plugins/admin/js/newsEdit.js
./run.sh restart
chmod +x health-check-word.sh && ./health-check-word.sh
./run.sh status
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
