PORT=5174 npm run dev
sudo chown -R $USER:$USER /home/<USER>/projects && rm -rf node_modules package-lock.json && npm install --force && PORT=5174 npm run dev
sudo rm -rf node_modules && sudo npm install --unsafe-perm
sudo chown -R $USER:$USER /home/<USER>/projects && rm -rf node_modules package-lock.json && npm install --force && PORT=5174 npm run dev
npm run dev
npm run dev
rm -rf node_modules/.vite && npm run build && npm run preview
npm run dev
sudo chown -R $USER:$USER /home/<USER>/projects && rm -rf node_modules package-lock.json && npm install --force && PORT=5174 npm run dev
sudo chown -R $USER:$USER /home/<USER>/projects
sudo rm -rf node_modules && sudo npm install --unsafe-perm
sudo chown -R $USER:$USER /home/<USER>/projects
sudo chown -R $USER:$USER /home/<USER>/projects && rm -rf node_modules package-lock.json .vite && npm install --force
PORT=5174 NODE_OPTIONS=--openssl-legacy-provider npm run dev
rm -rf node_modules package-lock.json && npm install --force && PORT=5174 NODE_OPTIONS=--openssl-legacy-provider npm run dev
npm run dev
rm -rf node_modules && npm install --force && PORT=5174 npm run dev
npm install element-plus @element-plus/icons-vue axios vue-router@4 --force
rm -rf node_modules && npm install --force && PORT=5174 npm run dev
rm -rf node_modules && npm install --force && PORT=5174 npm run dev
rm -rf node_modules && npm install --force
PORT=5174 npm run dev
npm install @element-plus/icons-vue --force
npm run dev
rm -rf node_modules && npm install --force
PORT=5174 npm run dev
npm run dev
rm -rf node_modules/.vite # 清除 Vite 缓存
npm run dev # 重启开发服务器
rm -rf node_modules/.vite # 清除 Vite 缓存
npm run dev # 重启开发服务器
npm run dev
npm run dev
npm run dev
rm /home/<USER>/projects/src/views/import\ {\ Component\ }\ from\ "react".js
rm '/home/<USER>/projects/src/views/import { Component } from "react".js'
rm '/home/<USER>/projects/src/views/"compilerOptions": {.json'
ls -la /home/<USER>/projects/src/views/
npm run dev
npm run dev -- --force
ls -l /home/<USER>/projects/src/{App.vue,views/AdminLayout.vue,router/index.js}
rm -rf node_modules/.vite && npm run dev -- --force
touch /home/<USER>/projects/src/components/Editor.vue
# Restart development server
npm run dev
rm -rf node_modules/.vite && npm run dev
rm -rf node_modules/.vite && npm run dev
# 查看Console和Network标签页的报错信息
rm -rf node_modules/.vite && npm run dev
localStorage.getItem('token')  # 应返回有效token
sessionStorage.getItem('auth') # 验证其他存储方式
- /api/login      # 登录请求状态码
- /api/userinfo  # 用户信息接口
- /api/news      # 新闻列表接口
Ctrl+Shift+I (Windows) 或 Command+Option+I (Mac)
rm -rf node_modules/.vite && npm run dev
npm run dev -- --port 3000
curl -X POST http://localhost:3000/login -H "Content-Type: application/json" -d '{"username":"test", "password":"test"}'
localStorage.clear() sessionStorage.clear()
npm install -g json-server
npx json-server --watch mock/news.js --port 3001
npx json-server --watch mock/news.js --port 3001 --routes mock/routes.json
npx json-server --watch mock/news.js --port 3001
npx json-server --watch mock/news.json --port 3001
npx json-server --watch mock/news.json --routes mock/routes.json --port 3001
npx json-server --watch mock/news.json --port 3001
json-server --validate mock/news.json
npx json-server mock/news.json --port 3001 --host 0.0.0.0
sudo lsof -i :3001
kill -9 <PID>
npx json-server mock/news.json --port 3002
curl http://localhost:3002/posts
curl -s http://localhost:3002/login | jq
rm -rf node_modules/.vite && npm run dev
# 检查请求payload和响应状态码
curl -X POST http://localhost:3002/login -H 'Content-Type: application/json' -d '{"username":"admin","password":"admin123"}'
curl -I http://localhost:3002/login
ps aux | grep json-server
jsonlint mock/news.json
npm list json-server
python3 -m json.tool mock/news.json
npm install jsonlint -g
jsonlint mock/news.json
npx json-server --watch mock/news.json --port 3002 --host 0.0.0.0
tail -f nohup.out
DEBUG=json-server npx json-server --watch mock/news.json --port 3002 --host 0.0.0.0
lsof -i :3002
sudo ss -K dst port 3002
sudo ss -K dst :3002
python3 -m json.tool mock/news.json
nohup npx json-server --watch mock/news.json --port 3003 --host 0.0.0.0 &
q
python3 -m json.tool mock/news.json
nohup npx json-server --watch mock/news.json --port 3003 --host 0.0.0.0 &
tail -f nohup.out
npx json-server --watch mock/news.json --port 3003 --host 0.0.0.0 &
npm run dev
curl -I http://localhost:3003/login
# 检查5173端口
curl -I http://localhost:5173
curl -X POST http://localhost:3003/login -d 'username=test&password=test123'
ps aux | grep json-server
sudo kill -9 $(lsof -t -i :3003)
pkill -f 'json-server.*3003'
python3 -m json.tool mock/news.json
npx json-server --watch mock/news.json --port 3004 --host 0.0.0.0
DEBUG=json-server* npx json-server --watch mock/news.json --port 3003 --host 0.0.0.0
curl -v -X POST http://localhost:3003/login -d 'username=test&password=test123'
tail -f mock/news.json
curl -X POST http://localhost:3004/login -d 'username=test&password=test123'
npx json-server --watch mock/news.json --port 3004 --host 0.0.0.0
curl -X POST http://localhost:3004/login -d 'username=test&password=test123'
npx json-server --watch mock/news.json --port 3004 --host 0.0.0.0 &
curl -I http://localhost:3004/login
# 检查前端服务
curl -I http://localhost:5173
npm run dev
npx json-server --watch mock/news.json --port 3004 --host 0.0.0.0
npm run dev
npm install -g nodemon
npm run dev:watch
npm run dev --force
npm run dev --force
npx json-server --watch mock/news.json --port 3004 --host 0.0.0.0 &
npm run dev
kill $(lsof -t -i:3004) && kill $(lsof -t -i:5173)
# 启动mock服务
npx json-server --watch mock/news.json --port 3004 --host 0.0.0.0 &
# 启动前端开发服务器
npm run dev
rm -rf node_modules/.vite && npm run dev
json-server --watch mock/news.json --routes mock/routes.json --port 3000
# 终端2 - 启动前端
npm run dev
npx json-server --watch mock/news.json --routes mock/routes.json --port 3004
npx json-server --watch mock/news.json --routes mock/routes.json --port 3004 --host 0.0.0.0
json-server --watch mock/news.json --routes mock/routes.json --port 3000
# 启动前端服务
npm run dev
sudo Power Off
Power Off
sudo PowerOff
PowerOff
sudo poweroff
rm -rf node_modules/.vite && npm run dev
ping www.baidu.com
ping *********
ping www.baidu.com
npm run dev
npm run dev -- --port 8080
npm install
json-server --watch mock/news.json --routes mock/routes.json -p 3000
json-server --watch mock/news.json --port 3000
json-server --watch mock/news.json --routes mock/routes.json -p 3000
json-server --version
npm install -g json-server@latest
json-server mock/news.json --watch -p 3000
json-server --watch mock/news.json -p 3000
sudo lsof -i :3000
sudo ss -tulnp | grep :3000
json-server --watch mock/news.json -p 8000
npm run dev -- --port 8080
npm install vite@5.2.8 @vitejs/plugin-vue@5.0.4 --save-dev
sudo tar -xJf node-v18.20.2-linux-x64.tar.xz -C /usr/local --strip-components=1
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
npx vite --version  # 应显示5.2.8
npm run dev -- --port 8080
sudo tar -xJf node-v18.20.2-linux-x64.tar.xz -C /usr/local --strip-components=1
npm cache clean --force
rm -rf node_modules package-lock.json .nuxt .output
npm install --force
npx vite --version  # 应显示5.2.8
npm run dev -- --port 8080 --host 0.0.0.0
npm run dev -- --port 8080
npm install
npm run dev -- --port 8080
npm install
npm install vite --save-dev
npm run dev -- --port 8080
rm -rf node_modules && npm install
npm run dev -- --port 8080
npm install @types/node --save-dev
npm run dev -- --port 8080
node -v
# 需要v18+版本
# 若版本过低请使用nvm管理：
nvm install 20
nvm use 20
node -v # 必须≥v18
npm cache clean --force
npm install
npm run dev -- --port 8080
npm install vite @vitejs/plugin-vue --save-dev
rm -rf node_modules package-lock.json
npm install
npm run dev -- --port 8080
npm install vite@5.2.8 @vitejs/plugin-vue@5.0.4 --save-dev
node -v
# 如果版本低于v18.20.0，请使用已下载的安装包升级：
sudo tar -xJf node-v18.20.2-linux-x64.tar.xz -C /usr/local --strip-components=1
npm cache clean --force
rm -rf node_modules package-lock.json
npm install vite@5.2.8 @vitejs/plugin-vue@5.0.4 --save-dev
npm install
npm run dev -- --port 8080
json-server --watch mock/news.json --port 8000
npm run dev -- --port 8080
sudo ss -tulpn | grep ':8000\|:8080'  # 查找占用进程
npm run dev -- --port 8080
cd /home/<USER>/projects
rm -rf !(node-v18.20.2-linux-x64.tar.xz) .*
npm create vite@latest . -- --template vue
npm install
npm create vite@latest . -- --template vue
npm install
npm run dev -- --port 8080
cd /home/<USER>/projects
shopt -s extglob
rm -rf !(node-v18.20.2-linux-x64.tar.xz) .*
npm create vite@latest . -- --template vue
rm -rf node_modules package-lock.json
npm install
rm -rf node_modules package-lock.json
npm install
node -v  # 必须 ≥ v18.20.2
npm run dev -- --port 8080
npm audit fix
# 若仍有残留高风险漏洞，执行深度修复（谨慎使用）
npm audit fix --force
mkdir -p src/{views,components,router,store,utils,api/assets}
# 创建路由配置文件
cat > src/router/index.js << 'EOF'
import { createRouter, createWebHashHistory } from 'vue-router'
import NewsList from '../views/NewsList.vue'
import AddNews from '../views/AddNews.vue'

export default createRouter({
  history: createWebHashHistory(),
  routes: [
    { path: '/news', component: NewsList },
    { path: '/news/add', component: AddNews }
  ]
})
EOF

npm run dev -- --port 8080
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash
# 刷新环境变量
source ~/.bashrc
nvm install 18.20.2
# 设置为默认版本
nvm alias default 18.20.2
# 验证版本
node -v  # 应输出 v18.20.2
rm -rf node_modules package-lock.json
npm cache clean --force
# 重新安装依赖
npm install
npm install vue@3.5.17 vue-router@4.4.5 --save
# 安装Element Plus组件库（匹配截图UI风格）
npm install element-plus@2.8.0 --save
# 安装富文本编辑器
npm install @wangeditor/editor-for-vue@5.1.23 --save
# 安装Axios和数据处理工具
npm install axios@1.7.7 lodash@4.17.21 --save
# 安装开发依赖
npm install sass@1.79.4 --save-dev
npx vite --version  # 应输出 5.2.8
rm -rf node_modules package-lock.json
npm cache clean --force
# 强制安装指定版本
npm install vite@5.2.8 @vitejs/plugin-vue@5.0.4 --save-dev
sudo su
npm run dev
rm -rf node_modules package-lock.json
npm cache clean --force
# 强制安装指定版本
npm install vite@5.2.8 @vitejs/plugin-vue@5.0.4 --save-dev
npm run dev -- --port 8080
npm run dev
git blame src/views/NewsList.vue -L 121,121
npm run dev
npm run dev
git checkout -- src/views/NewsList.vue
# 或者回退到上一个提交（谨慎使用会丢失最新修改）
git reset --hard HEAD^
npm run dev
npm run lint
npm install eslint eslint-plugin-vue --save-dev
npm run lint
mv .eslintrc.cjs eslint.config.js
npm install eslint-plugin-vue@9.30.1 --save-dev
npm install eslint-plugin-vue@latest --save-dev
npm view eslint-plugin-vue versions
npm run lint
npm run dev
grep -n '<template>' /home/<USER>/projects/src/views/NewsList.vue
npm run dev
grep -n '<template>' src/views/NewsList.vue
npm run dev
npm install @element-plus/icons-vue
npm run dev
cat package.json | grep element-plus
npm install element-plus
npm run dev
npm install -D vite-plugin-imagemin
sudo apt-get install autoconf automake
npm install -D vite-plugin-imagemin
npm install -D vite-plugin-image-optimizer
npm list vite-plugin-imagemin
sudo apt-get install autoconf automake
npm install -D vite-plugin-imagemin
npm install -D vite-plugin-image-optimizer
npm list vite-plugin-imagemin vite-plugin-image-optimizer
npm run dev
npm install -D vite-plugin-image-optimizer
npm run dev
npm install -D vite-plugin-image-optimizer@1.1.3
npm run dev
npm list vite-plugin-image-optimizer
npm install -D @vheemstra/vite-plugin-image-optimizer
npm uninstall vite-plugin-image-optimizer
rm -rf node_modules package-lock.json && npm install
npm run dev
npm install -D vite-plugin-static-copy
npm run dev
npm install @vitejs/plugin-vue
npm run dev
npm install vue-router@4
npm run dev
npm install pinia element-plus @element-plus/icons-vue
npm run dev
sudo poweOff
sudo poweoff
sudo Poweoff
sudo powe off
sudo poweroff
npm run dev
npm run dev
npm run dev
rm -rf node_modules && npm install
npm run dev
rm -rf node_modules && npm install && npm run dev
rm -rf node_modules && npm install && npm run dev
npm run dev
rm -rf node_modules && npm cache clean --force && npm install && npm run dev
rm -rf node_modules && npm cache clean --force && npm install
npm run dev
npm run dev
npm install @element-plus/icons-vue
npm run dev
curl http://localhost:3000/api/news/list?pageNum=1&pageSize=10
q
curl http://localhost:3000/api/news/list?pageNum=1&pageSize=10
q
mkdir server && cd server
npm init -y
npm install express cors body-parser
cd server
npm start
cd /home/<USER>/projects
# 创建并进入server目录
mkdir server && cd server
cd /home/<USER>/projects/server
npm install
node server.js
lsof -i :3000
# 输出示例：
# COMMAND  PID   USER   FD   TYPE DEVICE SIZE/OFF NODE NAME
# node    1234  user   12u  IPv6  12345      0t0  TCP *:3000 (LISTEN)
# 使用查找到的PID终止进程
kill -9 1234
# 重新启动后端服务
node server.js
curl http://localhost:3000/api/news/list?pageNum=1&pageSize=10
curl http://localhost:3000/api/news/list?pageNum=1&pageSize=10
q
curl http://localhost:3000/api/news/list?pageNum=1&pageSize=10
cd /home/<USER>/projects/server
# 启动服务
node server.js
cd /home/<USER>/projects
# 启动开发服务器
npm run dev
lsof -i :3000 -i :3001 -i :3002
# 输出示例（记录PID列数字）：
# COMMAND  PID   USER   FD   TYPE DEVICE SIZE/OFF NODE NAME
# node    5678  user   12u  IPv6  12345      0t0  TCP *:3001 (LISTEN)
# 终止占用进程（替换5678为实际PID）
kill -9 5678
# 重启后端服务
cd /home/<USER>/projects/server
node server.js
lsof -i :3000 -i :3001 -i :3002
# 输出示例（记录PID列数字）：
# COMMAND  PID   USER   FD   TYPE DEVICE SIZE/OFF NODE NAME
# node    5678  user   12u  IPv6  12345      0t0  TCP *:3001 (LISTEN)
# 终止占用进程（替换5678为实际PID）
kill -9 5678
# 重启后端服务
cd /home/<USER>/projects/server
node server.js
lsof -i :3000 -i :3001 -i :3002
# 输出示例（记录PID列数字）：
# COMMAND  PID   USER   FD   TYPE DEVICE SIZE/OFF NODE NAME
# node    5678  user   12u  IPv6  12345      0t0  TCP *:3001 (LISTEN)
# 终止占用进程（替换5678为实际PID）
kill -9 5678
# 重启后端服务
cd /home/<USER>/projects/server
node server.js
lsof -i :3002
# 如果无输出，表示端口可用
cd /home/<USER>/projects/server
node server.js
lsof -i :3002
# 如果无输出，表示端口可用
npm run dev
cd server && npm install mongoose
q
cd /home/<USER>
mkdir -p /home/<USER>/new_project
cd /home/<USER>/new_project
# 创建开发计划Markdown文件
cat > development_plan.md << 'EOF'
# 项目开发计划

## 1. 项目概述
- 项目名称：
- 项目目标：
- 技术栈：

## 2. 开发阶段
- 阶段一：需求分析与设计
- 阶段二：核心功能开发
- 阶段三：测试与优化
- 阶段四：部署上线

## 3. 任务分配

## 4. 时间节点

## 5. 风险评估
EOF

# 确认文件创建成功
ls -l development_plan.md
cat > development_plan.md << 'EOF'
# 项目开发计划

## 1. 项目概述
- 项目名称：
- 项目目标：
- 技术栈：

## 2. 开发阶段
...
EOF

touch filename.md
mkdir "荣联新闻管理平台"
mkdir "/path/to/荣联新闻管理平台"
mkdir "荣联新闻管理平台"
mkdir "/home/<USER>/荣联新闻管理平台"
ls -l "荣联新闻管理平台"
ls -l "/home/<USER>/荣联新闻管理平台"
sudo mkdir rlxwglpt
ls -l "/home/<USER>/rlxwglpt

ls -l "/home/<USER>/rlxwglpt"
ls -l "rlxwglpt"
sudo apt update
sudo apt upgrade
df
df -k
df -G
df -g
df -k
df -m
cd ~
ls
mkdir ronglianweb
exit
ls
pwd
exit
ls
pwd
exit
ls
pwd
ls
exit
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
npm start
sudo npm start
PORT=80 npm start
npm start
